﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectGuid>{58987d60-e773-4cd1-8a03-803a946cf0f9}</ProjectGuid>
    <MainSource>M2Server.dpr</MainSource>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
    <DCC_DependencyCheckOutputName>..\MirServer\Mir200\M2Server.exe</DCC_DependencyCheckOutputName>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_DebugInformation>False</DCC_DebugInformation>
    <DCC_LocalDebugSymbols>False</DCC_LocalDebugSymbols>
    <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
    <DCC_ExeOutput>..\MirServer</DCC_ExeOutput>
    <DCC_DcuOutput>..\Build\M2Server</DCC_DcuOutput>
    <DCC_ObjOutput>..\Build\M2Server</DCC_ObjOutput>
    <DCC_HppOutput>..\Build\M2Server</DCC_HppOutput>
    <DCC_UnitSearchPath>D:\EGameOfMir\Component\JSocket;D:\EGameOfMir\Component\加密控件;D:\EGameOfMir\Component\加密控件\Apps;D:\EGameOfMir\Component\加密控件\Ciphers;D:\EGameOfMir\Component\加密控件\Dcr32;D:\EGameOfMir\Component\加密控件\Hashes</DCC_UnitSearchPath>
    <DCC_ResourcePath>D:\EGameOfMir\Component\JSocket;D:\EGameOfMir\Component\加密控件;D:\EGameOfMir\Component\加密控件\Apps;D:\EGameOfMir\Component\加密控件\Ciphers;D:\EGameOfMir\Component\加密控件\Dcr32;D:\EGameOfMir\Component\加密控件\Hashes</DCC_ResourcePath>
    <DCC_ObjPath>D:\EGameOfMir\Component\JSocket;D:\EGameOfMir\Component\加密控件;D:\EGameOfMir\Component\加密控件\Apps;D:\EGameOfMir\Component\加密控件\Ciphers;D:\EGameOfMir\Component\加密控件\Dcr32;D:\EGameOfMir\Component\加密控件\Hashes</DCC_ObjPath>
    <DCC_IncludePath>D:\EGameOfMir\Component\JSocket;D:\EGameOfMir\Component\加密控件;D:\EGameOfMir\Component\加密控件\Apps;D:\EGameOfMir\Component\加密控件\Ciphers;D:\EGameOfMir\Component\加密控件\Dcr32;D:\EGameOfMir\Component\加密控件\Hashes</DCC_IncludePath>
    <DCC_Define>RELEASE</DCC_Define>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_ExeOutput>..\MirServer\Mir200</DCC_ExeOutput>
    <DCC_DcuOutput>..\Build\M2Server</DCC_DcuOutput>
    <DCC_ObjOutput>..\Build\M2Server</DCC_ObjOutput>
    <DCC_HppOutput>..\Build\M2Server</DCC_HppOutput>
    <DCC_UnitSearchPath>..\Component\JSocket;..\Component\加密控件;..\Component\加密控件\Apps;..\Component\加密控件\Ciphers;..\Component\加密控件\Dcr32;..\Component\加密控件\Hashes</DCC_UnitSearchPath>
    <DCC_ResourcePath>..\Component\JSocket;..\Component\加密控件;..\Component\加密控件\Apps;..\Component\加密控件\Ciphers;..\Component\加密控件\Dcr32;..\Component\加密控件\Hashes</DCC_ResourcePath>
    <DCC_ObjPath>..\Component\JSocket;..\Component\加密控件;..\Component\加密控件\Apps;..\Component\加密控件\Ciphers;..\Component\加密控件\Dcr32;..\Component\加密控件\Hashes</DCC_ObjPath>
    <DCC_IncludePath>..\Component\JSocket;..\Component\加密控件;..\Component\加密控件\Apps;..\Component\加密控件\Ciphers;..\Component\加密控件\Dcr32;..\Component\加密控件\Hashes</DCC_IncludePath>
    <DCC_Define>DEBUG</DCC_Define>
  </PropertyGroup>
  <ProjectExtensions>
    <Borland.Personality>Delphi.Personality</Borland.Personality>
    <Borland.ProjectType>VCLApplication</Borland.ProjectType>
    <BorlandProject>
<BorlandProject xmlns=""> <Delphi.Personality>   <Parameters>
      <Parameters Name="UseLauncher">False</Parameters>
      <Parameters Name="LoadAllSymbols">True</Parameters>
      <Parameters Name="LoadUnspecifiedSymbols">False</Parameters>
    </Parameters>
    <VersionInfo>
      <VersionInfo Name="IncludeVerInfo">False</VersionInfo>
      <VersionInfo Name="AutoIncBuild">False</VersionInfo>
      <VersionInfo Name="MajorVer">2</VersionInfo>
      <VersionInfo Name="MinorVer">0</VersionInfo>
      <VersionInfo Name="Release">0</VersionInfo>
      <VersionInfo Name="Build">0</VersionInfo>
      <VersionInfo Name="Debug">False</VersionInfo>
      <VersionInfo Name="PreRelease">False</VersionInfo>
      <VersionInfo Name="Special">False</VersionInfo>
      <VersionInfo Name="Private">False</VersionInfo>
      <VersionInfo Name="DLL">False</VersionInfo>
      <VersionInfo Name="Locale">2052</VersionInfo>
      <VersionInfo Name="CodePage">936</VersionInfo>
    </VersionInfo>
    <VersionInfoKeys>
      <VersionInfoKeys Name="CompanyName">飞尔世界</VersionInfoKeys>
      <VersionInfoKeys Name="FileDescription">飞尔世界数据处理引擎</VersionInfoKeys>
      <VersionInfoKeys Name="FileVersion">*******</VersionInfoKeys>
      <VersionInfoKeys Name="InternalName">EngineServer.exe</VersionInfoKeys>
      <VersionInfoKeys Name="LegalCopyright">版权所有(C) 飞尔世界.  2005-2008</VersionInfoKeys>
      <VersionInfoKeys Name="LegalTrademarks">飞尔世界游戏引擎</VersionInfoKeys>
      <VersionInfoKeys Name="OriginalFilename">EngineServer.exe</VersionInfoKeys>
      <VersionInfoKeys Name="ProductName">网络游戏数据处理引擎</VersionInfoKeys>
      <VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys>
      <VersionInfoKeys Name="Comments"></VersionInfoKeys>
    </VersionInfoKeys>
    <Source>
      <Source Name="MainSource">M2Server.dpr</Source>
    </Source>
  </Delphi.Personality> </BorlandProject></BorlandProject>
  </ProjectExtensions>
  <ItemGroup />
  <ItemGroup>
    <DelphiCompile Include="M2Server.dpr">
      <MainSource>MainSource</MainSource>
    </DelphiCompile>
    <DCCReference Include="..\Common\Common.pas" />
    <DCCReference Include="..\Common\DESTR.pas" />
    <DCCReference Include="..\Common\EDcodeUnit.pas" />
    <DCCReference Include="..\Common\Grobal2.pas" />
    <DCCReference Include="..\Common\HUtil32.pas" />
    <DCCReference Include="..\Common\Mudutil.pas" />
    <DCCReference Include="AboutUnit.pas">
      <Form>FrmAbout</Form>
    </DCCReference>
    <DCCReference Include="ActionSpeedConfig.pas">
      <Form>frmActionSpeed</Form>
    </DCCReference>
    <DCCReference Include="AttackSabukWallConfig.pas">
      <Form>FrmAttackSabukWall</Form>
    </DCCReference>
    <DCCReference Include="BnkEngn.pas" />
    <DCCReference Include="Castle.pas" />
    <DCCReference Include="CastleManage.pas">
      <Form>frmCastleManage</Form>
    </DCCReference>
    <DCCReference Include="ConfigMerchant.pas">
      <Form>frmConfigMerchant</Form>
    </DCCReference>
    <DCCReference Include="ConfigMonGen.pas">
      <Form>frmConfigMonGen</Form>
    </DCCReference>
    <DCCReference Include="EDcode.pas" />
    <DCCReference Include="EngineRegister.pas">
      <Form>FrmRegister</Form>
    </DCCReference>
    <DCCReference Include="Envir.pas" />
    <DCCReference Include="Event.pas" />
    <DCCReference Include="FrnEngn.pas" />
    <DCCReference Include="FSrvValue.pas">
      <Form>FrmServerValue</Form>
    </DCCReference>
    <DCCReference Include="FunctionConfig.pas">
      <Form>frmFunctionConfig</Form>
    </DCCReference>
    <DCCReference Include="GameCommand.pas">
      <Form>frmGameCmd</Form>
    </DCCReference>
    <DCCReference Include="GameConfig.pas">
      <Form>frmGameConfig</Form>
    </DCCReference>
    <DCCReference Include="GeneralConfig.pas">
      <Form>frmGeneralConfig</Form>
    </DCCReference>
    <DCCReference Include="Guild.pas" />
    <DCCReference Include="HumanInfo.pas">
      <Form>frmHumanInfo</Form>
    </DCCReference>
    <DCCReference Include="IdSrvClient.pas">
      <Form>FrmIDSoc</Form>
    </DCCReference>
    <DCCReference Include="InterMsgClient.pas">
      <Form>FrmMsgClient</Form>
    </DCCReference>
    <DCCReference Include="InterServerMsg.pas">
      <Form>FrmSrvMsg</Form>
    </DCCReference>
    <DCCReference Include="ItemSet.pas">
      <Form>frmItemSet</Form>
    </DCCReference>
    <DCCReference Include="ItmUnit.pas" />
    <DCCReference Include="JClasses.pas" />
    <DCCReference Include="LocalDB.pas">
      <Form>FrmDB</Form>
    </DCCReference>
    <DCCReference Include="M2Share.pas" />
    <DCCReference Include="Magic.pas" />
    <DCCReference Include="Mission.pas" />
    <DCCReference Include="MonsterConfig.pas">
      <Form>frmMonsterConfig</Form>
    </DCCReference>
    <DCCReference Include="NoticeM.pas" />
    <DCCReference Include="ObjAxeMon.pas" />
    <DCCReference Include="ObjBase.pas" />
    <DCCReference Include="ObjGuard.pas" />
    <DCCReference Include="ObjMon.pas" />
    <DCCReference Include="ObjMon2.pas" />
    <DCCReference Include="ObjNpc.pas" />
    <DCCReference Include="ObjPlayMon.pas" />
    <DCCReference Include="ObjRobot.pas" />
    <DCCReference Include="OnlineMsg.pas">
      <Form>frmOnlineMsg</Form>
    </DCCReference>
    <DCCReference Include="PlugIn.pas" />
    <DCCReference Include="PlugInManage.pas">
      <Form>ftmPlugInManage</Form>
    </DCCReference>
    <DCCReference Include="PlugOfEngine.pas" />
    <DCCReference Include="RunDB.pas" />
    <DCCReference Include="RunSock.pas" />
    <DCCReference Include="SDK.pas" />
    <DCCReference Include="svMain.pas">
      <Form>FrmMain</Form>
    </DCCReference>
    <DCCReference Include="UnitManage.pas" />
    <DCCReference Include="UsrEngn.pas" />
    <DCCReference Include="ViewKernelInfo.pas">
      <Form>frmViewKernelInfo</Form>
    </DCCReference>
    <DCCReference Include="ViewLevel.pas">
      <Form>frmViewLevel</Form>
    </DCCReference>
    <DCCReference Include="ViewList.pas">
      <Form>frmViewList</Form>
    </DCCReference>
    <DCCReference Include="ViewOnlineHuman.pas">
      <Form>frmViewOnlineHuman</Form>
    </DCCReference>
    <DCCReference Include="ViewSession.pas">
      <Form>frmViewSession</Form>
    </DCCReference>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Borland.Delphi.Targets" />
</Project>