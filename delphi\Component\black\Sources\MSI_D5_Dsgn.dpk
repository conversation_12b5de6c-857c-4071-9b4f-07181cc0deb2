package MSI_D5_Dsgn;

{$R *.RES}
{$ASSERTIONS ON}
{$BOOLEVAL OFF}
{$DEBU<PERSON>NFO ON}
{$EXTENDEDSYNTAX ON}
{$IMPORTEDDATA ON}
{$IOCHECKS ON}
{$LOCALSYMBOLS ON}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION ON}
{$OVERFLOWCHECKS OFF}
{$RANGECHECKS OFF}
{$REFERENCEINFO ON}
{$SAFEDIVIDE OFF}
{$STACKFRAMES OFF}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST ON}
{$MINENUMSIZE 1}
{$IMAGEBASE $400000}
{$DESCRIPTION 'MiTeC System Information Component'}
{$DESIGNONLY}
{$IMPLICITBUILD OFF}

requires
  vcl50,
  MSI_D5_Rtl;

contains
  MSI_DsgnIntf in 'MSI_DsgnIntf.pas';

end.
