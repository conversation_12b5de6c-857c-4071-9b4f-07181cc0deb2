# GameEngine 核心功能实现文档

## 概述

本文档描述了根据原Delphi项目实现的GameEngine四个核心功能：
1. 仓库和交易系统
2. 技能和魔法系统
3. 任务系统
4. 小地图功能

## 1. 仓库和交易系统

### 1.1 仓库系统 (StorageManager)

**功能特性：**
- 玩家个人仓库管理
- 密码保护机制
- 物品存储和取出
- 金币存储和取出
- 仓库容量管理
- 数据持久化

**主要接口：**
```cpp
bool OpenStorage(PlayObject* player, const std::string& password);
bool CloseStorage(PlayObject* player);
bool StoreItem(PlayObject* player, const UserItem& item);
bool TakeItem(PlayObject* player, WORD makeIndex, UserItem& outItem);
bool StoreGold(PlayObject* player, DWORD amount);
bool TakeGold(PlayObject* player, DWORD amount);
```

**实现特点：**
- 线程安全的数据访问
- 自动会话超时管理
- 密码加密存储
- 统计信息收集

### 1.2 交易系统 (TradeManager)

**功能特性：**
- 玩家间物品交易
- 金币交易
- 交易状态管理
- 交易安全验证
- 交易超时处理

**主要接口：**
```cpp
bool RequestTrade(PlayObject* requester, PlayObject* target);
bool AcceptTrade(PlayObject* player, const std::string& requesterName);
bool AddTradeItem(PlayObject* player, const UserItem& item);
bool SetTradeGold(PlayObject* player, DWORD amount);
bool LockTrade(PlayObject* player);
bool CompleteTrade(PlayObject* player);
```

**交易流程：**
1. 发起交易请求
2. 对方接受/拒绝
3. 双方添加物品和金币
4. 双方锁定交易
5. 确认完成交易

## 2. 技能和魔法系统

### 2.1 技能系统

**功能特性：**
- 技能学习和升级
- 技能经验管理
- 技能冷却时间
- 职业技能限制

### 2.2 魔法系统 (MagicManager - 已扩展)

**功能特性：**
- 魔法释放机制
- 魔法效果处理
- MP消耗管理
- 魔法冷却时间

## 3. 任务系统 (QuestManager)

### 3.1 任务管理

**功能特性：**
- 任务创建和分配
- 任务进度跟踪
- 任务完成验证
- 任务奖励发放
- 可重复任务支持

**任务类型：**
- 击杀怪物任务
- 收集物品任务
- 对话任务
- 探索地图任务
- 等级达成任务

**主要接口：**
```cpp
bool AcceptQuest(PlayObject* player, WORD questId);
bool CompleteQuest(PlayObject* player, WORD questId);
void UpdateQuestProgress(PlayObject* player, QuestType type, const std::string& target, int count);
void OnMonsterKilled(PlayObject* player, const std::string& monsterName);
void OnItemCollected(PlayObject* player, const UserItem& item);
```

### 3.2 任务数据结构

**QuestData：**
- 任务ID和名称
- 任务描述和类型
- 等级和职业要求
- 前置任务要求
- 任务目标列表
- 奖励信息

**PlayerQuestStatus：**
- 任务状态跟踪
- 目标完成进度
- 接取和完成时间

## 4. 小地图功能 (MiniMapManager)

### 4.1 小地图数据

**功能特性：**
- 小地图图像生成
- 地图标记管理
- 玩家位置跟踪
- 队友位置显示
- NPC和传送门标记

**主要接口：**
```cpp
bool LoadMiniMapData(const std::string& mapName);
bool SendMiniMapToPlayer(PlayObject* player, const std::string& mapName);
void AddMapMark(const std::string& mapName, const MapMark& mark);
void UpdatePlayerPosition(PlayObject* player, const Point& position);
```

### 4.2 地图标记类型

- 玩家位置 (绿色)
- 队友位置 (蓝色)
- NPC位置 (蓝色)
- 传送门 (紫色)
- 地标 (黄色)
- 任务目标 (橙色)
- 怪物位置 (红色)

## 5. 系统集成

### 5.1 GameEngine集成

所有核心功能都已集成到GameEngine中：

```cpp
// 管理器实例
std::unique_ptr<StorageManager> m_storageManager;
std::unique_ptr<TradeManager> m_tradeManager;
std::unique_ptr<QuestManager> m_questManager;
std::unique_ptr<MiniMapManager> m_miniMapManager;

// 访问器方法
StorageManager* GetStorageManager() const;
TradeManager* GetTradeManager() const;
QuestManager* GetQuestManager() const;
MiniMapManager* GetMiniMapManager() const;
```

### 5.2 运行时处理

在GameEngine的主循环中，所有管理器都会定期运行：

```cpp
void GameEngine::ProcessGameLogic() {
    // ... 其他管理器
    
    if (m_storageManager) {
        m_storageManager->CleanupExpiredSessions();
    }
    
    if (m_tradeManager) {
        m_tradeManager->Run();
    }
    
    if (m_questManager) {
        m_questManager->Run();
    }
    
    if (m_miniMapManager) {
        m_miniMapManager->Run();
    }
}
```

## 6. 数据持久化

### 6.1 数据库设计

每个系统都设计了相应的数据库接口：
- 仓库数据存储
- 交易记录
- 任务进度保存
- 小地图缓存

### 6.2 文件格式

- 任务配置文件：`data/quests.txt`
- 小地图缓存：`data/minimaps/*.minimap`

## 7. 配置和扩展

### 7.1 可配置参数

- 仓库最大容量
- 交易超时时间
- 任务时间限制
- 小地图更新间隔

### 7.2 扩展性

所有系统都设计为可扩展的：
- 新的任务类型可以轻松添加
- 地图标记类型可以扩展
- 交易规则可以自定义
- 仓库功能可以增强

## 8. 测试和验证

创建了测试文件 `test_core_features.cpp` 来验证各个系统的集成和功能。

## 9. 总结

本次实现完成了GameEngine的四个核心功能，保持了与原Delphi项目的一致性，同时采用了现代C++的设计模式和最佳实践。所有功能都已集成到GameEngine中，可以通过统一的接口进行访问和管理。
