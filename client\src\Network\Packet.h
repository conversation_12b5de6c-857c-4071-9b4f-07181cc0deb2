#pragma once

#include <cstdint>
#include <vector>
#include <string>
#include <memory>

/**
 * @enum PacketType
 * @brief Types of network packets
 */
enum class PacketType : uint16_t {
    NONE = 0,

    // Connection packets
    CONNECT = 1,
    DISCONNECT = 2,
    KEEP_ALIVE = 3,

    // Login packets
    LOGIN_REQUEST = 101,        // CM_IDPASSWORD
    LOGIN_RESPONSE = 102,       // SM_CERTIFICATION_SUCCESS, SM_CERTIFICATION_FAIL
    CHANGE_PASSWORD = 103,      // CM_CHANGEPASSWORD
    PASSWORD_CHANGED = 104,     // SM_CHGPASSWD_SUCCESS, SM_CHGPASSWD_FAIL
    GET_BACK_PASSWORD = 105,    // CM_GETBACKPASSWORD
    PASSWORD_RETRIEVED = 106,   // SM_GETBACKPASSWD_SUCCESS, SM_GETBACKPASSWD_FAIL

    // Server selection packets
    SERVER_LIST_REQUEST = 110,  // CM_SELECTSERVER
    SERVER_LIST = 111,          // SM_SELECTSERVER_OK
    SERVER_CONNECT_REQUEST = 112,
    SERVER_CONNECT_RESPONSE = 113,

    // Character packets
    CHARACTER_LIST_REQUEST = 120,   // CM_QUERYCHR
    CHARACTER_LIST = 121,           // SM_QUERYCHR
    CHARACTER_CREATE_REQUEST = 122, // CM_NEWCHR
    CHARACTER_CREATE_RESPONSE = 123,// SM_NEWCHR_SUCCESS, SM_NEWCHR_FAIL
    CHARACTER_DELETE_REQUEST = 124, // CM_DELCHR
    CHARACTER_DELETE_RESPONSE = 125,// SM_DELCHR_SUCCESS, SM_DELCHR_FAIL
    CHARACTER_SELECT_REQUEST = 126, // CM_SELCHR
    CHARACTER_SELECT_RESPONSE = 127,// SM_STARTPLAY, SM_STARTFAIL

    // Game packets
    ENTER_GAME = 201,               // SM_LOGON
    LEAVE_GAME = 202,               // CM_SOFTCLOSE
    PLAYER_MOVE = 203,              // CM_WALK, CM_RUN
    PLAYER_ATTACK = 204,            // CM_HIT
    PLAYER_CAST = 205,              // CM_SPELL
    PLAYER_PICKUP = 206,            // CM_PICKUP
    PLAYER_DROP = 207,              // CM_DROPITEM
    PLAYER_USE_ITEM = 208,          // CM_EAT
    PLAYER_EQUIP = 209,             // CM_TAKEONITEM
    PLAYER_UNEQUIP = 210,           // CM_TAKEOFFITEM

    // Extended game packets
    PLAYER_TURN = 220,              // CM_TURN
    PLAYER_SIT = 221,               // CM_SITDOWN
    PLAYER_HEAVY_HIT = 222,         // CM_HEAVYHIT
    PLAYER_BIG_HIT = 223,           // CM_BIGHIT
    PLAYER_POWER_HIT = 224,         // CM_POWERHIT
    PLAYER_LONG_HIT = 225,          // CM_LONGHIT
    PLAYER_WIDE_HIT = 226,          // CM_WIDEHIT
    PLAYER_FIRE_HIT = 227,          // CM_FIREHIT
    PLAYER_THROW = 228,             // CM_THROW
    PLAYER_BUTCH = 229,             // CM_BUTCH

    // Inventory and item packets
    QUERY_BAG_ITEMS = 250,          // CM_QUERYBAGITEMS
    BAG_ITEMS = 251,                // SM_BAGITEMS
    ADD_ITEM = 252,                 // SM_ADDITEM
    DELETE_ITEM = 253,              // SM_DELITEM
    UPDATE_ITEM = 254,              // SM_UPDATEITEM
    DROP_ITEM_RESULT = 255,         // SM_DROPITEM_SUCCESS, SM_DROPITEM_FAIL
    ITEM_SHOW = 256,                // SM_ITEMSHOW
    ITEM_HIDE = 257,                // SM_ITEMHIDE
    GOLD_CHANGED = 258,             // SM_GOLDCHANGED
    WEIGHT_CHANGED = 259,           // SM_WEIGHTCHANGED
    DURA_CHANGED = 260,             // SM_DURACHANGE

    // Door packets
    OPEN_DOOR = 270,                // CM_OPENDOOR
    OPEN_DOOR_RESULT = 271,         // SM_OPENDOOR_OK, SM_OPENDOOR_LOCK
    CLOSE_DOOR = 272,               // SM_CLOSEDOOR

    // Magic packets
    ADD_MAGIC = 280,                // SM_ADDMAGIC
    SEND_MY_MAGIC = 281,            // SM_SENDMYMAGIC
    DELETE_MAGIC = 282,             // SM_DELMAGIC
    MAGIC_FIRE = 283,               // SM_MAGICFIRE
    MAGIC_FIRE_FAIL = 284,          // SM_MAGICFIRE_FAIL
    MAGIC_LEVEL_EXP = 285,          // SM_MAGIC_LVEXP

    // Status packets
    QUERY_USER_STATE = 290,         // CM_QUERYUSERSTATE
    USER_STATE = 291,               // SM_SENDUSERSTATE
    HEALTH_SPELL_CHANGED = 292,     // SM_HEALTHSPELLCHANGED
    ABILITY = 293,                  // SM_ABILITY
    SUBABILITY = 294,               // SM_SUBABILITY
    ADJUST_BONUS = 295,             // SM_ADJUST_BONUS
    OPEN_HEALTH = 296,              // SM_OPENHEALTH
    CLOSE_HEALTH = 297,             // SM_CLOSEHEALTH
    CHANGE_FACE = 298,              // SM_CHANGEFACE
    BREAK_WEAPON = 299,             // SM_BREAKWEAPON

    // Chat packets
    CHAT_MESSAGE = 301,             // CM_SAY, SM_HEAR
    WHISPER_MESSAGE = 302,          // SM_WHISPER
    GUILD_MESSAGE = 303,            // SM_GUILDMESSAGE
    SYSTEM_MESSAGE = 304,           // SM_SYSMESSAGE
    CRY_MESSAGE = 305,              // SM_CRY
    GROUP_MESSAGE = 306,            // SM_GROUPMESSAGE
    MERCHANT_SAY = 307,             // SM_MERCHANTSAY

    // Entity packets
    SPAWN_PLAYER = 401,             // SM_TURN, SM_WALK, SM_RUN
    SPAWN_MONSTER = 402,            // SM_TURN, SM_WALK, SM_RUN
    SPAWN_NPC = 403,                // SM_TURN, SM_WALK, SM_RUN
    SPAWN_ITEM = 404,               // SM_ITEMSHOW
    DESPAWN_ENTITY = 405,           // SM_DISAPPEAR, SM_ITEMHIDE
    ENTITY_MOVE = 406,              // SM_TURN, SM_WALK, SM_RUN
    ENTITY_ATTACK = 407,            // SM_HIT, SM_HEAVYHIT, SM_BIGHIT, SM_POWERHIT, SM_LONGHIT, SM_WIDEHIT
    ENTITY_CAST = 408,              // SM_SPELL
    ENTITY_EFFECT = 409,            // SM_MAGICFIRE
    ENTITY_STATE = 410,             // SM_FEATURECHANGED, SM_CHARSTATUSCHANGED
    ENTITY_STRUCK = 411,            // SM_STRUCK
    ENTITY_DEATH = 412,             // SM_DEATH
    ENTITY_SKELETON = 413,          // SM_SKELETON
    ENTITY_ALIVE = 414,             // SM_ALIVE
    ENTITY_LEVEL_UP = 415,          // SM_LEVELUP
    ENTITY_CHANGE_NAME_COLOR = 416, // SM_CHANGENAMECOLOR
    ENTITY_CHANGE_LIGHT = 417,      // SM_CHANGELIGHT
    ENTITY_WIN_EXP = 418,           // SM_WINEXP

    // Map packets
    MAP_CHANGE = 501,               // SM_NEWMAP
    MAP_DATA = 502,                 // SM_MAPDESCRIPTION
    CLEAR_OBJECTS = 503,            // SM_CLEAROBJECTS
    CHANGE_MAP = 504,               // SM_CHANGEMAP
    AREA_STATE = 505,               // SM_AREASTATE
    DAY_CHANGING = 506,             // SM_DAYCHANGING

    // Game flow packets
    START_PLAY = 520,               // SM_STARTPLAY
    RECONNECT = 521,                // SM_RECONNECT

    // Group packets
    GROUP_MODE = 601,               // CM_GROUPMODE
    GROUP_MODE_CHANGED = 602,       // SM_GROUPMODECHANGED
    CREATE_GROUP = 603,             // CM_CREATEGROUP
    CREATE_GROUP_RESULT = 604,      // SM_CREATEGROUP_OK, SM_CREATEGROUP_FAIL
    ADD_GROUP_MEMBER = 605,         // CM_ADDGROUPMEMBER
    ADD_GROUP_MEMBER_RESULT = 606,  // SM_GROUPADDMEM_OK, SM_GROUPADDMEM_FAIL
    DELETE_GROUP_MEMBER = 607,      // CM_DELGROUPMEMBER
    DELETE_GROUP_MEMBER_RESULT = 608,// SM_GROUPDELMEM_OK, SM_GROUPDELMEM_FAIL
    GROUP_MEMBERS = 609,            // SM_GROUPMEMBERS
    GROUP_CANCEL = 610,             // SM_GROUPCANCEL

    // Guild packets
    OPEN_GUILD_DIALOG = 701,        // CM_OPENGUILDDLG
    OPEN_GUILD_DIALOG_RESULT = 702, // SM_OPENGUILDDLG, SM_OPENGUILDDLG_FAIL
    GUILD_HOME = 703,               // CM_GUILDHOME
    GUILD_MEMBER_LIST = 704,        // CM_GUILDMEMBERLIST
    SEND_GUILD_MEMBER_LIST = 705,   // SM_SENDGUILDMEMBERLIST
    ADD_GUILD_MEMBER = 706,         // CM_GUILDADDMEMBER
    ADD_GUILD_MEMBER_RESULT = 707,  // SM_GUILDADDMEMBER_OK, SM_GUILDADDMEMBER_FAIL
    DELETE_GUILD_MEMBER = 708,      // CM_GUILDDELMEMBER
    DELETE_GUILD_MEMBER_RESULT = 709,// SM_GUILDDELMEMBER_OK, SM_GUILDDELMEMBER_FAIL
    GUILD_UPDATE_NOTICE = 710,      // CM_GUILDUPDATENOTICE
    GUILD_UPDATE_RANK_INFO = 711,   // CM_GUILDUPDATERANKINFO
    GUILD_RANK_UPDATE_FAIL = 712,   // SM_GUILDRANKUPDATE_FAIL
    BUILD_GUILD_OK = 713,           // SM_BUILDGUILD_OK
    BUILD_GUILD_FAIL = 714,         // SM_BUILDGUILD_FAIL
    GUILD_ALLY = 715,               // CM_GUILDALLY
    GUILD_MAKE_ALLY_OK = 716,       // SM_GUILDMAKEALLY_OK
    GUILD_MAKE_ALLY_FAIL = 717,     // SM_GUILDMAKEALLY_FAIL
    GUILD_BREAK_ALLY = 718,         // CM_GUILDBREAKALLY
    GUILD_BREAK_ALLY_OK = 719,      // SM_GUILDBREAKALLY_OK
    GUILD_BREAK_ALLY_FAIL = 720,    // SM_GUILDBREAKALLY_FAIL
    CHANGE_GUILD_NAME = 721,        // SM_CHANGEGUILDNAME

    // NPC interaction packets
    CLICK_NPC = 801,                // CM_CLICKNPC
    MERCHANT_DIALOG_SELECT = 802,   // CM_MERCHANTDLGSELECT
    MERCHANT_QUERY_SELL_PRICE = 803,// CM_MERCHANTQUERYSELLPRICE
    USER_SELL_ITEM = 804,           // CM_USERSELLITEM
    USER_SELL_ITEM_RESULT = 805,    // SM_USERSELLITEM_OK, SM_USERSELLITEM_FAIL
    USER_BUY_ITEM = 806,            // CM_USERBUYITEM
    USER_BUY_ITEM_RESULT = 807,     // SM_BUYITEM_SUCCESS, SM_BUYITEM_FAIL
    USER_GET_DETAIL_ITEM = 808,     // CM_USERGETDETAILITEM
    SEND_GOODS_LIST = 809,          // SM_SENDGOODSLIST
    SEND_USER_SELL = 810,           // SM_SENDUSERSELL
    SEND_BUY_PRICE = 811,           // SM_SENDBUYPRICE
    SEND_DETAIL_GOODS_LIST = 812,   // SM_SENDDETAILGOODSLIST
    MERCHANT_DIALOG_CLOSE = 813,    // SM_MERCHANTDLGCLOSE

    // Error packets
    ERROR = 901,                    // SM_STARTFAIL, etc.
    VERSION_FAIL = 902              // SM_VERSION_FAIL
};

/**
 * @class Packet
 * @brief Network packet for client-server communication
 *
 * This class represents a network packet that can be sent between the client and server.
 * It provides methods for reading and writing different data types.
 */
class Packet {
private:
    PacketType m_type;                  ///< Packet type
    std::vector<uint8_t> m_data;        ///< Packet data
    mutable size_t m_readPos;           ///< Current read position (mutable to allow const access)

public:
    /**
     * @brief Constructor
     * @param type Packet type
     */
    Packet(PacketType type = PacketType::NONE);

    /**
     * @brief Destructor
     */
    ~Packet();

    /**
     * @brief Clear the packet
     */
    void Clear();

    /**
     * @brief Get the packet type
     * @return Packet type
     */
    PacketType GetType() const { return m_type; }

    /**
     * @brief Set the packet type
     * @param type Packet type
     */
    void SetType(PacketType type) { m_type = type; }

    /**
     * @brief Get the packet data
     * @return Packet data
     */
    const std::vector<uint8_t>& GetData() const { return m_data; }

    /**
     * @brief Get the packet size
     * @return Packet size in bytes
     */
    size_t GetSize() const { return m_data.size(); }

    /**
     * @brief Get the current read position
     * @return Read position
     */
    size_t GetReadPos() const { return m_readPos; }

    /**
     * @brief Set the read position
     * @param pos Read position
     */
    void SetReadPos(size_t pos) const { m_readPos = pos; }

    /**
     * @brief Check if all data has been read
     * @return true if all data has been read, false otherwise
     */
    bool EndOfPacket() const { return m_readPos >= m_data.size(); }

    /**
     * @brief Write a byte
     * @param value Byte value
     * @return Reference to this packet
     */
    Packet& operator<<(uint8_t value);

    /**
     * @brief Write a signed 8-bit integer
     * @param value Signed 8-bit integer value
     * @return Reference to this packet
     */
    Packet& operator<<(int8_t value);

    /**
     * @brief Write a 16-bit integer
     * @param value 16-bit integer value
     * @return Reference to this packet
     */
    Packet& operator<<(uint16_t value);

    /**
     * @brief Write a signed 16-bit integer
     * @param value Signed 16-bit integer value
     * @return Reference to this packet
     */
    Packet& operator<<(int16_t value);

    /**
     * @brief Write a 32-bit integer
     * @param value 32-bit integer value
     * @return Reference to this packet
     */
    Packet& operator<<(uint32_t value);

    /**
     * @brief Write a signed 32-bit integer
     * @param value Signed 32-bit integer value
     * @return Reference to this packet
     */
    Packet& operator<<(int32_t value);

    /**
     * @brief Write a 64-bit integer
     * @param value 64-bit integer value
     * @return Reference to this packet
     */
    Packet& operator<<(uint64_t value);

    /**
     * @brief Write a signed 64-bit integer
     * @param value Signed 64-bit integer value
     * @return Reference to this packet
     */
    Packet& operator<<(int64_t value);

    /**
     * @brief Write a boolean
     * @param value Boolean value
     * @return Reference to this packet
     */
    Packet& operator<<(bool value);

    /**
     * @brief Write a float
     * @param value Float value
     * @return Reference to this packet
     */
    Packet& operator<<(float value);

    /**
     * @brief Write a double
     * @param value Double value
     * @return Reference to this packet
     */
    Packet& operator<<(double value);

    /**
     * @brief Write a string
     * @param value String value
     * @return Reference to this packet
     */
    Packet& operator<<(const std::string& value);

    /**
     * @brief Read a byte
     * @param value Output byte value
     * @return Reference to this packet
     */
    Packet& operator>>(uint8_t& value) const;

    /**
     * @brief Read a signed 8-bit integer
     * @param value Output signed 8-bit integer value
     * @return Reference to this packet
     */
    Packet& operator>>(int8_t& value) const;

    /**
     * @brief Read a 16-bit integer
     * @param value Output 16-bit integer value
     * @return Reference to this packet
     */
    Packet& operator>>(uint16_t& value) const;

    /**
     * @brief Read a signed 16-bit integer
     * @param value Output signed 16-bit integer value
     * @return Reference to this packet
     */
    Packet& operator>>(int16_t& value) const;

    /**
     * @brief Read a 32-bit integer
     * @param value Output 32-bit integer value
     * @return Reference to this packet
     */
    Packet& operator>>(uint32_t& value) const;

    /**
     * @brief Read a signed 32-bit integer
     * @param value Output signed 32-bit integer value
     * @return Reference to this packet
     */
    Packet& operator>>(int32_t& value) const;

    /**
     * @brief Read a 64-bit integer
     * @param value Output 64-bit integer value
     * @return Reference to this packet
     */
    Packet& operator>>(uint64_t& value) const;

    /**
     * @brief Read a signed 64-bit integer
     * @param value Output signed 64-bit integer value
     * @return Reference to this packet
     */
    Packet& operator>>(int64_t& value) const;

    /**
     * @brief Read a boolean
     * @param value Output boolean value
     * @return Reference to this packet
     */
    Packet& operator>>(bool& value) const;

    /**
     * @brief Read a float
     * @param value Output float value
     * @return Reference to this packet
     */
    Packet& operator>>(float& value) const;

    /**
     * @brief Read a double
     * @param value Output double value
     * @return Reference to this packet
     */
    Packet& operator>>(double& value) const;

    /**
     * @brief Read a string
     * @param value Output string value
     * @return Reference to this packet
     */
    Packet& operator>>(std::string& value) const;
};
