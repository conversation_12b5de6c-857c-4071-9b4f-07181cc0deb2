#include "Skill.h"
#include <algorithm>
#include <cmath>

Skill::Skill(int id, const std::string& name, EffectType effectType, int effectValue,
             int manaCost, int power, int maxPower, JobClass jobClass, int cooldown)
    : m_id(id)
    , m_name(name)
    , m_effectType(effectType)
    , m_effectValue(effectValue)
    , m_manaCost(manaCost)
    , m_power(power)
    , m_maxPower(maxPower)
    , m_defaultManaCost(manaCost)
    , m_defaultPower(power)
    , m_defaultMaxPower(maxPower)
    , m_jobClass(jobClass)
    , m_level1Requirement(0)
    , m_level1TrainingPoints(0)
    , m_level2Requirement(0)
    , m_level2TrainingPoints(0)
    , m_level3Requirement(0)
    , m_level3TrainingPoints(0)
    , m_cooldown(cooldown)
    , m_description("")
    , m_attackRange(AttackRange::SINGLE)
    , m_castCondition(CastCondition::MANA)
    , m_level(0)
    , m_experience(0)
    , m_trainingPoints(0)
    , m_lastCastTime(0)
    , m_key(0)
{
}

bool Skill::AddTrainingPoints(int points)
{
    m_trainingPoints += points;

    // 检查是否可以升级
    if (m_level == 0 && m_trainingPoints >= m_level1TrainingPoints && m_level1TrainingPoints > 0) {
        m_level = 1;
        // 升级后增加技能威力
        m_power = static_cast<int>(m_defaultPower * 1.2f);
        m_maxPower = static_cast<int>(m_defaultMaxPower * 1.2f);
        return true;
    }
    else if (m_level == 1 && m_trainingPoints >= m_level2TrainingPoints && m_level2TrainingPoints > 0) {
        m_level = 2;
        // 升级后增加技能威力
        m_power = static_cast<int>(m_defaultPower * 1.5f);
        m_maxPower = static_cast<int>(m_defaultMaxPower * 1.5f);
        return true;
    }
    else if (m_level == 2 && m_trainingPoints >= m_level3TrainingPoints && m_level3TrainingPoints > 0) {
        m_level = 3;
        // 升级后增加技能威力
        m_power = static_cast<int>(m_defaultPower * 2.0f);
        m_maxPower = static_cast<int>(m_defaultMaxPower * 2.0f);
        return true;
    }

    return false;
}

bool Skill::CanCast(int currentMana, int currentTime) const
{
    // 检查魔法值是否足够
    if (currentMana < m_manaCost) {
        return false;
    }

    // 检查冷却时间
    if (currentTime - m_lastCastTime < m_cooldown) {
        return false;
    }

    return true;
}

void Skill::Cast(int currentTime)
{
    m_lastCastTime = currentTime;
}

bool Skill::AddExperience(int exp)
{
    m_experience += exp;

    // 检查是否可以升级
    return CheckLevelUp();
}

bool Skill::CheckLevelUp()
{
    // 根据经验值计算应该的等级
    int newLevel = m_level;

    // 1级需要100点经验
    if (m_experience >= 100 && m_level < 1 && m_level1Requirement > 0) {
        newLevel = 1;
    }
    // 2级需要300点经验
    else if (m_experience >= 300 && m_level < 2 && m_level2Requirement > 0) {
        newLevel = 2;
    }
    // 3级需要600点经验
    else if (m_experience >= 600 && m_level < 3 && m_level3Requirement > 0) {
        newLevel = 3;
    }

    // 如果等级提升了
    if (newLevel > m_level) {
        m_level = newLevel;

        // 根据等级调整技能威力
        if (m_level == 1) {
            m_power = static_cast<int>(m_defaultPower * 1.2f);
            m_maxPower = static_cast<int>(m_defaultMaxPower * 1.2f);
        }
        else if (m_level == 2) {
            m_power = static_cast<int>(m_defaultPower * 1.5f);
            m_maxPower = static_cast<int>(m_defaultMaxPower * 1.5f);
        }
        else if (m_level == 3) {
            m_power = static_cast<int>(m_defaultPower * 2.0f);
            m_maxPower = static_cast<int>(m_defaultMaxPower * 2.0f);
        }

        return true;
    }

    return false;
}

int Skill::CalculateDamage(int casterLevel, int casterMagicPower) const
{
    // 基础伤害
    float baseDamage = static_cast<float>(m_power);

    // 根据技能等级增加伤害
    float levelMultiplier = 1.0f;
    if (m_level == 1) {
        levelMultiplier = 1.2f;
    }
    else if (m_level == 2) {
        levelMultiplier = 1.5f;
    }
    else if (m_level == 3) {
        levelMultiplier = 2.0f;
    }

    // 根据施放者等级和魔法攻击力增加伤害
    float casterMultiplier = 1.0f + (casterLevel * 0.05f) + (casterMagicPower * 0.02f);

    // 计算最终伤害
    int finalDamage = static_cast<int>(baseDamage * levelMultiplier * casterMultiplier);

    // 确保伤害不超过最大威力
    finalDamage = std::min(finalDamage, m_maxPower);

    return finalDamage;
}

int Skill::CalculateEffect(int userLevel, int userMagicLevel) const
{
    // 基础效果值
    float baseEffect = static_cast<float>(m_power);

    // 根据技能等级增加效果
    float levelMultiplier = 1.0f;
    if (m_level == 1) {
        levelMultiplier = 1.2f;
    }
    else if (m_level == 2) {
        levelMultiplier = 1.5f;
    }
    else if (m_level == 3) {
        levelMultiplier = 2.0f;
    }

    // 根据使用者等级和魔法等级增加效果
    float userMultiplier = 1.0f + (userLevel * 0.03f) + (userMagicLevel * 0.02f);

    // 计算最终效果值
    int finalEffect = static_cast<int>(baseEffect * levelMultiplier * userMultiplier);

    // 根据效果类型调整
    switch (m_effectType) {
        case EffectType::HEAL:
            // 治疗效果增强
            finalEffect = static_cast<int>(finalEffect * 1.2f);
            break;
        case EffectType::BUFF:
            // Buff效果持续时间增强
            finalEffect = static_cast<int>(finalEffect * 1.5f);
            break;
        case EffectType::DEBUFF:
            // Debuff效果持续时间减弱
            finalEffect = static_cast<int>(finalEffect * 0.8f);
            break;
        default:
            break;
    }

    // 确保效果值不超过最大威力
    finalEffect = std::min(finalEffect, m_maxPower);

    return finalEffect;
}
