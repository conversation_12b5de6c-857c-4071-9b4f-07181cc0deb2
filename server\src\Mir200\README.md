# Mir200 - Legend of Mir Private Server (Phase 1)

## 项目概述

这是传奇私服Mir200的第一阶段重构实现，严格按照原项目（delphi目录下）的逻辑和结构进行C++重构。

## 重构原则

1. **遵循原项目逻辑** - 严格按照delphi/EM2Engine目录下的原始代码结构
2. **保持协议一致性** - 所有网络协议编号与原项目完全一致
3. **数值计算精确** - 所有数值计算和处理逻辑与原项目保持一致
4. **不简化逻辑** - 不对原项目的逻辑或结构进行简化
5. **避免重复创建** - 检查已存在的方法/类/结构，避免重复实现
6. **C++命名规范** - 使用符合C++标准的变量和函数命名
7. **模块化设计** - 按照职责分离原则，避免巨型文件

## 项目结构

```
server/src/Mir200/
├── Common/                 # 公共定义和工具
│   ├── Types.h            # 基础类型定义
│   ├── Protocol.h         # 网络协议定义
│   ├── M2Share.h          # 核心共享定义
│   ├── M2Share.cpp        # 共享功能实现
│   ├── Grobal2.h          # 全局常量定义
│   ├── Grobal2.cpp        # 全局常量实现
│   ├── HUtil32.h          # 工具函数
│   ├── HUtil32.cpp        # 工具函数实现
│   ├── MudUtil.h          # MUD工具
│   └── MudUtil.cpp        # MUD工具实现
├── Objects/               # 游戏对象模块
│   ├── BaseObject.h       # 基础对象类
│   ├── BaseObject.cpp     # 基础对象实现
│   ├── ObjectState.h      # 对象状态管理
│   ├── ObjectMovement.h   # 对象移动管理
│   ├── ObjectCombat.h     # 对象战斗管理
│   ├── ObjectMagic.h      # 对象魔法管理
│   ├── ObjectInventory.h  # 对象物品管理
│   ├── ObjectStatus.h     # 对象状态效果管理
│   ├── ObjectGroup.h      # 对象组队管理
│   ├── ObjectGuild.h      # 对象行会管理
│   ├── PlayObject.h       # 玩家对象
│   ├── PlayObject.cpp     # 玩家对象实现
│   ├── NormNpc.h          # 普通NPC
│   ├── NormNpc.cpp        # 普通NPC实现
│   ├── Merchant.h         # 商人NPC
│   ├── Merchant.cpp       # 商人NPC实现
│   ├── Monster.h          # 怪物对象
│   ├── Monster.cpp        # 怪物对象实现
│   ├── Guard.h            # 守卫对象
│   └── Guard.cpp          # 守卫对象实现
├── Engine/                # 游戏引擎模块
│   ├── UserEngine.h       # 用户引擎
│   ├── UserEngine.cpp     # 用户引擎实现
│   ├── Environment.h      # 环境管理
│   ├── Environment.cpp    # 环境管理实现
│   ├── LocalDatabase.h    # 本地数据库
│   ├── LocalDatabase.cpp  # 本地数据库实现
│   ├── Magic.h            # 魔法系统
│   ├── Magic.cpp          # 魔法系统实现
│   ├── Guild.h            # 行会系统
│   ├── Guild.cpp          # 行会系统实现
│   ├── Castle.h           # 城堡系统
│   └── Castle.cpp         # 城堡系统实现
├── Network/               # 网络通信模块
│   ├── RunSocket.h        # 运行时Socket
│   ├── RunSocket.cpp      # 运行时Socket实现
│   ├── GateSocket.h       # 网关Socket
│   └── GateSocket.cpp     # 网关Socket实现
├── Database/              # 数据库模块
│   ├── RunDB.h            # 数据库运行时
│   └── RunDB.cpp          # 数据库运行时实现
├── Script/                # 脚本系统模块
│   ├── Event.h            # 事件脚本
│   ├── Event.cpp          # 事件脚本实现
│   ├── Mission.h          # 任务脚本
│   └── Mission.cpp        # 任务脚本实现
├── config/                # 配置文件
│   └── M2Server.ini       # 服务器配置
├── main.cpp               # 主程序入口
├── M2Server.h             # 服务器主类
├── M2Server.cpp           # 服务器主类实现
├── CMakeLists.txt         # CMake构建文件
└── README.md              # 项目说明
```

## 核心特性

### 第一阶段实现内容

1. **基础架构**
   - 完整的类型系统定义（Types.h）
   - 网络协议定义（Protocol.h）- 严格按照原项目协议编号
   - 核心共享系统（M2Share）
   - 模块化对象设计

2. **对象系统**
   - BaseObject基类 - 按职责分离为多个管理器
   - ObjectState - 状态管理（隐身、麻痹、中毒等）
   - ObjectMovement - 移动管理（行走、跑步、传送等）
   - ObjectCombat - 战斗管理（攻击、技能、PK等）
   - 其他专门管理器（魔法、物品、组队、行会等）

3. **网络系统**
   - 协议编号与原项目完全一致
   - 支持原项目的所有消息类型
   - 客户端兼容性保证

4. **配置系统**
   - 完整的服务器配置支持
   - 与原项目配置格式兼容

## 编译说明

### 环境要求

- Windows 10/11 或 Windows Server 2016+
- Visual Studio 2019+ 或 MinGW-w64
- CMake 3.16+
- C++17 支持

### 编译步骤

1. 克隆项目到本地
2. 进入构建目录：
   ```bash
   cd server/src/Mir200
   mkdir build
   cd build
   ```

3. 生成构建文件：
   ```bash
   cmake ..
   ```

4. 编译项目：
   ```bash
   cmake --build . --config Release
   ```

5. 运行程序：
   ```bash
   cd bin
   ./Mir200.exe
   ```

## 配置说明

服务器配置文件位于 `config/M2Server.ini`，主要配置项包括：

- **[Server]** - 服务器基本信息
- **[Network]** - 网络配置
- **[Database]** - 数据库配置
- **[Game]** - 游戏机制配置
- **[Security]** - 安全设置
- **[Performance]** - 性能优化
- **[Debug]** - 调试设置

## 原项目对应关系

| C++模块 | 原Delphi文件 | 说明 |
|---------|-------------|------|
| BaseObject | ObjBase.pas | 基础对象类 |
| M2Share | M2Share.pas | 核心共享定义 |
| Protocol | Grobal2.pas | 网络协议定义 |
| UserEngine | UsrEngn.pas | 用户引擎 |
| LocalDatabase | LocalDB.pas | 本地数据库 |
| Magic | Magic.pas | 魔法系统 |
| Guild | Guild.pas | 行会系统 |

## 开发规范

1. **严格遵循原项目逻辑** - 所有实现必须与原Delphi代码逻辑一致
2. **保持协议兼容性** - 网络协议编号不得修改
3. **数值精确性** - 所有数值计算必须与原项目完全一致
4. **模块化设计** - 按职责分离，避免单个文件过大
5. **C++最佳实践** - 使用现代C++特性，但保持兼容性
6. **错误处理** - 完善的异常处理和错误恢复机制
7. **性能优化** - 在保持逻辑一致的前提下进行性能优化

## 测试说明

### 单元测试

每个模块都应该有对应的单元测试，确保：
- 功能正确性
- 与原项目行为一致性
- 边界条件处理
- 错误情况处理

### 集成测试

- 客户端连接测试
- 协议兼容性测试
- 数据一致性测试
- 性能基准测试

## 后续阶段规划

- **第二阶段** - 完善所有对象管理器的实现
- **第三阶段** - 实现完整的游戏引擎功能
- **第四阶段** - 网络和数据库系统完善
- **第五阶段** - 脚本系统和任务系统
- **第六阶段** - 性能优化和稳定性提升

## 注意事项

1. **不要修改协议编号** - 所有SM_*和CM_*常量必须与原项目一致
2. **保持数值精确** - 经验值、伤害计算等必须与原项目完全一致
3. **遵循原项目结构** - 不要随意改变原项目的设计模式
4. **模块化实现** - 避免创建巨型文件，按职责分离
5. **兼容性优先** - 在性能和兼容性之间，优先保证兼容性

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 开发文档
- 技术讨论群
