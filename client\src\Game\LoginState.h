#pragma once

#include "../GameState.h"
#include "../Graphics/Texture.h"
#include "../Graphics/WILLoader.h"
#include "../Graphics/ResourceManager.h"
#include "../UI/Button.h"
#include "../UI/Label.h"
#include "../UI/TextInput.h"
#include "../UI/UIManager.h"
#include "../UI/NewAccountDialog.h"
#include "../UI/ChangePasswordDialog.h"
#include "../UI/DialogManager.h"
#include "../UI/MessageDialog.h"
#include "../Network/NetworkManager.h"
#include "../Network/Packet.h"
#include <memory>
#include <vector>
#include <string>

/**
 * @class LoginState
 * @brief Game state for the login screen
 *
 * This class represents the login screen of the game, where the player can
 * enter their username and password to log in.
 */
class LoginState : public GameState {
private:
    std::shared_ptr<Texture> m_backgroundTexture;  ///< Background texture
    std::shared_ptr<Texture> m_logoTexture;        ///< Logo texture

    // Resource management
    std::shared_ptr<WILManager> m_wilManager;      ///< WIL manager
    ResourceManager* m_resourceManager;            ///< Resource manager
    std::shared_ptr<UIManager> m_uiManager;        ///< UI manager
    std::shared_ptr<DialogManager> m_dialogManager; ///< Dialog manager

    // UI controls
    // std::shared_ptr<Label> m_titleLabel;           ///< Title label
    std::shared_ptr<Label> m_usernameLabel;        ///< Username label
    std::shared_ptr<Label> m_passwordLabel;        ///< Password label
    std::shared_ptr<TextInput> m_usernameInput;    ///< Username input
    std::shared_ptr<TextInput> m_passwordInput;    ///< Password input
    std::shared_ptr<Button> m_loginButton;         ///< Login button
    std::shared_ptr<Button> m_exitButton;          ///< Exit button
    std::shared_ptr<Button> m_newAccountButton;    ///< New account button
    std::shared_ptr<Button> m_changePasswordButton; ///< Change password button
    std::shared_ptr<Label> m_statusLabel;          ///< Status label

    // Dialogs
    std::shared_ptr<NewAccountDialog> m_newAccountDialog;        ///< New account dialog
    std::shared_ptr<ChangePasswordDialog> m_changePasswordDialog; ///< Change password dialog

    // Network manager
    std::shared_ptr<NetworkManager> m_networkManager;  ///< Network manager

    // Login state
    bool m_loggingIn;                              ///< Whether we're currently logging in
    std::string m_serverAddress;                   ///< Server address
    uint16_t m_serverPort;                         ///< Server port

    // Resource indices
    int m_backgroundIndex;                         ///< Background image index
    int m_logoIndex;                               ///< Logo image index

    // Font
    TTF_Font* m_font;                              ///< Font for text rendering

    /**
     * @brief Create UI controls
     */
    void CreateControls();

    /**
     * @brief Handle login button click
     */
    void OnLoginButtonClick();

    /**
     * @brief Handle exit button click
     */
    void OnExitButtonClick();

    /**
     * @brief Handle new account button click
     */
    void OnNewAccountButtonClick();

    /**
     * @brief Handle change password button click
     */
    void OnChangePasswordButtonClick();

    /**
     * @brief Handle login response
     * @param packet Login response packet
     */
    void OnLoginResponse(const Packet& packet);

    /**
     * @brief Handle new account response
     * @param packet New account response packet
     */
    void OnNewAccountResponse(const Packet& packet);

    /**
     * @brief Handle change password response
     * @param packet Change password response packet
     */
    void OnChangePasswordResponse(const Packet& packet);

public:
    /**
     * @brief Constructor
     * @param app Pointer to the application
     */
    LoginState(Application* app);

    /**
     * @brief Destructor
     */
    virtual ~LoginState();

    /**
     * @brief Called when entering the state
     */
    virtual void Enter() override;

    /**
     * @brief Called when exiting the state
     */
    virtual void Exit() override;

    /**
     * @brief Update the state
     * @param deltaTime Time elapsed since last frame in seconds
     */
    virtual void Update(float deltaTime) override;

    /**
     * @brief Render the state
     */
    virtual void Render() override;

    /**
     * @brief Handle SDL events
     * @param event SDL event to handle
     */
    virtual void HandleEvents(SDL_Event& event) override;
};
