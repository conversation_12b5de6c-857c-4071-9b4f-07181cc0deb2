#include "MessageConverter.h"
#include <cstring>
#include <vector>
#include <unordered_map>

// Constants for 6-bit encoding
static const int BUFFERSIZE = 10000;
static const uint8_t Masks[8] = { 0xFC, 0xF8, 0xF0, 0xE0, 0xC0, 0x80, 0x00, 0x00 };
static const uint8_t Masks2[8] = { 0x03, 0x07, 0x0F, 0x1F, 0x3F, 0x7F, 0xFF, 0xFF };
static const char Base64Chars[64] = {
    'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
    'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
    'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
    '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '+', '/'
};

// Mapping between original message identifiers and packet types
static const std::unordered_map<uint16_t, PacketType> IdentToTypeMap = {
    // Client to server messages (CM_*)
    { 2001, PacketType::LOGIN_REQUEST },
    { 2002, PacketType::CHARACTER_CREATE_REQUEST },
    { 2003, PacketType::CHANGE_PASSWORD },
    { 3011, PacketType::PLAYER_MOVE },
    { 3013, PacketType::PLAYER_MOVE },
    { 3014, PacketType::PLAYER_ATTACK },
    { 3017, PacketType::PLAYER_CAST },
    { 3030, PacketType::CHAT_MESSAGE },
    { 1000, PacketType::PLAYER_DROP },
    { 1001, PacketType::PLAYER_PICKUP },
    { 1003, PacketType::PLAYER_EQUIP },
    { 1004, PacketType::PLAYER_UNEQUIP },
    { 1006, PacketType::PLAYER_USE_ITEM },
    { 3010, PacketType::PLAYER_TURN },
    { 3012, PacketType::PLAYER_SIT },
    { 3015, PacketType::PLAYER_HEAVY_HIT },
    { 3016, PacketType::PLAYER_BIG_HIT },
    { 3018, PacketType::PLAYER_POWER_HIT },
    { 3019, PacketType::PLAYER_LONG_HIT },
    { 3024, PacketType::PLAYER_WIDE_HIT },
    { 3025, PacketType::PLAYER_FIRE_HIT },
    { 3005, PacketType::PLAYER_THROW },
    { 1007, PacketType::PLAYER_BUTCH },
    { 81, PacketType::QUERY_BAG_ITEMS },
    { 82, PacketType::QUERY_USER_STATE },
    { 1002, PacketType::OPEN_DOOR },
    { 100, PacketType::CHARACTER_LIST_REQUEST },
    { 101, PacketType::CHARACTER_CREATE_REQUEST },
    { 102, PacketType::CHARACTER_DELETE_REQUEST },
    { 103, PacketType::CHARACTER_SELECT_REQUEST },
    { 104, PacketType::SERVER_LIST_REQUEST },
    { 1009, PacketType::LEAVE_GAME },
    { 1019, PacketType::GROUP_MODE },
    { 1020, PacketType::CREATE_GROUP },
    { 1021, PacketType::ADD_GROUP_MEMBER },
    { 1022, PacketType::DELETE_GROUP_MEMBER },
    { 1035, PacketType::OPEN_GUILD_DIALOG },
    { 1036, PacketType::GUILD_HOME },
    { 1037, PacketType::GUILD_MEMBER_LIST },
    { 1038, PacketType::ADD_GUILD_MEMBER },
    { 1039, PacketType::DELETE_GUILD_MEMBER },
    { 1040, PacketType::GUILD_UPDATE_NOTICE },
    { 1041, PacketType::GUILD_UPDATE_RANK_INFO },
    { 1044, PacketType::GUILD_ALLY },
    { 1045, PacketType::GUILD_BREAK_ALLY },
    { 1010, PacketType::CLICK_NPC },
    { 1011, PacketType::MERCHANT_DIALOG_SELECT },
    { 1012, PacketType::MERCHANT_QUERY_SELL_PRICE },
    { 1013, PacketType::USER_SELL_ITEM },
    { 1014, PacketType::USER_BUY_ITEM },
    { 1015, PacketType::USER_GET_DETAIL_ITEM },

    // Server to client messages (SM_*)
    { 10, PacketType::ENTITY_MOVE },
    { 11, PacketType::ENTITY_MOVE },
    { 13, PacketType::ENTITY_MOVE },
    { 14, PacketType::ENTITY_ATTACK },
    { 15, PacketType::ENTITY_ATTACK },
    { 16, PacketType::ENTITY_ATTACK },
    { 17, PacketType::ENTITY_CAST },
    { 18, PacketType::ENTITY_ATTACK },
    { 19, PacketType::ENTITY_ATTACK },
    { 24, PacketType::ENTITY_ATTACK },
    { 40, PacketType::CHAT_MESSAGE },
    { 103, PacketType::WHISPER_MESSAGE },
    { 104, PacketType::GUILD_MESSAGE },
    { 51, PacketType::MAP_CHANGE },
    { 52, PacketType::ABILITY },
    { 53, PacketType::HEALTH_SPELL_CHANGED },
    { 54, PacketType::MAP_DATA },
    { 50, PacketType::ENTER_GAME },
    { 500, PacketType::LOGIN_RESPONSE },
    { 501, PacketType::LOGIN_RESPONSE },
    { 520, PacketType::CHARACTER_LIST },
    { 521, PacketType::CHARACTER_CREATE_RESPONSE },
    { 522, PacketType::CHARACTER_CREATE_RESPONSE },
    { 523, PacketType::CHARACTER_DELETE_RESPONSE },
    { 524, PacketType::CHARACTER_DELETE_RESPONSE },
    { 525, PacketType::ENTER_GAME },
    { 526, PacketType::ERROR },
    { 600, PacketType::DROP_ITEM_RESULT },
    { 601, PacketType::DROP_ITEM_RESULT },
    { 610, PacketType::SPAWN_ITEM },
    { 611, PacketType::DESPAWN_ENTITY },
    { 612, PacketType::OPEN_DOOR_RESULT },
    { 613, PacketType::OPEN_DOOR_RESULT },
    { 614, PacketType::CLOSE_DOOR },
    { 615, PacketType::PLAYER_EQUIP },
    { 616, PacketType::PLAYER_EQUIP },
    { 619, PacketType::PLAYER_UNEQUIP },
    { 620, PacketType::PLAYER_UNEQUIP },
    { 621, PacketType::SEND_MY_MAGIC },
    { 622, PacketType::WEIGHT_CHANGED },
    { 633, PacketType::CLEAR_OBJECTS },
    { 634, PacketType::CHANGE_MAP },
    { 635, PacketType::PLAYER_USE_ITEM },
    { 636, PacketType::PLAYER_USE_ITEM },
    { 637, PacketType::PLAYER_BUTCH },
    { 638, PacketType::MAGIC_FIRE },
    { 639, PacketType::MAGIC_FIRE_FAIL },
    { 640, PacketType::MAGIC_LEVEL_EXP },
    { 642, PacketType::DURA_CHANGED },
    { 643, PacketType::MERCHANT_SAY },
    { 644, PacketType::MERCHANT_DIALOG_CLOSE },
    { 645, PacketType::SEND_GOODS_LIST },
    { 646, PacketType::SEND_USER_SELL },
    { 647, PacketType::SEND_BUY_PRICE },
    { 648, PacketType::USER_SELL_ITEM_RESULT },
    { 649, PacketType::USER_SELL_ITEM_RESULT },
    { 650, PacketType::USER_BUY_ITEM_RESULT },
    { 651, PacketType::USER_BUY_ITEM_RESULT },
    { 652, PacketType::SEND_DETAIL_GOODS_LIST },
    { 653, PacketType::GOLD_CHANGED },
    { 654, PacketType::ENTITY_CHANGE_LIGHT },
    { 656, PacketType::ENTITY_CHANGE_NAME_COLOR },
    { 657, PacketType::ENTITY_STATE },
    { 659, PacketType::GROUP_MODE_CHANGED },
    { 660, PacketType::CREATE_GROUP_RESULT },
    { 661, PacketType::CREATE_GROUP_RESULT },
    { 662, PacketType::ADD_GROUP_MEMBER_RESULT },
    { 663, PacketType::DELETE_GROUP_MEMBER_RESULT },
    { 664, PacketType::ADD_GROUP_MEMBER_RESULT },
    { 665, PacketType::DELETE_GROUP_MEMBER_RESULT },
    { 666, PacketType::GROUP_CANCEL },
    { 667, PacketType::GROUP_MEMBERS },
    { 750, PacketType::CHANGE_GUILD_NAME },
    { 751, PacketType::USER_STATE },
    { 752, PacketType::SUBABILITY },
    { 753, PacketType::OPEN_GUILD_DIALOG_RESULT },
    { 754, PacketType::OPEN_GUILD_DIALOG_RESULT },
    { 756, PacketType::SEND_GUILD_MEMBER_LIST },
    { 757, PacketType::ADD_GUILD_MEMBER_RESULT },
    { 758, PacketType::ADD_GUILD_MEMBER_RESULT },
    { 759, PacketType::DELETE_GUILD_MEMBER_RESULT },
    { 760, PacketType::DELETE_GUILD_MEMBER_RESULT },
    { 761, PacketType::GUILD_RANK_UPDATE_FAIL },
    { 762, PacketType::BUILD_GUILD_OK },
    { 763, PacketType::BUILD_GUILD_FAIL },
    { 768, PacketType::GUILD_MAKE_ALLY_OK },
    { 769, PacketType::GUILD_MAKE_ALLY_FAIL },
    { 770, PacketType::GUILD_BREAK_ALLY_OK },
    { 771, PacketType::GUILD_BREAK_ALLY_FAIL },
    { 31, PacketType::ENTITY_STRUCK },
    { 32, PacketType::ENTITY_DEATH },
    { 33, PacketType::ENTITY_SKELETON },
    { 27, PacketType::ENTITY_ALIVE },
    { 45, PacketType::ENTITY_LEVEL_UP },
    { 44, PacketType::ENTITY_WIN_EXP },
    { 46, PacketType::DAY_CHANGING },
    { 100, PacketType::SYSTEM_MESSAGE },
    { 101, PacketType::GROUP_MESSAGE },
    { 102, PacketType::CRY_MESSAGE },
    { 200, PacketType::ADD_ITEM },
    { 201, PacketType::BAG_ITEMS },
    { 202, PacketType::DELETE_ITEM },
    { 203, PacketType::UPDATE_ITEM },
    { 210, PacketType::ADD_MAGIC },
    { 211, PacketType::SEND_MY_MAGIC },
    { 212, PacketType::DELETE_MAGIC },
    { 1100, PacketType::OPEN_HEALTH },
    { 1101, PacketType::CLOSE_HEALTH },
    { 1102, PacketType::BREAK_WEAPON },
    { 1104, PacketType::CHANGE_FACE },
    { 1106, PacketType::VERSION_FAIL },
    { 708, PacketType::AREA_STATE },
    { 766, PacketType::USER_STATE },
    { 530, PacketType::START_PLAY },
    { 529, PacketType::RECONNECT }
};

// Reverse mapping from packet types to message identifiers
static const std::unordered_map<PacketType, uint16_t> TypeToIdentMap = {
    { PacketType::LOGIN_REQUEST, 2001 },
    { PacketType::CHARACTER_CREATE_REQUEST, 2002 },
    { PacketType::CHANGE_PASSWORD, 2003 },
    { PacketType::PLAYER_MOVE, 3011 },
    { PacketType::PLAYER_ATTACK, 3014 },
    { PacketType::PLAYER_CAST, 3017 },
    { PacketType::CHAT_MESSAGE, 3030 },
    { PacketType::PLAYER_DROP, 1000 },
    { PacketType::PLAYER_PICKUP, 1001 },
    { PacketType::PLAYER_EQUIP, 1003 },
    { PacketType::PLAYER_UNEQUIP, 1004 },
    { PacketType::PLAYER_USE_ITEM, 1006 },
    { PacketType::PLAYER_TURN, 3010 },
    { PacketType::PLAYER_SIT, 3012 },
    { PacketType::PLAYER_HEAVY_HIT, 3015 },
    { PacketType::PLAYER_BIG_HIT, 3016 },
    { PacketType::PLAYER_POWER_HIT, 3018 },
    { PacketType::PLAYER_LONG_HIT, 3019 },
    { PacketType::PLAYER_WIDE_HIT, 3024 },
    { PacketType::PLAYER_FIRE_HIT, 3025 },
    { PacketType::PLAYER_THROW, 3005 },
    { PacketType::PLAYER_BUTCH, 1007 },
    { PacketType::QUERY_BAG_ITEMS, 81 },
    { PacketType::QUERY_USER_STATE, 82 },
    { PacketType::OPEN_DOOR, 1002 },
    { PacketType::CHARACTER_LIST_REQUEST, 100 },
    { PacketType::CHARACTER_DELETE_REQUEST, 102 },
    { PacketType::CHARACTER_SELECT_REQUEST, 103 },
    { PacketType::SERVER_LIST_REQUEST, 104 },
    { PacketType::LEAVE_GAME, 1009 },
    { PacketType::LOGIN_RESPONSE, 500 },
    { PacketType::CHARACTER_LIST, 520 },
    { PacketType::CHARACTER_CREATE_RESPONSE, 521 },
    { PacketType::CHARACTER_DELETE_RESPONSE, 523 },
    { PacketType::ENTER_GAME, 50 },
    { PacketType::ENTITY_MOVE, 11 },
    { PacketType::ENTITY_ATTACK, 14 },
    { PacketType::ENTITY_CAST, 17 },
    { PacketType::WHISPER_MESSAGE, 103 },
    { PacketType::GUILD_MESSAGE, 104 },
    { PacketType::MAP_CHANGE, 51 },
    { PacketType::ABILITY, 52 },
    { PacketType::HEALTH_SPELL_CHANGED, 53 },
    { PacketType::MAP_DATA, 54 },
    { PacketType::ERROR, 526 },
    { PacketType::DROP_ITEM_RESULT, 600 },
    { PacketType::SPAWN_ITEM, 610 },
    { PacketType::DESPAWN_ENTITY, 611 },
    { PacketType::OPEN_DOOR_RESULT, 612 },
    { PacketType::CLOSE_DOOR, 614 },
    { PacketType::CLEAR_OBJECTS, 633 },
    { PacketType::CHANGE_MAP, 634 },
    { PacketType::MAGIC_FIRE, 638 },
    { PacketType::MAGIC_FIRE_FAIL, 639 },
    { PacketType::MAGIC_LEVEL_EXP, 640 },
    { PacketType::DURA_CHANGED, 642 },
    { PacketType::MERCHANT_SAY, 643 },
    { PacketType::MERCHANT_DIALOG_CLOSE, 644 },
    { PacketType::SEND_GOODS_LIST, 645 },
    { PacketType::SEND_USER_SELL, 646 },
    { PacketType::SEND_BUY_PRICE, 647 },
    { PacketType::USER_SELL_ITEM_RESULT, 648 },
    { PacketType::USER_BUY_ITEM_RESULT, 650 },
    { PacketType::SEND_DETAIL_GOODS_LIST, 652 },
    { PacketType::GOLD_CHANGED, 653 },
    { PacketType::ENTITY_CHANGE_LIGHT, 654 },
    { PacketType::ENTITY_CHANGE_NAME_COLOR, 656 },
    { PacketType::ENTITY_STATE, 657 },
    { PacketType::GROUP_MODE_CHANGED, 659 },
    { PacketType::CREATE_GROUP_RESULT, 660 },
    { PacketType::ADD_GROUP_MEMBER_RESULT, 662 },
    { PacketType::DELETE_GROUP_MEMBER_RESULT, 663 },
    { PacketType::GROUP_CANCEL, 666 },
    { PacketType::GROUP_MEMBERS, 667 },
    { PacketType::CHANGE_GUILD_NAME, 750 },
    { PacketType::USER_STATE, 751 },
    { PacketType::SUBABILITY, 752 },
    { PacketType::OPEN_GUILD_DIALOG_RESULT, 753 },
    { PacketType::SEND_GUILD_MEMBER_LIST, 756 },
    { PacketType::ADD_GUILD_MEMBER_RESULT, 757 },
    { PacketType::DELETE_GUILD_MEMBER_RESULT, 759 },
    { PacketType::GUILD_RANK_UPDATE_FAIL, 761 },
    { PacketType::BUILD_GUILD_OK, 762 },
    { PacketType::BUILD_GUILD_FAIL, 763 },
    { PacketType::GUILD_MAKE_ALLY_OK, 768 },
    { PacketType::GUILD_MAKE_ALLY_FAIL, 769 },
    { PacketType::GUILD_BREAK_ALLY_OK, 770 },
    { PacketType::GUILD_BREAK_ALLY_FAIL, 771 },
    { PacketType::ENTITY_STRUCK, 31 },
    { PacketType::ENTITY_DEATH, 32 },
    { PacketType::ENTITY_SKELETON, 33 },
    { PacketType::ENTITY_ALIVE, 27 },
    { PacketType::ENTITY_LEVEL_UP, 45 },
    { PacketType::ENTITY_WIN_EXP, 44 },
    { PacketType::DAY_CHANGING, 46 },
    { PacketType::SYSTEM_MESSAGE, 100 },
    { PacketType::GROUP_MESSAGE, 101 },
    { PacketType::CRY_MESSAGE, 102 },
    { PacketType::ADD_ITEM, 200 },
    { PacketType::BAG_ITEMS, 201 },
    { PacketType::DELETE_ITEM, 202 },
    { PacketType::UPDATE_ITEM, 203 },
    { PacketType::ADD_MAGIC, 210 },
    { PacketType::SEND_MY_MAGIC, 211 },
    { PacketType::DELETE_MAGIC, 212 },
    { PacketType::OPEN_HEALTH, 1100 },
    { PacketType::CLOSE_HEALTH, 1101 },
    { PacketType::BREAK_WEAPON, 1102 },
    { PacketType::CHANGE_FACE, 1104 },
    { PacketType::VERSION_FAIL, 1106 },
    { PacketType::AREA_STATE, 708 },
    { PacketType::START_PLAY, 530 },
    { PacketType::RECONNECT, 529 }
};

// Implementation of MessageConverter methods
Packet MessageConverter::ToPacket(const TDefaultMessage& msg) {
    // Convert message identifier to packet type
    PacketType type = IdentToPacketType(msg.Ident);

    // Create packet with the converted type
    Packet packet(type);

    // Add message fields to packet
    packet << msg.Recog << msg.Param << msg.Tag << msg.Series;

    return packet;
}

TDefaultMessage MessageConverter::ToDefaultMessage(const Packet& packet) {
    // Convert packet type to message identifier
    uint16_t ident = PacketTypeToIdent(packet.GetType());

    // Create default message
    TDefaultMessage msg;
    msg.Ident = ident;

    // Read message fields from packet
    Packet tempPacket = packet;
    tempPacket.SetReadPos(0);

    // Try to read fields if packet has data
    if (tempPacket.GetSize() >= 10) { // At least Recog(4) + Param(2) + Tag(2) + Series(2)
        tempPacket >> msg.Recog >> msg.Param >> msg.Tag >> msg.Series;
    } else {
        // Default values if packet doesn't have enough data
        msg.Recog = 0;
        msg.Param = 0;
        msg.Tag = 0;
        msg.Series = 0;
    }

    return msg;
}

TDefaultMessage MessageConverter::MakeDefaultMessage(uint16_t ident, int32_t recog, uint16_t param, uint16_t tag, uint16_t series) {
    TDefaultMessage msg;
    msg.Ident = ident;
    msg.Recog = recog;
    msg.Param = param;
    msg.Tag = tag;
    msg.Series = series;
    return msg;
}

PacketType MessageConverter::IdentToPacketType(uint16_t ident) {
    auto it = IdentToTypeMap.find(ident);
    if (it != IdentToTypeMap.end()) {
        return it->second;
    }
    return PacketType::NONE;
}

uint16_t MessageConverter::PacketTypeToIdent(PacketType type) {
    auto it = TypeToIdentMap.find(type);
    if (it != TypeToIdentMap.end()) {
        return it->second;
    }
    return 0;
}

void MessageConverter::Encode6BitBuf(const char* src, char* dest, int srcLen, int destLen) {
    int restCount = 0;
    uint8_t rest = 0;
    int destPos = 0;

    for (int i = 0; i < srcLen; i++) {
        if (destPos >= destLen) break;

        uint8_t ch = static_cast<uint8_t>(src[i]);
        uint8_t made = (rest | ((ch >> (2 + restCount)) & 0x3F)) & 0x3F;
        rest = ((ch << (8 - (2 + restCount))) >> 2) & 0x3F;

        restCount += 2;

        if (restCount < 6) {
            dest[destPos++] = Base64Chars[made];
        } else {
            destPos--;
            dest[destPos++] = Base64Chars[made];
            dest[destPos++] = Base64Chars[rest];
            rest = 0;
            restCount = 0;
        }
    }

    if (restCount > 0) {
        dest[destPos++] = Base64Chars[rest];
    }

    dest[destPos] = '\0';
}

void MessageConverter::Decode6BitBuf(const char* src, char* dest, int srcLen, int destLen) {
    int destPos = 0;
    int bitPos = 2;
    int madeBit = 0;
    uint8_t ch = 0;

    for (int i = 0; i < srcLen; i++) {
        if (src[i] == '\0') break;

        // Convert Base64 character to 6-bit value
        uint8_t n = 0;
        char c = src[i];

        if (c >= 'A' && c <= 'Z') {
            n = c - 'A';
        } else if (c >= 'a' && c <= 'z') {
            n = c - 'a' + 26;
        } else if (c >= '0' && c <= '9') {
            n = c - '0' + 52;
        } else if (c == '+') {
            n = 62;
        } else if (c == '/') {
            n = 63;
        }

        if (madeBit + 6 >= 8) {
            ch = ch | ((n & Masks2[bitPos]) << (8 - madeBit));
            dest[destPos++] = ch;

            if (destPos >= destLen) break;

            madeBit = madeBit + 6 - 8;
            ch = (n >> (6 - madeBit)) & Masks2[madeBit];
        } else {
            ch = ch | ((n & 0x3F) << (8 - (madeBit + 6)));
            madeBit += 6;
        }
    }

    dest[destPos] = '\0';
}

std::string MessageConverter::EncodeBuffer(const char* buf, int bufSize) {
    if (bufSize >= BUFFERSIZE) {
        return "";
    }

    std::vector<char> encBuf(BUFFERSIZE + 100);
    Encode6BitBuf(buf, encBuf.data(), bufSize, BUFFERSIZE);

    return std::string(encBuf.data());
}

void MessageConverter::DecodeBuffer(const std::string& str, char* buf, int bufSize) {
    std::vector<char> encBuf(BUFFERSIZE + 100);
    Decode6BitBuf(str.c_str(), encBuf.data(), str.length(), BUFFERSIZE);

    memcpy(buf, encBuf.data(), bufSize);
}

std::string MessageConverter::EncodeString(const std::string& str) {
    return EncodeBuffer(str.c_str(), str.length());
}

std::string MessageConverter::DecodeString(const std::string& str) {
    std::vector<char> buf(BUFFERSIZE + 100);
    Decode6BitBuf(str.c_str(), buf.data(), str.length(), BUFFERSIZE);

    return std::string(buf.data());
}

std::string MessageConverter::EncodeMessage(const TDefaultMessage& msg) {
    std::vector<char> tempBuf(sizeof(TDefaultMessage) + 100);
    memcpy(tempBuf.data(), &msg, sizeof(TDefaultMessage));

    return EncodeBuffer(tempBuf.data(), sizeof(TDefaultMessage));
}

TDefaultMessage MessageConverter::DecodeMessage(const std::string& str) {
    TDefaultMessage msg;
    std::vector<char> encBuf(BUFFERSIZE + 100);

    Decode6BitBuf(str.c_str(), encBuf.data(), str.length(), 1024);
    memcpy(&msg, encBuf.data(), sizeof(TDefaultMessage));

    return msg;
}
