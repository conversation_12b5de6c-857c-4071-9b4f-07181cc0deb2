#pragma once

#include "Actor.h"

/**
 * @enum MonsterType
 * @brief Type of monster
 */
enum class MonsterType {
    NORMAL,     ///< Normal monster
    ELITE,      ///< Elite monster
    BOSS        ///< Boss monster
};

/**
 * @class Monster
 * @brief Represents a monster
 * 
 * This class represents a monster, which is an enemy that can be fought by the player.
 * It extends the Actor class with monster-specific functionality.
 */
class Monster : public Actor {
private:
    MonsterType m_type;             ///< Monster type
    
    int m_level;                    ///< Monster level
    int m_health;                   ///< Current health
    int m_maxHealth;                ///< Maximum health
    
    int m_attackPower;              ///< Attack power
    int m_defense;                  ///< Defense
    
    int m_experienceValue;          ///< Experience points awarded when killed
    int m_goldValue;                ///< Gold awarded when killed
    
    std::vector<int> m_dropItems;   ///< Items that can be dropped
    std::vector<float> m_dropRates; ///< Drop rates for each item
    
    int m_aggroRange;               ///< Aggro range in tiles
    int m_attackRange;              ///< Attack range in tiles
    
    bool m_aggressive;              ///< Whether the monster is aggressive
    
    int m_targetId;                 ///< ID of the target actor
    int m_patrolX;                  ///< X coordinate of patrol point
    int m_patrolY;                  ///< Y coordinate of patrol point
    int m_homeX;                    ///< X coordinate of home point
    int m_homeY;                    ///< Y coordinate of home point
    
    int m_aiTimer;                  ///< Timer for AI updates
    int m_aiUpdateInterval;         ///< Interval between AI updates in milliseconds
    
    /**
     * @brief Update AI
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    void UpdateAI(int deltaTime);
    
    /**
     * @brief Check if a target is in range
     * @param targetX Target X coordinate
     * @param targetY Target Y coordinate
     * @param range Range in tiles
     * @return true if in range, false otherwise
     */
    bool IsInRange(int targetX, int targetY, int range) const;
    
    /**
     * @brief Calculate path to target
     * @param targetX Target X coordinate
     * @param targetY Target Y coordinate
     * @return true if path found, false otherwise
     */
    bool PathToTarget(int targetX, int targetY);
    
public:
    /**
     * @brief Constructor
     * @param id Monster ID
     * @param name Monster name
     * @param type Monster type
     * @param level Monster level
     */
    Monster(int id, const std::string& name, MonsterType type, int level);
    
    /**
     * @brief Destructor
     */
    virtual ~Monster();
    
    /**
     * @brief Update the monster
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    virtual void Update(int deltaTime) override;
    
    /**
     * @brief Render the monster
     */
    virtual void Render() override;
    
    /**
     * @brief Set the target
     * @param targetId Target actor ID
     */
    void SetTarget(int targetId);
    
    /**
     * @brief Clear the target
     */
    void ClearTarget();
    
    /**
     * @brief Set the home position
     * @param x X coordinate
     * @param y Y coordinate
     */
    void SetHomePosition(int x, int y);
    
    /**
     * @brief Set the patrol position
     * @param x X coordinate
     * @param y Y coordinate
     */
    void SetPatrolPosition(int x, int y);
    
    /**
     * @brief Set whether the monster is aggressive
     * @param aggressive Aggressive flag
     */
    void SetAggressive(bool aggressive);
    
    /**
     * @brief Take damage
     * @param damage Damage amount
     * @return true if the monster is still alive, false otherwise
     */
    virtual bool TakeDamage(int damage) override;
    
    /**
     * @brief Get the monster type
     * @return Monster type
     */
    MonsterType GetType() const { return m_type; }
    
    /**
     * @brief Get the level
     * @return Monster level
     */
    int GetLevel() const { return m_level; }
    
    /**
     * @brief Get the current health
     * @return Current health
     */
    int GetHealth() const { return m_health; }
    
    /**
     * @brief Get the maximum health
     * @return Maximum health
     */
    int GetMaxHealth() const { return m_maxHealth; }
    
    /**
     * @brief Get the experience value
     * @return Experience points awarded when killed
     */
    int GetExperienceValue() const { return m_experienceValue; }
    
    /**
     * @brief Get the gold value
     * @return Gold awarded when killed
     */
    int GetGoldValue() const { return m_goldValue; }
    
    /**
     * @brief Check if the monster is aggressive
     * @return true if aggressive, false otherwise
     */
    bool IsAggressive() const { return m_aggressive; }
    
    /**
     * @brief Get the target ID
     * @return Target actor ID
     */
    int GetTargetId() const { return m_targetId; }
    
    /**
     * @brief Check if the monster has a target
     * @return true if has target, false otherwise
     */
    bool HasTarget() const { return m_targetId != -1; }
    
    /**
     * @brief Generate drops
     * @return Vector of item IDs that were dropped
     */
    std::vector<int> GenerateDrops();
};
