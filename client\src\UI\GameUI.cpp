#include "GameUI.h"
#include "UIConstants.h"
#include "UILayout.h"
#include "ResourcePaths.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>
#include <sstream>
#include <iomanip>

GameUI::GameUI(int x, int y, int width, int height, std::shared_ptr<Player> player, const std::string& name)
    : UIControl(x, y, width, height, name)
    , m_player(player)
    , m_healthBarX(0)
    , m_healthBarY(0)
    , m_healthBarWidth(0)
    , m_healthBarHeight(0)
    , m_manaBarX(0)
    , m_manaBarY(0)
    , m_manaBarWidth(0)
    , m_manaBarHeight(0)
    , m_expBarX(0)
    , m_expBarY(0)
    , m_expBarWidth(0)
    , m_expBarHeight(0)
    , m_bottomPanelX(0)
    , m_bottomPanelY(0)
    , m_bottomPanelWidth(0)
    , m_bottomPanelHeight(0)
    , m_beltItemWidth(32)
    , m_beltItemHeight(29)
    , m_goldX(0)
    , m_goldY(0)
    , m_goldWidth(0)
    , m_goldHeight(0)
    , m_gold(0)
    , m_miniMapX(0)
    , m_miniMapY(0)
    , m_miniMapWidth(0)
    , m_miniMapHeight(0)
    , m_showMiniMap(true)
    , m_miniMapMode(0)
    , m_miniMapIndex(0)
    , m_mainFrameIndex(0)
    , m_healthBarFrameIndex(0)
    , m_manaBarFrameIndex(0)
    , m_expBarFrameIndex(0)
    , m_bottomPanelIndex(0)
    , m_beltItemFrameIndex(0)
    , m_goldFrameIndex(0)
    , m_stateBtnIndex(0)
    , m_bagBtnIndex(0)
    , m_skillBtnIndex(0)
    , m_optionBtnIndex(0)
    , m_miniMapBtnIndex(0)
    , m_tradeBtnIndex(0)
    , m_guildBtnIndex(0)
    , m_groupBtnIndex(0)
    , m_exitBtnIndex(0)
    , m_logoutBtnIndex(0)
    , m_font(nullptr)
    , m_onStatusButtonClick(nullptr)
    , m_onSkillButtonClick(nullptr)
{
    // Set resource file for UI elements
    SetResourceFile(ResourcePaths::INTERFACE);

    // Set resource indices using constants from UIConstants
    m_mainFrameIndex = UIConstants::MAIN_FRAME_INDEX;
    m_healthBarFrameIndex = UIConstants::HEALTH_BAR_FRAME_INDEX;
    m_manaBarFrameIndex = UIConstants::MANA_BAR_FRAME_INDEX;
    m_expBarFrameIndex = UIConstants::EXP_BAR_FRAME_INDEX;
    m_bottomPanelIndex = UIConstants::BOTTOM_PANEL_INDEX;
    m_beltItemFrameIndex = UIConstants::BELT_ITEM_FRAME_INDEX;
    m_goldFrameIndex = UIConstants::GOLD_FRAME_INDEX;
    m_stateBtnIndex = UIConstants::STATE_BTN_INDEX;
    m_bagBtnIndex = UIConstants::BAG_BTN_INDEX;
    m_skillBtnIndex = UIConstants::SKILL_BTN_INDEX;
    m_optionBtnIndex = UIConstants::OPTION_BTN_INDEX;
    m_miniMapBtnIndex = UIConstants::MINIMAP_BTN_INDEX;
    m_tradeBtnIndex = UIConstants::TRADE_BTN_INDEX;
    m_guildBtnIndex = UIConstants::GUILD_BTN_INDEX;
    m_groupBtnIndex = UIConstants::GROUP_BTN_INDEX;
    m_exitBtnIndex = UIConstants::EXIT_BTN_INDEX;
    m_logoutBtnIndex = UIConstants::LOGOUT_BTN_INDEX;

    // Initialize text colors
    m_textColor = {255, 255, 255, 255};         // White
    m_textShadowColor = {0, 0, 0, 255};         // Black

    // Initialize font
    m_font = TTF_OpenFont("fonts/gulim.ttf", 12);  // Use appropriate font file and size
    if (!m_font) {
        std::cerr << "Failed to load font: " << TTF_GetError() << std::endl;
    }

    // Create UI controls
    CreateControls();
}

GameUI::~GameUI()
{
    if (m_font) {
        TTF_CloseFont(m_font);
        m_font = nullptr;
    }
}

void GameUI::CreateControls()
{
    // 使用UILayout中的常量设置UI元素的位置和大小

    // 设置控件大小以匹配主框架
    m_width = UILayout::MainUI::BOTTOM_PANEL_WIDTH;
    m_height = UILayout::MainUI::BOTTOM_PANEL_HEIGHT;

    // 在屏幕底部定�?    m_x = UILayout::MainUI::BOTTOM_PANEL_X;
    m_y = UILayout::MainUI::BOTTOM_PANEL_Y;

    // 底部面板位置和大�?    m_bottomPanelX = UILayout::MainUI::BOTTOM_PANEL_X;
    m_bottomPanelY = UILayout::MainUI::BOTTOM_PANEL_Y;
    m_bottomPanelWidth = UILayout::MainUI::BOTTOM_PANEL_WIDTH;
    m_bottomPanelHeight = UILayout::MainUI::BOTTOM_PANEL_HEIGHT;

    // 生命条位置和大小
    m_healthBarX = UILayout::MainUI::HEALTH_BAR_X;
    m_healthBarY = UILayout::MainUI::HEALTH_BAR_Y;
    m_healthBarWidth = UILayout::MainUI::HEALTH_BAR_WIDTH;
    m_healthBarHeight = UILayout::MainUI::HEALTH_BAR_HEIGHT;

    // 魔法条位置和大小
    m_manaBarX = UILayout::MainUI::MANA_BAR_X;
    m_manaBarY = UILayout::MainUI::MANA_BAR_Y;
    m_manaBarWidth = UILayout::MainUI::MANA_BAR_WIDTH;
    m_manaBarHeight = UILayout::MainUI::MANA_BAR_HEIGHT;

    // 经验条位置和大小
    m_expBarX = UILayout::MainUI::EXP_BAR_X;
    m_expBarY = UILayout::MainUI::EXP_BAR_Y;
    m_expBarWidth = UILayout::MainUI::EXP_BAR_WIDTH;
    m_expBarHeight = UILayout::MainUI::EXP_BAR_HEIGHT;

    // 金币显示位置和大�?    m_goldX = UILayout::MainUI::GOLD_X;
    m_goldY = UILayout::MainUI::GOLD_Y;
    m_goldWidth = UILayout::MainUI::GOLD_WIDTH;
    m_goldHeight = UILayout::MainUI::GOLD_HEIGHT;

    // 小地图位置和大小
    m_miniMapX = UILayout::MainUI::MINIMAP_X;
    m_miniMapY = UILayout::MainUI::MINIMAP_Y;
    m_miniMapWidth = UILayout::MainUI::MINIMAP_WIDTH;
    m_miniMapHeight = UILayout::MainUI::MINIMAP_HEIGHT;

    // 按钮位置
    m_stateBtnX = UILayout::MainUI::STATE_BTN_X;
    m_stateBtnY = UILayout::MainUI::STATE_BTN_Y;
    m_bagBtnX = UILayout::MainUI::BAG_BTN_X;
    m_bagBtnY = UILayout::MainUI::BAG_BTN_Y;
    m_skillBtnX = UILayout::MainUI::SKILL_BTN_X;
    m_skillBtnY = UILayout::MainUI::SKILL_BTN_Y;
    m_optionBtnX = UILayout::MainUI::OPTION_BTN_X;
    m_optionBtnY = UILayout::MainUI::OPTION_BTN_Y;

    // 底部按钮
    m_miniMapBtnX = UILayout::MainUI::MINIMAP_BTN_X;
    m_miniMapBtnY = UILayout::MainUI::MINIMAP_BTN_Y;
    m_tradeBtnX = UILayout::MainUI::TRADE_BTN_X;
    m_tradeBtnY = UILayout::MainUI::TRADE_BTN_Y;
    m_guildBtnX = UILayout::MainUI::GUILD_BTN_X;
    m_guildBtnY = UILayout::MainUI::GUILD_BTN_Y;
    m_groupBtnX = UILayout::MainUI::GROUP_BTN_X;
    m_groupBtnY = UILayout::MainUI::GROUP_BTN_Y;
    m_exitBtnX = UILayout::MainUI::EXIT_BTN_X;
    m_exitBtnY = UILayout::MainUI::EXIT_BTN_Y;
    m_logoutBtnX = UILayout::MainUI::LOGOUT_BTN_X;
    m_logoutBtnY = UILayout::MainUI::LOGOUT_BTN_Y;

    // 腰带物品位置
    for (int i = 0; i < MAX_BELT_ITEMS; ++i) {
        m_beltItemX[i] = UILayout::MainUI::BELT_ITEM_X[i];
        m_beltItemY[i] = UILayout::MainUI::BELT_ITEM_Y[i];
    }

    m_beltItemWidth = UILayout::MainUI::BELT_ITEM_WIDTH;
    m_beltItemHeight = UILayout::MainUI::BELT_ITEM_HEIGHT;
}

void GameUI::Update(int deltaTime)
{
    // Update UI based on player stats
    // Nothing to update here, as we'll get the latest values when rendering

    // Call base class update
    UIControl::Update(deltaTime);
}

void GameUI::Render(SDL_Renderer* renderer)
{
    // Skip if not visible
    if (!m_visible) {
        return;
    }

    // Render bottom panel
    RenderBottomPanel(renderer);

    // Render health bar
    RenderHealthBar(renderer);

    // Render mana bar
    RenderManaBar(renderer);

    // Render experience bar
    RenderExpBar(renderer);

    // Render player stats
    RenderPlayerStats(renderer);

    // Render belt items
    RenderBeltItems(renderer);

    // Render gold display
    RenderGold(renderer);

    // Render buttons
    RenderButtons(renderer);

    // Render mini map if visible
    if (m_showMiniMap) {
        RenderMiniMap(renderer);
    }

    // Render children
    UIControl::Render(renderer);
}

void GameUI::RenderBottomPanel(SDL_Renderer* renderer)
{
    // Render bottom panel background
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_bottomPanelIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {m_bottomPanelX, m_bottomPanelY, m_bottomPanelWidth, m_bottomPanelHeight};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    } else {
        // Render a placeholder background if no resource
        SDL_Rect rect = {m_bottomPanelX, m_bottomPanelY, m_bottomPanelWidth, m_bottomPanelHeight};
        SDL_SetRenderDrawColor(renderer, 50, 50, 50, 200); // Dark gray with alpha
        SDL_RenderFillRect(renderer, &rect);

        SDL_SetRenderDrawColor(renderer, 100, 100, 100, 255); // Gray
        SDL_RenderDrawRect(renderer, &rect);
    }
}

void GameUI::RenderBeltItems(SDL_Renderer* renderer)
{
    // Render belt item slots
    for (int i = 0; i < MAX_BELT_ITEMS; ++i) {
        if (m_wilManager) {
            SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_beltItemFrameIndex);
            if (surface) {
                // Create a temporary texture from the surface
                SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
                if (texture) {
                    // Render the texture
                    SDL_Rect destRect = {m_beltItemX[i], m_beltItemY[i], m_beltItemWidth, m_beltItemHeight};
                    SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                    // Free the texture
                    SDL_DestroyTexture(texture);
                }
            }
        } else {
            // Render a placeholder if no resource
            SDL_Rect rect = {m_beltItemX[i], m_beltItemY[i], m_beltItemWidth, m_beltItemHeight};
            SDL_SetRenderDrawColor(renderer, 70, 70, 70, 200); // Dark gray with alpha
            SDL_RenderFillRect(renderer, &rect);

            SDL_SetRenderDrawColor(renderer, 120, 120, 120, 255); // Gray
            SDL_RenderDrawRect(renderer, &rect);
        }

        // TODO: Render belt item content if available
    }
}

void GameUI::RenderGold(SDL_Renderer* renderer)
{
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_goldFrameIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {m_goldX, m_goldY, m_goldWidth, m_goldHeight};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    } else {
        // Render a placeholder if no resource
        SDL_Rect rect = {m_goldX, m_goldY, m_goldWidth, m_goldHeight};
        SDL_SetRenderDrawColor(renderer, 70, 70, 0, 200); // Dark yellow with alpha
        SDL_RenderFillRect(renderer, &rect);

        SDL_SetRenderDrawColor(renderer, 120, 120, 0, 255); // Yellow
        SDL_RenderDrawRect(renderer, &rect);
    }

    // Render gold amount
    std::stringstream ss;
    ss << m_gold;
    RenderTextWithShadow(renderer, ss.str(), m_goldX + 20, m_goldY + 5, m_textColor, m_textShadowColor);
}

void GameUI::RenderMiniMap(SDL_Renderer* renderer)
{
    // Render mini map background
    SDL_Rect rect = {m_miniMapX, m_miniMapY, m_miniMapWidth, m_miniMapHeight};

    // Set transparency based on mini map mode
    Uint8 alpha = (m_miniMapMode == 1) ? 150 : 255;

    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface("mmap", m_miniMapIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Set texture alpha
                SDL_SetTextureAlphaMod(texture, alpha);

                // Render the texture
                SDL_RenderCopy(renderer, texture, nullptr, &rect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    } else {
        // Render a placeholder if no resource
        SDL_SetRenderDrawColor(renderer, 0, 0, 0, alpha); // Black with alpha
        SDL_RenderFillRect(renderer, &rect);

        SDL_SetRenderDrawColor(renderer, 100, 100, 100, 255); // Gray
        SDL_RenderDrawRect(renderer, &rect);
    }

    // TODO: Render player position on mini map
}

void GameUI::RenderButtons(SDL_Renderer* renderer)
{
    // Render state button
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_stateBtnIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {m_stateBtnX, m_stateBtnY, UILayout::MainUI::STATE_BTN_WIDTH, UILayout::MainUI::STATE_BTN_HEIGHT};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }

    // Render bag button
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_bagBtnIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {m_bagBtnX, m_bagBtnY, UILayout::MainUI::BAG_BTN_WIDTH, UILayout::MainUI::BAG_BTN_HEIGHT};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }

    // Render skill button
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_skillBtnIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {m_skillBtnX, m_skillBtnY, UILayout::MainUI::SKILL_BTN_WIDTH, UILayout::MainUI::SKILL_BTN_HEIGHT};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }

    // Render option button
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_optionBtnIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {m_optionBtnX, m_optionBtnY, UILayout::MainUI::OPTION_BTN_WIDTH, UILayout::MainUI::OPTION_BTN_HEIGHT};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }

    // Render mini map button
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_miniMapBtnIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {m_miniMapBtnX, m_miniMapBtnY, UILayout::MainUI::MINIMAP_BTN_WIDTH, UILayout::MainUI::MINIMAP_BTN_HEIGHT};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }

    // Render trade button
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_tradeBtnIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {m_tradeBtnX, m_tradeBtnY, UILayout::MainUI::TRADE_BTN_WIDTH, UILayout::MainUI::TRADE_BTN_HEIGHT};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }

    // Render guild button
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_guildBtnIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {m_guildBtnX, m_guildBtnY, UILayout::MainUI::GUILD_BTN_WIDTH, UILayout::MainUI::GUILD_BTN_HEIGHT};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }

    // Render group button
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_groupBtnIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {m_groupBtnX, m_groupBtnY, UILayout::MainUI::GROUP_BTN_WIDTH, UILayout::MainUI::GROUP_BTN_HEIGHT};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }

    // Render exit button
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_exitBtnIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {m_exitBtnX, m_exitBtnY, UILayout::MainUI::EXIT_BTN_WIDTH, UILayout::MainUI::EXIT_BTN_HEIGHT};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }

    // Render logout button
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_logoutBtnIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {m_logoutBtnX, m_logoutBtnY, UILayout::MainUI::LOGOUT_BTN_WIDTH, UILayout::MainUI::LOGOUT_BTN_HEIGHT};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }
}

void GameUI::RenderHealthBar(SDL_Renderer* renderer)
{
    if (!m_player) {
        return;
    }

    // Get player health values
    int health = m_player->GetHealth();
    int maxHealth = m_player->GetMaxHealth();

    // Calculate health bar fill width
    int fillWidth = static_cast<int>((static_cast<float>(health) / maxHealth) * m_healthBarWidth);

    // Render health bar background
    SDL_Rect bgRect = {m_healthBarX, m_healthBarY, m_healthBarWidth, m_healthBarHeight};
    SDL_SetRenderDrawColor(renderer, 50, 0, 0, 255); // Dark red
    SDL_RenderFillRect(renderer, &bgRect);

    // Render health bar fill
    SDL_Rect fillRect = {m_healthBarX, m_healthBarY, fillWidth, m_healthBarHeight};
    SDL_SetRenderDrawColor(renderer, 255, 0, 0, 255); // Bright red
    SDL_RenderFillRect(renderer, &fillRect);

    // Render health bar border
    SDL_SetRenderDrawColor(renderer, 200, 200, 200, 255); // Light gray
    SDL_RenderDrawRect(renderer, &bgRect);

    // Render health text
    std::stringstream ss;
    ss << health << "/" << maxHealth;

    // Render text with shadow
    RenderTextWithShadow(renderer, ss.str(), m_healthBarX + m_healthBarWidth + 5, m_healthBarY, m_textColor, m_textShadowColor);
}

void GameUI::RenderManaBar(SDL_Renderer* renderer)
{
    if (!m_player) {
        return;
    }

    // Get player mana values
    int mana = m_player->GetMana();
    int maxMana = m_player->GetMaxMana();

    // Calculate mana bar fill width
    int fillWidth = static_cast<int>((static_cast<float>(mana) / maxMana) * m_manaBarWidth);

    // Render mana bar background
    SDL_Rect bgRect = {m_manaBarX, m_manaBarY, m_manaBarWidth, m_manaBarHeight};
    SDL_SetRenderDrawColor(renderer, 0, 0, 50, 255); // Dark blue
    SDL_RenderFillRect(renderer, &bgRect);

    // Render mana bar fill
    SDL_Rect fillRect = {m_manaBarX, m_manaBarY, fillWidth, m_manaBarHeight};
    SDL_SetRenderDrawColor(renderer, 0, 0, 255, 255); // Bright blue
    SDL_RenderFillRect(renderer, &fillRect);

    // Render mana bar border
    SDL_SetRenderDrawColor(renderer, 200, 200, 200, 255); // Light gray
    SDL_RenderDrawRect(renderer, &bgRect);

    // Render mana text
    std::stringstream ss;
    ss << mana << "/" << maxMana;

    // Render text with shadow
    RenderTextWithShadow(renderer, ss.str(), m_manaBarX + m_manaBarWidth + 5, m_manaBarY, m_textColor, m_textShadowColor);
}

void GameUI::RenderExpBar(SDL_Renderer* renderer)
{
    if (!m_player) {
        return;
    }

    // Get player experience values
    int exp = m_player->GetExperience();
    int maxExp = m_player->GetMaxExperience();

    // Calculate experience bar fill width
    int fillWidth = static_cast<int>((static_cast<float>(exp) / maxExp) * m_expBarWidth);

    // Render experience bar background
    SDL_Rect bgRect = {m_expBarX, m_expBarY, m_expBarWidth, m_expBarHeight};
    SDL_SetRenderDrawColor(renderer, 50, 50, 0, 255); // Dark yellow
    SDL_RenderFillRect(renderer, &bgRect);

    // Render experience bar fill
    SDL_Rect fillRect = {m_expBarX, m_expBarY, fillWidth, m_expBarHeight};
    SDL_SetRenderDrawColor(renderer, 255, 255, 0, 255); // Bright yellow
    SDL_RenderFillRect(renderer, &fillRect);

    // Render experience bar border
    SDL_SetRenderDrawColor(renderer, 200, 200, 200, 255); // Light gray
    SDL_RenderDrawRect(renderer, &bgRect);

    // Render experience text
    std::stringstream ss;
    ss << exp << "/" << maxExp;

    // Render text with shadow
    RenderTextWithShadow(renderer, ss.str(), m_expBarX + m_expBarWidth + 5, m_expBarY, m_textColor, m_textShadowColor);
}

void GameUI::RenderPlayerStats(SDL_Renderer* renderer)
{
    if (!m_player) {
        return;
    }

    // Get player stats
    int level = m_player->GetLevel();
    std::string name = m_player->GetName();
    std::string job = PlayerClassToString(m_player->GetClass());

    // Render player name and level
    std::stringstream ss;
    ss << name << " Lv." << level << " " << job;

    // Render text with shadow
    RenderTextWithShadow(renderer, ss.str(), 10, 10, m_textColor, m_textShadowColor);

    // Render additional player stats
    int attackMin = m_player->GetAttackMin();
    int attackMax = m_player->GetAttackMax();
    int defense = m_player->GetAC();  // AC = Armor Class (Defense)

    std::stringstream ss2;
    ss2 << "Attack: " << attackMin << "-" << attackMax << " Defense: " << defense;

    // Render text with shadow
    RenderTextWithShadow(renderer, ss2.str(), 10, 30, m_textColor, m_textShadowColor);
}

void GameUI::RenderTextWithShadow(SDL_Renderer* renderer, const std::string& text, int x, int y,
                                 const SDL_Color& color, const SDL_Color& shadowColor)
{
    if (!m_font) {
        return;
    }

    // Render shadow text
    SDL_Surface* shadowSurface = TTF_RenderText_Solid(m_font, text.c_str(), shadowColor);
    if (shadowSurface) {
        SDL_Texture* shadowTexture = SDL_CreateTextureFromSurface(renderer, shadowSurface);
        if (shadowTexture) {
            SDL_Rect shadowRect = {x + 1, y + 1, shadowSurface->w, shadowSurface->h};
            SDL_RenderCopy(renderer, shadowTexture, nullptr, &shadowRect);
            SDL_DestroyTexture(shadowTexture);
        }
        SDL_FreeSurface(shadowSurface);
    }

    // Render main text
    SDL_Surface* textSurface = TTF_RenderText_Solid(m_font, text.c_str(), color);
    if (textSurface) {
        SDL_Texture* textTexture = SDL_CreateTextureFromSurface(renderer, textSurface);
        if (textTexture) {
            SDL_Rect textRect = {x, y, textSurface->w, textSurface->h};
            SDL_RenderCopy(renderer, textTexture, nullptr, &textRect);
            SDL_DestroyTexture(textTexture);
        }
        SDL_FreeSurface(textSurface);
    }
}

std::string GameUI::PlayerClassToString(PlayerClass playerClass)
{
    switch (playerClass) {
        case PlayerClass::WARRIOR:
            return "Warrior";
        case PlayerClass::WIZARD:
            return "Wizard";
        case PlayerClass::TAOIST:
            return "Taoist";
        case PlayerClass::ASSASSIN:
            return "Assassin";
        default:
            return "Unknown";
    }
}

void GameUI::SetPlayer(std::shared_ptr<Player> player)
{
    m_player = player;
}

bool GameUI::HandleMouseButton(int button, bool pressed, int x, int y)
{
    // Skip if not visible
    if (!m_visible) {
        return false;
    }

    // Check if a child control handled the event
    if (UIControl::HandleMouseButton(button, pressed, x, y)) {
        return true;
    }

    // Only handle left mouse button presses
    if (button != SDL_BUTTON_LEFT || !pressed) {
        return false;
    }

    // Check if clicked on state button
    SDL_Rect stateBtnRect = {m_stateBtnX, m_stateBtnY, UILayout::MainUI::STATE_BTN_WIDTH, UILayout::MainUI::STATE_BTN_HEIGHT};
    if (x >= stateBtnRect.x && x < stateBtnRect.x + stateBtnRect.w &&
        y >= stateBtnRect.y && y < stateBtnRect.y + stateBtnRect.h) {
        // Call status button click callback if set
        if (m_onStatusButtonClick) {
            m_onStatusButtonClick();
        }
        return true;
    }

    // Check if clicked on skill button
    SDL_Rect skillBtnRect = {m_skillBtnX, m_skillBtnY, UILayout::MainUI::SKILL_BTN_WIDTH, UILayout::MainUI::SKILL_BTN_HEIGHT};
    if (x >= skillBtnRect.x && x < skillBtnRect.x + skillBtnRect.w &&
        y >= skillBtnRect.y && y < skillBtnRect.y + skillBtnRect.h) {
        // Call skill button click callback if set
        if (m_onSkillButtonClick) {
            m_onSkillButtonClick();
        }
        return true;
    }

    // Check if clicked on other buttons
    // TODO: Implement other button click handlers

    return false;
}

