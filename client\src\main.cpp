#include "Application.h"
#include "Game/IntroState.h"
#include <iostream>
#include <SDL2/SDL.h>
#include <filesystem>
#include "Utils/Logger.h"
#include "Utils/ExceptionHandler.h"
#include <fstream>
#include <thread>
#include <sstream>
#include <iomanip>

#ifdef _WIN32
#include <windows.h>

#endif

// Helper function to log crash exceptions (forward declaration)
void LogCrashException(const std::string& errorMsg);
int main(int argc, char* argv[]);
#ifdef _WIN32
/**
 * @brief Windows entry point for the application
 * @param hInstance Handle to the current instance
 * @param hPrevInstance Handle to the previous instance (always NULL)
 * @param lpCmdLine Command line arguments
 * @param nCmdShow How the window should be shown
 * @return Exit code
 */
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    return main(__argc, __argv);
}
#endif

/**
 * @brief Main entry point for the application
 * @param argc Count of command line arguments
 * @param argv Array of command line arguments
 * @return Exit code
 */
int main(int argc, char* argv[])
{
    // Create debug log
    WriteDebugLog("Application starting");

    // Collect command line arguments into a single string for logging
    std::string cmdLine;
    for (int i = 1; i < argc; i++) {
        cmdLine += argv[i];
        if (i < argc - 1) cmdLine += " ";
    }

#ifdef _DEBUG
    // Create a console window for debugging output in debug mode
#ifdef _WIN32
    // Windows-specific console allocation
    AllocConsole();
    FILE* pConsole;
    freopen_s(&pConsole, "CONOUT$", "w", stdout);
    freopen_s(&pConsole, "CONOUT$", "w", stderr);
#endif
    std::cout << "Starting MirClient application (Debug Mode)..." << std::endl;
    WriteDebugLog("Debug console created");
#else
    // In release mode, redirect stdout and stderr to log files
    try {
        // Create logs directory if it doesn't exist
        std::filesystem::path logsDir = "logs";
        if (!std::filesystem::exists(logsDir)) {
            std::filesystem::create_directories(logsDir);
        }

        // Redirect stdout to logs/stdout.log
        std::string stdoutLogPath = "logs/stdout.log";
        std::freopen(stdoutLogPath.c_str(), "a", stdout);

        // Redirect stderr to logs/stderr.log
        std::string stderrLogPath = "logs/stderr.log";
        std::freopen(stderrLogPath.c_str(), "a", stderr);

        WriteDebugLog("Redirected stdout to " + stdoutLogPath + " and stderr to " + stderrLogPath);
    } catch (const std::exception& e) {
        WriteDebugLog("Failed to redirect stdout/stderr to log files: " + std::string(e.what()));
    }
#endif

    try {
        WriteDebugLog("Attempting to initialize logger system");

        // Get current date and time for log file naming
        auto now = std::chrono::system_clock::now();
        auto time = std::chrono::system_clock::to_time_t(now);
        char timeBuffer[20];
#ifdef _WIN32
        struct tm timeinfo;
        localtime_s(&timeinfo, &time);
        strftime(timeBuffer, sizeof(timeBuffer), "%Y%m%d_%H%M%S", &timeinfo);
#else
        struct tm* timeinfo;
        timeinfo = localtime(&time);
        strftime(timeBuffer, sizeof(timeBuffer), "%Y%m%d_%H%M%S", timeinfo);
#endif
        std::string timestamp(timeBuffer);

        // Initialize main logger with date-based filename
        std::string mainLogPath = "logs/main_" + timestamp + ".log";
        WriteDebugLog("Setting up main log file: " + mainLogPath);

        bool loggerInitialized = Logger::GetInstance().Initialize(
            mainLogPath,                       // Log file path with timestamp
            LogLevel::LOG_LEVEL_INFO,          // Log level
            LogRotationMode::SIZE_BASED,       // Size-based rotation
            5 * 1024,                          // 5MB rotation size
            10                                 // Keep 10 history files
        );

        // Create a symlink or shortcut to the latest log file for easy access
#ifdef _WIN32
        // On Windows, create a shortcut
        std::string latestLogLink = "logs/latest_main.log";
        if (std::filesystem::exists(latestLogLink)) {
            std::filesystem::remove(latestLogLink);
        }
        try {
            std::filesystem::copy(mainLogPath, latestLogLink);
            WriteDebugLog("Created link to latest log: " + latestLogLink);
        } catch (const std::exception& e) {
            WriteDebugLog("Failed to create link to latest log: " + std::string(e.what()));
        }
#else
        // On Unix systems, create a symbolic link
        std::string latestLogLink = "logs/latest_main.log";
        if (std::filesystem::exists(latestLogLink)) {
            std::filesystem::remove(latestLogLink);
        }
        try {
            std::filesystem::create_symlink(mainLogPath, latestLogLink);
            WriteDebugLog("Created symlink to latest log: " + latestLogLink);
        } catch (const std::exception& e) {
            WriteDebugLog("Failed to create symlink to latest log: " + std::string(e.what()));
        }
#endif

        // Log initialization status
        std::string initStatus = loggerInitialized ? "Logger initialized successfully" : "Failed to initialize logger";
#ifdef _DEBUG
        std::cout << initStatus << std::endl;
#endif
        WriteDebugLog(initStatus);

        if (!loggerInitialized) {
            WriteDebugLog("Logger initialization failed, but program will continue");
            WriteDebugLog("Command line arguments: " + cmdLine);
        } else {
            // Log system information and command line arguments
            LOG_INFO("Application started");
            LOG_INFO("System info: " + GetSystemInfo());
            LOG_INFO("Command line: " + cmdLine);

            // Log build configuration
#ifdef _DEBUG
            LOG_INFO("Build configuration: Debug");
#else
            LOG_INFO("Build configuration: Release");
#endif
        }

        SafeLogInfo("Creating application instance");

        // Create the application
        Application app("MirClient", 800, 600);

        // Initialize the application
        SafeLogInfo("Initializing application");
        if (!app.Initialize()) {
            SafeLogError("Application initialization failed");
            return 1;
        }
        SafeLogInfo("Application initialized successfully");

        // Set initial state to intro screen with fade effects
        SafeLogInfo("Setting initial state to IntroState");
        app.ChangeState(std::make_unique<IntroState>(&app));

        // Run the application
        SafeLogInfo("Starting main loop");
        app.Run();

        // Shutdown the application
        SafeLogInfo("Application closing");
        app.Shutdown();

        SafeLogInfo("Application exited normally");

        return 0;
    }
    catch (const std::exception& e) {
        // Log any uncaught exceptions
        std::string errorMsg = std::string("Unhandled exception: ") + e.what();
        WriteDebugLog(errorMsg);
#ifdef _DEBUG
        std::cerr << "Unhandled exception: " << e.what() << std::endl;
#endif

        LogCrashException(e.what());
        return 1;
    }
    catch (...) {
        // Log unknown exceptions
        WriteDebugLog("Unknown exception");
#ifdef _DEBUG
        std::cerr << "Unknown unhandled exception" << std::endl;
#endif

        LogCrashException("Unknown unhandled exception");
        return 1;
    }
}

// Helper function to log crash exceptions
void LogCrashException(const std::string& errorMsg)
{
    try {
        // Get current date and time for crash log file naming
        auto now = std::chrono::system_clock::now();
        auto time = std::chrono::system_clock::to_time_t(now);
        char timeBuffer[20];
#ifdef _WIN32
        struct tm timeinfo;
        localtime_s(&timeinfo, &time);
        strftime(timeBuffer, sizeof(timeBuffer), "%Y%m%d_%H%M%S", &timeinfo);
#else
        struct tm* timeinfo;
        timeinfo = localtime(&time);
        strftime(timeBuffer, sizeof(timeBuffer), "%Y%m%d_%H%M%S", timeinfo);
#endif
        std::string timestamp(timeBuffer);

        // Create logs directory if it doesn't exist
        std::filesystem::path logsDir = "logs";
        if (!std::filesystem::exists(logsDir)) {
            std::filesystem::create_directories(logsDir);
        }

        // Use timestamp in crash log filename
        std::string crashLogPath = "logs/crash_" + timestamp + ".log";
        WriteDebugLog("Creating crash log: " + crashLogPath);

        // Use logger with log rotation for crash log
        bool crashLogInitialized = Logger::GetInstance().Initialize(
            crashLogPath,
            LogLevel::LOG_LEVEL_ERROR,
            LogRotationMode::DAILY,  // Daily rotation
            20 * 1024,               // 20MB size
            30                       // Keep 30 days
        );

        if (crashLogInitialized) {
            LOG_FATAL("Exception: " + errorMsg);

            // Also log stack trace if available
            LOG_FATAL("Stack trace not available in this build");

            // Log system information
            LOG_FATAL("System info: " + GetSystemInfo());

            // Create a link to the latest crash log
#ifdef _WIN32
            // On Windows, create a copy
            std::string latestCrashLink = "logs/latest_crash.log";
            if (std::filesystem::exists(latestCrashLink)) {
                std::filesystem::remove(latestCrashLink);
            }
            try {
                std::filesystem::copy(crashLogPath, latestCrashLink);
            } catch (...) {
                // Ignore errors
            }
#else
            // On Unix systems, create a symbolic link
            std::string latestCrashLink = "logs/latest_crash.log";
            if (std::filesystem::exists(latestCrashLink)) {
                std::filesystem::remove(latestCrashLink);
            }
            try {
                std::filesystem::create_symlink(crashLogPath, latestCrashLink);
            } catch (...) {
                // Ignore errors
            }
#endif
        }
    } catch (const std::exception& e) {
#ifdef _DEBUG
        std::cerr << "Failed to log exception: " << e.what() << std::endl;
#endif
        WriteDebugLog("Failed to log exception: " + std::string(e.what()));

        // Try emergency logging
        try {
            std::ofstream emergencyLog("logs/emergency_crash.log", std::ios::app);
            if (emergencyLog.is_open()) {
                auto now = std::chrono::system_clock::now();
                auto time = std::chrono::system_clock::to_time_t(now);
                char timeBuffer[30];
#ifdef _WIN32
                struct tm timeinfo;
                localtime_s(&timeinfo, &time);
                strftime(timeBuffer, sizeof(timeBuffer), "%Y-%m-%d %H:%M:%S", &timeinfo);
#else
                struct tm* timeinfo;
                timeinfo = localtime(&time);
                strftime(timeBuffer, sizeof(timeBuffer), "%Y-%m-%d %H:%M:%S", timeinfo);
#endif
                emergencyLog << timeBuffer << " - CRASH: " << errorMsg << std::endl;
                emergencyLog.close();
            }
        } catch (...) {
            // Nothing more we can do
        }
    }
}
