#include "MapCell.h"

MapCell::MapCell()
    : m_backgroundImageIndex(0)
    , m_middleImageIndex(0)
    , m_objectImageIndex(0)
    , m_walkable(true)
    , m_transparent(true)
    , m_tileType(TileType::GROUND)
    , m_light(0)
    , m_doorIndex(0)
    , m_doorOffset(0)
    , m_doorOpen(false)
    , m_aniFrame(0)
    , m_aniTick(0)
    , m_aniTickMax(10)  // Default animation speed
{
}

MapCell::~MapCell()
{
}

void MapCell::SetBackgroundImageIndex(uint16_t index)
{
    m_backgroundImageIndex = index;
}

void MapCell::SetMiddleImageIndex(uint16_t index)
{
    m_middleImageIndex = index;
}

void MapCell::SetObjectImageIndex(uint16_t index)
{
    m_objectImageIndex = index;
}

void MapCell::SetWalkable(bool walkable)
{
    m_walkable = walkable;
}

void MapCell::SetTransparent(bool transparent)
{
    m_transparent = transparent;
}

void MapCell::SetTileType(TileType type)
{
    m_tileType = type;

    // Update walkable status based on tile type
    switch (type) {
        case TileType::GROUND:
            m_walkable = true;
            m_transparent = true;
            break;
        case TileType::OBSTACLE:
            m_walkable = false;
            m_transparent = false;
            break;
        case TileType::WATER:
            m_walkable = false;  // Can be walkable with special abilities
            m_transparent = true;
            break;
        case TileType::DOOR:
            m_walkable = false;  // Can be opened
            m_transparent = false;
            break;
        case TileType::EMPTY:
        default:
            m_walkable = false;
            m_transparent = true;
            break;
    }
}

void MapCell::SetLight(uint8_t light)
{
    m_light = light;
}

void MapCell::SetDoorIndex(uint8_t index)
{
    m_doorIndex = index;
}

void MapCell::SetDoorOffset(uint8_t offset)
{
    m_doorOffset = offset;
}

void MapCell::SetDoorOpen(bool open)
{
    m_doorOpen = open;
}

void MapCell::SetAniFrame(uint8_t frame)
{
    m_aniFrame = frame;
}

void MapCell::SetAniTick(uint8_t tick)
{
    m_aniTick = tick;
}

void MapCell::SetAniTickMax(uint8_t tickMax)
{
    m_aniTickMax = tickMax;
}

bool MapCell::UpdateAnimation(int deltaTime)
{
    // Skip if not animated
    if (m_aniTickMax == 0) {
        return false;
    }

    // Increment tick counter
    m_aniTick++;

    // Check if it's time to advance to the next frame
    if (m_aniTick >= m_aniTickMax) {
        m_aniTick = 0;
        m_aniFrame++;

        // Wrap around if needed (assuming 4 frames for animation)
        if (m_aniFrame >= 4) {
            m_aniFrame = 0;
        }

        return true;
    }

    return false;
}

bool MapCell::ToggleDoor()
{
    // Toggle door state
    m_doorOpen = !m_doorOpen;

    // Update door offset based on state
    if (m_doorOpen) {
        m_doorOffset = 1;  // Open door offset
    } else {
        m_doorOffset = 0;  // Closed door offset
    }

    return m_doorOpen;
}

void MapCell::OpenDoor()
{
    // Set door state to open
    m_doorOpen = true;
    m_doorOffset = 1;  // Open door offset

    // Update walkable status
    m_walkable = true;
}

void MapCell::CloseDoor()
{
    // Set door state to closed
    m_doorOpen = false;
    m_doorOffset = 0;  // Closed door offset

    // Update walkable status
    m_walkable = false;
}
