#include "ActorManager.h"
#include <cmath>
#include <algorithm>
#include <iostream>

ActorManager::Actor<PERSON>ana<PERSON>(std::shared_ptr<WILManager> wil<PERSON><PERSON><PERSON>, SDL_Renderer* renderer)
    : m_wil<PERSON><PERSON><PERSON>(wilManager)
    , m_renderer(renderer)
    , m_chat<PERSON><PERSON>(nullptr)
    , m_nextActorId(1)
    , m_localPlayer(nullptr)
{
}

ActorManager::~ActorManager()
{
    ClearActors();
}

void ActorManager::Update(int deltaTime)
{
    // Update all actors
    for (auto& pair : m_actors) {
        pair.second->Update(deltaTime);
    }
}

void ActorManager::Render()
{
    // Collect all actors for sorting
    std::vector<std::shared_ptr<Actor>> actorsToRender;
    for (auto& pair : m_actors) {
        if (pair.second->IsVisible()) {
            actorsToRender.push_back(pair.second);
        }
    }

    // Sort actors by Y position for proper rendering order
    std::sort(actorsToRender.begin(), actorsToRender.end(),
        [](const std::shared_ptr<Actor>& a, const std::shared_ptr<Actor>& b) {
            return a->GetY() < b->GetY();
        });

    // Render all actors
    for (auto& actor : actorsToRender) {
        actor->Render();
    }
}

std::shared_ptr<Player> ActorManager::CreatePlayer(const std::string& name, PlayerClass playerClass, bool isLocalPlayer)
{
    // Create player
    std::shared_ptr<Player> player = std::make_shared<Player>(m_nextActorId++, name, playerClass);

    // Set as local player if specified
    if (isLocalPlayer) {
        player->SetLocalPlayer(true);
        m_localPlayer = player;
    }

    // Load player sprites
    std::string spritesetName;
    switch (playerClass) {
        case PlayerClass::WARRIOR: spritesetName = "warrior"; break;
        case PlayerClass::WIZARD: spritesetName = "wizard"; break;
        case PlayerClass::TAOIST: spritesetName = "taoist"; break;
        case PlayerClass::ASSASSIN: spritesetName = "assassin"; break;
    }

    if (!LoadActorSprites(player, spritesetName)) {
        std::cerr << "Failed to load player sprites: " << spritesetName << std::endl;
    }

    // Add to actors map
    m_actors[player->GetId()] = player;

    return player;
}

std::shared_ptr<Monster> ActorManager::CreateMonster(const std::string& name, MonsterType type, int level)
{
    // Create monster
    std::shared_ptr<Monster> monster = std::make_shared<Monster>(m_nextActorId++, name, type, level);

    // Load monster sprites
    std::string spritesetName = name;
    if (!LoadActorSprites(monster, spritesetName)) {
        std::cerr << "Failed to load monster sprites: " << spritesetName << std::endl;
    }

    // Add to actors map
    m_actors[monster->GetId()] = monster;

    return monster;
}

std::shared_ptr<Actor> ActorManager::GetActor(int id)
{
    auto it = m_actors.find(id);
    if (it != m_actors.end()) {
        return it->second;
    }

    return nullptr;
}

std::shared_ptr<Player> ActorManager::GetLocalPlayer()
{
    return m_localPlayer;
}

std::vector<std::shared_ptr<Actor>> ActorManager::GetActorsAt(int x, int y)
{
    std::vector<std::shared_ptr<Actor>> result;

    for (auto& pair : m_actors) {
        if (pair.second->GetX() == x && pair.second->GetY() == y) {
            result.push_back(pair.second);
        }
    }

    return result;
}

std::vector<std::shared_ptr<Actor>> ActorManager::GetActorsInRange(int x, int y, int range)
{
    std::vector<std::shared_ptr<Actor>> result;

    for (auto& pair : m_actors) {
        int dx = pair.second->GetX() - x;
        int dy = pair.second->GetY() - y;
        int distanceSquared = dx * dx + dy * dy;

        if (distanceSquared <= range * range) {
            result.push_back(pair.second);
        }
    }

    return result;
}

bool ActorManager::RemoveActor(int id)
{
    auto it = m_actors.find(id);
    if (it != m_actors.end()) {
        // Check if it's the local player
        if (m_localPlayer && m_localPlayer->GetId() == id) {
            m_localPlayer = nullptr;
        }

        // Remove from map
        m_actors.erase(it);
        return true;
    }

    return false;
}

void ActorManager::ClearActors()
{
    m_actors.clear();
    m_localPlayer = nullptr;
}

bool ActorManager::LoadActorSprites(std::shared_ptr<Actor> actor, const std::string& spritesetName)
{
    // Load WIL files for each state
    std::string idleWIL = spritesetName + "_idle.wil";
    std::string walkWIL = spritesetName + "_walk.wil";
    std::string attackWIL = spritesetName + "_attack.wil";
    std::string hitWIL = spritesetName + "_hit.wil";
    std::string dieWIL = spritesetName + "_die.wil";

    // Load palette file
    std::string paletteFile = spritesetName + ".pal";

    // Load WIL files
    if (!m_wilManager->LoadWIL(idleWIL, paletteFile)) {
        std::cerr << "Failed to load idle WIL: " << idleWIL << std::endl;
        return false;
    }

    if (!m_wilManager->LoadWIL(walkWIL, paletteFile)) {
        std::cerr << "Failed to load walk WIL: " << walkWIL << std::endl;
        return false;
    }

    if (!m_wilManager->LoadWIL(attackWIL, paletteFile)) {
        std::cerr << "Failed to load attack WIL: " << attackWIL << std::endl;
        return false;
    }

    if (!m_wilManager->LoadWIL(hitWIL, paletteFile)) {
        std::cerr << "Failed to load hit WIL: " << hitWIL << std::endl;
        return false;
    }

    if (!m_wilManager->LoadWIL(dieWIL, paletteFile)) {
        std::cerr << "Failed to load die WIL: " << dieWIL << std::endl;
        return false;
    }

    // Create sprites for each state
    // TODO: Create animated sprites from WIL files

    return true;
}

std::vector<std::shared_ptr<Actor>> ActorManager::GetActors() const
{
    std::vector<std::shared_ptr<Actor>> result;
    result.reserve(m_actors.size());

    for (const auto& pair : m_actors) {
        result.push_back(pair.second);
    }

    return result;
}

bool ActorManager::ActorSay(int id, const std::string& message, int duration)
{
    // Find the actor
    auto it = m_actors.find(id);
    if (it == m_actors.end()) {
        return false;
    }

    // Make sure the actor has a chat font
    if (m_chatFont && !it->second->GetChatFont()) {
        it->second->SetChatFont(m_chatFont);
    }

    // Make the actor say the message
    it->second->Say(message, duration);

    return true;
}

void ActorManager::SetChatFont(TTF_Font* font)
{
    // Store the font
    m_chatFont = font;

    // Set the font for all actors
    for (auto& pair : m_actors) {
        pair.second->SetChatFont(font);
    }
}

void ActorManager::ClearNonPlayerActors()
{
    // Create a list of actors to remove
    std::vector<int> actorsToRemove;

    for (auto& pair : m_actors) {
        // Skip the local player
        if (m_localPlayer && pair.second->GetId() == m_localPlayer->GetId()) {
            continue;
        }

        // Skip other players (if needed)
        if (dynamic_cast<Player*>(pair.second.get()) != nullptr) {
            continue;
        }

        // Add to removal list
        actorsToRemove.push_back(pair.first);
    }

    // Remove the actors
    for (int id : actorsToRemove) {
        m_actors.erase(id);
    }
}

std::shared_ptr<Player> ActorManager::SpawnPlayer(int id, const std::string& name, PlayerClass playerClass, int x, int y, bool isLocalPlayer)
{
    // Create player
    std::shared_ptr<Player> player = std::make_shared<Player>(id, name, playerClass);

    // Set position
    player->SetPosition(x, y);

    // Set as local player if specified
    if (isLocalPlayer) {
        player->SetLocalPlayer(true);
        m_localPlayer = player;
    }

    // Load player sprites
    std::string spritesetName;
    switch (playerClass) {
        case PlayerClass::WARRIOR: spritesetName = "warrior"; break;
        case PlayerClass::WIZARD: spritesetName = "wizard"; break;
        case PlayerClass::TAOIST: spritesetName = "taoist"; break;
        case PlayerClass::ASSASSIN: spritesetName = "assassin"; break;
    }

    if (!LoadActorSprites(player, spritesetName)) {
        std::cerr << "Failed to load player sprites: " << spritesetName << std::endl;
    }

    // Add to actors map
    m_actors[player->GetId()] = player;

    return player;
}

std::shared_ptr<Monster> ActorManager::SpawnMonster(int id, const std::string& name, MonsterType type, int level, int x, int y)
{
    // Create monster
    std::shared_ptr<Monster> monster = std::make_shared<Monster>(id, name, type, level);

    // Set position
    monster->SetPosition(x, y);

    // Load monster sprites
    std::string spritesetName = name;
    if (!LoadActorSprites(monster, spritesetName)) {
        std::cerr << "Failed to load monster sprites: " << spritesetName << std::endl;
    }

    // Add to actors map
    m_actors[monster->GetId()] = monster;

    return monster;
}

std::shared_ptr<Actor> ActorManager::SpawnNPC(int id, const std::string& name, int appearance, int x, int y)
{
    // Create NPC (using Actor as base class since we don't have a dedicated NPC class)
    std::shared_ptr<Actor> npc = std::make_shared<Actor>(id, name);

    // Set position
    npc->SetPosition(x, y);

    // Load NPC sprites
    std::string spritesetName = "npc_" + std::to_string(appearance);
    if (!LoadActorSprites(npc, spritesetName)) {
        std::cerr << "Failed to load NPC sprites: " << spritesetName << std::endl;
    }

    // Add to actors map
    m_actors[npc->GetId()] = npc;

    return npc;
}

std::shared_ptr<Actor> ActorManager::SpawnItem(int id, int itemType, int itemLook, int x, int y)
{
    // Create item (using Actor as base class since we don't have a dedicated Item class)
    std::string itemName = "Item_" + std::to_string(itemType);
    std::shared_ptr<Actor> item = std::make_shared<Actor>(id, itemName);

    // Set position
    item->SetPosition(x, y);

    // Load item sprites
    std::string spritesetName = "item_" + std::to_string(itemLook);
    if (!LoadActorSprites(item, spritesetName)) {
        std::cerr << "Failed to load item sprites: " << spritesetName << std::endl;
    }

    // Add to actors map
    m_actors[item->GetId()] = item;

    return item;
}

bool ActorManager::DespawnActor(int id)
{
    // Simply call RemoveActor
    return RemoveActor(id);
}

bool ActorManager::MoveActor(int id, int x, int y, int direction, int moveType)
{
    // Find the actor
    auto it = m_actors.find(id);
    if (it == m_actors.end()) {
        return false;
    }

    // Get the actor
    std::shared_ptr<Actor> actor = it->second;

    // Set position
    actor->SetPosition(x, y);

    // Set direction
    actor->SetDirection(static_cast<Direction>(direction));

    // Set state based on move type
    ActorState state = ActorState::WALKING;
    if (moveType == 1) {
        state = ActorState::RUNNING;
    }
    actor->SetState(state);

    return true;
}

bool ActorManager::ActorAttack(int id, int targetId, int attackType)
{
    // Find the actor
    auto it = m_actors.find(id);
    if (it == m_actors.end()) {
        return false;
    }

    // Get the actor
    std::shared_ptr<Actor> actor = it->second;

    // Set state to attacking
    actor->SetState(ActorState::ATTACKING);

    // Perform attack
    actor->Attack();

    return true;
}

bool ActorManager::ActorCast(int id, int targetId, int skillId, int skillLevel)
{
    // Find the actor
    auto it = m_actors.find(id);
    if (it == m_actors.end()) {
        return false;
    }

    // Get the actor
    std::shared_ptr<Actor> actor = it->second;

    // Set state to casting
    actor->SetState(ActorState::CASTING);

    // Cast spell
    actor->CastSpell(skillId);

    return true;
}

bool ActorManager::ApplyEffectToActor(int id, int effectId)
{
    // Find the actor
    auto it = m_actors.find(id);
    if (it == m_actors.end()) {
        return false;
    }

    // Get the actor
    std::shared_ptr<Actor> actor = it->second;

    // Apply effect (in a real implementation, this would create a visual effect)
    std::cout << "Applying effect " << effectId << " to actor " << id << std::endl;

    return true;
}


