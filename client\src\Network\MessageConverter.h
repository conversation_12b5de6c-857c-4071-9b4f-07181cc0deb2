#pragma once

#include "Packet.h"
#include <cstdint>
#include <string>

/**
 * @struct TDefaultMessage
 * @brief Original message structure from the Delphi project
 */
struct TDefaultMessage {
    int32_t Recog;
    uint16_t Ident;
    uint16_t Param;
    uint16_t Tag;
    uint16_t Series;
};

/**
 * @class MessageConverter
 * @brief Converts between original TDefaultMessage format and new Packet format
 */
class MessageConverter {
public:
    /**
     * @brief Convert a TDefaultMessage to a Packet
     * @param msg Original message
     * @return Converted packet
     */
    static Packet ToPacket(const TDefaultMessage& msg);

    /**
     * @brief Convert a Packet to a TDefaultMessage
     * @param packet Packet to convert
     * @return Converted message
     */
    static TDefaultMessage ToDefaultMessage(const Packet& packet);

    /**
     * @brief Create a TDefaultMessage
     * @param ident Message identifier
     * @param recog Recognition value
     * @param param Parameter value
     * @param tag Tag value
     * @param series Series value
     * @return Created message
     */
    static TDefaultMessage MakeDefaultMessage(uint16_t ident, int32_t recog, uint16_t param, uint16_t tag, uint16_t series);

    /**
     * @brief Convert a CM_* or SM_* constant to a PacketType
     * @param ident Original message identifier
     * @return Corresponding PacketType
     */
    static PacketType IdentToPacketType(uint16_t ident);

    /**
     * @brief Convert a PacketType to a CM_* or SM_* constant
     * @param type Packet type
     * @return Corresponding message identifier
     */
    static uint16_t PacketTypeToIdent(PacketType type);

    /**
     * @brief Encode a string using the original 6-bit encoding
     * @param str String to encode
     * @return Encoded string
     */
    static std::string EncodeString(const std::string& str);

    /**
     * @brief Decode a string using the original 6-bit encoding
     * @param str Encoded string
     * @return Decoded string
     */
    static std::string DecodeString(const std::string& str);

    /**
     * @brief Encode a TDefaultMessage using the original 6-bit encoding
     * @param msg Message to encode
     * @return Encoded string
     */
    static std::string EncodeMessage(const TDefaultMessage& msg);

    /**
     * @brief Decode a string to a TDefaultMessage using the original 6-bit encoding
     * @param str Encoded string
     * @return Decoded message
     */
    static TDefaultMessage DecodeMessage(const std::string& str);

private:
    /**
     * @brief Encode a buffer using the original 6-bit encoding
     * @param buf Buffer to encode
     * @param bufSize Buffer size
     * @return Encoded string
     */
    static std::string EncodeBuffer(const char* buf, int bufSize);

    /**
     * @brief Decode a string to a buffer using the original 6-bit encoding
     * @param str Encoded string
     * @param buf Output buffer
     * @param bufSize Buffer size
     */
    static void DecodeBuffer(const std::string& str, char* buf, int bufSize);

    /**
     * @brief Encode a buffer using the original 6-bit encoding
     * @param src Source buffer
     * @param dest Destination buffer
     * @param srcLen Source buffer length
     * @param destLen Destination buffer length
     */
    static void Encode6BitBuf(const char* src, char* dest, int srcLen, int destLen);

    /**
     * @brief Decode a buffer using the original 6-bit encoding
     * @param src Source buffer
     * @param dest Destination buffer
     * @param srcLen Source buffer length
     * @param destLen Destination buffer length
     */
    static void Decode6BitBuf(const char* src, char* dest, int srcLen, int destLen);
};
