#include <iostream>
#include <csignal>
#include <thread>
#include <chrono>
#include "LoginServer.h"
#include "../Common/Logger.h"

using namespace MirServer;

static std::unique_ptr<LoginServer> g_loginServer;
static bool g_running = true;

void SignalHandler(int signal) {
    if (signal == SIGINT || signal == SIGTERM) {
        Logger::Info("Received shutdown signal");
        g_running = false;
        if (g_loginServer) {
            g_loginServer->Stop();
        }
    }
}

void PrintUsage(const char* programName) {
    std::cout << "Usage: " << programName << " [options]" << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  -c <config>  Specify configuration file (default: ./config/LoginServer.ini)" << std::endl;
    std::cout << "  -h           Show this help message" << std::endl;
}

int main(int argc, char* argv[]) {
    // Set up signal handlers
    std::signal(SIGINT, SignalHandler);
    std::signal(SIGTERM, SignalHandler);
    
    // Parse command line arguments
    std::string configFile = "./config/LoginServer.ini";
    
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "-c" && i + 1 < argc) {
            configFile = argv[++i];
        } else if (arg == "-h" || arg == "--help") {
            PrintUsage(argv[0]);
            return 0;
        }
    }
    
    // Initialize logger
    Logger::SetLogFile("LoginServer.log");
    Logger::Info("===========================================");
    Logger::Info("MirServer LoginServer Starting...");
    Logger::Info("Version: 1.0.0");
    Logger::Info("===========================================");
    
    // Create and initialize LoginServer
    g_loginServer = std::make_unique<LoginServer>();
    
    if (!g_loginServer->Initialize(configFile)) {
        Logger::Error("Failed to initialize LoginServer");
        return 1;
    }
    
    if (!g_loginServer->Start()) {
        Logger::Error("Failed to start LoginServer");
        return 1;
    }
    
    // Main loop - print statistics every 30 seconds
    auto lastStats = std::chrono::steady_clock::now();
    
    while (g_running && g_loginServer->IsRunning()) {
        auto now = std::chrono::steady_clock::now();
        
        if (std::chrono::duration_cast<std::chrono::seconds>(now - lastStats).count() >= 30) {
            const auto& stats = g_loginServer->GetStats();
            Logger::Info("=== LoginServer Statistics ===");
            Logger::Info("Total Logins: " + std::to_string(stats.totalLogins.load()));
            Logger::Info("Failed Logins: " + std::to_string(stats.failedLogins.load()));
            Logger::Info("Online Users: " + std::to_string(stats.onlineUsers.load()));
            Logger::Info("New Accounts: " + std::to_string(stats.newAccounts.load()));
            Logger::Info("Password Changes: " + std::to_string(stats.passwordChanges.load()));
            Logger::Info("==============================");
            lastStats = now;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // Shutdown
    Logger::Info("LoginServer shutting down...");
    g_loginServer->Stop();
    g_loginServer.reset();
    
    Logger::Info("LoginServer stopped");
    return 0;
} 