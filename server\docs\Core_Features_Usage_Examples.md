# GameEngine 核心功能使用示例

## 概述

本文档提供了GameEngine四个核心功能的实际使用示例，展示如何在游戏服务器中集成和使用这些功能。

## 1. 仓库系统使用示例

### 1.1 玩家打开仓库

```cpp
// 玩家请求打开仓库
void HandleOpenStorageRequest(PlayObject* player, const std::string& password) {
    auto& engine = GameEngine::GetInstance();
    auto* storageManager = engine.GetStorageManager();
    
    if (storageManager->OpenStorage(player, password)) {
        // 发送仓库物品列表给客户端
        const auto& items = storageManager->GetStorageItems(player);
        DWORD gold = storageManager->GetStorageGold(player);
        
        // 构造并发送仓库数据包
        SendStorageItemsToClient(player, items, gold);
        
        player->SendMessage("仓库已打开");
    } else {
        player->SendMessage("仓库密码错误或无法打开");
    }
}
```

### 1.2 存储物品到仓库

```cpp
// 玩家存储物品
void HandleStoreItemRequest(PlayObject* player, WORD bagIndex) {
    auto& engine = GameEngine::GetInstance();
    auto* storageManager = engine.GetStorageManager();
    
    if (!storageManager->IsStorageOpen(player)) {
        player->SendMessage("请先打开仓库");
        return;
    }
    
    // 获取背包中的物品
    UserItem item;
    if (player->GetBagItem(bagIndex, item)) {
        if (storageManager->StoreItem(player, item)) {
            // 从背包中移除物品
            player->DeleteBagItem(item.makeIndex);
            player->SendMessage("物品已存入仓库");
        } else {
            player->SendMessage("仓库已满或存储失败");
        }
    }
}
```

## 2. 交易系统使用示例

### 2.1 发起交易请求

```cpp
// 玩家发起交易请求
void HandleTradeRequest(PlayObject* requester, const std::string& targetName) {
    auto& engine = GameEngine::GetInstance();
    auto* tradeManager = engine.GetTradeManager();
    auto* userEngine = engine.GetUserEngine();
    
    // 查找目标玩家
    PlayObject* target = userEngine->GetPlayerByName(targetName);
    if (!target) {
        requester->SendMessage("玩家不在线");
        return;
    }
    
    if (tradeManager->RequestTrade(requester, target)) {
        requester->SendMessage("交易请求已发送");
        target->SendMessage(requester->GetCharName() + " 请求与您交易");
    } else {
        requester->SendMessage("无法发起交易");
    }
}
```

### 2.2 处理交易操作

```cpp
// 玩家添加交易物品
void HandleAddTradeItem(PlayObject* player, WORD bagIndex) {
    auto& engine = GameEngine::GetInstance();
    auto* tradeManager = engine.GetTradeManager();
    
    if (!tradeManager->IsInTrade(player)) {
        player->SendMessage("您不在交易中");
        return;
    }
    
    UserItem item;
    if (player->GetBagItem(bagIndex, item)) {
        if (tradeManager->AddTradeItem(player, item)) {
            // 通知交易双方更新界面
            PlayObject* partner = tradeManager->GetTradePartner(player);
            if (partner) {
                NotifyTradeUpdate(player, partner);
            }
        }
    }
}

// 完成交易
void HandleCompleteTrade(PlayObject* player) {
    auto& engine = GameEngine::GetInstance();
    auto* tradeManager = engine.GetTradeManager();
    
    if (tradeManager->CompleteTrade(player)) {
        player->SendMessage("交易完成");
        
        PlayObject* partner = tradeManager->GetTradePartner(player);
        if (partner) {
            partner->SendMessage("交易完成");
        }
    } else {
        player->SendMessage("交易失败");
    }
}
```

## 3. 任务系统使用示例

### 3.1 NPC对话处理任务

```cpp
// NPC对话处理
void HandleNPCTalk(PlayObject* player, const std::string& npcName) {
    auto& engine = GameEngine::GetInstance();
    auto* questManager = engine.GetQuestManager();
    
    // 更新对话类型任务进度
    questManager->OnNPCTalk(player, npcName);
    
    // 检查可接取的任务
    auto availableQuests = questManager->GetAvailableQuests(player, npcName);
    if (!availableQuests.empty()) {
        // 发送任务列表给客户端
        SendQuestListToClient(player, availableQuests);
    }
    
    // 检查可完成的任务
    auto completableQuests = questManager->GetCompletableQuests(player, npcName);
    if (!completableQuests.empty()) {
        // 发送可完成任务列表
        SendCompletableQuestListToClient(player, completableQuests);
    }
}
```

### 3.2 怪物击杀处理

```cpp
// 怪物被击杀时的处理
void OnMonsterKilled(PlayObject* killer, Monster* monster) {
    auto& engine = GameEngine::GetInstance();
    auto* questManager = engine.GetQuestManager();
    
    // 更新击杀任务进度
    questManager->OnMonsterKilled(killer, monster->GetName());
    
    // 如果是组队，更新队友的任务进度
    auto* party = killer->GetParty();
    if (party) {
        for (auto* member : party->GetMembers()) {
            if (member != killer && IsNearby(member, killer)) {
                questManager->OnMonsterKilled(member, monster->GetName());
            }
        }
    }
}
```

### 3.3 任务完成处理

```cpp
// 玩家完成任务
void HandleCompleteQuest(PlayObject* player, WORD questId) {
    auto& engine = GameEngine::GetInstance();
    auto* questManager = engine.GetQuestManager();
    
    if (questManager->CompleteQuest(player, questId)) {
        player->SendMessage("任务完成！获得奖励");
        
        // 检查是否有后续任务
        const QuestData* quest = questManager->GetQuest(questId);
        if (quest) {
            // 可以在这里处理后续任务逻辑
            CheckFollowUpQuests(player, questId);
        }
    } else {
        player->SendMessage("任务完成失败");
    }
}
```

## 4. 小地图系统使用示例

### 4.1 玩家进入地图

```cpp
// 玩家进入新地图
void OnPlayerEnterMap(PlayObject* player, const std::string& mapName) {
    auto& engine = GameEngine::GetInstance();
    auto* miniMapManager = engine.GetMiniMapManager();
    auto* questManager = engine.GetQuestManager();
    
    // 更新探索任务进度
    questManager->OnMapEnter(player, mapName);
    
    // 发送小地图数据
    if (miniMapManager->SendMiniMapToPlayer(player, mapName)) {
        // 更新玩家位置标记
        miniMapManager->UpdatePlayerPosition(player, player->GetCurrentPos());
    }
}
```

### 4.2 玩家移动更新

```cpp
// 玩家位置更新
void OnPlayerMove(PlayObject* player, const Point& newPos) {
    auto& engine = GameEngine::GetInstance();
    auto* miniMapManager = engine.GetMiniMapManager();
    
    // 更新小地图上的玩家位置
    miniMapManager->UpdatePlayerPosition(player, newPos);
    
    // 如果在队伍中，更新队友小地图
    auto* party = player->GetParty();
    if (party) {
        for (auto* member : party->GetMembers()) {
            if (member != player) {
                miniMapManager->UpdatePartyMemberPositions(member);
            }
        }
    }
}
```

### 4.3 添加任务标记

```cpp
// 接受任务时添加目标标记
void OnQuestAccepted(PlayObject* player, WORD questId) {
    auto& engine = GameEngine::GetInstance();
    auto* questManager = engine.GetQuestManager();
    auto* miniMapManager = engine.GetMiniMapManager();
    
    const QuestData* quest = questManager->GetQuest(questId);
    if (quest) {
        // 根据任务类型添加地图标记
        for (const auto& objective : quest->objectives) {
            if (objective.type == QuestType::KILL_MONSTER) {
                // 添加怪物区域标记
                Point monsterArea = GetMonsterArea(objective.target);
                miniMapManager->AddQuestMark(player->GetMapName(), monsterArea, 
                                            "击杀 " + objective.target);
            }
        }
    }
}
```

## 5. 系统集成示例

### 5.1 服务器启动初始化

```cpp
// 服务器启动时的初始化
bool InitializeGameServer() {
    auto& engine = GameEngine::GetInstance();
    
    if (!engine.Initialize()) {
        Logger::Error("Failed to initialize GameEngine");
        return false;
    }
    
    // 验证所有核心功能是否可用
    if (!engine.GetStorageManager() || 
        !engine.GetTradeManager() || 
        !engine.GetQuestManager() || 
        !engine.GetMiniMapManager()) {
        Logger::Error("Core features not available");
        return false;
    }
    
    Logger::Info("All core features initialized successfully");
    return true;
}
```

### 5.2 玩家登录处理

```cpp
// 玩家登录时的处理
void OnPlayerLogin(PlayObject* player) {
    auto& engine = GameEngine::GetInstance();
    
    // 加载玩家任务数据
    auto* questManager = engine.GetQuestManager();
    questManager->LoadPlayerQuests(player);
    
    // 加载玩家仓库数据
    auto* storageManager = engine.GetStorageManager();
    storageManager->LoadStorageData(player);
    
    // 发送小地图数据
    auto* miniMapManager = engine.GetMiniMapManager();
    miniMapManager->SendMiniMapToPlayer(player, player->GetMapName());
    
    Logger::Info("Player " + player->GetCharName() + " logged in successfully");
}
```

### 5.3 玩家登出处理

```cpp
// 玩家登出时的处理
void OnPlayerLogout(PlayObject* player) {
    auto& engine = GameEngine::GetInstance();
    
    // 保存玩家任务数据
    auto* questManager = engine.GetQuestManager();
    questManager->SavePlayerQuests(player);
    
    // 关闭仓库并保存数据
    auto* storageManager = engine.GetStorageManager();
    if (storageManager->IsStorageOpen(player)) {
        storageManager->CloseStorage(player);
    }
    
    // 取消进行中的交易
    auto* tradeManager = engine.GetTradeManager();
    if (tradeManager->IsInTrade(player)) {
        tradeManager->CancelTrade(player);
    }
    
    Logger::Info("Player " + player->GetCharName() + " logged out");
}
```

## 6. 错误处理和日志

### 6.1 统一错误处理

```cpp
// 统一的错误处理函数
void HandleCoreFeatureError(PlayObject* player, const std::string& feature, 
                           const std::string& operation, const std::string& error) {
    Logger::Error("Core feature error - Feature: " + feature + 
                  ", Operation: " + operation + 
                  ", Error: " + error + 
                  ", Player: " + (player ? player->GetCharName() : "Unknown"));
    
    if (player) {
        player->SendMessage("系统错误，请稍后重试");
    }
}
```

### 6.2 性能监控

```cpp
// 定期性能监控
void MonitorCoreFeatures() {
    auto& engine = GameEngine::GetInstance();
    
    // 收集各个系统的统计信息
    auto storageStats = engine.GetStorageManager()->GetStatistics();
    auto tradeStats = engine.GetTradeManager()->GetStatistics();
    auto questStats = engine.GetQuestManager()->GetStatistics();
    auto mapStats = engine.GetMiniMapManager()->GetStatistics();
    
    // 记录性能数据
    Logger::Info("Performance Stats - Storage: " + std::to_string(storageStats.activeStorages) + 
                 ", Trade: " + std::to_string(tradeStats.activeTrades) + 
                 ", Quest: " + std::to_string(questStats.activeQuests) + 
                 ", Maps: " + std::to_string(mapStats.generatedMaps));
}
```

## 总结

这些示例展示了如何在实际游戏服务器中使用GameEngine的四个核心功能。每个功能都提供了完整的API接口，支持复杂的游戏逻辑，并且具有良好的错误处理和性能监控机制。
