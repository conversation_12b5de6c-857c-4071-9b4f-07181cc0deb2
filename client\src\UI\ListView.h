#pragma once

#include "UIControl.h"
#include <SDL2/SDL_ttf.h>
#include <string>
#include <vector>
#include <functional>

/**
 * @class ListViewItem
 * @brief Item in a list view
 */
class ListViewItem {
private:
    std::vector<std::string> m_columns;  ///< Column values
    void* m_userData;                    ///< User data

public:
    /**
     * @brief Constructor
     * @param columns Column values
     * @param userData User data
     */
    ListViewItem(const std::vector<std::string>& columns = {}, void* userData = nullptr);

    /**
     * @brief Destructor
     */
    ~ListViewItem();

    /**
     * @brief Get the column values
     * @return Column values
     */
    const std::vector<std::string>& GetColumns() const { return m_columns; }

    /**
     * @brief Set the column values
     * @param columns Column values
     */
    void SetColumns(const std::vector<std::string>& columns) { m_columns = columns; }

    /**
     * @brief Get the column value at the specified index
     * @param index Column index
     * @return Column value
     */
    const std::string& GetColumn(size_t index) const;

    /**
     * @brief Set the column value at the specified index
     * @param index Column index
     * @param value Column value
     */
    void SetColumn(size_t index, const std::string& value);

    /**
     * @brief Get the user data
     * @return User data
     */
    void* GetUserData() const { return m_userData; }

    /**
     * @brief Set the user data
     * @param userData User data
     */
    void SetUserData(void* userData) { m_userData = userData; }
};

/**
 * @class ListView
 * @brief List view UI control
 *
 * This class represents a list view UI control, which displays a list of items
 * with multiple columns.
 */
class ListView : public UIControl {
private:
    std::vector<std::string> m_columnHeaders;      ///< Column headers
    std::vector<int> m_columnWidths;               ///< Column widths
    std::vector<ListViewItem> m_items;             ///< Items
    TTF_Font* m_font;                              ///< Font
    SDL_Color m_textColor;                         ///< Text color
    SDL_Color m_backgroundColor;                   ///< Background color
    SDL_Color m_borderColor;                       ///< Border color
    SDL_Color m_headerBackgroundColor;             ///< Header background color
    SDL_Color m_headerTextColor;                   ///< Header text color
    SDL_Color m_selectionColor;                    ///< Selection color
    int m_selectedIndex;                           ///< Selected item index
    int m_itemHeight;                              ///< Item height
    int m_headerHeight;                            ///< Header height
    int m_scrollOffset;                            ///< Scroll offset
    int m_visibleItems;                            ///< Number of visible items

    // Callbacks
    std::function<void(int)> m_onSelectionChanged;  ///< Selection changed callback
    std::function<void(int)> m_onItemDoubleClicked;  ///< Item double clicked callback

public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param name Control name
     */
    ListView(int x, int y, int width, int height, const std::string& name = "");

    /**
     * @brief Destructor
     */
    virtual ~ListView();

    /**
     * @brief Update the list view
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    virtual void Update(int deltaTime) override;

    /**
     * @brief Render the list view
     * @param renderer SDL renderer
     */
    virtual void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Handle SDL event
     * @param event SDL event
     * @return true if the event was handled, false otherwise
     */
    virtual bool HandleEvent(const SDL_Event& event);

    /**
     * @brief Add a column
     * @param header Column header
     * @param width Column width
     */
    void AddColumn(const std::string& header, int width);

    /**
     * @brief Add an item
     * @param item Item to add
     * @return Index of the added item
     */
    int AddItem(const ListViewItem& item);

    /**
     * @brief Remove an item
     * @param index Item index
     */
    void RemoveItem(int index);

    /**
     * @brief Clear all items
     */
    void Clear();

    /**
     * @brief Get the number of items
     * @return Number of items
     */
    size_t GetItemCount() const { return m_items.size(); }

    /**
     * @brief Get an item
     * @param index Item index
     * @return Item
     */
    const ListViewItem& GetItem(int index) const;

    /**
     * @brief Get the selected item index
     * @return Selected item index
     */
    int GetSelectedIndex() const { return m_selectedIndex; }

    /**
     * @brief Set the selected item index
     * @param index Selected item index
     */
    void SetSelectedIndex(int index);

    /**
     * @brief Get the selected item
     * @return Selected item
     */
    const ListViewItem& GetSelectedItem() const;

    /**
     * @brief Set the font
     * @param font Font
     */
    void SetFont(TTF_Font* font) { m_font = font; }

    /**
     * @brief Set the text color
     * @param color Text color
     */
    void SetTextColor(const SDL_Color& color) { m_textColor = color; }

    /**
     * @brief Set the background color
     * @param color Background color
     */
    void SetBackgroundColor(const SDL_Color& color) { m_backgroundColor = color; }

    /**
     * @brief Set the border color
     * @param color Border color
     */
    void SetBorderColor(const SDL_Color& color) { m_borderColor = color; }

    /**
     * @brief Set the header background color
     * @param color Header background color
     */
    void SetHeaderBackgroundColor(const SDL_Color& color) { m_headerBackgroundColor = color; }

    /**
     * @brief Set the header text color
     * @param color Header text color
     */
    void SetHeaderTextColor(const SDL_Color& color) { m_headerTextColor = color; }

    /**
     * @brief Set the selection color
     * @param color Selection color
     */
    void SetSelectionColor(const SDL_Color& color) { m_selectionColor = color; }

    /**
     * @brief Set the item height
     * @param height Item height
     */
    void SetItemHeight(int height) { m_itemHeight = height; }

    /**
     * @brief Set the header height
     * @param height Header height
     */
    void SetHeaderHeight(int height) { m_headerHeight = height; }

    /**
     * @brief Set the selection changed callback
     * @param callback Selection changed callback
     */
    void SetOnSelectionChanged(std::function<void(int)> callback) { m_onSelectionChanged = callback; }

    /**
     * @brief Set the item double clicked callback
     * @param callback Item double clicked callback
     */
    void SetOnItemDoubleClicked(std::function<void(int)> callback) { m_onItemDoubleClicked = callback; }
};

