#pragma once

#include "../GameState.h"
#include "../Graphics/Texture.h"
#include <memory>
#include <vector>

/**
 * @class MenuState
 * @brief Game state for the menu screen
 * 
 * This class represents the menu screen of the game, which provides options
 * to resume the game, save, load, or exit.
 */
class MenuState : public GameState {
private:
    std::shared_ptr<Texture> m_backgroundTexture;  ///< Background texture
    std::vector<std::shared_ptr<Texture>> m_textTextures;  ///< Text textures
    
    GameState* m_previousState;                    ///< Previous game state
    int m_selectedOption;                          ///< Selected menu option
    
    /**
     * @brief Create text textures
     */
    void CreateTextTextures();
    
    /**
     * @brief Handle menu selection
     */
    void HandleMenuSelection();
    
public:
    /**
     * @brief Constructor
     * @param app Pointer to the application
     * @param previousState Previous game state
     */
    MenuState(Application* app, GameState* previousState);
    
    /**
     * @brief Destructor
     */
    virtual ~MenuState();
    
    /**
     * @brief Called when entering the state
     */
    virtual void Enter() override;
    
    /**
     * @brief Called when exiting the state
     */
    virtual void Exit() override;
    
    /**
     * @brief Update the state
     * @param deltaTime Time elapsed since last frame in seconds
     */
    virtual void Update(float deltaTime) override;
    
    /**
     * @brief Render the state
     */
    virtual void Render() override;
    
    /**
     * @brief Handle SDL events
     * @param event SDL event to handle
     */
    virtual void HandleEvents(SDL_Event& event) override;
};
