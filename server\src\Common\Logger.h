#pragma once

#include <string>
#include <mutex>
#include <fstream>
#include <memory>

namespace MirServer {

// 日志级别
enum class LogLevel {
    LOG_DEBUG = 0,
    LOG_INFO = 1,
    LOG_WARNING = 2,
    LOG_ERROR = 3,
    LOG_FATAL = 4
};

// 日志记录器类
class Logger {
public:
    // 静态方法，方便使用
    static void Debug(const std::string& message);
    static void Info(const std::string& message);
    static void Warning(const std::string& message);
    static void Error(const std::string& message);
    static void Fatal(const std::string& message);
    
    // 格式化日志
    static void Logf(LogLevel level, const char* format, ...);
    
    // 日志管理
    static void SetLogLevel(LogLevel level);
    static void SetLogFile(const std::string& filename);
    static void EnableConsoleOutput(bool enable);
    static void EnableFileOutput(bool enable);
    
    // 获取实例
    static Logger& Instance();
    
private:
    Logger();
    ~Logger();
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;
    
    void Log(LogLevel level, const std::string& message);
    std::string GetTimestamp() const;
    std::string GetLevelString(LogLevel level) const;
    
private:
    LogLevel m_logLevel = LogLevel::LOG_INFO;
    bool m_consoleOutput = true;
    bool m_fileOutput = true;
    std::string m_logFileName = "server.log";
    std::ofstream m_logFile;
    std::mutex m_mutex;
};

} // namespace MirServer 