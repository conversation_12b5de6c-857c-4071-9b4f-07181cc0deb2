@echo off
echo Building and running the program...
echo.

REM Navigate to build directory
cd build

REM Build the project
echo Building the project...
cmake --build .

REM Check if build was successful
if %ERRORLEVEL% NEQ 0 (
    echo Build failed with error code %ERRORLEVEL%
    exit /b %ERRORLEVEL%
)

echo Build successful!
echo.

REM Run the program
echo Running the program...
echo.
echo Note: The program will now start. If it crashes, you will see an error message.
echo If it runs successfully, you should see the login screen.
echo.
echo Press Ctrl+C to exit the program at any time.
echo.

MirClient.exe

echo.
echo Program exited with code %ERRORLEVEL%
pause
