// Logger.cpp - 日志系统实现
// TODO: 实现日志系统

#include "Logger.h"
#include <iostream>
#include <iomanip>
#include <ctime>
#include <sstream>
#include <cstdarg>

namespace MirServer {

Logger::Logger() {
}

Logger::~Logger() {
    if (m_logFile.is_open()) {
        m_logFile.close();
    }
}

Logger& Logger::Instance() {
    static Logger instance;
    return instance;
}

void Logger::Debug(const std::string& message) {
    Instance().Log(LogLevel::LOG_DEBUG, message);
}

void Logger::Info(const std::string& message) {
    Instance().Log(LogLevel::LOG_INFO, message);
}

void Logger::Warning(const std::string& message) {
    Instance().Log(LogLevel::LOG_WARNING, message);
}

void Logger::Error(const std::string& message) {
    Instance().Log(LogLevel::LOG_ERROR, message);
}

void Logger::Fatal(const std::string& message) {
    Instance().Log(LogLevel::LOG_FATAL, message);
}

void Logger::Logf(LogLevel level, const char* format, ...) {
    char buffer[4096];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    Instance().Log(level, std::string(buffer));
}

void Logger::SetLogLevel(LogLevel level) {
    Instance().m_logLevel = level;
}

void Logger::SetLogFile(const std::string& filename) {
    auto& logger = Instance();
    std::lock_guard<std::mutex> lock(logger.m_mutex);
    
    if (logger.m_logFile.is_open()) {
        logger.m_logFile.close();
    }
    
    logger.m_logFileName = filename;
}

void Logger::EnableConsoleOutput(bool enable) {
    Instance().m_consoleOutput = enable;
}

void Logger::EnableFileOutput(bool enable) {
    Instance().m_fileOutput = enable;
}

void Logger::Log(LogLevel level, const std::string& message) {
    if (level < m_logLevel) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_mutex);
    
    std::string timestamp = GetTimestamp();
    std::string levelStr = GetLevelString(level);
    std::string logLine = timestamp + " [" + levelStr + "] " + message;
    
    // 输出到控制台
    if (m_consoleOutput) {
        switch (level) {
            case LogLevel::LOG_ERROR:
            case LogLevel::LOG_FATAL:
                std::cerr << logLine << std::endl;
                break;
            default:
                std::cout << logLine << std::endl;
                break;
        }
    }
    
    // 输出到文件
    if (m_fileOutput) {
        if (!m_logFile.is_open()) {
            m_logFile.open(m_logFileName, std::ios::app);
        }
        
        if (m_logFile.is_open()) {
            m_logFile << logLine << std::endl;
            m_logFile.flush();
        }
    }
}

std::string Logger::GetTimestamp() const {
    auto now = std::time(nullptr);
    auto tm = std::localtime(&now);
    
    std::ostringstream oss;
    oss << std::put_time(tm, "%Y-%m-%d %H:%M:%S");
    return oss.str();
}

std::string Logger::GetLevelString(LogLevel level) const {
    switch (level) {
        case LogLevel::LOG_DEBUG: return "DEBUG";
        case LogLevel::LOG_INFO: return "INFO";
        case LogLevel::LOG_WARNING: return "WARN";
        case LogLevel::LOG_ERROR: return "ERROR";
        case LogLevel::LOG_FATAL: return "FATAL";
        default: return "UNKNOWN";
    }
}

} // namespace MirServer 