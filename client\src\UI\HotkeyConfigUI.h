#pragma once

#include <memory>
#include <vector>
#include <string>
#include <SDL2/SDL.h>
#include "../Graphics/WILLoader.h"
#include "../Graphics/Texture.h"
#include "UIControl.h"
#include "Button.h"
#include "Label.h"
#include "../Skill/Skill.h"
#include "../Skill/SkillManager.h"
#include "../Actor/Player.h"

/**
 * @class HotkeyConfigUI
 * @brief UI for configuring skill hotkeys
 */
class HotkeyConfigUI : public UIControl {
private:
    std::shared_ptr<Player> m_player;                      ///< Reference to player
    std::shared_ptr<SkillManager> m_skillManager;          ///< Reference to skill manager
    std::vector<std::shared_ptr<Button>> m_hotkeyButtons;  ///< Hotkey buttons
    std::vector<std::shared_ptr<Label>> m_hotkeyLabels;    ///< Hotkey labels
    std::vector<std::shared_ptr<Button>> m_clearButtons;   ///< Clear buttons
    std::shared_ptr<Button> m_closeButton;                 ///< Close button
    std::shared_ptr<Button> m_resetButton;                 ///< Reset button

    bool m_isSelectingHotkey;                              ///< Is selecting a hotkey
    int m_selectedHotkeyIndex;                             ///< Index of the selected hotkey

    /**
     * @brief Create UI controls
     */
    void CreateControls();

    /**
     * @brief Handle hotkey button click
     * @param control The control that was clicked
     */
    void OnHotkeyButtonClick(UIControl* control);

    /**
     * @brief Handle clear button click
     * @param control The control that was clicked
     * @param hotkeyIndex Index of the hotkey to clear
     */
    void OnClearButtonClick(UIControl* control, int hotkeyIndex);

    /**
     * @brief Handle close button click
     * @param control The control that was clicked
     */
    void OnCloseButtonClick(UIControl* control);

    /**
     * @brief Handle reset button click
     * @param control The control that was clicked
     */
    void OnResetButtonClick(UIControl* control);

public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param player Player reference
     * @param skillManager Skill manager reference
     */
    HotkeyConfigUI(int x, int y, int width, int height,
                  std::shared_ptr<Player> player,
                  std::shared_ptr<SkillManager> skillManager);

    /**
     * @brief Destructor
     */
    virtual ~HotkeyConfigUI();

    /**
     * @brief Update the UI
     * @param deltaTime Time elapsed since last frame
     */
    virtual void Update(int deltaTime) override;

    /**
     * @brief Render the UI
     * @param renderer SDL renderer
     */
    virtual void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Handle mouse button event
     * @param button Mouse button
     * @param pressed Whether the button was pressed or released
     * @param x Mouse X position
     * @param y Mouse Y position
     * @return Whether the event was handled
     */
    virtual bool HandleMouseButton(Uint8 button, bool pressed, int x, int y) override;

    /**
     * @brief Handle key event
     * @param key Key code
     * @param pressed Whether the key was pressed or released
     * @return Whether the event was handled
     */
    virtual bool HandleKey(SDL_Keycode key, bool pressed) override;

    /**
     * @brief Refresh the hotkey display
     */
    void RefreshHotkeys();

    /**
     * @brief Show the UI
     */
    void Show();

    /**
     * @brief Hide the UI
     */
    void Hide();

    /**
     * @brief Set a skill to a hotkey
     * @param hotkeyIndex Hotkey index
     * @param skillId Skill ID
     */
    void SetHotkey(int hotkeyIndex, int skillId);
};

