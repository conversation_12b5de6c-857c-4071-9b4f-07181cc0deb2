#pragma once

#include "../Common/Types.h"
#include "../Protocol/PacketTypes.h"
#include "../Protocol/NetworkManager.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <string>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <queue>

namespace MirServer {

// 使用Network命名空间中的类
using Network::ClientConnection;
using Network::NetworkManager;

// 网关协议定义（对应delphi Common.pas）
enum GateMessageType : WORD {
    GM_OPEN = 1,                  // 打开连接
    GM_CLOSE = 2,                 // 关闭连接
    GM_CHECKSERVER = 3,           // 服务器检查信号
    GM_CHECKCLIENT = 4,           // 客户端检查信号
    GM_DATA = 5,                  // 转发游戏数据
    GM_SERVERUSERINDEX = 6,       // 用户索引管理
    GM_RECEIVE_OK = 7,            // 接收确认
    GM_TEST = 20                  // 测试消息
};

// 网关消息头（对应delphi TMsgHeader）
#pragma pack(push, 1)
struct GateMessageHeader {
    DWORD dwCode;                 // 识别码 RUNGATECODE = 0xAA55AA55
    int nSocket;                  // Socket句柄
    WORD wGSocketIdx;             // 网关Socket索引
    WORD wIdent;                  // 消息标识
    WORD wUserListIndex;          // 用户列表索引
    int nLength;                  // 数据长度
    
    GateMessageHeader() 
        : dwCode(0xAA55AA55), nSocket(0), wGSocketIdx(0), 
          wIdent(0), wUserListIndex(0), nLength(0) {}
};
#pragma pack(pop)

// 游戏速度控制结构（对应delphi TGameSpeed）
struct GameSpeed {
    std::chrono::steady_clock::time_point dwHitTimeTick;
    std::chrono::steady_clock::time_point dwSpellTimeTick;
    std::chrono::steady_clock::time_point dwRunTimeTick;
    std::chrono::steady_clock::time_point dwWalkTimeTick;
    std::chrono::steady_clock::time_point dwTurnTimeTick;
    int nHitCount;
    int nSpellCount;
    int nRunCount;
    int nWalkCount;
    int nTurnCount;
    
    GameSpeed() : nHitCount(0), nSpellCount(0), nRunCount(0), 
                  nWalkCount(0), nTurnCount(0) {
        auto now = std::chrono::steady_clock::now();
        dwHitTimeTick = dwSpellTimeTick = dwRunTimeTick = 
        dwWalkTimeTick = dwTurnTimeTick = now;
    }
};

// 网关客户端会话信息（对应delphi TSessionInfo，重命名避免冲突）
struct GateSessionInfo {
    std::shared_ptr<ClientConnection> socket;
    std::string socData;          // 接收数据缓冲区
    std::string sendData;         // 发送数据缓冲区
    int nUserListIndex;           // 用户列表索引
    int nPacketIdx;               // 数据包索引
    int nPacketErrCount;          // 错误包计数
    bool boStartLogon;            // 是否刚开始登录
    bool boSendLock;              // 发送锁定
    bool boOverNomSize;           // 超过正常大小
    int nOverNomSizeCount;        // 超大小计数
    
    std::chrono::steady_clock::time_point dwSendLatestTime;    // 最后发送时间
    int nCheckSendLength;         // 检查发送长度
    bool boSendAvailable;         // 可发送标志
    bool boSendCheck;             // 发送检查
    std::chrono::steady_clock::time_point dwTimeOutTime;      // 超时时间
    
    int nReceiveLength;           // 接收长度
    std::chrono::steady_clock::time_point dwReceiveLengthTick; // 接收长度时间戳
    std::chrono::steady_clock::time_point dwReceiveTick;       // 接收时间戳
    int nSckHandle;               // Socket句柄
    std::string sRemoteAddr;      // 远程地址
    std::chrono::steady_clock::time_point dwSayMsgTick;       // 聊天消息时间戳
    
    GameSpeed gameSpeed;          // 游戏速度控制
    
    GateSessionInfo() : nUserListIndex(0), nPacketIdx(-1), nPacketErrCount(0),
                    boStartLogon(true), boSendLock(false), boOverNomSize(false),
                    nOverNomSizeCount(0), nCheckSendLength(0), boSendAvailable(true),
                    boSendCheck(false), nReceiveLength(0), nSckHandle(-1) {
        auto now = std::chrono::steady_clock::now();
        dwSendLatestTime = dwTimeOutTime = dwReceiveLengthTick = 
        dwReceiveTick = dwSayMsgTick = now;
    }
};

// 发送用户数据结构（对应delphi TSendUserData）
struct SendUserData {
    int nSocketIdx;               // Socket索引
    int nSocketHandle;            // Socket句柄
    std::string sMsg;             // 消息内容
    
    SendUserData(int sockIdx = 0, int sockHandle = 0, const std::string& msg = "")
        : nSocketIdx(sockIdx), nSocketHandle(sockHandle), sMsg(msg) {}
};

// IP地址信息结构
struct SockAddr {
    int nIPaddr;                  // IP地址
    std::chrono::steady_clock::time_point dwStartAttackTick;  // 开始攻击时间
    int nAttackCount;             // 攻击计数
    int nSocketHandle;            // Socket句柄
    
    SockAddr() : nIPaddr(0), nAttackCount(0), nSocketHandle(0) {
        dwStartAttackTick = std::chrono::steady_clock::now();
    }
};

// 配置结构
struct GateConfig {
    bool boHit;                   // 检查攻击速度
    bool boSpell;                 // 检查魔法速度
    bool boRun;                   // 检查跑步速度
    bool boWalk;                  // 检查走路速度
    bool boTurn;                  // 检查转向速度
    
    int nHitTime;                 // 攻击时间间隔
    int nSpellTime;               // 魔法时间间隔
    int nRunTime;                 // 跑步时间间隔
    int nWalkTime;                // 走路时间间隔
    int nTurnTime;                // 转向时间间隔
    
    int nHitCount;                // 攻击计数限制
    int nSpellCount;              // 魔法计数限制
    int nRunCount;                // 跑步计数限制
    int nWalkCount;               // 走路计数限制
    int nTurnCount;               // 转向计数限制
    
    BYTE btSpeedControlMode;      // 速度控制模式
    bool boSpeedShowMsg;          // 显示速度提示
    std::string sSpeedShowMsg;    // 速度提示消息
    BYTE btMsgColor;              // 消息颜色
    
    GateConfig() : boHit(true), boSpell(true), boRun(true), boWalk(true), boTurn(true),
                   nHitTime(500), nSpellTime(500), nRunTime(300), nWalkTime(300), nTurnTime(200),
                   nHitCount(3), nSpellCount(3), nRunCount(3), nWalkCount(3), nTurnCount(3),
                   btSpeedControlMode(1), boSpeedShowMsg(true), btMsgColor(0),
                   sSpeedShowMsg("系统提示: 请按照游戏节奏,不要使用非法外挂!") {}
};

// 阻止IP方法枚举
enum BlockIPMethod {
    DISCONNECT = 0,               // 断开连接
    BLOCK = 1,                    // 临时阻止
    BLOCKLIST = 2                 // 加入阻止列表
};

class GateServer {
public:
    static constexpr int GATE_MAX_SESSION = 1000;    // 最大会话数
    static constexpr int MSG_MAX_LENGTH = 20000;     // 最大消息长度
    static constexpr int SEND_CHECK_SIZE = 512;      // 发送检查大小
    static constexpr int SEND_CHECK_SIZE_MAX = 2048; // 最大发送检查大小
    static constexpr int DEF_BLOCK_SIZE = 16;        // 默认块大小
    
    GateServer();
    ~GateServer();
    
    // 主要接口
    bool Initialize(const std::string& configFile);
    bool Start();
    void Stop();
    bool IsRunning() const { return m_isRunning; }
    
    // 配置管理
    void LoadConfig();
    void SaveConfig();
    
    // 连接管理
    void OnClientConnect(std::shared_ptr<ClientConnection> connection);
    void OnClientDisconnect(std::shared_ptr<ClientConnection> connection);
    void OnClientMessage(std::shared_ptr<ClientConnection> connection, const std::vector<BYTE>& data);
    
    // 服务器通信
    void OnServerConnect();
    void OnServerDisconnect();
    void OnServerMessage(const std::vector<BYTE>& data);
    
    // IP管理
    bool IsBlockedIP(const std::string& ipAddress);
    bool IsConnectionLimited(const std::string& ipAddress, int socketHandle);
    int AddBlockIP(const std::string& ipAddress);
    int AddTempBlockIP(const std::string& ipAddress);
    int GetConnectCountOfIP(const std::string& ipAddress);
    int GetAttackIPCount(const std::string& ipAddress);
    void CloseConnectByIP(const std::string& ipAddress);
    
    // 统计信息
    int GetSessionCount() const { return m_sessionCount; }
    int GetActiveConnections() const;
    
private:
    // 配置参数
    std::string m_configFile;
    GateConfig m_config;
    
    // 网络组件
    std::unique_ptr<NetworkManager> m_clientManager;     // 客户端网络管理器
    std::shared_ptr<ClientConnection> m_serverConnection;   // 服务器连接
    
    // 会话管理
    std::array<GateSessionInfo, GATE_MAX_SESSION> m_sessions;
    std::atomic<int> m_sessionCount;
    std::mutex m_sessionMutex;
    
    // 消息队列
    std::queue<SendUserData> m_receiveQueue;
    std::queue<SendUserData> m_sendQueue;
    std::mutex m_queueMutex;
    
    // IP管理
    std::vector<SockAddr> m_blockIPList;
    std::vector<SockAddr> m_tempBlockIPList;
    std::unordered_map<std::string, std::vector<SockAddr>> m_currentIPList;
    std::vector<SockAddr> m_attackIPList;
    std::mutex m_ipMutex;
    
    // 运行状态
    std::atomic<bool> m_isRunning;
    std::atomic<bool> m_serverReady;
    std::atomic<bool> m_gateReady;
    
    // 工作线程
    std::thread m_processThread;
    std::thread m_checkThread;
    
    // 配置参数
    std::string m_serverAddr;
    int m_serverPort;
    std::string m_gateAddr;
    int m_gatePort;
    int m_maxConnOfIP;
    int m_maxClientPacketSize;
    int m_nomClientPacketSize;
    int m_maxClientMsgCount;
    BlockIPMethod m_blockMethod;
    int m_clientSendBlockSize;
    int m_clientTimeOut;
    int m_sessionTimeOut;
    int m_sayMsgMaxLen;
    int m_sayMsgTime;
    int m_attackTick;
    int m_attackCount;
    
    // 内部方法
    void ProcessThread();
    void CheckThread();
    void ProcessUserPacket(const SendUserData& userData);
    void ProcessPacket(const SendUserData& userData);
    void SendServerMsg(int nIdent, WORD wSocketIndex, int nSocket, 
                      int nUserListIndex, int nLen, const char* data);
    void SendSocket(const char* buffer, int nLen);
    void RestSessionArray();
    void FilterSayMsg(std::string& msg);
    int CheckDefaultMessage(const Protocol::DefaultMessage* defMsg, GateSessionInfo* sessionInfo);
    void SendActionFail(std::shared_ptr<ClientConnection> connection);
    void SendWarnMsg(GateSessionInfo* sessionInfo);
    
    // 辅助方法
    std::string GetConfigString(const std::string& section, const std::string& key, const std::string& defaultValue);
    int GetConfigInt(const std::string& section, const std::string& key, int defaultValue);
    bool GetConfigBool(const std::string& section, const std::string& key, bool defaultValue);
    void SetConfigString(const std::string& section, const std::string& key, const std::string& value);
    void SetConfigInt(const std::string& section, const std::string& key, int value);
    void SetConfigBool(const std::string& section, const std::string& key, bool value);
    
    void AddMainLogMsg(const std::string& msg, int level = 0);
    void LoadAbuseFile();
    void LoadBlockIPFile();
    void SaveBlockIPFile();
    
    std::vector<std::string> m_abuseList;  // 敏感词列表
    std::mutex m_abuseMutex;
};

} // namespace MirServer 