cmake_minimum_required(VERSION 3.10)
project(SDL2Test)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find SDL2 package
find_package(SDL2 REQUIRED)

# Include directories
include_directories(${SDL2_INCLUDE_DIRS})

# Create executable as a Windows GUI application
add_executable(SDL2Test WIN32 test_win32.cpp)

# Link libraries
target_link_libraries(SDL2Test ${SDL2_LIBRARIES})
