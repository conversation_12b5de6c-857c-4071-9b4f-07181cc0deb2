#include "CharacterSelectionState.h"
#include "PlayState.h"
#include "ServerSelectionState.h"
#include "CharacterCreationState.h"
#include "../Application.h"
#include "../UI/DialogManager.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>

CharacterSelectionState::CharacterSelectionState(Application* app, std::shared_ptr<NetworkManager> networkManager)
    : GameState(app)
    , m_networkManager(networkManager)
    , m_selectedCharacterIndex(-1)
    , m_selecting(false)
{
    // Register packet handlers
    m_networkManager->RegisterPacketHandler(
        PacketType::CHARACTER_LIST,
        [this](const Packet& packet) { OnCharacterListResponse(packet); }
    );

    m_networkManager->RegisterPacketHandler(
        PacketType::CHARACTER_SELECT_RESPONSE,
        [this](const Packet& packet) { OnCharacterSelectResponse(packet); }
    );

    m_networkManager->RegisterPacketHandler(
        PacketType::CHARACTER_CREATE_RESPONSE,
        [this](const Packet& packet) { OnCharacterCreateResponse(packet); }
    );

    m_networkManager->RegisterPacketHandler(
        PacketType::CHARACTER_DELETE_RESPONSE,
        [this](const Packet& packet) { OnCharacterDeleteResponse(packet); }
    );
}

CharacterSelectionState::~CharacterSelectionState()
{
    // Unregister packet handlers
    if (m_networkManager) {
        m_networkManager->UnregisterPacketHandler(PacketType::CHARACTER_LIST);
        m_networkManager->UnregisterPacketHandler(PacketType::CHARACTER_SELECT_RESPONSE);
        m_networkManager->UnregisterPacketHandler(PacketType::CHARACTER_CREATE_RESPONSE);
        m_networkManager->UnregisterPacketHandler(PacketType::CHARACTER_DELETE_RESPONSE);
    }
}

void CharacterSelectionState::Enter()
{
    // Load background texture
    m_backgroundTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (!m_backgroundTexture->LoadFromFile("assets/data/character_selection_background.png")) {
        std::cerr << "Failed to load character selection background texture" << std::endl;
    }

    // Create UI controls
    CreateControls();

    // Request character list
    m_statusLabel->SetText("Requesting character list...");

    Packet packet(PacketType::CHARACTER_LIST_REQUEST);
    m_networkManager->QueuePacket(packet);
}

void CharacterSelectionState::Exit()
{
    // Clear textures
    m_backgroundTexture.reset();
    m_characterPreviewTexture.reset();

    // Clear UI controls
    m_titleLabel.reset();
    m_characterListView.reset();
    m_selectButton.reset();
    m_createButton.reset();
    m_deleteButton.reset();
    m_backButton.reset();
    m_statusLabel.reset();
}

void CharacterSelectionState::Update(float deltaTime)
{
    // Update network manager
    if (m_networkManager) {
        m_networkManager->Update();
    }

    // Update UI controls
    if (m_titleLabel) m_titleLabel->Update(static_cast<int>(deltaTime * 1000));
    if (m_characterListView) m_characterListView->Update(static_cast<int>(deltaTime * 1000));
    if (m_selectButton) m_selectButton->Update(static_cast<int>(deltaTime * 1000));
    if (m_createButton) m_createButton->Update(static_cast<int>(deltaTime * 1000));
    if (m_deleteButton) m_deleteButton->Update(static_cast<int>(deltaTime * 1000));
    if (m_backButton) m_backButton->Update(static_cast<int>(deltaTime * 1000));
    if (m_statusLabel) m_statusLabel->Update(static_cast<int>(deltaTime * 1000));
}

void CharacterSelectionState::Render()
{
    // Render background
    if (m_backgroundTexture) {
        m_backgroundTexture->Render(0, 0);
    }

    // Render character preview
    if (m_characterPreviewTexture) {
        int previewX = 500;
        int previewY = 150;
        m_characterPreviewTexture->Render(previewX, previewY);
    }

    // Render UI controls
    if (m_titleLabel) m_titleLabel->Render(m_app->GetRenderer());
    if (m_characterListView) m_characterListView->Render(m_app->GetRenderer());
    if (m_selectButton) m_selectButton->Render(m_app->GetRenderer());
    if (m_createButton) m_createButton->Render(m_app->GetRenderer());
    if (m_deleteButton) m_deleteButton->Render(m_app->GetRenderer());
    if (m_backButton) m_backButton->Render(m_app->GetRenderer());
    if (m_statusLabel) m_statusLabel->Render(m_app->GetRenderer());
}

void CharacterSelectionState::HandleEvents(SDL_Event& event)
{
    // Handle UI control events
    if (m_characterListView) m_characterListView->HandleEvent(event);
    if (m_selectButton) m_selectButton->HandleEvent(event);
    if (m_createButton) m_createButton->HandleEvent(event);
    if (m_deleteButton) m_deleteButton->HandleEvent(event);
    if (m_backButton) m_backButton->HandleEvent(event);

    // Handle keyboard events
    if (event.type == SDL_KEYDOWN) {
        if (event.key.keysym.sym == SDLK_RETURN) {
            // Enter key pressed, select character
            OnSelectButtonClick();
        } else if (event.key.keysym.sym == SDLK_ESCAPE) {
            // Escape key pressed, go back
            OnBackButtonClick();
        }
    }
}

void CharacterSelectionState::CreateControls()
{
    // Load font
    TTF_Font* font = TTF_OpenFont("assets/data/font.ttf", 24);
    if (!font) {
        std::cerr << "Failed to load font: " << TTF_GetError() << std::endl;
        return;
    }

    // Create title label
    m_titleLabel = std::make_shared<Label>(0, 50, 800, 40, "Select Character");
    m_titleLabel->SetFont(font);
    m_titleLabel->SetTextColor({255, 255, 255, 255});
    m_titleLabel->SetAlignment(TextAlignment::CENTER);

    // Create character list view
    m_characterListView = std::make_shared<ListView>(50, 100, 400, 300);
    m_characterListView->SetFont(font);
    m_characterListView->SetTextColor({0, 0, 0, 255});
    m_characterListView->SetBackgroundColor({255, 255, 255, 255});
    m_characterListView->SetBorderColor({0, 0, 0, 255});
    m_characterListView->SetHeaderBackgroundColor({200, 200, 200, 255});
    m_characterListView->SetHeaderTextColor({0, 0, 0, 255});
    m_characterListView->SetSelectionColor({0, 120, 215, 255});
    m_characterListView->SetItemHeight(30);
    m_characterListView->SetHeaderHeight(30);

    // Add columns
    m_characterListView->AddColumn("Name", 150);
    m_characterListView->AddColumn("Class", 100);
    m_characterListView->AddColumn("Level", 50);
    m_characterListView->AddColumn("Map", 100);

    // Set selection changed callback
    m_characterListView->SetOnSelectionChanged([this](int index) {
        m_selectedCharacterIndex = index;
        m_selectButton->SetEnabled(index >= 0);
        m_deleteButton->SetEnabled(index >= 0);

        // Update character preview
        if (index >= 0 && index < static_cast<int>(m_characters.size())) {
            // TODO: Load character preview texture based on character class
            const CharacterInfo& character = m_characters[index];
            std::string previewFile = "assets/data/character_preview_" + std::to_string(static_cast<int>(character.GetClass())) + ".png";
            m_characterPreviewTexture = std::make_shared<Texture>(m_app->GetRenderer());
            if (!m_characterPreviewTexture->LoadFromFile(previewFile)) {
                std::cerr << "Failed to load character preview texture: " << previewFile << std::endl;
            }
        } else {
            m_characterPreviewTexture.reset();
        }
    });

    // Set item double clicked callback
    m_characterListView->SetOnItemDoubleClicked([this](int index) {
        m_selectedCharacterIndex = index;
        OnSelectButtonClick();
    });

    // Create select button
    m_selectButton = std::make_shared<Button>(100, 420, 100, 40, "Select");
    m_selectButton->SetFont(font);
    m_selectButton->SetTextColor({255, 255, 255, 255});
    m_selectButton->SetOnClick([this]() { OnSelectButtonClick(); });
    m_selectButton->SetEnabled(false);

    // Create create button
    m_createButton = std::make_shared<Button>(220, 420, 100, 40, "Create");
    m_createButton->SetFont(font);
    m_createButton->SetTextColor({255, 255, 255, 255});
    m_createButton->SetOnClick([this]() { OnCreateButtonClick(); });

    // Create delete button
    m_deleteButton = std::make_shared<Button>(340, 420, 100, 40, "Delete");
    m_deleteButton->SetFont(font);
    m_deleteButton->SetTextColor({255, 255, 255, 255});
    m_deleteButton->SetOnClick([this]() { OnDeleteButtonClick(); });
    m_deleteButton->SetEnabled(false);

    // Create back button
    m_backButton = std::make_shared<Button>(460, 420, 100, 40, "Back");
    m_backButton->SetFont(font);
    m_backButton->SetTextColor({255, 255, 255, 255});
    m_backButton->SetOnClick([this]() { OnBackButtonClick(); });

    // Create status label
    m_statusLabel = std::make_shared<Label>(0, 480, 800, 30, "");
    m_statusLabel->SetFont(font);
    m_statusLabel->SetTextColor({255, 0, 0, 255});
    m_statusLabel->SetAlignment(TextAlignment::CENTER);

    // Close font
    TTF_CloseFont(font);
}

void CharacterSelectionState::OnSelectButtonClick()
{
    // Check if we're already selecting
    if (m_selecting) {
        return;
    }

    // Check if a character is selected
    if (m_selectedCharacterIndex < 0 || m_selectedCharacterIndex >= static_cast<int>(m_characters.size())) {
        m_statusLabel->SetText("Please select a character");
        return;
    }

    // Get selected character
    const CharacterInfo& character = m_characters[m_selectedCharacterIndex];

    // Set selecting state
    m_selecting = true;
    m_statusLabel->SetText("Selecting character...");

    // Send character select request
    Packet packet(PacketType::CHARACTER_SELECT_REQUEST);
    packet << character.GetName();
    m_networkManager->QueuePacket(packet);
}

void CharacterSelectionState::OnCreateButtonClick()
{
    // Transition to character creation state
    m_app->ChangeState(std::make_unique<CharacterCreationState>(m_app, m_networkManager));
}

void CharacterSelectionState::OnDeleteButtonClick()
{
    // Check if a character is selected
    if (m_selectedCharacterIndex < 0 || m_selectedCharacterIndex >= static_cast<int>(m_characters.size())) {
        m_statusLabel->SetText("Please select a character");
        return;
    }

    // Get selected character
    const CharacterInfo& character = m_characters[m_selectedCharacterIndex];

    // TODO: Show confirmation dialog

    // Send character delete request
    Packet packet(PacketType::CHARACTER_DELETE_REQUEST);
    packet << character.GetName();
    m_networkManager->QueuePacket(packet);
}

void CharacterSelectionState::OnBackButtonClick()
{
    // Go back to server selection state
    m_app->ChangeState(std::make_unique<ServerSelectionState>(m_app, m_networkManager));
}

void CharacterSelectionState::OnCharacterListResponse(const Packet& packet)
{
    // Clear character list
    m_characters.clear();
    m_characterListView->Clear();

    // Read character count
    uint16_t characterCount;
    packet >> characterCount;

    // Read characters
    for (uint16_t i = 0; i < characterCount; i++) {
        // Read character info
        std::string name;
        uint8_t playerClass;
        uint16_t level;
        uint32_t experience;
        uint16_t health, mana;
        uint16_t strength, dexterity, vitality, energy;
        uint16_t lastMapIndex;
        uint16_t lastPositionX, lastPositionY;

        packet >> name >> playerClass >> level >> experience
               >> health >> mana
               >> strength >> dexterity >> vitality >> energy
               >> lastMapIndex >> lastPositionX >> lastPositionY;

        // Create character info
        CharacterInfo character(
            name,
            static_cast<PlayerClass>(playerClass),
            level,
            experience,
            health,
            mana,
            strength,
            dexterity,
            vitality,
            energy,
            lastMapIndex,
            lastPositionX,
            lastPositionY
        );
        m_characters.push_back(character);

        // Add to list view
        ListViewItem item({name, character.GetClassString(), std::to_string(level), "Map " + std::to_string(lastMapIndex)});
        m_characterListView->AddItem(item);
    }

    // Update status
    if (characterCount > 0) {
        m_statusLabel->SetText("Select a character to play");
    } else {
        m_statusLabel->SetText("No characters available. Create a new character.");
    }
}

void CharacterSelectionState::OnCharacterSelectResponse(const Packet& packet)
{
    // Reset selecting state
    m_selecting = false;

    // Read response
    uint8_t success;
    std::string message;
    packet >> success >> message;

    if (success) {
        // Select successful
        m_statusLabel->SetText("Character selected");

        // Hide all UI elements before transitioning to play state
        if (m_characterListView) m_characterListView->Hide();
        if (m_selectButton) m_selectButton->Hide();
        if (m_createButton) m_createButton->Hide();
        if (m_deleteButton) m_deleteButton->Hide();
        if (m_backButton) m_backButton->Hide();

        // Transition to play state
        m_app->ChangeState(std::make_unique<PlayState>(m_app, m_networkManager));
    } else {
        // Select failed
        m_statusLabel->SetText(message);
    }
}

void CharacterSelectionState::OnCharacterCreateResponse(const Packet& packet)
{
    // Read response
    uint8_t success;
    std::string message;
    packet >> success >> message;

    if (success) {
        // Create successful
        m_statusLabel->SetText("Character created");

        // Request character list
        Packet listPacket(PacketType::CHARACTER_LIST_REQUEST);
        m_networkManager->QueuePacket(listPacket);
    } else {
        // Create failed
        m_statusLabel->SetText(message);
    }
}

void CharacterSelectionState::OnCharacterDeleteResponse(const Packet& packet)
{
    // Read response
    uint8_t success;
    std::string message;
    packet >> success >> message;

    if (success) {
        // Delete successful
        m_statusLabel->SetText("Character deleted");

        // Request character list
        Packet listPacket(PacketType::CHARACTER_LIST_REQUEST);
        m_networkManager->QueuePacket(listPacket);
    } else {
        // Delete failed
        m_statusLabel->SetText(message);
    }
}

