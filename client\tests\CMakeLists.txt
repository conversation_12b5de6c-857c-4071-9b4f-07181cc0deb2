# Add executable for exception logging test (explicitly NOT using WIN32 to make it a console app)
if(WIN32)
    # For Windows, create a console application
    add_executable(ExceptionLoggingTest
        ExceptionLoggingTest.cpp
        ExceptionLoggingExample.cpp
        ${CMAKE_SOURCE_DIR}/src/Utils/Logger.cpp
        ${CMAKE_SOURCE_DIR}/src/Utils/ExceptionHandler.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TestMain.cpp
    )
else()
    # For other platforms
    add_executable(ExceptionLoggingTest
        ExceptionLoggingTest.cpp
        ExceptionLoggingExample.cpp
        ${CMAKE_SOURCE_DIR}/src/Utils/Logger.cpp
        ${CMAKE_SOURCE_DIR}/src/Utils/ExceptionHandler.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TestMain.cpp
    )
endif()

# Set include directories
target_include_directories(ExceptionLoggingTest PRIVATE ${CMAKE_SOURCE_DIR})

# Set output directory
set_target_properties(ExceptionLoggingTest PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Add compile options
if(MSVC)
    target_compile_options(ExceptionLoggingTest PRIVATE /W4)
else()
    # For GCC/MinGW
    target_compile_options(ExceptionLoggingTest PRIVATE -Wall -Wextra -pedantic -g)

    # Enable debugging symbols for Debug build
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        target_compile_options(ExceptionLoggingTest PRIVATE -g3 -Og)
    endif()

    # Optimization for Release build
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        target_compile_options(ExceptionLoggingTest PRIVATE -O2)
    endif()
endif()