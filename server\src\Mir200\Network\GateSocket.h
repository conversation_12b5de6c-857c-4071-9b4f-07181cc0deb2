#pragma once

// Mir200 GateSocket - Gate communication socket system
// Based on delphi/EM2Engine gate communication - Following original project structure
// Phase 1 Implementation - Basic placeholder for M2Server integration

#include "Common/M2Share.h"
#include <memory>
#include <string>
#include <mutex>

// GateSocket class - Gate server communication socket
class GateSocket {
private:
    // Socket state
    bool m_initialized;
    bool m_running;
    
    // Network configuration
    std::string m_gate_addr;
    int m_gate_port;
    
    // Thread safety
    std::mutex m_socket_mutex;
    
public:
    GateSocket();
    ~GateSocket();
    
    // Core lifecycle
    bool Initialize();
    void Finalize();
    bool Start();
    void Stop();
    
    // Message processing
    void ProcessMessages();
    
    // Emergency operations
    void EmergencyStop();
    
    // State management
    bool IsInitialized() const { return m_initialized; }
    bool IsRunning() const { return m_running; }
    
    // Configuration
    void SetGateAddress(const std::string& addr) { m_gate_addr = addr; }
    void SetGatePort(int port) { m_gate_port = port; }
};
