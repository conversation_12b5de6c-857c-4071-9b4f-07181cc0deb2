# LocalDatabase - 本地数据库缓存系统

## 概述

LocalDatabase 是基于原 Delphi 项目中 `LocalDB.pas` 重构的本地数据库缓存系统。它提供了高性能的游戏数据缓存机制，支持从数据库和文件加载各种游戏配置数据，并提供线程安全的查询接口。

## 主要功能

### 1. 数据源支持
- **数据库源**: 支持从 SQLite/MySQL 等数据库加载数据
- **文件源**: 支持从文本配置文件加载数据
- **混合源**: 同时支持数据库和文件数据的统一管理

### 2. 缓存的数据类型

#### 从数据库加载的数据
- **StdItems**: 标准物品信息（武器、装备、道具等）
- **Magic**: 魔法技能信息（技能效果、消耗、威力等）
- **Monster**: 怪物信息（属性、AI类型、掉落等）

#### 从文件加载的数据
- **AdminList**: 管理员列表（管理员权限、IP限制）
- **GuardList**: 守卫列表（NPC守卫配置）
- **MakeItem**: 物品制作配置（合成公式）
- **MapEvent**: 地图事件配置（传送点、安全区等）
- **NPCScript**: NPC脚本（对话、商店、任务）

### 3. 核心特性
- **线程安全**: 使用读写锁确保多线程环境下的数据安全
- **高性能查询**: 内存索引提供毫秒级数据查询
- **缓存管理**: 支持缓存刷新、清理和统计
- **事件回调**: 数据加载完成通知机制
- **错误处理**: 完善的异常处理和日志记录

## 使用方法

### 1. 基本初始化

```cpp
#include "LocalDatabase.h"
#include "../Database/SQLiteDatabase.h"
#include "UserEngine.h"

// 创建数据库连接
auto database = std::make_unique<SQLiteDatabase>();
database->Connect("./GameData/mir.db");

// 创建UserEngine实例
auto userEngine = std::make_unique<UserEngine>();

// 初始化LocalDatabase
g_LocalDatabase = std::make_unique<LocalDatabase>();
g_LocalDatabase->Initialize(std::move(database), userEngine.get());

// 设置数据加载回调
g_LocalDatabase->SetDataLoadedCallback([](const std::string& dataType, bool success) {
    if (success) {
        Logger::GetInstance()->LogInfo("Data loaded: " + dataType);
    } else {
        Logger::GetInstance()->LogError("Failed to load: " + dataType);
    }
});
```

### 2. 数据加载

```cpp
// 从数据库加载
g_LocalDatabase->LoadItemsDB();      // 加载物品数据
g_LocalDatabase->LoadMagicDB();      // 加载魔法数据
g_LocalDatabase->LoadMonsterDB();    // 加载怪物数据

// 从文件加载
g_LocalDatabase->LoadAdminList();    // 加载管理员列表
g_LocalDatabase->LoadGuardList();    // 加载守卫列表
g_LocalDatabase->LoadMakeItem();     // 加载制作配置
g_LocalDatabase->LoadMapEvent();     // 加载地图事件

// 加载NPC脚本
g_LocalDatabase->LoadNPCScript("比奇武器商", "./scripts", "weapon_merchant");
```

### 3. 数据查询

```cpp
// 查询标准物品
auto item = g_LocalDatabase->GetStdItem(100);
if (item) {
    std::cout << "物品名称: " << item->name << std::endl;
    std::cout << "攻击力: " << item->dc.min << "-" << item->dc.max << std::endl;
}

// 按名称查询物品
auto itemByName = g_LocalDatabase->GetStdItemByName("木剑");
if (itemByName) {
    std::cout << "木剑索引: " << itemByName->idx << std::endl;
}

// 查询魔法信息
auto magic = g_LocalDatabase->GetMagicInfo(1);
if (magic) {
    std::cout << "魔法名称: " << magic->name << std::endl;
    std::cout << "魔法威力: " << magic->power << std::endl;
}

// 查询怪物信息
auto monster = g_LocalDatabase->GetMonsterInfo("鸡");
if (monster) {
    std::cout << "怪物等级: " << monster->level << std::endl;
    std::cout << "生命值: " << monster->ability.HP << std::endl;
}

// 查询管理员
auto admin = g_LocalDatabase->GetAdminInfo("Admin");
if (admin) {
    std::cout << "管理员级别: " << admin->level << std::endl;
}
```

### 4. 缓存管理

```cpp
// 获取缓存统计信息
auto stats = g_LocalDatabase->GetStatistics();
std::cout << "物品数量: " << stats.stdItemCount << std::endl;
std::cout << "魔法数量: " << stats.magicCount << std::endl;
std::cout << "缓存大小: " << g_LocalDatabase->GetCacheSize() << " 字节" << std::endl;

// 刷新缓存（重新加载所有数据）
g_LocalDatabase->RefreshCache();

// 清理缓存
g_LocalDatabase->ClearCache();

// 重新加载特定类型数据
g_LocalDatabase->ReloadMerchants();
g_LocalDatabase->ReloadNPC();
```

## 配置文件格式

### 1. AdminList.txt - 管理员列表
```
; 格式: 级别 角色名 IP地址
; 级别: * = 10级, 1-9 对应 9-1 级
*Admin *************
1GameMaster ***********/24
2Moderator 0.0.0.0
```

### 2. GuardList.txt - 守卫列表
```
; 格式: 守卫名 怪物名 地图名 X坐标 Y坐标 方向
比奇守卫 守卫 3 330 330 0
沙巴克守卫 高级守卫 Sabuk 100 100 4
```

### 3. MakeItem.txt - 制作物品配置
```
; 格式: [结果物品名]
; 材料名 数量
[铁剑]
铁矿 3
木头 1
金币 1000

[银手镯]
银矿 2
宝石 1
```

### 4. MapEvent.txt - 地图事件配置
```
; 格式: 事件类型 地图名 X坐标 Y坐标 范围 参数 数值
SAFE_ZONE 3 300 300 50 比奇安全区 0
TELEPORT 3 100 100 1 D5001 0
EXP_RATE D5001 0 0 0 经验加倍区 200
```

## 数据库表结构

### StdItems 表
```sql
CREATE TABLE StdItems (
    Idx INTEGER PRIMARY KEY,
    Name TEXT NOT NULL,
    StdMode INTEGER,
    Shape INTEGER,
    Weight INTEGER,
    AniCount INTEGER,
    Source INTEGER,
    Reserved INTEGER,
    Looks INTEGER,
    DuraMax INTEGER,
    Ac INTEGER, Ac2 INTEGER,
    Mac INTEGER, MAc2 INTEGER,
    Dc INTEGER, Dc2 INTEGER,
    Mc INTEGER, Mc2 INTEGER,
    Sc INTEGER, Sc2 INTEGER,
    Need INTEGER,
    NeedLevel INTEGER,
    Price INTEGER
);
```

### Magic 表
```sql
CREATE TABLE Magic (
    MagId INTEGER PRIMARY KEY,
    MagName TEXT NOT NULL,
    EffectType INTEGER,
    Effect INTEGER,
    Spell INTEGER,
    Power INTEGER,
    MaxPower INTEGER,
    Job INTEGER,
    NeedL1 INTEGER, NeedL2 INTEGER, NeedL3 INTEGER,
    L1Train INTEGER, L2Train INTEGER, L3Train INTEGER,
    Delay INTEGER,
    DefSpell INTEGER,
    DefPower INTEGER,
    DefMaxPower INTEGER,
    Descr TEXT
);
```

### Monster 表
```sql
CREATE TABLE Monster (
    Race INTEGER,
    Name TEXT PRIMARY KEY,
    Level INTEGER,
    HP INTEGER, MP INTEGER,
    AC INTEGER, AC2 INTEGER,
    MAC INTEGER, MAC2 INTEGER,
    DC INTEGER, DC2 INTEGER,
    MC INTEGER, MC2 INTEGER,
    SC INTEGER, SC2 INTEGER,
    Undead BOOLEAN,
    AIType INTEGER,
    ViewRange INTEGER,
    MoveSpeed INTEGER,
    AttackSpeed INTEGER
);
```

## 性能特性

### 查询性能
- **索引查询**: O(1) 时间复杂度
- **名称查询**: 哈希表查找，平均 O(1) 时间复杂度
- **范围查询**: 根据具体需求优化的查询算法

### 内存使用
- **智能指针**: 使用 `std::unique_ptr` 管理内存，避免内存泄漏
- **索引映射**: 使用 `std::unordered_map` 提供高效索引
- **按需加载**: 只加载实际使用的数据类型

### 线程安全
- **读写锁**: 使用 `std::shared_mutex` 支持多读单写
- **细粒度锁**: 不同数据类型使用独立的锁，减少锁竞争
- **原子操作**: 统计计数器使用原子操作

## 扩展和定制

### 1. 添加新的数据类型

```cpp
// 1. 在头文件中定义新的结构体
struct CustomDataInfo {
    int id;
    std::string name;
    // 其他字段...
};

// 2. 添加成员变量
std::vector<std::unique_ptr<CustomDataInfo>> m_customData;
std::unordered_map<int, size_t> m_customDataIndex;
mutable std::shared_mutex m_customDataMutex;

// 3. 实现加载和查询方法
bool LoadCustomData();
const CustomDataInfo* GetCustomData(int id) const;
```

### 2. 自定义文件格式解析

```cpp
void ParseCustomData(const std::vector<std::string>& lines) {
    std::unique_lock<std::shared_mutex> lock(m_customDataMutex);
    
    for (const auto& line : lines) {
        // 自定义解析逻辑
        auto data = std::make_unique<CustomDataInfo>();
        // 填充数据...
        
        size_t index = m_customData.size();
        m_customData.push_back(std::move(data));
        m_customDataIndex[m_customData[index]->id] = index;
    }
}
```

## 注意事项

### 1. 初始化顺序
- 必须先初始化数据库连接和 UserEngine
- LocalDatabase 的初始化依赖于这些组件

### 2. 内存管理
- 使用全局实例 `g_LocalDatabase`，需要在程序结束时正确清理
- 大量数据加载时注意内存使用情况

### 3. 线程安全
- 所有查询操作都是线程安全的
- 加载操作建议在单线程环境下进行

### 4. 错误处理
- 数据库连接失败会导致相关加载操作失败
- 文件不存在时会记录警告但不会中断程序执行

### 5. 性能优化
- 建议在服务器启动时一次性加载所有数据
- 避免频繁的缓存刷新操作
- 对于大型数据集，考虑分页或懒加载机制

## 示例代码

完整的使用示例请参考 `LocalDatabaseExample.cpp` 文件，其中包含了：
- 基本初始化流程
- 数据加载示例
- 查询操作示例
- 性能测试代码
- GameEngine 集成示例

## 未来扩展

计划中的功能扩展：
1. **异步加载**: 支持后台异步加载大型数据集
2. **增量更新**: 支持数据的增量更新而非全量重新加载
3. **数据验证**: 添加数据完整性和一致性检查
4. **热重载**: 支持运行时重新加载配置文件
5. **分布式缓存**: 支持多服务器间的缓存同步 