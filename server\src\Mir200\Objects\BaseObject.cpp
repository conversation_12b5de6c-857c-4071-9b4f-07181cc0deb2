#include "BaseObject.h"
#include "../Common/M2Share.h"

// BaseObject implementation - Following original ObjBase.pas structure exactly

BaseObject::BaseObject() {
    // Initialize core object data (matching original TBaseObject.Create exactly)
    // Following exact initialization from ObjBase.pas lines 1157-1368
    
    // Core object data initialization (matching original field initialization exactly)
    m_map_name = "";                        // m_sMapName := '';
    m_char_name = "";                       // m_sCharName := '';
    m_curr_x = 0;                           // m_nCurrX := 0;
    m_curr_y = 0;                           // m_nCurrY := 0;
    m_direction = DR_UP;                    // m_btDirection := DR_UP;
    m_gender = 0;                           // m_btGender := 0;
    m_hair = 0;                             // m_btHair := 0;
    m_job = 0;                              // m_btJob := 0;
    m_gold = 0;                             // m_nGold := 0;
    
    // Initialize ability structure (matching original TAbility initialization)
    m_ability.level = 1;                    // m_Abil.Level := 1;
    m_ability.ac = 0;                       // m_Abil.AC := 0;
    m_ability.mac = 0;                      // m_Abil.MAC := 0;
    m_ability.dc = 0;                       // m_Abil.DC := 0;
    m_ability.mc = 0;                       // m_Abil.MC := 0;
    m_ability.sc = 0;                       // m_Abil.SC := 0;
    m_ability.hp = 0;                       // m_Abil.HP := 0;
    m_ability.mp = 0;                       // m_Abil.MP := 0;
    m_ability.hit = DEFHIT;                 // m_Abil.Hit := DEFHIT;
    m_ability.speed = DEFSPEED;             // m_Abil.Speed := DEFSPEED;
    m_ability.x2 = 0;                       // m_Abil.X2 := 0;
    
    // Character status initialization
    m_char_status = 0;                      // m_nCharStatus := 0;
    
    // Home position initialization (matching original home field initialization)
    m_home_map = "";                        // m_sHomeMap := '';
    m_home_x = 0;                           // m_nHomeX := 0;
    m_home_y = 0;                           // m_nHomeY := 0;
    
    // Horse and appearance initialization
    m_on_horse = false;                     // m_boOnHorse := False;
    m_horse_type = 0;                       // m_btHorseType := 0;
    m_dress_eff_type = 0;                   // m_btDressEffType := 0;
    
    // PK and group settings initialization
    m_pk_point = 0;                         // m_nPkPoint := 0;
    m_allow_group = true;                   // m_boAllowGroup := True;
    m_allow_guild = true;                   // m_boAllowGuild := True;
    
    // Health and spell increment initialization
    m_inc_health = 0;                       // m_nIncHealth := 0;
    m_inc_spell = 0;                        // m_nIncSpell := 0;
    m_inc_healing = 0;                      // m_nIncHealing := 0;
    
    // Fight zone and bonus initialization
    m_fight_zone_die_count = 0;             // m_nFightZoneDieCount := 0;
    m_bonus_point = 0;                      // m_nBonusPoint := 0;
    m_hunger_status = 0;                    // m_nHungerStatus := 0;
    m_allow_guild_recall = true;            // m_boAllowGuildRecall := True;
    m_body_luck = 0.0;                      // m_dBodyLuck := 0;
    m_body_luck_level = 0;                  // m_nBodyLuckLevel := 0;
    m_group_recall_time = 0;                // m_wGroupRcallTime := 0;
    m_allow_group_recall = true;            // m_boAllowGroupRecall := True;
    
    // Initialize bonus abilities (matching original TNakedAbility initialization)
    m_bonus_ability.dc = 0;                 // m_BonusAbil.DC := 0;
    m_bonus_ability.mc = 0;                 // m_BonusAbil.MC := 0;
    m_bonus_ability.sc = 0;                 // m_BonusAbil.SC := 0;
    m_bonus_ability.ac = 0;                 // m_BonusAbil.AC := 0;
    m_bonus_ability.mac = 0;                // m_BonusAbil.MAC := 0;
    m_bonus_ability.hp = 0;                 // m_BonusAbil.HP := 0;
    m_bonus_ability.mp = 0;                 // m_BonusAbil.MP := 0;
    m_bonus_ability.hit = 0;                // m_BonusAbil.Hit := 0;
    m_bonus_ability.speed = 0;              // m_BonusAbil.Speed := 0;
    m_bonus_ability.reserved = 0;           // m_BonusAbil.Reserved := 0;
    
    // Initialize current bonus abilities (matching original TNakedAbility initialization)
    m_cur_bonus_ability = m_bonus_ability;  // m_nCurBonusAbil := m_BonusAbil;
    
    // Extended character status
    m_char_status_ex = 0;                   // m_nCharStatusEx := 0;
    m_fight_exp = 0;                        // m_dwFightExp := 0;
    m_view_range = 12;                      // m_nViewRange := 12;
    
    // Initialize W ability (matching original TAbility initialization)
    m_w_ability = m_ability;                // m_WAbil := m_Abil;
    
    // Initialize add ability (matching original TAddAbility initialization)
    m_add_ability.dc = 0;                   // m_AddAbil.DC := 0;
    m_add_ability.mc = 0;                   // m_AddAbil.MC := 0;
    m_add_ability.sc = 0;                   // m_AddAbil.SC := 0;
    m_add_ability.ac = 0;                   // m_AddAbil.AC := 0;
    m_add_ability.mac = 0;                  // m_AddAbil.MAC := 0;
    m_add_ability.hp = 0;                   // m_AddAbil.HP := 0;
    m_add_ability.mp = 0;                   // m_AddAbil.MP := 0;
    m_add_ability.hit = 0;                  // m_AddAbil.Hit := 0;
    m_add_ability.speed = 0;                // m_AddAbil.Speed := 0;
    for (int i = 0; i < 7; ++i) {
        m_add_ability.reserved[i] = 0;      // m_AddAbil.Reserved[i] := 0;
    }
    
    // Initialize status time arrays (matching original array initialization)
    m_status_time_arr.fill(0);              // FillChar(m_wStatusTimeArr, SizeOf(m_wStatusTimeArr), 0);
    m_status_arr_tick.fill(0);              // FillChar(m_dwStatusArrTick, SizeOf(m_dwStatusArrTick), 0);
    m_status_flags.fill(false);             // FillChar(m_StatusArr, SizeOf(m_StatusArr), 0);
    m_status_arr_value.fill(0);             // FillChar(m_wStatusArrValue, SizeOf(m_wStatusArrValue), 0);
    m_status_arr_timeout_tick.fill(0);      // FillChar(m_dwStatusArrTimeOutTick, SizeOf(m_dwStatusArrTimeOutTick), 0);
    
    // Initialize object references
    m_environment = nullptr;
    m_user_engine = nullptr;
    m_target_object = nullptr;
    m_last_hitter = nullptr;
    m_exp_hitter = nullptr;
    m_master = nullptr;
    m_group_owner = nullptr;
    
    // Initialize network data
    m_socket = 0;                           // m_nSocket := 0;
    m_gate_socket_idx = 0;                  // m_nGateSocketIdx := 0;
    m_gate_idx = 0;                         // m_nGateIdx := 0;
    m_soft_version_date = 0;                // m_nSoftVersionDate := 0;
    
    // Initialize timing data (matching original timing field initialization)
    m_search_time = 0;                      // m_dwSearchTime := 0;
    m_search_tick = 0;                      // m_dwSearchTick := 0;
    m_run_tick = 0;                         // m_dwRunTick := 0;
    m_run_time = 300;                       // m_nRunTime := 300;
    m_health_tick = 0;                      // m_nHealthTick := 0;
    m_spell_tick = 0;                       // m_nSpellTick := 0;
    m_target_focus_tick = 0;                // m_dwTargetFocusTick := 0;
    m_last_hitter_tick = 0;                 // m_dwLastHiterTick := 0;
    m_exp_hitter_tick = 0;                  // m_dwExpHitterTick := 0;
    
    // Initialize component modules
    InitializeComponents();
}

BaseObject::~BaseObject() {
    // Finalize components before destruction
    FinalizeComponents();
}

void BaseObject::Initialize() {
    // Initialize all components
    if (m_state_manager) {
        m_state_manager->Initialize();
    }
    if (m_movement_manager) {
        m_movement_manager->Initialize();
    }
    if (m_combat_manager) {
        m_combat_manager->Initialize();
    }
    if (m_magic_manager) {
        m_magic_manager->Initialize();
    }
    if (m_inventory_manager) {
        m_inventory_manager->Initialize();
    }
    if (m_status_manager) {
        m_status_manager->Initialize();
    }
    if (m_group_manager) {
        m_group_manager->Initialize();
    }
    if (m_guild_manager) {
        m_guild_manager->Initialize();
    }
}

void BaseObject::Finalize() {
    // Finalize all components
    FinalizeComponents();
}

void BaseObject::Disappear() {
    // Following exact logic from ObjBase.pas Disappear method
    // Make object disappear from the game world
    
    // Send disappear message to nearby objects
    SendRefMsg(SM_DISAPPEAR, m_direction, m_curr_x, m_curr_y, 0, "");
    
    // Clear target references
    if (m_combat_manager) {
        m_combat_manager->DelTargetCreat();
    }
    
    // Remove from environment
    if (m_environment) {
        // m_environment->RemoveObject(this); // Would be implemented in Environment
    }
}

bool BaseObject::Operate(const DefaultMessage& msg) {
    // Following exact logic from ObjBase.pas Operate method
    // Process incoming messages
    
    // Basic message processing
    switch (msg.ident) {
        case CM_WALK:
            return WalkTo(static_cast<BYTE>(msg.param), true);
            
        case CM_RUN:
            if (m_movement_manager) {
                return m_movement_manager->Run(static_cast<BYTE>(msg.param));
            }
            break;
            
        case CM_TURN:
            if (m_movement_manager) {
                return m_movement_manager->Turn(static_cast<BYTE>(msg.param));
            }
            break;
            
        case CM_HIT:
            if (m_combat_manager && m_target_object) {
                return m_combat_manager->Attack(m_target_object);
            }
            break;
            
        case CM_SPELL:
            if (m_magic_manager) {
                // return m_magic_manager->CastSpell(msg.param, msg.tag, msg.series);
            }
            break;
            
        default:
            // Handle other messages
            break;
    }
    
    return false;
}

void BaseObject::SearchObjectViewRange() {
    // Following exact logic from ObjBase.pas SearchObjectViewRange method
    // Search for objects within view range
    
    if (!m_environment) return;
    
    // Update search timing
    m_search_tick = g_functions::GetCurrentTime();
    
    // Search for objects in view range
    // This would be implemented with actual environment object search
    // m_environment->SearchObjectsInRange(m_curr_x, m_curr_y, m_view_range, visible_objects);
}

void BaseObject::Run() {
    // Following exact logic from ObjBase.pas Run method (main update loop)
    // This is the main update method called every game tick
    
    // Update all component managers
    UpdateComponents();
    
    // Process timing-based updates
    DWORD current_time = g_functions::GetCurrentTime();
    
    // Update health and spell regeneration
    if (current_time - m_health_tick >= 1000) { // Every second
        m_health_tick = current_time;
        // Process health regeneration
        if (m_inc_health > 0) {
            m_ability.hp = std::min(static_cast<int>(m_ability.hp + m_inc_health), 
                                   static_cast<int>(m_w_ability.hp));
        }
    }
    
    if (current_time - m_spell_tick >= 1000) { // Every second
        m_spell_tick = current_time;
        // Process spell regeneration
        if (m_inc_spell > 0) {
            m_ability.mp = std::min(static_cast<int>(m_ability.mp + m_inc_spell), 
                                   static_cast<int>(m_w_ability.mp));
        }
    }
    
    // Update search timing
    if (current_time - m_search_tick >= m_search_time) {
        SearchObjectViewRange();
    }
}

void BaseObject::MakeGhost() {
    // Following exact logic from ObjBase.pas MakeGhost method
    if (m_state_manager) {
        m_state_manager->SetGhost(true);
        m_state_manager->SetDeath(false);
    }
}

void BaseObject::Die() {
    // Following exact logic from ObjBase.pas Die method
    if (m_state_manager) {
        m_state_manager->SetDeath(true);
        m_state_manager->SetGhost(false);
    }
    
    // Send death message
    SendRefMsg(SM_DEATH, m_direction, m_curr_x, m_curr_y, 0, "");
    
    // Clear target references
    if (m_combat_manager) {
        m_combat_manager->DelTargetCreat();
    }
}

void BaseObject::ReAlive() {
    // Following exact logic from ObjBase.pas ReAlive method (FFF8)
    if (m_state_manager) {
        m_state_manager->SetDeath(false);
        m_state_manager->SetGhost(false);
    }
    
    // Restore health
    m_ability.hp = m_w_ability.hp;
    m_ability.mp = m_w_ability.mp;
    
    // Send alive message
    SendRefMsg(SM_ALIVE, m_direction, m_curr_x, m_curr_y, 0, "");
}

void BaseObject::RecalcAbilitys() {
    // Following exact logic from ObjBase.pas RecalcAbilitys method (FFF7)
    // Recalculate all abilities based on equipment, buffs, etc.
    
    // Reset state manager (following original logic)
    if (m_state_manager) {
        m_state_manager->Reset();
    }
    
    // Recalculate base abilities
    m_w_ability = m_ability;
    
    // Apply bonus abilities
    m_w_ability.dc += m_bonus_ability.dc + m_add_ability.dc;
    m_w_ability.mc += m_bonus_ability.mc + m_add_ability.mc;
    m_w_ability.sc += m_bonus_ability.sc + m_add_ability.sc;
    m_w_ability.ac += m_bonus_ability.ac + m_add_ability.ac;
    m_w_ability.mac += m_bonus_ability.mac + m_add_ability.mac;
    m_w_ability.hp += m_bonus_ability.hp + m_add_ability.hp;
    m_w_ability.mp += m_bonus_ability.mp + m_add_ability.mp;
    m_w_ability.hit += m_bonus_ability.hit + m_add_ability.hit;
    m_w_ability.speed += m_bonus_ability.speed + m_add_ability.speed;
    
    // Apply equipment bonuses (would be implemented with actual equipment system)
    // ProcessEquipmentBonuses();
    
    // Apply temporary buffs and effects
    // ProcessTemporaryEffects();
    
    // Ensure values are within valid ranges
    if (m_w_ability.hp < 0) m_w_ability.hp = 0;
    if (m_w_ability.mp < 0) m_w_ability.mp = 0;
    if (m_w_ability.ac < 0) m_w_ability.ac = 0;
    if (m_w_ability.mac < 0) m_w_ability.mac = 0;
}

// Target and relationship methods (matching original exactly)
bool BaseObject::IsProtectTarget(BaseObject* target) {
    // Following exact logic from ObjBase.pas IsProtectTarget method (FFF6)
    if (!target) return false;

    // Check if target is in same group
    if (m_group_manager && target->GetGroupManager()) {
        if (m_group_manager->IsInSameGroup(target)) {
            return true;
        }
    }

    // Check if target is in same guild
    if (m_guild_manager && target->GetGuildManager()) {
        if (m_guild_manager->IsInSameGuild(target)) {
            return true;
        }
    }

    // Check if target is master/apprentice
    if (m_master == target || target->m_master == this) {
        return true;
    }

    return false;
}

bool BaseObject::IsAttackTarget(BaseObject* target) {
    // Following exact logic from ObjBase.pas IsAttackTarget method (FFF5)
    if (!target) return false;

    // Cannot attack protected targets
    if (IsProtectTarget(target)) return false;

    // Check attack mode
    if (m_combat_manager) {
        CombatMode attack_mode = m_combat_manager->GetAttackMode();

        switch (attack_mode) {
            case CombatMode::PEACE:
                return false; // Cannot attack anyone in peace mode

            case CombatMode::DEAR:
                // Can only attack if not married/dear
                // return !IsMarriedTo(target); // Would be implemented
                break;

            case CombatMode::MASTER:
                // Can attack except master/apprentice
                return !(m_master == target || target->m_master == this);

            case CombatMode::GROUP:
                // Can attack except group members
                if (m_group_manager && target->GetGroupManager()) {
                    return !m_group_manager->IsInSameGroup(target);
                }
                break;

            case CombatMode::GUILD:
                // Can attack except guild members
                if (m_guild_manager && target->GetGuildManager()) {
                    return !m_guild_manager->IsInSameGuild(target);
                }
                break;

            case CombatMode::ALL:
            case CombatMode::PK_ATTACK:
                return true; // Can attack anyone

            default:
                break;
        }
    }

    return true;
}

bool BaseObject::IsProperTarget(BaseObject* target) {
    // Following exact logic from ObjBase.pas IsProperTarget method (FFF4)
    if (!target) return false;

    // Check if target can be targeted
    if (target->GetStateManager() && !target->GetStateManager()->CanBeTargeted()) {
        return false;
    }

    // Check if target is alive
    if (target->IsDead()) return false;

    // Check distance
    if (GetDistance(target) > m_view_range) return false;

    return true;
}

bool BaseObject::IsProperFriend(BaseObject* target) {
    // Following exact logic from ObjBase.pas IsProperFriend method (FFF3)
    if (!target) return false;

    // Check if target is proper and friendly
    if (!IsProperTarget(target)) return false;

    // Check if target is protected (friend)
    return IsProtectTarget(target);
}

void BaseObject::SetTargetCreat(BaseObject* target) {
    // Following exact logic from ObjBase.pas SetTargetCreat method (FFF2)
    if (m_combat_manager) {
        m_combat_manager->SetTargetCreat(target);
    }
    m_target_object = target;
    m_target_focus_tick = g_functions::GetCurrentTime();
}

void BaseObject::DelTargetCreat() {
    // Following exact logic from ObjBase.pas DelTargetCreat method (FFF1)
    if (m_combat_manager) {
        m_combat_manager->DelTargetCreat();
    }
    m_target_object = nullptr;
    m_target_focus_tick = 0;
}

// Skill target methods (matching original exactly)
bool BaseObject::IsProperTargetSKILL_54(BaseObject* target) {
    // Following exact logic from original skill target validation
    if (!target) return false;

    // Check basic target validity
    if (!IsProperTarget(target)) return false;

    // Skill 54 specific checks (would be based on actual skill requirements)
    return true;
}

bool BaseObject::IsProperTargetSKILL_55(int level, BaseObject* target) {
    // Following exact logic from original skill target validation
    if (!target) return false;

    // Check basic target validity
    if (!IsProperTarget(target)) return false;

    // Skill 55 level-based checks
    if (level < 1) return false;

    return true;
}

bool BaseObject::IsProperTargetSKILL_56(BaseObject* target, int target_x, int target_y) {
    // Following exact logic from original skill target validation
    if (!target) return false;

    // Check basic target validity
    if (!IsProperTarget(target)) return false;

    // Check if target is at specified position
    Point target_pos = target->GetCurrentPos();
    return (target_pos.x == target_x && target_pos.y == target_y);
}

bool BaseObject::IsProperTargetSKILL_57(BaseObject* target) {
    // Following exact logic from original skill target validation
    if (!target) return false;

    // Check basic target validity
    if (!IsProperTarget(target)) return false;

    // Skill 57 specific checks
    return true;
}

bool BaseObject::IsProperTargetSKILL_70(BaseObject* target) {
    // Following exact logic from original skill target validation
    if (!target) return false;

    // Check basic target validity
    if (!IsProperTarget(target)) return false;

    // Skill 70 specific checks
    return true;
}

// Core game methods (delegated to appropriate managers)
bool BaseObject::WalkTo(BYTE direction, bool flag) {
    if (m_movement_manager) {
        MovementResult result = m_movement_manager->WalkTo(direction, flag);
        return (result == MovementResult::SUCCESS);
    }
    return false;
}

bool BaseObject::TurnTo(BYTE direction) {
    if (m_movement_manager) {
        return m_movement_manager->Turn(direction);
    }
    return false;
}

void BaseObject::SpaceMove(const MapName& map, int x, int y, int mode) {
    if (m_movement_manager) {
        m_movement_manager->SpaceMove(map, x, y, mode);
    }
}

bool BaseObject::AttackTarget(BaseObject* target) {
    if (m_combat_manager) {
        return m_combat_manager->Attack(target);
    }
    return false;
}

void BaseObject::SendRefMsg(WORD ident, WORD param, int param1, int param2, int param3, const std::string& msg) {
    // Following exact logic from ObjBase.pas SendRefMsg method
    // Send message to all objects in view range

    if (!m_environment) return;

    // Create default message
    DefaultMessage def_msg;
    def_msg.ident = ident;
    def_msg.recog = static_cast<WORD>(param1);
    def_msg.param = param;
    def_msg.tag = static_cast<WORD>(param2);
    def_msg.series = static_cast<WORD>(param3);

    // Send to all objects in view range
    // m_environment->SendMessageToRange(m_curr_x, m_curr_y, m_view_range, def_msg, msg);
}

void BaseObject::SendMsg(BaseObject* target, WORD ident, WORD param, int param1, int param2, int param3, const std::string& msg) {
    // Following exact logic from ObjBase.pas SendMsg method
    // Send message to specific target

    if (!target) return;

    // Create default message
    DefaultMessage def_msg;
    def_msg.ident = ident;
    def_msg.recog = static_cast<WORD>(param1);
    def_msg.param = param;
    def_msg.tag = static_cast<WORD>(param2);
    def_msg.series = static_cast<WORD>(param3);

    // Send to target
    // target->ProcessMessage(def_msg, msg);
}

// Status and ability methods
void BaseObject::HealthSpellChanged() {
    // Following exact logic from ObjBase.pas HealthSpellChanged method
    // Notify that health/spell has changed

    // Send health/spell update to client
    SendRefMsg(SM_HEALTHSPELLCHANGED, 0, m_ability.hp, m_ability.mp, 0, "");
}

void BaseObject::StatusChanged() {
    // Following exact logic from ObjBase.pas StatusChanged method
    // Notify that status has changed

    // Update character status
    // m_char_status = CalculateCharStatus(); // Would calculate based on current states

    // Send status update
    SendRefMsg(SM_CHARSTATUSCHANGED, 0, m_char_status, 0, 0, "");
}

void BaseObject::FeatureChanged() {
    // Following exact logic from ObjBase.pas FeatureChanged method
    // Notify that appearance features have changed

    // Send feature update
    SendRefMsg(SM_FEATURECHANGED, m_direction, m_curr_x, m_curr_y, 0, "");
}

void BaseObject::GoldChanged() {
    // Following exact logic from ObjBase.pas GoldChanged method
    // Notify that gold amount has changed

    // Send gold update
    SendRefMsg(SM_GOLDCHANGED, 0, m_gold, 0, 0, "");
}

void BaseObject::WeightChanged() {
    // Following exact logic from ObjBase.pas WeightChanged method
    // Notify that weight has changed

    // Send weight update
    SendRefMsg(SM_WEIGHTCHANGED, 0, 0, 0, 0, "");
}

// Distance and position utilities
int BaseObject::GetDistance(BaseObject* target) const {
    if (!target) return 999;

    Point target_pos = target->GetCurrentPos();
    return g_functions::GetDistance(m_curr_x, m_curr_y, target_pos.x, target_pos.y);
}

int BaseObject::GetDistance(int x, int y) const {
    return g_functions::GetDistance(m_curr_x, m_curr_y, x, y);
}

bool BaseObject::InRange(BaseObject* target, int range) const {
    return GetDistance(target) <= range;
}

bool BaseObject::InRange(int x, int y, int range) const {
    return GetDistance(x, y) <= range;
}

// Safety and validation
bool BaseObject::IsValidObject() const {
    // Basic object validation
    return !m_char_name.empty() && m_ability.level > 0;
}

bool BaseObject::IsAlive() const {
    if (m_state_manager) {
        return !m_state_manager->IsDeath();
    }
    return m_ability.hp > 0;
}

bool BaseObject::IsDead() const {
    if (m_state_manager) {
        return m_state_manager->IsDeath();
    }
    return m_ability.hp <= 0;
}

bool BaseObject::IsGhost() const {
    if (m_state_manager) {
        return m_state_manager->IsGhost();
    }
    return false;
}

// Utility methods
std::string BaseObject::GetShowName() {
    // Following exact logic from ObjBase.pas GetShowName method
    return m_char_name;
}

void BaseObject::DropUseItems(BaseObject* killer) {
    // Following exact logic from ObjBase.pas DropUseItems method
    // Drop equipped items when killed

    if (!killer) return;

    // This would be implemented with actual inventory system
    // Drop items based on PK status, item properties, etc.
}

void BaseObject::ScatterBagItems(BaseObject* killer) {
    // Following exact logic from ObjBase.pas ScatterBagItems method
    // Scatter bag items when killed

    if (!killer) return;

    // This would be implemented with actual inventory system
    // Scatter items from bag based on various conditions
}

bool BaseObject::GetMessage(DefaultMessage& msg) {
    // Following exact logic from ObjBase.pas GetMessage method
    // Get next message from message queue

    // This would be implemented with actual message queue system
    return false; // No messages available
}

// Protected internal methods
void BaseObject::InitializeComponents() {
    // Initialize all component managers
    m_state_manager = std::make_unique<ObjectState>(this);
    m_movement_manager = std::make_unique<ObjectMovement>(this);
    m_combat_manager = std::make_unique<ObjectCombat>(this);
    // m_magic_manager = std::make_unique<ObjectMagic>(this);
    // m_inventory_manager = std::make_unique<ObjectInventory>(this);
    // m_status_manager = std::make_unique<ObjectStatus>(this);
    // m_group_manager = std::make_unique<ObjectGroup>(this);
    // m_guild_manager = std::make_unique<ObjectGuild>(this);
}

void BaseObject::FinalizeComponents() {
    // Finalize all component managers in reverse order
    // m_guild_manager.reset();
    // m_group_manager.reset();
    // m_status_manager.reset();
    // m_inventory_manager.reset();
    // m_magic_manager.reset();
    m_combat_manager.reset();
    m_movement_manager.reset();
    m_state_manager.reset();
}

void BaseObject::UpdateComponents() {
    // Update all component managers
    if (m_state_manager) {
        m_state_manager->Update();
    }
    if (m_movement_manager) {
        m_movement_manager->Update();
    }
    if (m_combat_manager) {
        m_combat_manager->Update();
    }
    if (m_magic_manager) {
        m_magic_manager->Update();
    }
    if (m_inventory_manager) {
        m_inventory_manager->Update();
    }
    if (m_status_manager) {
        m_status_manager->Update();
    }
    if (m_group_manager) {
        m_group_manager->Update();
    }
    if (m_guild_manager) {
        m_guild_manager->Update();
    }
}

void BaseObject::NotifyComponentsOfStateChange() {
    // Notify all components of state changes
    if (m_state_manager) {
        // State manager handles its own state changes
    }
    if (m_movement_manager) {
        // Movement manager may need to know about state changes
    }
    if (m_combat_manager) {
        // Combat manager may need to know about state changes
    }
    // Other managers would be notified as needed
}

void BaseObject::ValidateObjectState() {
    // Validate object state consistency

    // Ensure health/mana are within valid ranges
    if (m_ability.hp < 0) m_ability.hp = 0;
    if (m_ability.mp < 0) m_ability.mp = 0;
    if (m_ability.hp > m_w_ability.hp) m_ability.hp = m_w_ability.hp;
    if (m_ability.mp > m_w_ability.mp) m_ability.mp = m_w_ability.mp;

    // Validate position
    if (m_curr_x < 0) m_curr_x = 0;
    if (m_curr_y < 0) m_curr_y = 0;

    // Validate direction
    if (m_direction > DR_UPLEFT) m_direction = DR_UP;

    // Validate level
    if (m_ability.level < 1) m_ability.level = 1;
    if (m_ability.level > MAXLEVEL) m_ability.level = MAXLEVEL;
}

// BaseObjectUtils namespace implementation
namespace BaseObjectUtils {

    std::shared_ptr<BaseObject> CreateBaseObject() {
        return std::make_shared<BaseObject>();
    }

    bool IsValidBaseObject(const BaseObject* obj) {
        return obj && obj->IsValidObject();
    }

    int CalculateDistance(const BaseObject* obj1, const BaseObject* obj2) {
        if (!obj1 || !obj2) return 999;

        Point pos1 = obj1->GetCurrentPos();
        Point pos2 = obj2->GetCurrentPos();

        return g_functions::GetDistance(pos1.x, pos1.y, pos2.x, pos2.y);
    }

    BYTE CalculateDirection(const BaseObject* from, const BaseObject* to) {
        if (!from || !to) return DR_UP;

        Point from_pos = from->GetCurrentPos();
        Point to_pos = to->GetCurrentPos();

        return g_functions::GetDirection(from_pos.x, from_pos.y, to_pos.x, to_pos.y);
    }

    Point CalculateFrontPosition(const BaseObject* obj) {
        if (!obj) return Point(0, 0);

        Point current = obj->GetCurrentPos();
        BYTE direction = obj->GetDirection();

        return g_functions::GetFrontPosition(current.x, current.y, direction);
    }

    Point CalculateBackPosition(const BaseObject* obj) {
        if (!obj) return Point(0, 0);

        Point current = obj->GetCurrentPos();
        BYTE direction = obj->GetDirection();

        return g_functions::GetBackPosition(current.x, current.y, direction);
    }

    bool AreAllies(const BaseObject* obj1, const BaseObject* obj2) {
        if (!obj1 || !obj2) return false;

        // Check if they are in same group
        if (obj1->GetGroupManager() && obj2->GetGroupManager()) {
            if (obj1->GetGroupManager()->IsInSameGroup(const_cast<BaseObject*>(obj2))) {
                return true;
            }
        }

        // Check if they are in same guild
        if (obj1->GetGuildManager() && obj2->GetGuildManager()) {
            if (obj1->GetGuildManager()->IsInSameGuild(const_cast<BaseObject*>(obj2))) {
                return true;
            }
        }

        return false;
    }

    bool AreEnemies(const BaseObject* obj1, const BaseObject* obj2) {
        if (!obj1 || !obj2) return false;

        // If they are allies, they cannot be enemies
        if (AreAllies(obj1, obj2)) return false;

        // Check PK status, guild wars, etc.
        // This would be implemented with actual faction/war system

        return false; // Default to not enemies
    }

    bool CanAttack(const BaseObject* attacker, const BaseObject* target) {
        if (!attacker || !target) return false;

        return const_cast<BaseObject*>(attacker)->IsAttackTarget(const_cast<BaseObject*>(target));
    }

    bool CanCast(const BaseObject* caster, const BaseObject* target) {
        if (!caster || !target) return false;

        return const_cast<BaseObject*>(caster)->IsProperTarget(const_cast<BaseObject*>(target));
    }
}
