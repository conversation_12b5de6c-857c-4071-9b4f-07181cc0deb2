#pragma once

#include "UIControl.h"
#include "../Graphics/WILLoader.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <string>

/**
 * @class UIManager
 * @brief Manages UI controls
 *
 * This class manages all UI controls in the game, handling their update, rendering,
 * and event processing.
 */
class UIManager {
private:
    std::vector<std::shared_ptr<UIControl>> m_controls;  ///< Root controls
    std::unordered_map<std::string, std::shared_ptr<UIControl>> m_namedControls;  ///< Controls by name

    SDL_Renderer* m_renderer;  ///< SDL renderer
    std::shared_ptr<WILManager> m_wilManager;  ///< WIL manager

    std::weak_ptr<UIControl> m_focusedControl;

public:
    /**
     * @brief Constructor
     * @param renderer SDL renderer
     */
    UIManager(SDL_Renderer* renderer);

    /**
     * @brief Destructor
     */
    ~UIManager();

    /**
     * @brief Update all controls
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    void Update(int deltaTime);

    /**
     * @brief Render all controls
     */
    void Render();

    /**
     * @brief Handle SDL events
     * @param event SDL event
     * @return true if handled, false otherwise
     */
    bool HandleEvent(const SDL_Event& event);

    /**
     * @brief Add a control
     * @param control Control to add
     */
    void AddControl(std::shared_ptr<UIControl> control);

    /**
     * @brief Remove a control
     * @param control Control to remove
     * @return true if successful, false otherwise
     */
    bool RemoveControl(UIControl* control);

    /**
     * @brief Get a control by name
     * @param name Control name
     * @return Control or nullptr if not found
     */
    std::shared_ptr<UIControl> GetControl(const std::string& name);

    /**
     * @brief Clear all controls
     */
    void ClearControls();

    /**
     * @brief Set the WIL manager
     * @param wilManager WIL manager
     */
    void SetWILManager(std::shared_ptr<WILManager> wilManager);

    /**
     * @brief Get the WIL manager
     * @return WIL manager
     */
    std::shared_ptr<WILManager> GetWILManager() const { return m_wilManager; }

    /**
     * @brief Get all controls
     * @return Vector of controls
     */
    const std::vector<std::shared_ptr<UIControl>>& GetControls() const { return m_controls; }

    void SetFocusedControl(std::shared_ptr<UIControl> control);
    void ClearFocusedControl();
    std::shared_ptr<UIControl> GetFocusedControl();

    /**
     * @brief Add a container (UIPanel/UIContainer)
     * @param container Container to add
     */
    void AddContainer(std::shared_ptr<UIContainer> container);
};
