#pragma once

// Mir200 ObjectMovement - Object movement and positioning module
// Based on movement-related functionality from original ObjBase.pas
// Handles walking, running, turning, teleporting, and position validation

#include "../Common/M2Share.h"

// Forward declaration
class BaseObject;
class Environment;

// Movement type enumeration
enum class MovementType : BYTE {
    WALK = 0,
    RUN = 1,
    TURN = 2,
    TELEPORT = 3,
    PUSH = 4,
    SPACE_MOVE = 5
};

// Movement result enumeration
enum class MovementResult : BYTE {
    SUCCESS = 0,
    BLOCKED = 1,
    INVALID_POSITION = 2,
    INVALID_DIRECTION = 3,
    TOO_FAST = 4,
    PARALYZED = 5,
    DEAD = 6,
    OVERWEIGHT = 7,
    COOLDOWN = 8
};

// Movement data structure
struct MovementData {
    Point from_pos;
    Point to_pos;
    BYTE direction;
    MovementType type;
    DWORD timestamp;
    int speed;
    
    MovementData() : from_pos(0, 0), to_pos(0, 0), direction(DR_UP), 
                     type(MovementType::WALK), timestamp(0), speed(0) {}
    
    MovementData(const Point& from, const Point& to, BYTE dir, MovementType t)
        : from_pos(from), to_pos(to), direction(dir), type(t), 
          timestamp(g_functions::GetCurrentTime()), speed(0) {}
};

// ObjectMovement class - Manages all object movement functionality
class ObjectMovement {
private:
    BaseObject* m_owner;
    
    // Movement state (matching original ObjBase.pas movement fields)
    int m_walk_speed;                       // 0x4FC - 行走速度
    int m_walk_step;                        // 0x500 - 行走步数
    int m_walk_count;                       // 0x504 - 行走计数
    DWORD m_walk_wait;                      // 0x508 - 行走等待
    DWORD m_walk_wait_tick;                 // 0x50C - 行走等待计时
    bool m_walk_wait_locked;                // 0x510 - 行走等待锁定
    
    // Movement timing (matching original timing fields)
    DWORD m_walk_tick;                      // 0x3FC - 行走计时
    DWORD m_run_tick;                       // 0x368 - 跑步计时
    int m_run_time;                         // 0x36C - 跑步时间
    DWORD m_teleport_tick;                  // 0x390 - 传送计时
    DWORD m_map_move_tick;                  // 0x398 - 地图移动计时
    
    // Position history for validation
    std::vector<MovementData> m_movement_history;
    static constexpr size_t MAX_MOVEMENT_HISTORY = 10;
    
    // Movement configuration
    int m_base_walk_speed;
    int m_base_run_speed;
    int m_walk_interval;
    int m_run_interval;
    int m_turn_interval;
    bool m_anti_speed_hack;
    
    // Current movement state
    bool m_is_moving;
    MovementType m_current_movement_type;
    Point m_target_position;
    DWORD m_movement_start_time;

public:
    explicit ObjectMovement(BaseObject* owner);
    ~ObjectMovement();

    // Core movement interface
    void Initialize();
    void Update();
    void Reset();

    // Primary movement methods (matching original ObjBase.pas methods)
    bool Walk(BYTE direction);
    bool Run(BYTE direction);
    bool Turn(BYTE direction);
    MovementResult WalkTo(BYTE direction, bool flag);
    void SpaceMove(const MapName& map, int x, int y, int mode);
    bool EnterAnotherMap(Environment* env, int dest_x, int dest_y);
    
    // Position and direction methods
    Point GetCurrentPosition() const;
    Point GetFrontPosition() const;
    Point GetBackPosition() const;
    bool GetFrontPosition(int& x, int& y) const;
    bool GetBackPosition(int& x, int& y) const;
    BYTE GetDirection() const;
    void SetDirection(BYTE direction);
    
    // Movement validation
    bool CanMove() const;
    bool CanWalk(BYTE direction) const;
    bool CanRun(BYTE direction) const;
    bool CanTurn(BYTE direction) const;
    bool IsValidPosition(int x, int y) const;
    bool IsValidDirection(BYTE direction) const;
    
    // Position utilities
    bool IsBlocked(int x, int y) const;
    bool IsWalkable(int x, int y) const;
    bool IsFlyable(int x, int y) const;
    bool IsInMap(int x, int y) const;
    bool IsInSafeZone(int x, int y) const;
    
    // Speed and timing
    int GetWalkSpeed() const { return m_walk_speed; }
    int GetRunSpeed() const { return m_base_run_speed; }
    int GetWalkInterval() const { return m_walk_interval; }
    int GetRunInterval() const { return m_run_interval; }
    void SetWalkSpeed(int speed) { m_walk_speed = speed; }
    void SetRunSpeed(int speed) { m_base_run_speed = speed; }
    
    // Movement state accessors
    bool IsMoving() const { return m_is_moving; }
    MovementType GetCurrentMovementType() const { return m_current_movement_type; }
    const Point& GetTargetPosition() const { return m_target_position; }
    DWORD GetMovementStartTime() const { return m_movement_start_time; }
    
    // Walk state accessors (matching original fields)
    int GetWalkStep() const { return m_walk_step; }
    int GetWalkCount() const { return m_walk_count; }
    DWORD GetWalkWait() const { return m_walk_wait; }
    DWORD GetWalkWaitTick() const { return m_walk_wait_tick; }
    bool IsWalkWaitLocked() const { return m_walk_wait_locked; }
    
    // Walk state setters
    void SetWalkStep(int step) { m_walk_step = step; }
    void SetWalkCount(int count) { m_walk_count = count; }
    void SetWalkWait(DWORD wait) { m_walk_wait = wait; }
    void SetWalkWaitTick(DWORD tick) { m_walk_wait_tick = tick; }
    void SetWalkWaitLocked(bool locked) { m_walk_wait_locked = locked; }
    
    // Timing accessors
    DWORD GetWalkTick() const { return m_walk_tick; }
    DWORD GetRunTick() const { return m_run_tick; }
    int GetRunTime() const { return m_run_time; }
    DWORD GetTeleportTick() const { return m_teleport_tick; }
    DWORD GetMapMoveTick() const { return m_map_move_tick; }
    
    // Timing setters
    void SetWalkTick(DWORD tick) { m_walk_tick = tick; }
    void SetRunTick(DWORD tick) { m_run_tick = tick; }
    void SetRunTime(int time) { m_run_time = time; }
    void SetTeleportTick(DWORD tick) { m_teleport_tick = tick; }
    void SetMapMoveTick(DWORD tick) { m_map_move_tick = tick; }
    
    // Advanced movement methods
    int CharPushed(BYTE direction, int push_count);
    bool GetDropPosition(int org_x, int org_y, int range, int& dest_x, int& dest_y);
    void MapRandomMove(const MapName& map, int mode);
    
    // Direction utilities
    BYTE GetBackDir(BYTE direction) const;
    BYTE GetAttackDir(BaseObject* target) const;
    bool GetAttackDir(BaseObject* target, BYTE& direction) const;
    bool TargetInSpitRange(BaseObject* target, BYTE& direction) const;
    
    // Distance and range calculations
    int GetDistance(BaseObject* target) const;
    int GetDistance(int x, int y) const;
    bool InRange(BaseObject* target, int range) const;
    bool InRange(int x, int y, int range) const;
    
    // Movement history and anti-cheat
    void AddMovementToHistory(const MovementData& movement);
    bool ValidateMovementSpeed(const MovementData& movement) const;
    bool DetectSpeedHack() const;
    void ClearMovementHistory();
    const std::vector<MovementData>& GetMovementHistory() const { return m_movement_history; }
    
    // Configuration
    void SetAntiSpeedHack(bool enabled) { m_anti_speed_hack = enabled; }
    bool IsAntiSpeedHackEnabled() const { return m_anti_speed_hack; }
    void SetWalkInterval(int interval) { m_walk_interval = interval; }
    void SetRunInterval(int interval) { m_run_interval = interval; }
    void SetTurnInterval(int interval) { m_turn_interval = interval; }
    
    // Movement callbacks and notifications
    void OnMovementStarted(MovementType type, const Point& target);
    void OnMovementCompleted(MovementType type, const Point& final_pos);
    void OnMovementFailed(MovementType type, MovementResult result);
    void OnDirectionChanged(BYTE old_dir, BYTE new_dir);
    void OnPositionChanged(const Point& old_pos, const Point& new_pos);
    
    // Map and environment integration
    void OnMapChanged(const MapName& old_map, const MapName& new_map);
    void OnEnvironmentChanged(Environment* old_env, Environment* new_env);
    
    // Synchronization and network
    void SendMovementToClient(MovementType type, const Point& pos, BYTE direction);
    void SendMovementToNearby(MovementType type, const Point& pos, BYTE direction);
    void SynchronizePosition();

private:
    // Internal movement processing
    bool ProcessWalk(BYTE direction);
    bool ProcessRun(BYTE direction);
    bool ProcessTurn(BYTE direction);
    bool ProcessTeleport(const Point& target);
    bool ProcessSpaceMove(const MapName& map, const Point& target, int mode);
    
    // Internal validation
    bool ValidateMovementTiming(MovementType type) const;
    bool ValidateMovementDistance(const Point& from, const Point& to, MovementType type) const;
    bool ValidateMovementPath(const Point& from, const Point& to) const;
    
    // Internal utilities
    void UpdateMovementState();
    void UpdateMovementTiming();
    void ProcessMovementQueue();
    void CleanupMovementHistory();
    
    // Position calculation helpers
    Point CalculateNextPosition(BYTE direction, MovementType type) const;
    Point CalculatePositionInDirection(const Point& from, BYTE direction, int distance = 1) const;
    BYTE CalculateDirectionToTarget(const Point& target) const;
    
    // Environment interaction
    bool CheckEnvironmentCollision(const Point& pos) const;
    bool CheckObjectCollision(const Point& pos) const;
    bool CheckMapBoundaries(const Point& pos) const;
    
    // Movement effect processing
    void ApplyMovementEffects(MovementType type);
    void ProcessMovementSounds(MovementType type);
    void ProcessMovementVisuals(MovementType type);
    
    // Anti-cheat and validation
    bool IsMovementTooFast(const MovementData& movement) const;
    bool IsMovementPatternSuspicious() const;
    void LogSuspiciousMovement(const MovementData& movement) const;
};
