// SendRefMsg使用示例.cpp - 展示SendRefMsg在游戏中的实际应用
#include "BaseObject.h"
#include "../Common/Logger.h"
#include "../Protocol/PacketTypes.h"
#include <iostream>

using namespace MirServer;

// 示例：玩家攻击时的SendRefMsg使用
class ExamplePlayer : public BaseObject {
public:
    ExamplePlayer(const std::string& name) {
        m_sCharName = name;
        m_charName = name;
        m_btRaceServer = RC_PLAYOBJECT;
        m_WAbil.HP = 1000;
        m_WAbil.MaxHP = 1000;
        m_WAbil.DC.min = 50;
        m_WAbil.DC.max = 80;
    }
    
    ObjectType GetObjectType() const override {
        return ObjectType::Player;
    }
    
    void SendMsg(BaseObject* obj, WORD msgId, int param1, int param2, int param3, int param4, const std::string& msg) override {
        std::cout << "[" << m_sCharName << "] 收到消息 " << msgId << " 来自 " 
                  << (obj ? obj->GetCharName() : "unknown") << std::endl;
    }
    
    // 示例1：玩家攻击时发送攻击消息
    void AttackTarget(BaseObject* target) {
        if (!target) return;
        
        std::cout << m_sCharName << " 攻击 " << target->GetCharName() << std::endl;
        
        // 计算伤害
        int damage = m_WAbil.DC.min + (rand() % (m_WAbil.DC.max - m_WAbil.DC.min + 1));
        
        // 发送攻击动作给周围玩家
        SendRefMsg(RM_HIT, m_btDirection, m_currentPos.x, m_currentPos.y, 0, "");
        
        // 如果命中目标
        if (target->TakeHit(damage)) {
            // 发送攻击成功消息给周围玩家
            SendRefMsg(RM_STRUCK, 0, damage, target->GetHP(), target->GetMaxHP(), "");
            
            std::cout << "造成 " << damage << " 点伤害" << std::endl;
        }
    }
    
    // 示例2：玩家说话时发送聊天消息
    void Say(const std::string& message) {
        std::cout << m_sCharName << " 说: " << message << std::endl;
        
        // 发送聊天消息给周围玩家
        SendRefMsg(RM_HEAR, 0, 0, 0, 0, message);
    }
    
    // 示例3：玩家死亡时发送死亡消息
    void Die() override {
        BaseObject::Die(); // 调用基类方法
        
        std::cout << m_sCharName << " 死亡了" << std::endl;
        
        // 发送死亡消息给周围玩家
        SendRefMsg(RM_DEATH, 0, m_currentPos.x, m_currentPos.y, 0, "");
    }
    
    // 示例4：玩家状态改变时发送状态消息
    void ChangeStatus(int newStatus) {
        int oldStatus = m_nCharStatus;
        m_nCharStatus = newStatus;
        
        std::cout << m_sCharName << " 状态从 " << oldStatus << " 变为 " << newStatus << std::endl;
        
        // 发送状态变化消息给周围玩家
        SendRefMsg(RM_CHARSTATUSCHANGED, 0, newStatus, oldStatus, 0, "");
    }
    
    // 示例5：玩家使用技能时发送技能消息
    void CastSpell(int spellId, BaseObject* target) {
        std::cout << m_sCharName << " 使用技能 " << spellId;
        if (target) {
            std::cout << " 目标: " << target->GetCharName();
        }
        std::cout << std::endl;
        
        // 发送技能施放消息给周围玩家
        int targetX = target ? target->GetCurrentPos().x : m_currentPos.x;
        int targetY = target ? target->GetCurrentPos().y : m_currentPos.y;
        
        SendRefMsg(RM_SPELL, spellId, targetX, targetY, 
                  target ? reinterpret_cast<int>(target) : 0, "");
    }
    
    // 辅助方法
    bool TakeHit(int damage) {
        m_WAbil.HP = std::max(0, m_WAbil.HP - damage);
        if (m_WAbil.HP <= 0) {
            Die();
            return true;
        }
        return true;
    }
    
    int GetHP() const { return m_WAbil.HP; }
    int GetMaxHP() const { return m_WAbil.MaxHP; }
};

// 示例：怪物类的SendRefMsg使用
class ExampleMonster : public BaseObject {
public:
    ExampleMonster(const std::string& name) {
        m_sCharName = name;
        m_charName = name;
        m_btRaceServer = RC_MONSTER;
        m_WAbil.HP = 500;
        m_WAbil.MaxHP = 500;
    }
    
    ObjectType GetObjectType() const override {
        return ObjectType::Monster;
    }
    
    void SendMsg(BaseObject* obj, WORD msgId, int param1, int param2, int param3, int param4, const std::string& msg) override {
        std::cout << "[怪物 " << m_sCharName << "] 收到消息 " << msgId << " 来自 " 
                  << (obj ? obj->GetCharName() : "unknown") << std::endl;
    }
    
    // 怪物攻击玩家
    void AttackPlayer(BaseObject* player) {
        if (!player) return;
        
        std::cout << "怪物 " << m_sCharName << " 攻击 " << player->GetCharName() << std::endl;
        
        // 发送怪物攻击动作
        SendRefMsg(RM_HIT, m_btDirection, m_currentPos.x, m_currentPos.y, 0, "");
        
        int damage = 30 + (rand() % 20);
        
        // 发送攻击结果
        SendRefMsg(RM_STRUCK, 0, damage, player->GetHP(), player->GetMaxHP(), "");
    }
    
    // 怪物死亡
    void Die() override {
        BaseObject::Die();
        
        std::cout << "怪物 " << m_sCharName << " 被击败了" << std::endl;
        
        // 发送怪物死亡消息
        SendRefMsg(RM_DEATH, 0, m_currentPos.x, m_currentPos.y, 0, "");
    }
};

// 示例：NPC类的SendRefMsg使用
class ExampleNPC : public BaseObject {
public:
    ExampleNPC(const std::string& name) {
        m_sCharName = name;
        m_charName = name;
        m_btRaceServer = RC_MONSTER; // NPC通常使用怪物种族
    }
    
    ObjectType GetObjectType() const override {
        return ObjectType::NPC;
    }
    
    bool WantRefMsg() const override {
        return true; // NPC想要接收某些消息
    }
    
    void SendMsg(BaseObject* obj, WORD msgId, int param1, int param2, int param3, int param4, const std::string& msg) override {
        // NPC只处理特定消息
        if (msgId == RM_HEAR) {
            std::cout << "[NPC " << m_sCharName << "] 听到: " << msg << std::endl;
        }
    }
    
    // NPC说话
    void SayToNearby(const std::string& message) {
        std::cout << "NPC " << m_sCharName << " 说: " << message << std::endl;
        
        // NPC说话也会发送给周围的玩家和其他NPC
        SendRefMsg(RM_HEAR, 0, 0, 0, 0, message);
    }
};

// 演示函数
void DemonstrateUsage() {
    std::cout << "=== SendRefMsg使用示例演示 ===" << std::endl;
    
    // 创建测试对象
    auto player1 = std::make_shared<ExamplePlayer>("勇士");
    auto player2 = std::make_shared<ExamplePlayer>("法师");
    auto monster = std::make_shared<ExampleMonster>("骷髅");
    auto npc = std::make_shared<ExampleNPC>("铁匠");
    
    // 设置位置
    player1->SetPosition(100, 100);
    player2->SetPosition(105, 105);
    monster->SetPosition(102, 102);
    npc->SetPosition(103, 103);
    
    std::cout << "\n--- 场景1: 玩家攻击怪物 ---" << std::endl;
    player1->AttackTarget(monster.get());
    
    std::cout << "\n--- 场景2: 玩家聊天 ---" << std::endl;
    player1->Say("大家好！");
    player2->Say("你好！");
    
    std::cout << "\n--- 场景3: 怪物反击 ---" << std::endl;
    monster->AttackPlayer(player1.get());
    
    std::cout << "\n--- 场景4: 玩家使用技能 ---" << std::endl;
    player2->CastSpell(1001, monster.get()); // 火球术
    
    std::cout << "\n--- 场景5: NPC说话 ---" << std::endl;
    npc->SayToNearby("欢迎来到铁匠铺！");
    
    std::cout << "\n--- 场景6: 玩家状态变化 ---" << std::endl;
    player1->ChangeStatus(1); // 中毒状态
    
    std::cout << "\n--- 场景7: 怪物死亡 ---" << std::endl;
    monster->Die();
    
    std::cout << "\n=== 演示完成 ===" << std::endl;
}

int main() {
    std::cout << "SendRefMsg使用示例程序" << std::endl;
    
    try {
        DemonstrateUsage();
        
        std::cout << "\n总结：SendRefMsg的主要用途" << std::endl;
        std::cout << "1. 战斗系统：攻击动作、伤害结果、死亡消息" << std::endl;
        std::cout << "2. 聊天系统：玩家说话、NPC对话" << std::endl;
        std::cout << "3. 状态系统：角色状态变化通知" << std::endl;
        std::cout << "4. 技能系统：技能施放效果" << std::endl;
        std::cout << "5. 游戏事件：各种游戏内事件的广播" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "程序运行异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
