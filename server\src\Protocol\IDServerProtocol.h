#ifndef IDSERVER_PROTOCOL_H
#define IDSERVER_PROTOCOL_H

#include "../Common/Types.h"

namespace MirServer {
namespace Protocol {

// IDServer协议命令
enum IDServerCommand {
    ID_REGISTER_DBSERVER = 1000,   // DBServer注册到IDServer
    ID_CHECK_SESSION = 1001,        // 检查会话有效性
    ID_SESSION_CHECKED = 1002,      // 会话检查结果
    ID_NOTIFY_SAVE = 1003,          // 通知角色保存
    ID_KEEPALIVE = 1004,            // 心跳包
};

// DBServer注册请求
struct DBServerRegisterMsg {
    char sServerName[32];           // 服务器名称
    char sServerAddr[16];           // 服务器地址
    int32_t nServerPort;            // 服务器端口
    int32_t nMaxConnections;        // 最大连接数
};

// 会话检查请求
struct SessionCheckRequest {
    char sAccount[13];              // 账号
    char sIPAddr[16];               // IP地址
    int32_t nSessionID;             // 会话ID
    int32_t nRequestID;             // 请求ID（用于匹配响应）
};

// 会话检查响应
struct SessionCheckResponse {
    int32_t nRequestID;             // 请求ID
    bool boValid;                   // 是否有效
    int32_t nErrorCode;             // 错误码（0=成功，-1=无效会话，-2=IP不匹配）
};

// 保存通知
struct SaveNotifyMsg {
    char sAccount[13];              // 账号
    char sChrName[15];              // 角色名
    int64_t nSaveTime;              // 保存时间
};

// 心跳包
struct KeepAliveMsg {
    int64_t nTimestamp;             // 时间戳
    int32_t nLoadCount;             // 加载次数统计
    int32_t nSaveCount;             // 保存次数统计
    int32_t nQueryCount;            // 查询次数统计
};

} // namespace Protocol
} // namespace MirServer

#endif // IDSERVER_PROTOCOL_H 