#include "Inventory.h"
#include <algorithm>

Inventory::Inventory()
    : m_gold(0)
    , m_weight(0)
    , m_maxWeight(100)
{
    // Reserve space for the maximum number of items
    m_items.resize(MAX_INVENTORY_SIZE, nullptr);
}

Inventory::~Inventory()
{
}

bool Inventory::AddItem(std::shared_ptr<Item> item, int slot)
{
    if (!item) {
        return false;
    }

    // If slot is -1, find the first empty slot
    if (slot == -1) {
        slot = FindEmptySlot();
        if (slot == -1) {
            return false; // Inventory is full
        }
    }

    // Check if the slot is valid
    if (slot < 0 || slot >= MAX_INVENTORY_SIZE) {
        return false;
    }

    // Add the item to the inventory
    m_items[slot] = item;

    // Update the total weight
    m_weight = CalculateWeight();

    return true;
}

std::shared_ptr<Item> Inventory::RemoveItem(int slot)
{
    // Check if the slot is valid
    if (slot < 0 || slot >= MAX_INVENTORY_SIZE) {
        return nullptr;
    }

    // Get the item at the specified slot
    std::shared_ptr<Item> item = m_items[slot];

    // Remove the item from the inventory
    m_items[slot] = nullptr;

    // Update the total weight
    m_weight = CalculateWeight();

    return item;
}

std::shared_ptr<Item> Inventory::GetItem(int slot) const
{
    // Check if the slot is valid
    if (slot < 0 || slot >= MAX_INVENTORY_SIZE) {
        return nullptr;
    }

    return m_items[slot];
}

bool Inventory::UpdateItem(int slot, std::shared_ptr<Item> item)
{
    // Check if the slot is valid
    if (slot < 0 || slot >= MAX_INVENTORY_SIZE) {
        return false;
    }

    // Update the item at the specified slot
    m_items[slot] = item;

    // Update the total weight
    m_weight = CalculateWeight();

    return true;
}

void Inventory::Clear()
{
    // Clear the inventory
    for (auto& item : m_items) {
        item = nullptr;
    }

    // Clear the equipment
    m_equipment.clear();

    // Reset the weight
    m_weight = 0;
}

bool Inventory::EquipItem(int slot, EquipmentSlot equipSlot)
{
    // Check if the slot is valid
    if (slot < 0 || slot >= MAX_INVENTORY_SIZE) {
        return false;
    }

    // Get the item at the specified slot
    std::shared_ptr<Item> item = m_items[slot];
    if (!item) {
        return false;
    }

    // Check if the item can be equipped in the specified slot
    if (!CanEquip(*item, equipSlot)) {
        return false;
    }

    // Unequip any item currently in the equipment slot
    UnequipItem(equipSlot);

    // Equip the item
    m_equipment[static_cast<int>(equipSlot)] = item;

    // Remove the item from the inventory
    m_items[slot] = nullptr;

    return true;
}

bool Inventory::EquipItem(std::shared_ptr<Item> item, EquipmentSlot equipSlot)
{
    if (!item) {
        return false;
    }

    // Check if the item can be equipped in the specified slot
    if (!CanEquip(*item, equipSlot)) {
        return false;
    }

    // Unequip any item currently in the equipment slot
    UnequipItem(equipSlot);

    // Equip the item
    m_equipment[static_cast<int>(equipSlot)] = item;

    return true;
}

bool Inventory::UnequipItem(EquipmentSlot equipSlot)
{
    // Check if there's an item equipped in the specified slot
    auto it = m_equipment.find(static_cast<int>(equipSlot));
    if (it == m_equipment.end()) {
        return false;
    }

    // Get the equipped item
    std::shared_ptr<Item> item = it->second;

    // Find an empty slot in the inventory
    int slot = FindEmptySlot();
    if (slot == -1) {
        return false; // Inventory is full
    }

    // Add the item to the inventory
    m_items[slot] = item;

    // Remove the item from the equipment
    m_equipment.erase(it);

    return true;
}

std::shared_ptr<Item> Inventory::GetEquippedItem(EquipmentSlot equipSlot) const
{
    // Check if there's an item equipped in the specified slot
    auto it = m_equipment.find(static_cast<int>(equipSlot));
    if (it == m_equipment.end()) {
        return nullptr;
    }

    return it->second;
}

std::vector<std::shared_ptr<Item>> Inventory::FindItems(std::function<bool(const Item&)> predicate) const
{
    std::vector<std::shared_ptr<Item>> result;

    // Search the inventory
    for (const auto& item : m_items) {
        if (item && predicate(*item)) {
            result.push_back(item);
        }
    }

    // Search the equipment
    for (const auto& pair : m_equipment) {
        if (pair.second && predicate(*pair.second)) {
            result.push_back(pair.second);
        }
    }

    return result;
}

int Inventory::FindEmptySlot() const
{
    // Find the first empty slot
    for (int i = 0; i < MAX_INVENTORY_SIZE; i++) {
        if (!m_items[i]) {
            return i;
        }
    }

    return -1; // Inventory is full
}

bool Inventory::IsFull() const
{
    return FindEmptySlot() == -1;
}

int Inventory::CalculateWeight() const
{
    int weight = 0;

    // Calculate the weight of items in the inventory
    for (const auto& item : m_items) {
        if (item) {
            weight += item->GetWeight();
        }
    }

    // Calculate the weight of equipped items
    for (const auto& pair : m_equipment) {
        if (pair.second) {
            weight += pair.second->GetWeight();
        }
    }

    return weight;
}

bool Inventory::CanEquip(const Item& item, EquipmentSlot equipSlot) const
{
    // Check if the item type matches the equipment slot
    ItemType requiredType = EquipmentSlotToItemType(equipSlot);
    return item.GetType() == requiredType;
}

ItemType Inventory::EquipmentSlotToItemType(EquipmentSlot equipSlot) const
{
    switch (equipSlot) {
        case EquipmentSlot::WEAPON:
            return ItemType::WEAPON;
        case EquipmentSlot::ARMOR:
            return ItemType::ARMOR;
        case EquipmentSlot::HELMET:
            return ItemType::HELMET;
        case EquipmentSlot::NECKLACE:
            return ItemType::NECKLACE;
        case EquipmentSlot::LEFT_HAND:
            return ItemType::WEAPON; // Shield or dual wield
        case EquipmentSlot::RIGHT_HAND:
            return ItemType::RING;
        case EquipmentSlot::LEFT_BRACELET:
        case EquipmentSlot::RIGHT_BRACELET:
            return ItemType::BRACELET;
        case EquipmentSlot::SHOES:
            return ItemType::SHOES;
        case EquipmentSlot::BELT:
            return ItemType::BELT;
        case EquipmentSlot::STONE:
            return ItemType::STONE;
        case EquipmentSlot::TORCH:
            return ItemType::TORCH;
        case EquipmentSlot::BOOK:
            return ItemType::BOOK;
        default:
            return ItemType::NONE;
    }
}

Inventory::EquipmentSlot Inventory::ItemTypeToEquipmentSlot(ItemType type) const
{
    switch (type) {
        case ItemType::WEAPON:
            return EquipmentSlot::WEAPON;
        case ItemType::ARMOR:
            return EquipmentSlot::ARMOR;
        case ItemType::HELMET:
            return EquipmentSlot::HELMET;
        case ItemType::NECKLACE:
            return EquipmentSlot::NECKLACE;
        case ItemType::RING:
            return EquipmentSlot::RIGHT_HAND;
        case ItemType::BRACELET:
            return EquipmentSlot::LEFT_BRACELET;
        case ItemType::SHOES:
            return EquipmentSlot::SHOES;
        case ItemType::BELT:
            return EquipmentSlot::BELT;
        case ItemType::STONE:
            return EquipmentSlot::STONE;
        case ItemType::TORCH:
            return EquipmentSlot::TORCH;
        case ItemType::BOOK:
            return EquipmentSlot::BOOK;
        default:
            return EquipmentSlot::WEAPON;
    }
}
