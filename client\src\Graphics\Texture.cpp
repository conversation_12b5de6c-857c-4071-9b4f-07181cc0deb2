#include "Texture.h"
#include <SDL2/SDL_image.h>
#include <SDL2/SDL_ttf.h>
#include <iostream>

Texture::Texture()
    : m_texture(nullptr)
    , m_renderer(nullptr)
    , m_width(0)
    , m_height(0)
    , m_isColorKeySet(false)
{
    m_colorKey = {0, 0, 0, 0};
}

Texture::Texture(SDL_Renderer* renderer)
    : m_texture(nullptr)
    , m_renderer(renderer)
    , m_width(0)
    , m_height(0)
    , m_isColorKeySet(false)
{
    m_colorKey = {0, 0, 0, 0};
}

Texture::~Texture()
{
    Free();
}

bool Texture::LoadFromFile(const std::string& filename)
{
    // Free any existing texture
    Free();

    // Load image from file
    SDL_Surface* surface = IMG_Load(filename.c_str());
    if (!surface) {
        std::cerr << "Failed to load image: " << filename << " - " << IMG_GetError() << std::endl;
        return false;
    }

    // Apply color key if set
    if (m_isColorKeySet) {
        SDL_SetColorKey(surface, SDL_TRUE, SDL_MapRGB(surface->format, m_colorKey.r, m_colorKey.g, m_colorKey.b));
    }

    // Create texture from surface
    m_texture = SDL_CreateTextureFromSurface(m_renderer, surface);
    if (!m_texture) {
        std::cerr << "Failed to create texture: " << SDL_GetError() << std::endl;
        SDL_FreeSurface(surface);
        return false;
    }

    // Get image dimensions
    m_width = surface->w;
    m_height = surface->h;

    // Free the surface
    SDL_FreeSurface(surface);

    return true;
}

bool Texture::LoadFromSurface(SDL_Surface* surface)
{
    // Check if surface is valid
    if (!surface) {
        std::cerr << "Invalid surface" << std::endl;
        return false;
    }

    // Free any existing texture
    Free();

    // Apply color key if set
    if (m_isColorKeySet) {
        SDL_SetColorKey(surface, SDL_TRUE, SDL_MapRGB(surface->format, m_colorKey.r, m_colorKey.g, m_colorKey.b));
    }

    // Create texture from surface
    m_texture = SDL_CreateTextureFromSurface(m_renderer, surface);
    if (!m_texture) {
        std::cerr << "Failed to create texture: " << SDL_GetError() << std::endl;
        return false;
    }

    // Get image dimensions
    m_width = surface->w;
    m_height = surface->h;

    return true;
}

bool Texture::CreateBlank(int width, int height, int access)
{
    // Free any existing texture
    Free();

    // Create blank texture
    m_texture = SDL_CreateTexture(m_renderer, SDL_PIXELFORMAT_RGBA8888, access, width, height);
    if (!m_texture) {
        std::cerr << "Failed to create blank texture: " << SDL_GetError() << std::endl;
        return false;
    }

    // Set dimensions
    m_width = width;
    m_height = height;

    return true;
}

bool Texture::CreateFromData(const uint8_t* data, int width, int height, int depth)
{
    // Free any existing texture
    Free();

    // Create a surface from the data
    SDL_Surface* surface = nullptr;

    if (depth == 8) {
        // 8-bit grayscale
        surface = SDL_CreateRGBSurfaceWithFormatFrom(
            const_cast<uint8_t*>(data),
            width,
            height,
            8,
            width,
            SDL_PIXELFORMAT_INDEX8
        );
    } else if (depth == 24) {
        // 24-bit RGB
        surface = SDL_CreateRGBSurfaceWithFormatFrom(
            const_cast<uint8_t*>(data),
            width,
            height,
            24,
            width * 3,
            SDL_PIXELFORMAT_RGB24
        );
    } else if (depth == 32) {
        // 32-bit RGBA
        surface = SDL_CreateRGBSurfaceWithFormatFrom(
            const_cast<uint8_t*>(data),
            width,
            height,
            32,
            width * 4,
            SDL_PIXELFORMAT_RGBA32
        );
    } else {
        std::cerr << "Unsupported bit depth: " << depth << std::endl;
        return false;
    }

    if (!surface) {
        std::cerr << "Failed to create surface from data: " << SDL_GetError() << std::endl;
        return false;
    }

    // Create texture from surface
    m_texture = SDL_CreateTextureFromSurface(m_renderer, surface);
    if (!m_texture) {
        std::cerr << "Failed to create texture: " << SDL_GetError() << std::endl;
        SDL_FreeSurface(surface);
        return false;
    }

    // Get image dimensions
    m_width = width;
    m_height = height;

    // Free the surface
    SDL_FreeSurface(surface);

    return true;
}

bool Texture::LoadFromText(const std::string& text, TTF_Font* font, SDL_Color color)
{
    // Check if font is valid
    if (!font) {
        std::cerr << "Invalid font" << std::endl;
        return false;
    }

    // Free any existing texture
    Free();

    // Render text to surface
    SDL_Surface* surface = TTF_RenderText_Blended(font, text.c_str(), color);
    if (!surface) {
        std::cerr << "Failed to render text: " << TTF_GetError() << std::endl;
        return false;
    }

    // Create texture from surface
    m_texture = SDL_CreateTextureFromSurface(m_renderer, surface);
    if (!m_texture) {
        std::cerr << "Failed to create texture: " << SDL_GetError() << std::endl;
        SDL_FreeSurface(surface);
        return false;
    }

    // Get image dimensions
    m_width = surface->w;
    m_height = surface->h;

    // Free the surface
    SDL_FreeSurface(surface);

    return true;
}

void Texture::SetColorKey(Uint8 r, Uint8 g, Uint8 b)
{
    m_colorKey.r = r;
    m_colorKey.g = g;
    m_colorKey.b = b;
    m_isColorKeySet = true;
}

void Texture::SetBlendMode(SDL_BlendMode blendMode)
{
    if (m_texture) {
        SDL_SetTextureBlendMode(m_texture, blendMode);
    }
}

void Texture::SetAlpha(Uint8 alpha)
{
    if (m_texture) {
        SDL_SetTextureAlphaMod(m_texture, alpha);
    }
}

void Texture::SetColor(Uint8 r, Uint8 g, Uint8 b)
{
    if (m_texture) {
        SDL_SetTextureColorMod(m_texture, r, g, b);
    }
}

void Texture::Render(int x, int y, SDL_Rect* clip)
{
    if (!m_texture) {
        return;
    }

    // Set rendering area
    SDL_Rect renderQuad = {x, y, m_width, m_height};

    // Set clip dimensions if clip is provided
    if (clip) {
        renderQuad.w = clip->w;
        renderQuad.h = clip->h;
    }

    // Render to screen
    SDL_RenderCopy(m_renderer, m_texture, clip, &renderQuad);
}

void Texture::RenderEx(int x, int y, SDL_Rect* clip, double angle, SDL_Point* center, SDL_RendererFlip flip)
{
    if (!m_texture) {
        return;
    }

    // Set rendering area
    SDL_Rect renderQuad = {x, y, m_width, m_height};

    // Set clip dimensions if clip is provided
    if (clip) {
        renderQuad.w = clip->w;
        renderQuad.h = clip->h;
    }

    // Render to screen with rotation and flipping
    SDL_RenderCopyEx(m_renderer, m_texture, clip, &renderQuad, angle, center, flip);
}

void Texture::Free()
{
    // Free texture if it exists
    if (m_texture) {
        SDL_DestroyTexture(m_texture);
        m_texture = nullptr;
        m_width = 0;
        m_height = 0;
    }
}

