# SelGateServer 配置文件
# 基于原Delphi ESelGate重构的选择网关服务器

[SelGate]
# 服务器名称
ServerName=选择网关
# 监听IP地址 (0.0.0.0 表示监听所有网卡)
ListenIP=0.0.0.0
# 监听端口
ListenPort=7100
# 每个IP最大连接数
MaxConnOfIP=10
# 会话超时时间(毫秒)
SessionTimeOut=300000
# 心跳检查间隔(秒)
HeartbeatInterval=30
# 服务器检查超时(秒)
CheckTimeOut=60
# IP阻挡方法 (0=断开连接, 1=临时阻挡, 2=永久阻挡)
BlockMethod=1
# 服务器选择策略 (0=轮询, 1=最少连接, 2=负载均衡, 3=随机)
SelectStrategy=1
# 启用负载均衡
EnableLoadBalance=1
# 启用IP过滤
EnableIPFilter=1

# 注意：
# 1. RunGate服务器列表在 RunGateList.txt 文件中配置
# 2. IP阻挡列表在 BlockIPList.txt 文件中配置
# 3. 详细日志记录在 SelGateServer.log 文件中 