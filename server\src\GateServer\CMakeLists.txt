# GateServer CMakeLists.txt

# 设置源文件
set(GATESERVER_SOURCES
    main.cpp
    GateServer.cpp
    GateServer.h
)

# 创建可执行文件
add_executable(GateServer ${GATESERVER_SOURCES})

# 设置包含目录
target_include_directories(GateServer PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/src/Common
    ${CMAKE_SOURCE_DIR}/src/Protocol
)

# 链接依赖库
target_link_libraries(GateServer
    Common
    Protocol
)

# Windows特定的网络库
if(WIN32)
    target_link_libraries(GateServer ws2_32 wsock32)
endif()

# 设置编译器特定选项
if(MSVC)
    target_compile_options(GateServer PRIVATE /W4)
else()
    target_compile_options(GateServer PRIVATE -Wall -Wextra -Wpedantic)
endif()

# 设置C++标准
set_property(TARGET GateServer PROPERTY CXX_STANDARD 17)
set_property(TARGET GateServer PROPERTY CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set_target_properties(GateServer PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    OUTPUT_NAME "GateServer"
)

# 安装配置
install(TARGETS GateServer
    RUNTIME DESTINATION bin
)

# 复制配置文件
install(FILES
    ${CMAKE_SOURCE_DIR}/config/GateServer.ini
    DESTINATION config
)

# 复制过滤文件
install(FILES
    ${CMAKE_SOURCE_DIR}/WordFilter.txt
    ${CMAKE_SOURCE_DIR}/BlockIPList.txt
    DESTINATION .
) 