# LoginServer CMakeLists.txt

# Add LoginServer executable
add_executable(LoginServer
    main.cpp
    LoginServer.cpp
    LoginServerHandlers.cpp
    AccountDB.cpp
    MsgServerManager.cpp
    MonitorServer.cpp
)

# Include directories
target_include_directories(LoginServer PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/src
)

# Link libraries
target_link_libraries(LoginServer PRIVATE
    Common
    Protocol
    ${CMAKE_THREAD_LIBS_INIT}
)

# Platform-specific settings
if(WIN32)
    target_link_libraries(LoginServer PRIVATE ws2_32)
endif()

# Set C++ standard
set_target_properties(LoginServer PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# Install target
install(TARGETS LoginServer
    RUNTIME DESTINATION bin
) 