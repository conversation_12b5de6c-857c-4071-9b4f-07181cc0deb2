{ $HDR$}
{**********************************************************************}
{ Unit archived using Team Coherence                                   }
{ Team Coherence is Copyright 2002 by Quality Software Components      }
{                                                                      }
{ For further information / comments, visit our WEB site at            }
{ http://www.TeamCoherence.com                                         }
{**********************************************************************}
{}
{ $Log:  10066: Kpzcnst.pas 
{
{   Rev 1.1    11/29/2003 11:55:08 PM  Supervisor    Version: VCLZip 3.X
{ IDS_BAD_UNCOMPRESSED_SIZE
}
{
{   Rev 1.0    10/15/2002 8:15:20 PM  Supervisor
}
{
{   Rev 1.0    9/3/2002 8:16:48 PM  Supervisor
}
unit kpZcnst;

interface

{ Constant definitions for VCLZip StringTable }

const
	IDS_OPENZIP =				176;
	IDS_ZIPNAMEFILTER =		177;
	IDS_CANCELDESTDIR =		178;
	IDS_INDEXOUTOFRANGE =	179;
	IDS_CANCELLOADDISK =		180;
	IDS_CANCELOPERATION =	181;
	IDS_INCOMPLETEZIP =		182;
	IDS_INVALIDZIP =			183;
	IDS_INSERTDISK =			184;
	IDS_OFMULTISET =			185;
	IDS_CANCELZIPNAME =		186;
	IDS_CANCELZIPOPERATION =187;
	IDS_NEWFIXEDNAME =		188;
	IDS_ZIPFILESFILTER =		189;
	IDS_SEEKERROR =			190;
	IDS_SEEKORIGINERROR =	191;
	IDS_PUTBACKOVERFLOW =	192;
  IDS_LOWMEM =            193;
  IDS_PREMEND =           194;
  IDS_REPLACEFILE =       195;
  IDS_FILEXISTALERT =     196;
  IDS_UNKNOWNMETH =       197;
  IDS_ZIPERROR =          198;
  IDS_OUTPUTTOLARGE =     199;
  IDS_NOTREGISTERED =     200;
  IDS_WARNING =           201;
  IDS_NOCOPY =            202;
  IDS_ERROR =             203;
  IDS_NOTENOUGHROOM =     204;
  IDS_CANTCREATEZCF =     205;
  IDS_CANTWRITEZCF =      206;
  IDS_CANTWRITEUCF =     207;
  IDS_BAD_UNCOMPRESSED_SIZE = 208;
  
implementation

{$R kpZCnst.res}

end.

