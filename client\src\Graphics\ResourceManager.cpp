#include "ResourceManager.h"
#include "../UI/ResourcePaths.h"
#include <iostream>

// Initialize static instance
ResourceManager* ResourceManager::s_instance = nullptr;

ResourceManager::ResourceManager()
    : m_wilManager(nullptr)
{
    // Initialize resource paths with values from ResourcePaths
    m_resourcePaths[ResourceType::MAIN] = ResourcePaths::MAIN;
    m_resourcePaths[ResourceType::MAIN2] = ResourcePaths::MAIN2;
    m_resourcePaths[ResourceType::MAIN3] = ResourcePaths::MAIN3;
    m_resourcePaths[ResourceType::CHR_SELECT] = ResourcePaths::CHR_SELECT;
    m_resourcePaths[ResourceType::MINIMAP] = ResourcePaths::MINIMAP;
    m_resourcePaths[ResourceType::TILES] = ResourcePaths::TILES;
    m_resourcePaths[ResourceType::SMALL_TILES] = ResourcePaths::SMALL_TILES;
    m_resourcePaths[ResourceType::HUM_EFFECT] = ResourcePaths::HUM_EFFECT;
    m_resourcePaths[ResourceType::ITEMS] = ResourcePaths::ITEMS;
    m_resourcePaths[ResourceType::STATE_ITEMS] = ResourcePaths::STATE_ITEMS;
    m_resourcePaths[ResourceType::DROP_ITEMS] = ResourcePaths::DROP_ITEMS;
    m_resourcePaths[ResourceType::HUMAN] = ResourcePaths::HUMAN;
    m_resourcePaths[ResourceType::HAIR] = ResourcePaths::HAIR;
    m_resourcePaths[ResourceType::WEAPON] = ResourcePaths::WEAPON;
    m_resourcePaths[ResourceType::MAGIC_ICON] = ResourcePaths::MAGIC_ICON;
    m_resourcePaths[ResourceType::NPC] = ResourcePaths::NPC;
    m_resourcePaths[ResourceType::MAGIC] = ResourcePaths::MAGIC;
    m_resourcePaths[ResourceType::MAGIC2] = ResourcePaths::MAGIC2;
    m_resourcePaths[ResourceType::MAGIC3] = ResourcePaths::MAGIC3;
    m_resourcePaths[ResourceType::MAGIC4] = ResourcePaths::MAGIC4;
    m_resourcePaths[ResourceType::EVENT_EFFECT] = ResourcePaths::EVENT_EFFECT;
    m_resourcePaths[ResourceType::OBJECTS] = ResourcePaths::OBJECTS;

    // Initialize resource loaded status
    for (const auto& pair : m_resourcePaths) {
        m_resourceLoaded[pair.first] = false;
    }
}

ResourceManager::~ResourceManager()
{
    UnloadAllResources();
}

ResourceManager* ResourceManager::GetInstance()
{
    if (s_instance == nullptr) {
        s_instance = new ResourceManager();
    }
    return s_instance;
}

bool ResourceManager::Initialize(std::shared_ptr<WILManager> wilManager)
{
    if (!wilManager) {
        std::cerr << "Failed to initialize ResourceManager: WILManager is null" << std::endl;
        return false;
    }

    m_wilManager = wilManager;
    return true;
}

void ResourceManager::RegisterResourcePath(ResourceType type, const std::string& path)
{
    m_resourcePaths[type] = path;
    m_resourceLoaded[type] = false;
}

std::string ResourceManager::GetResourcePath(ResourceType type)
{
    auto it = m_resourcePaths.find(type);
    if (it == m_resourcePaths.end()) {
        std::cerr << "Resource path not found for type: " << static_cast<int>(type) << std::endl;
        return "";
    }

    return it->second;
}

bool ResourceManager::LoadResource(ResourceType type, const std::string& paletteFile)
{
    if (!m_wilManager) {
        std::cerr << "WILManager is not initialized" << std::endl;
        return false;
    }

    auto it = m_resourcePaths.find(type);
    if (it == m_resourcePaths.end()) {
        std::cerr << "Resource path not found for type: " << static_cast<int>(type) << std::endl;
        return false;
    }

    // Load the resource
    bool success = m_wilManager->LoadWIL(it->second, paletteFile);
    if (success) {
        m_resourceLoaded[type] = true;
    }

    return success;
}

SDL_Surface* ResourceManager::GetSurface(ResourceType type, int index)
{
    if (!m_wilManager) {
        std::cerr << "WILManager is not initialized" << std::endl;
        return nullptr;
    }

    auto it = m_resourcePaths.find(type);
    if (it == m_resourcePaths.end()) {
        std::cerr << "Resource path not found for type: " << static_cast<int>(type) << std::endl;
        return nullptr;
    }

    // Check if the resource is loaded
    auto loadedIt = m_resourceLoaded.find(type);
    if (loadedIt == m_resourceLoaded.end() || !loadedIt->second) {
        // Try to load the resource
        if (!LoadResource(type)) {
            std::cerr << "Failed to load resource: " << it->second << std::endl;
            return nullptr;
        }
    }

    // Get the surface
    return m_wilManager->GetSurface(it->second, index);
}

bool ResourceManager::GetImageOffset(ResourceType type, int index, int& offsetX, int& offsetY)
{
    if (!m_wilManager) {
        std::cerr << "WILManager is not initialized" << std::endl;
        return false;
    }

    auto it = m_resourcePaths.find(type);
    if (it == m_resourcePaths.end()) {
        std::cerr << "Resource path not found for type: " << static_cast<int>(type) << std::endl;
        return false;
    }

    // Check if the resource is loaded
    auto loadedIt = m_resourceLoaded.find(type);
    if (loadedIt == m_resourceLoaded.end() || !loadedIt->second) {
        // Try to load the resource
        if (!LoadResource(type)) {
            std::cerr << "Failed to load resource: " << it->second << std::endl;
            return false;
        }
    }

    // Get the offset
    return m_wilManager->GetImageOffset(it->second, index, offsetX, offsetY);
}

std::string ResourceManager::ResourceTypeToString(ResourceType type)
{
    switch (type) {
        case ResourceType::MAIN: return "MAIN";
        case ResourceType::MAIN2: return "MAIN2";
        case ResourceType::MAIN3: return "MAIN3";
        case ResourceType::CHR_SELECT: return "CHR_SELECT";
        case ResourceType::MINIMAP: return "MINIMAP";
        case ResourceType::TILES: return "TILES";
        case ResourceType::SMALL_TILES: return "SMALL_TILES";
        case ResourceType::HUM_EFFECT: return "HUM_EFFECT";
        case ResourceType::ITEMS: return "ITEMS";
        case ResourceType::STATE_ITEMS: return "STATE_ITEMS";
        case ResourceType::DROP_ITEMS: return "DROP_ITEMS";
        case ResourceType::HUMAN: return "HUMAN";
        case ResourceType::HAIR: return "HAIR";
        case ResourceType::WEAPON: return "WEAPON";
        case ResourceType::MAGIC_ICON: return "MAGIC_ICON";
        case ResourceType::NPC: return "NPC";
        case ResourceType::MAGIC: return "MAGIC";
        case ResourceType::MAGIC2: return "MAGIC2";
        case ResourceType::MAGIC3: return "MAGIC3";
        case ResourceType::MAGIC4: return "MAGIC4";
        case ResourceType::EVENT_EFFECT: return "EVENT_EFFECT";
        case ResourceType::OBJECTS: return "OBJECTS";
        case ResourceType::MONSTER: return "MONSTER";
        default: return "UNKNOWN";
    }
}

bool ResourceManager::LoadAllResources()
{
    if (!m_wilManager) {
        std::cerr << "WILManager is not initialized" << std::endl;
        return false;
    }

    bool success = true;

    // Load all resources
    for (const auto& pair : m_resourcePaths) {
        if (!LoadResource(pair.first)) {
            std::cerr << "Failed to load resource: " << pair.second << std::endl;
            success = false;
        }
    }

    return success;
}

void ResourceManager::UnloadAllResources()
{
    if (m_wilManager) {
        m_wilManager->UnloadAll();
    }

    // Reset loaded status
    for (auto& pair : m_resourceLoaded) {
        pair.second = false;
    }
}
