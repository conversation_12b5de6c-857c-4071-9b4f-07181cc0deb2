#pragma once

#include "WILLoader.h"
#include <SDL2/SDL.h>
#include <string>
#include <memory>
#include <unordered_map>

/**
 * @class ResourceManager
 * @brief Manages game resources with standardized names
 *
 * This class provides a unified way to create and manage resource objects.
 * It defines standard resource names for all game assets and allows data
 * to be loaded on demand.
 */
class ResourceManager {
public:
    // Resource types
    enum class ResourceType {
        MAIN,           // Main UI elements
        MAIN2,          // Additional UI elements
        MAIN3,          // More UI elements
        CHR_SELECT,     // Character selection screen
        MINIMAP,        // Minimap
        TILES,          // Map tiles
        SMALL_TILES,    // Small map tiles
        HUM_EFFECT,     // Human effects (wings, etc.)
        ITEMS,          // Item images
        STATE_ITEMS,    // State item images
        DROP_ITEMS,     // Dropped item images
        HUMAN,          // Human character images
        HAIR,           // Hair images
        WEAPON,         // Weapon images
        MAGIC_ICON,     // Magic icons
        NPC,            // NPC images
        MAGIC,          // Magic effect images
        MAGIC2,         // Additional magic effect images
        MAGIC3,         // More magic effect images
        MAGIC4,         // Even more magic effect images
        EVENT_EFFECT,   // Event effect images
        OBJECTS,        // Map objects
        MONSTER         // Monster images
    };

private:
    std::shared_ptr<WILManager> m_wilManager;                           ///< WIL manager
    std::unordered_map<ResourceType, std::string> m_resourcePaths;      ///< Resource paths by type
    std::unordered_map<ResourceType, bool> m_resourceLoaded;            ///< Resource loaded status

    // Singleton instance
    static ResourceManager* s_instance;

    /**
     * @brief Constructor
     */
    ResourceManager();

public:
    /**
     * @brief Destructor
     */
    ~ResourceManager();

    /**
     * @brief Get the singleton instance
     * @return ResourceManager instance
     */
    static ResourceManager* GetInstance();

    /**
     * @brief Initialize the resource manager
     * @param wilManager WIL manager
     * @return true if successful, false otherwise
     */
    bool Initialize(std::shared_ptr<WILManager> wilManager);

    /**
     * @brief Register a resource path
     * @param type Resource type
     * @param path Resource path
     */
    void RegisterResourcePath(ResourceType type, const std::string& path);

    /**
     * @brief Get a resource path
     * @param type Resource type
     * @return Resource path
     */
    std::string GetResourcePath(ResourceType type);

    /**
     * @brief Load a resource
     * @param type Resource type
     * @param paletteFile Palette file name (optional)
     * @return true if successful, false otherwise
     */
    bool LoadResource(ResourceType type, const std::string& paletteFile = "");

    /**
     * @brief Get a surface from a resource
     * @param type Resource type
     * @param index Image index
     * @return SDL_Surface pointer or nullptr if not found
     */
    SDL_Surface* GetSurface(ResourceType type, int index);

    /**
     * @brief Get image offset values
     * @param type Resource type
     * @param index Image index
     * @param offsetX X offset (output)
     * @param offsetY Y offset (output)
     * @return true if successful, false otherwise
     */
    bool GetImageOffset(ResourceType type, int index, int& offsetX, int& offsetY);

    /**
     * @brief Convert ResourceType to string
     * @param type Resource type
     * @return String representation of the resource type
     */
    static std::string ResourceTypeToString(ResourceType type);

    /**
     * @brief Load all resources
     * @return true if all resources were loaded successfully, false otherwise
     */
    bool LoadAllResources();

    /**
     * @brief Unload all resources
     */
    void UnloadAllResources();
};

