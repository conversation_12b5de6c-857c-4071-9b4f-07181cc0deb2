#pragma once

#include "../Common/Types.h"
#include "../Common/Utils.h"
#include "../Common/IniFile.h"
#include "../Protocol/PacketTypes.h"
#include "../Protocol/NetworkManager.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <string>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <queue>
#include <array>
#include <iostream>

namespace MirServer {

// 使用Network命名空间中的类
using Network::ClientConnection;
using Network::NetworkManager;
using Network::NetworkEvent;
using Network::NetworkEventType;

// RunGate特定的协议定义（完全对应原项目Common.pas）
enum RunGateMessageType : WORD {
    GM_OPEN = 1,                  // 打开连接
    GM_CLOSE = 2,                 // 关闭连接  
    GM_CHECKSERVER = 3,           // 服务器检查信号
    GM_CHECKCLIENT = 4,           // 客户端检查信号
    GM_DATA = 5,                  // 转发游戏数据
    GM_SERVERUSERINDEX = 6,       // 用户索引管理
    GM_RECEIVE_OK = 7,            // 接收确认
    GM_TEST = 20                  // 测试消息
};

// RunGate消息头结构（对应delphi TMsgHeader）
#pragma pack(push, 1)
struct RunGateMessageHeader {
    DWORD dwCode;                 // 识别码 RUNGATECODE = 0xAA55AA55
    int nSocket;                  // Socket句柄
    WORD wGSocketIdx;             // 网关Socket索引
    WORD wIdent;                  // 消息标识
    WORD wUserListIndex;          // 用户列表索引
    int nLength;                  // 数据长度
    
    RunGateMessageHeader() 
        : dwCode(0xAA55AA55), nSocket(0), wGSocketIdx(0), 
          wIdent(0), wUserListIndex(0), nLength(0) {}
};
#pragma pack(pop)

// 游戏速度控制配置（对应delphi TConfig）
struct SpeedControlConfig {
    bool boHit;                   // 检查攻击速度
    bool boSpell;                 // 检查魔法速度
    bool boRun;                   // 检查跑步速度
    bool boWalk;                  // 检查走路速度
    bool boTurn;                  // 检查转向速度
    
    int nHitTime;                 // 攻击时间间隔(ms)
    int nSpellTime;               // 魔法时间间隔(ms)
    int nRunTime;                 // 跑步时间间隔(ms)
    int nWalkTime;                // 走路时间间隔(ms)
    int nTurnTime;                // 转向时间间隔(ms)
    
    int nHitCount;                // 攻击计数限制
    int nSpellCount;              // 魔法计数限制
    int nRunCount;                // 跑步计数限制
    int nWalkCount;               // 走路计数限制
    int nTurnCount;               // 转向计数限制
    
    BYTE btSpeedControlMode;      // 速度控制模式 (0=关闭, 1=警告, 2=断开)
    bool boSpeedShowMsg;          // 显示速度提示
    std::string sSpeedShowMsg;    // 速度提示消息
    BYTE btMsgColor;              // 消息颜色
    
    SpeedControlConfig() 
        : boHit(true), boSpell(true), boRun(true), boWalk(true), boTurn(true),
          nHitTime(500), nSpellTime(500), nRunTime(300), nWalkTime(300), nTurnTime(200),
          nHitCount(3), nSpellCount(3), nRunCount(3), nWalkCount(3), nTurnCount(3),
          btSpeedControlMode(1), boSpeedShowMsg(true), btMsgColor(0),
          sSpeedShowMsg("系统提示: 请按照游戏节奏,不要使用非法外挂!") {}
};

// 游戏速度状态（对应delphi TGameSpeed）
struct GameSpeedState {
    std::chrono::steady_clock::time_point dwHitTimeTick;
    std::chrono::steady_clock::time_point dwSpellTimeTick;
    std::chrono::steady_clock::time_point dwRunTimeTick;
    std::chrono::steady_clock::time_point dwWalkTimeTick;
    std::chrono::steady_clock::time_point dwTurnTimeTick;
    
    int nHitCount;
    int nSpellCount;
    int nRunCount;
    int nWalkCount;
    int nTurnCount;
    
    GameSpeedState() : nHitCount(0), nSpellCount(0), nRunCount(0), 
                       nWalkCount(0), nTurnCount(0) {
        auto now = std::chrono::steady_clock::now();
        dwHitTimeTick = dwSpellTimeTick = dwRunTimeTick = 
        dwWalkTimeTick = dwTurnTimeTick = now;
    }
    
    void Reset() {
        auto now = std::chrono::steady_clock::now();
        dwHitTimeTick = dwSpellTimeTick = dwRunTimeTick = 
        dwWalkTimeTick = dwTurnTimeTick = now;
        nHitCount = nSpellCount = nRunCount = nWalkCount = nTurnCount = 0;
    }
};

// RunGate会话信息（对应delphi TSessionInfo）
struct RunGateSessionInfo {
    std::shared_ptr<ClientConnection> socket;
    std::string sSocData;         // 接收数据缓冲区
    std::string sSendData;        // 发送数据缓冲区
    int nUserListIndex;           // 用户列表索引
    int nPacketIdx;               // 数据包索引
    int nPacketErrCount;          // 错误包计数
    
    bool boStartLogon;            // 是否刚开始登录
    bool boSendLock;              // 发送锁定
    bool boOverNomSize;           // 超过正常大小
    int nOverNomSizeCount;        // 超大小计数
    
    std::chrono::steady_clock::time_point dwSendLatestTime;    // 最后发送时间
    int nCheckSendLength;         // 检查发送长度
    bool boSendAvailable;         // 可发送标志
    bool boSendCheck;             // 发送检查
    std::chrono::steady_clock::time_point dwTimeOutTime;      // 超时时间
    
    int nReceiveLength;           // 接收长度
    std::chrono::steady_clock::time_point dwReceiveLengthTick; // 接收长度时间戳
    std::chrono::steady_clock::time_point dwReceiveTick;       // 接收时间戳
    int nSckHandle;               // Socket句柄
    std::string sRemoteAddr;      // 远程地址
    std::chrono::steady_clock::time_point dwSayMsgTick;       // 聊天消息时间戳
    
    GameSpeedState gameSpeed;     // 游戏速度控制状态
    
    RunGateSessionInfo() 
        : nUserListIndex(0), nPacketIdx(-1), nPacketErrCount(0),
          boStartLogon(true), boSendLock(false), boOverNomSize(false),
          nOverNomSizeCount(0), nCheckSendLength(0), boSendAvailable(true),
          boSendCheck(false), nReceiveLength(0), nSckHandle(-1) {
        auto now = std::chrono::steady_clock::now();
        dwSendLatestTime = dwTimeOutTime = dwReceiveLengthTick = 
        dwReceiveTick = dwSayMsgTick = now;
    }
};

// 发送用户数据结构（对应delphi TSendUserData）
struct RunGateSendUserData {
    int nSocketIdx;               // Socket索引
    int nSocketHandle;            // Socket句柄
    std::string sMsg;             // 消息内容
    
    RunGateSendUserData(int sockIdx = 0, int sockHandle = 0, const std::string& msg = "")
        : nSocketIdx(sockIdx), nSocketHandle(sockHandle), sMsg(msg) {}
};

// IP地址信息结构（对应delphi TSockaddr）
struct RunGateSockAddr {
    int nIPaddr;                  // IP地址（网络字节序）
    std::chrono::steady_clock::time_point dwStartAttackTick;  // 开始攻击时间
    int nAttackCount;             // 攻击计数
    int nSocketHandle;            // Socket句柄
    
    RunGateSockAddr() : nIPaddr(0), nAttackCount(0), nSocketHandle(0) {
        dwStartAttackTick = std::chrono::steady_clock::now();
    }
    
    RunGateSockAddr(int ip, int handle) 
        : nIPaddr(ip), nAttackCount(0), nSocketHandle(handle) {
        dwStartAttackTick = std::chrono::steady_clock::now();
    }
};

// 阻止IP方法枚举（对应delphi TBlockIPMethod）
enum RunGateBlockIPMethod {
    RUNGATE_DISCONNECT = 0,       // 断开连接
    RUNGATE_BLOCK = 1,            // 临时阻止
    RUNGATE_BLOCKLIST = 2         // 加入阻止列表
};

// RunGate服务器主类
class RunGateServer {
public:
    // 常量定义（对应delphi常量）
    static constexpr int RUNGATE_MAX_SESSION = 1000;         // 最大会话数
    static constexpr int RUNGATE_MSG_MAX_LENGTH = 20000;     // 最大消息长度
    static constexpr int RUNGATE_SEND_CHECK_SIZE = 512;      // 发送检查大小
    static constexpr int RUNGATE_SEND_CHECK_SIZE_MAX = 2048; // 最大发送检查大小
    static constexpr DWORD RUNGATE_CODE = 0xAA55AA55;        // RunGate识别码
    static constexpr int RUNGATE_DEF_BLOCK_SIZE = 16;        // 默认块大小（对应DEFBLOCKSIZE）
    
    // 状态字符串常量（对应原项目）
    static constexpr const char* STATUS_FAIL = "+FAIL/";
    static constexpr const char* STATUS_GOOD = "+GOOD/";
    
    RunGateServer();
    ~RunGateServer();
    
    // 主要接口
    bool Initialize(const std::string& configFile = "config/RunGateServer.ini");
    bool Start();
    void Stop();
    bool IsRunning() const { return m_isRunning; }
    
    // 配置管理
    void LoadConfig();
    void SaveConfig();
    void ReloadConfig();
    
    // 连接管理
    void OnClientConnect(std::shared_ptr<ClientConnection> connection);
    void OnClientDisconnect(std::shared_ptr<ClientConnection> connection);
    void OnClientMessage(std::shared_ptr<ClientConnection> connection, const std::vector<BYTE>& data);
    
    // 服务器通信
    void OnServerConnect();
    void OnServerDisconnect();
    void OnServerMessage(const std::vector<BYTE>& data);
    
    // IP管理功能
    bool IsBlockedIP(const std::string& ipAddress);
    bool IsConnectionLimited(const std::string& ipAddress, int socketHandle);
    bool AddAttackIP(const std::string& ipAddress);
    int AddBlockIP(const std::string& ipAddress);
    int AddTempBlockIP(const std::string& ipAddress);
    int GetConnectCountOfIP(const std::string& ipAddress);
    int GetAttackIPCount(const std::string& ipAddress);
    void CloseConnectByIP(const std::string& ipAddress);
    void CloseAllUser();
    
    // 统计信息
    int GetSessionCount() const { return m_sessionCount; }
    int GetActiveConnections() const;
    std::string GetVersionInfo() const;
    
    // 消息过滤
    void LoadAbuseFile();
    void FilterSayMsg(std::string& msg);
    
    // 时间相关工具函数（使用MirServer命名空间已有的）
    DWORD GetRunGateTickCount() const { return MirServer::GetCurrentTime(); }
    
    // 字符串编码解码函数（对应原ERunGate的编码格式）
    std::string EncodeString(const std::string& str) const;
    std::string DecodeString(const std::string& str) const;
    
    // 消息过滤函数
    bool CheckAbuseWord(const std::string& msg) const;
    
private:
    // 配置参数
    std::string m_configFile;
    std::unique_ptr<IniFile> m_iniFile;     // INI文件处理器
    SpeedControlConfig m_speedConfig;
    
    // 网络组件
    std::unique_ptr<NetworkManager> m_clientManager;       // 客户端网络管理器
    std::shared_ptr<ClientConnection> m_serverConnection;  // 服务器连接
    
    // 会话管理
    std::array<RunGateSessionInfo, RUNGATE_MAX_SESSION> m_sessions;
    std::atomic<int> m_sessionCount;
    std::mutex m_sessionMutex;
    
    // 消息队列
    std::queue<RunGateSendUserData> m_receiveQueue;
    std::queue<RunGateSendUserData> m_sendQueue;
    std::mutex m_queueMutex;
    
    // IP管理
    std::vector<RunGateSockAddr> m_blockIPList;            // 永久阻止IP列表
    std::vector<RunGateSockAddr> m_tempBlockIPList;        // 临时阻止IP列表
    std::unordered_map<std::string, std::vector<RunGateSockAddr>> m_currentIPList;  // 当前连接IP列表
    std::vector<RunGateSockAddr> m_attackIPList;           // 攻击IP列表
    std::mutex m_ipMutex;
    
    // 运行状态
    std::atomic<bool> m_isRunning;
    std::atomic<bool> m_serverReady;
    std::atomic<bool> m_gateReady;
    std::atomic<bool> m_checkServerFail;
    
    // 工作线程
    std::thread m_processThread;
    std::thread m_checkThread;
    std::thread m_decodeThread;
    
    // 配置参数
    std::string m_serverAddr;             // 游戏服务器地址
    int m_serverPort;                     // 游戏服务器端口
    std::string m_gateAddr;               // 网关监听地址
    int m_gatePort;                       // 网关监听端口
    int m_maxConnOfIP;                    // 每个IP最大连接数
    int m_maxClientPacketSize;            // 客户端最大包大小
    int m_nomClientPacketSize;            // 客户端正常包大小
    int m_maxClientMsgCount;              // 客户端最大消息数
    int m_clientSendBlockSize;            // 客户端发送块大小
    DWORD m_clientCheckTimeOut;           // 客户端检查超时
    int m_maxOverNomSizeCount;            // 最大超大小计数
    DWORD m_checkServerTimeOutTime;       // 服务器检查超时时间
    RunGateBlockIPMethod m_blockMethod;   // 阻止IP方法
    DWORD m_attackTick;                   // 攻击时间窗口
    int m_attackCount;                    // 攻击计数阈值
    int m_sayMsgMaxLen;                   // 聊天消息最大长度
    DWORD m_sayMsgTime;                   // 聊天消息时间间隔
    std::string m_replaceWord;            // 替换词
    
    // 统计信息
    std::atomic<int> m_reviceMsgSize;     // 接收消息大小
    std::atomic<int> m_deCodeMsgSize;     // 解码消息大小
    std::atomic<int> m_sendBlockSize;     // 发送块大小
    std::atomic<int> m_processMsgSize;    // 处理消息大小
    std::atomic<int> m_humLogonMsgSize;   // 人物登录消息大小
    std::atomic<int> m_humPlayMsgSize;    // 人物游戏消息大小
    
    // 时间戳
    std::chrono::steady_clock::time_point m_dwCheckClientTick;
    std::chrono::steady_clock::time_point m_dwProcessPacketTick;
    std::chrono::steady_clock::time_point m_dwRefConsolMsgTick;
    std::chrono::steady_clock::time_point m_dwLoopCheckTick;
    std::chrono::steady_clock::time_point m_dwCheckServerTick;
    
    // 性能控制参数（对应原项目）
    DWORD m_dwProcessReviceMsgTimeLimit;  // 接收消息处理时间限制
    DWORD m_dwProcessSendMsgTimeLimit;    // 发送消息处理时间限制
    DWORD m_dwLoopTime;                   // 循环时间
    DWORD m_dwProcessServerMsgTime;       // 处理服务器消息时间
    DWORD m_dwProcessClientMsgTime;       // 处理客户端消息时间
    DWORD m_dwReConnectServerTime;        // 重连服务器时间
    DWORD m_dwRefConsoleMsgTick;          // 刷新控制台消息时间戳
    bool m_boDecodeMsgLock;               // 解码消息锁定标志
    
    // 内部方法
    void ProcessThread();                 // 消息处理线程
    void CheckThread();                   // 检查线程
    void DecodeThread();                  // 解码线程
    
    // 数据包处理方法（完全对应原项目）
    void ProcessUserPacket(const RunGateSendUserData& userData);
    void ProcessPacket(const RunGateSendUserData& userData);
    void ProcessMakeSocketStr(int nSocket, int nSocketIndex, const char* buffer, int nMsgLen);
    void ProcReceiveBuffer(const char* buffer, int nMsgLen);
    
    // 消息发送方法（完全对应原项目）
    void SendServerMsg(int nIdent, WORD wSocketIndex, int nSocket, 
                      int nUserListIndex, int nLen, const char* data);
    void SendSocket(const char* buffer, int nLen);
    void SendWarnMsg(RunGateSessionInfo* sessionInfo);
    void SendActionFail(std::shared_ptr<ClientConnection> connection);
    
    // 会话管理方法（完全对应原项目）
    void RestSessionArray();
    int CheckDefMsg(const Protocol::DefaultMessage* defMsg, RunGateSessionInfo* sessionInfo);
    
    // 字符串处理方法（对应原项目EDcode.pas）
    std::string ArrestStringEx(const std::string& sSource, char cStart, char cEnd, std::string& sResult);
    std::string EncodeMessage(const Protocol::DefaultMessage& defMsg);
    std::string DecodeMessage(const std::string& encodedMsg, Protocol::DefaultMessage& defMsg);
    int Str_ToInt(char c, int nDefault);
    
    // 配置文件操作
    std::string GetConfigString(const std::string& section, const std::string& key, const std::string& defaultValue);
    int GetConfigInt(const std::string& section, const std::string& key, int defaultValue);
    bool GetConfigBool(const std::string& section, const std::string& key, bool defaultValue);
    void SetConfigString(const std::string& section, const std::string& key, const std::string& value);
    void SetConfigInt(const std::string& section, const std::string& key, int value);
    void SetConfigBool(const std::string& section, const std::string& key, bool value);
    
    // 日志和文件操作
    void AddMainLogMsg(const std::string& msg, int level = 0);
    void LoadBlockIPFile();
    void SaveBlockIPFile();
    
    // 敏感词过滤
    std::vector<std::string> m_abuseList;  // 敏感词列表
    std::mutex m_abuseMutex;
    
    // IP地址转换辅助函数
    int IPStringToInt(const std::string& ipStr);
    std::string IPIntToString(int ipInt);
    
    // 工具函数
    WORD MakeWord(BYTE low, BYTE high) const { return static_cast<WORD>((high << 8) | low); }
    BYTE LoByte(WORD value) const { return static_cast<BYTE>(value & 0xFF); }
    BYTE HiByte(WORD value) const { return static_cast<BYTE>((value >> 8) & 0xFF); }
};

} // namespace MirServer 