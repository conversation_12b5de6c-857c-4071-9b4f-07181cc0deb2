#pragma once

// Mir200 Core Types - Following Original Delphi Project Structure
// Based on delphi/Common/Grobal2.pas and delphi/EM2Engine/M2Share.pas

// Include Windows headers first to get proper type definitions
#ifdef _WIN32
#include <windows.h>
#endif

#include <cstdint>
#include <string>
#include <vector>
#include <memory>
#include <array>
#include <chrono>
#include <cmath>
#include <cstdlib>

// Basic type definitions matching original Delphi types
// Use Windows types when available, otherwise define our own
#ifndef _WIN32
    using BYTE = uint8_t;
    using WORD = uint16_t;
    using DWORD = uint32_t;
#endif
using LONGWORD = uint32_t;
using ShortInt = int8_t;

// Constants from original Grobal2.pas
constexpr int MAXPATHLEN = 255;
constexpr int DIRPATHLEN = 80;
constexpr int MAP_NAME_LEN = 16;
constexpr int ACTOR_NAME_LEN = 14;
constexpr int DEFBLOCKSIZE = 16;
constexpr int BUFFERSIZE = 10000;
constexpr int DATA_BUFSIZE = 8192;
constexpr int GROUPMAX = 11;
constexpr int BAGGOLD = 5000000;
constexpr int BODYLUCKUNIT = 10;
constexpr int MAX_STATUS_ATTRIBUTE = 12;

// Direction constants
constexpr BYTE DR_UP = 0;
constexpr BYTE DR_UPRIGHT = 1;
constexpr BYTE DR_RIGHT = 2;
constexpr BYTE DR_DOWNRIGHT = 3;
constexpr BYTE DR_DOWN = 4;
constexpr BYTE DR_DOWNLEFT = 5;
constexpr BYTE DR_LEFT = 6;
constexpr BYTE DR_UPLEFT = 7;

// Equipment positions
constexpr BYTE U_DRESS = 0;
constexpr BYTE U_WEAPON = 1;
constexpr BYTE U_RIGHTHAND = 2;
constexpr BYTE U_NECKLACE = 3;
constexpr BYTE U_HELMET = 4;
constexpr BYTE U_ARMRINGL = 5;
constexpr BYTE U_ARMRINGR = 6;
constexpr BYTE U_RINGL = 7;
constexpr BYTE U_RINGR = 8;
constexpr BYTE U_BUJUK = 9;
constexpr BYTE U_BELT = 10;
constexpr BYTE U_BOOTS = 11;
constexpr BYTE U_CHARM = 12;

// Poison types
constexpr BYTE POISON_DECHEALTH = 0;
constexpr BYTE POISON_DAMAGEARMOR = 1;
constexpr BYTE POISON_LOCKSPELL = 2;
constexpr BYTE POISON_DONTMOVE = 4;
constexpr BYTE POISON_STONE = 5;

// State types
constexpr BYTE STATE_TRANSPARENT = 8;
constexpr BYTE STATE_DEFENCEUP = 9;
constexpr BYTE STATE_MAGDEFENCEUP = 10;
constexpr BYTE STATE_BUBBLEDEFENCEUP = 11;

// User modes
constexpr BYTE USERMODE_PLAYGAME = 1;
constexpr BYTE USERMODE_LOGIN = 2;
constexpr BYTE USERMODE_LOGOFF = 3;
constexpr BYTE USERMODE_NOTICE = 4;

// Race constants
constexpr BYTE RC_PLAYOBJECT = 0;
constexpr BYTE RC_PLAYMOSTER = 60;
constexpr BYTE RC_HEROOBJECT = 66;
constexpr BYTE RC_GUARD = 11;
constexpr BYTE RC_PEACENPC = 15;
constexpr BYTE RC_ANIMAL = 50;
constexpr BYTE RC_MONSTER = 80;
constexpr BYTE RC_NPC = 10;
constexpr BYTE RC_ARCHERGUARD = 112;

// Job types (matching original)
enum class JobType : BYTE {
    WARRIOR = 0,    // WARR
    WIZARD = 1,     // WIZARD
    TAOIST = 2      // TAOS
};

// Gender types
enum class GenderType : BYTE {
    MAN = 0,        // gMan
    WOMAN = 1       // gWoMan
};

// Client action types
enum class ClientAction : BYTE {
    HIT = 0,        // cHit
    MAGHIT = 1,     // cMagHit
    RUN = 2,        // cRun
    WALK = 3,       // cWalk
    DIGUP = 4,      // cDigUp
    TURN = 5        // cTurn
};

// Attack modes (from M2Share.pas)
constexpr BYTE HAM_ALL = 0;
constexpr BYTE HAM_PEACE = 1;
constexpr BYTE HAM_DEAR = 2;
constexpr BYTE HAM_MASTER = 3;
constexpr BYTE HAM_GROUP = 4;
constexpr BYTE HAM_GUILD = 5;
constexpr BYTE HAM_PKATTACK = 6;

// Basic structures matching original Delphi types
struct Point {
    int x;
    int y;
    
    Point() : x(0), y(0) {}
    Point(int x_, int y_) : x(x_), y(y_) {}
    
    bool operator==(const Point& other) const {
        return x == other.x && y == other.y;
    }
    
    bool operator!=(const Point& other) const {
        return !(*this == other);
    }
};

// Ability structure (matching TAbility from ObjBase.pas)
struct Ability {
    WORD level;             // 等级
    WORD ac;                // 防御力
    WORD mac;               // 魔法防御力
    WORD dc;                // 攻击力
    WORD mc;                // 魔法力
    WORD sc;                // 道术
    WORD hp;                // 生命值
    WORD mp;                // 魔法值
    WORD hit;               // 准确
    WORD speed;             // 敏捷
    WORD x2;                // 未知
    
    Ability() : level(1), ac(0), mac(0), dc(0), mc(0), sc(0), 
                hp(0), mp(0), hit(0), speed(0), x2(0) {}
};

// Naked ability structure (matching TNakedAbility)
struct NakedAbility {
    WORD dc;                // 攻击力
    WORD mc;                // 魔法力
    WORD sc;                // 道术
    WORD ac;                // 防御力
    WORD mac;               // 魔法防御力
    WORD hp;                // 生命值
    WORD mp;                // 魔法值
    WORD hit;               // 准确
    WORD speed;             // 敏捷
    WORD reserved;          // 保留
    
    NakedAbility() : dc(0), mc(0), sc(0), ac(0), mac(0), 
                     hp(0), mp(0), hit(0), speed(0), reserved(0) {}
};

// Add ability structure (matching TAddAbility)
struct AddAbility {
    ShortInt dc;            // 攻击力加成
    ShortInt mc;            // 魔法力加成
    ShortInt sc;            // 道术加成
    ShortInt ac;            // 防御力加成
    ShortInt mac;           // 魔法防御力加成
    ShortInt hp;            // 生命值加成
    ShortInt mp;            // 魔法值加成
    ShortInt hit;           // 准确加成
    ShortInt speed;         // 敏捷加成
    ShortInt reserved[7];   // 保留字段
    
    AddAbility() : dc(0), mc(0), sc(0), ac(0), mac(0), 
                   hp(0), mp(0), hit(0), speed(0) {
        for (int i = 0; i < 7; ++i) reserved[i] = 0;
    }
};

// Status time array (matching TStatusTime)
using StatusTime = std::array<WORD, 6>;

// Quest structures (matching original)
struct QuestUnit {
    std::array<int, 100> flags;
    
    QuestUnit() {
        flags.fill(0);
    }
};

struct QuestFlag {
    std::array<BYTE, 2> flags;
    
    QuestFlag() {
        flags.fill(0);
    }
};

// User item structure (matching TUserItem)
struct UserItem {
    WORD make_index;        // 物品制造索引
    WORD index;             // 物品索引
    WORD dura;              // 持久度
    WORD dura_max;          // 最大持久度
    std::array<BYTE, 14> desc; // 物品描述
    
    UserItem() : make_index(0), index(0), dura(0), dura_max(0) {
        desc.fill(0);
    }
};

// Human use items (matching THumanUseItems)
struct HumanUseItems {
    std::array<UserItem, 13> items; // U_DRESS to U_CHARM
    
    HumanUseItems() = default;
};

// Default message structure (matching TDefaultMessage)
struct DefaultMessage {
    WORD ident;             // 消息标识
    WORD recog;             // 识别码
    WORD param;             // 参数
    WORD tag;               // 标签
    WORD series;            // 序列号
    
    DefaultMessage() : ident(0), recog(0), param(0), tag(0), series(0) {}
};

// Forward declarations
class BaseObject;
class PlayObject;
class Environment;
class UserEngine;

// Smart pointer types
using BaseObjectPtr = std::shared_ptr<BaseObject>;
using PlayObjectPtr = std::shared_ptr<PlayObject>;
using EnvironmentPtr = std::shared_ptr<Environment>;
using UserEnginePtr = std::shared_ptr<UserEngine>;

// String types
using MapName = std::string;
using CharName = std::string;
using AccountName = std::string;

// Utility functions
inline DWORD GetCurrentTime() {
#ifdef _WIN32
    return static_cast<DWORD>(GetTickCount());
#else
    return static_cast<DWORD>(std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
#endif
}

inline int Random(int max) {
    return rand() % max;
}

inline int Random(int min, int max) {
    return min + (rand() % (max - min + 1));
}

// Direction utilities
inline BYTE GetNextDirection(BYTE dir, bool clockwise = true) {
    if (clockwise) {
        return (dir + 1) % 8;
    } else {
        return (dir + 7) % 8;
    }
}

inline BYTE GetOppositeDirection(BYTE dir) {
    return (dir + 4) % 8;
}

// Distance calculation
inline double GetDistance(const Point& p1, const Point& p2) {
    int dx = p1.x - p2.x;
    int dy = p1.y - p2.y;
    return std::sqrt(dx * dx + dy * dy);
}

inline int GetDistance2(const Point& p1, const Point& p2) {
    int dx = abs(p1.x - p2.x);
    int dy = abs(p1.y - p2.y);
    return (dx > dy) ? dx : dy;
}
