#pragma once

/**
 * @file UILayout.h
 * @brief 定义UI元素的位置和大小常量
 *
 * 这个文件定义了所有UI元素的位置和大小常量，确保与原始Delphi项目保持一致。
 */

namespace UILayout {
    // 屏幕尺寸
    constexpr int SCREEN_WIDTH = 800;
    constexpr int SCREEN_HEIGHT = 600;
    constexpr int MAP_SURFACE_HEIGHT = 445;  // 地图区域高度

    // 主界面布局
    namespace MainUI {
        constexpr int BOTTOM_PANEL_X = 0;
        constexpr int BOTTOM_PANEL_Y = SCREEN_HEIGHT - 155;  // 底部面板Y坐标
        constexpr int BOTTOM_PANEL_WIDTH = SCREEN_WIDTH;
        constexpr int BOTTOM_PANEL_HEIGHT = 155;

        // 生命条
        constexpr int HEALTH_BAR_X = 30;
        constexpr int HEALTH_BAR_Y = BOTTOM_PANEL_Y + 20;
        constexpr int HEALTH_BAR_WIDTH = 150;
        constexpr int HEALTH_BAR_HEIGHT = 15;

        // 魔法条
        constexpr int MANA_BAR_X = 30;
        constexpr int MANA_BAR_Y = HEALTH_BAR_Y + 20;
        constexpr int MANA_BAR_WIDTH = 150;
        constexpr int MANA_BAR_HEIGHT = 15;

        // 经验条
        constexpr int EXP_BAR_X = 30;
        constexpr int EXP_BAR_Y = MANA_BAR_Y + 20;
        constexpr int EXP_BAR_WIDTH = 150;
        constexpr int EXP_BAR_HEIGHT = 15;

        // 金币显示
        constexpr int GOLD_X = 200;
        constexpr int GOLD_Y = BOTTOM_PANEL_Y + 20;
        constexpr int GOLD_WIDTH = 100;
        constexpr int GOLD_HEIGHT = 30;

        // 腰带物品位置 - 从原始Delphi项目中提取的确切坐标
        constexpr int BELT_ITEM_X[6] = {285, 328, 371, 415, 459, 503};
        constexpr int BELT_ITEM_Y[6] = {59, 59, 59, 59, 59, 59};
        constexpr int BELT_ITEM_WIDTH = 35;
        constexpr int BELT_ITEM_HEIGHT = 35;

        // 按钮位置 - 从原始Delphi项目中提取的确切坐标
        constexpr int STATE_BTN_X = 550;
        constexpr int STATE_BTN_Y = BOTTOM_PANEL_Y + 20;
        constexpr int STATE_BTN_WIDTH = 30;
        constexpr int STATE_BTN_HEIGHT = 30;

        constexpr int BAG_BTN_X = 590;
        constexpr int BAG_BTN_Y = BOTTOM_PANEL_Y + 20;
        constexpr int BAG_BTN_WIDTH = 30;
        constexpr int BAG_BTN_HEIGHT = 30;

        constexpr int SKILL_BTN_X = 630;
        constexpr int SKILL_BTN_Y = BOTTOM_PANEL_Y + 20;
        constexpr int SKILL_BTN_WIDTH = 30;
        constexpr int SKILL_BTN_HEIGHT = 30;

        constexpr int OPTION_BTN_X = 670;
        constexpr int OPTION_BTN_Y = BOTTOM_PANEL_Y + 20;
        constexpr int OPTION_BTN_WIDTH = 30;
        constexpr int OPTION_BTN_HEIGHT = 30;

        constexpr int MINIMAP_BTN_X = 710;
        constexpr int MINIMAP_BTN_Y = BOTTOM_PANEL_Y + 20;
        constexpr int MINIMAP_BTN_WIDTH = 30;
        constexpr int MINIMAP_BTN_HEIGHT = 30;

        constexpr int TRADE_BTN_X = 550;
        constexpr int TRADE_BTN_Y = BOTTOM_PANEL_Y + 60;
        constexpr int TRADE_BTN_WIDTH = 30;
        constexpr int TRADE_BTN_HEIGHT = 30;

        constexpr int GUILD_BTN_X = 590;
        constexpr int GUILD_BTN_Y = BOTTOM_PANEL_Y + 60;
        constexpr int GUILD_BTN_WIDTH = 30;
        constexpr int GUILD_BTN_HEIGHT = 30;

        constexpr int GROUP_BTN_X = 630;
        constexpr int GROUP_BTN_Y = BOTTOM_PANEL_Y + 60;
        constexpr int GROUP_BTN_WIDTH = 30;
        constexpr int GROUP_BTN_HEIGHT = 30;

        constexpr int EXIT_BTN_X = 670;
        constexpr int EXIT_BTN_Y = BOTTOM_PANEL_Y + 60;
        constexpr int EXIT_BTN_WIDTH = 30;
        constexpr int EXIT_BTN_HEIGHT = 30;

        constexpr int LOGOUT_BTN_X = 710;
        constexpr int LOGOUT_BTN_Y = BOTTOM_PANEL_Y + 60;
        constexpr int LOGOUT_BTN_WIDTH = 30;
        constexpr int LOGOUT_BTN_HEIGHT = 30;

        // 小地图
        constexpr int MINIMAP_X = 600;
        constexpr int MINIMAP_Y = 20;
        constexpr int MINIMAP_WIDTH = 180;
        constexpr int MINIMAP_HEIGHT = 180;
    }

    // 技能界面布局
    namespace SkillUI {
        constexpr int WINDOW_WIDTH = 400;
        constexpr int WINDOW_HEIGHT = 500;
        constexpr int WINDOW_X = (SCREEN_WIDTH - WINDOW_WIDTH) / 2;
        constexpr int WINDOW_Y = (SCREEN_HEIGHT - WINDOW_HEIGHT) / 2;

        constexpr int CLOSE_BTN_X_OFFSET = -30;  // 相对于窗口右边缘
        constexpr int CLOSE_BTN_Y_OFFSET = 10;   // 相对于窗口顶部
        constexpr int CLOSE_BTN_WIDTH = 20;
        constexpr int CLOSE_BTN_HEIGHT = 20;

        constexpr int TITLE_X_OFFSET = 20;  // 相对于窗口左边缘
        constexpr int TITLE_Y_OFFSET = 10;  // 相对于窗口顶部

        constexpr int SKILL_GRID_X_OFFSET = 20;  // 相对于窗口左边缘
        constexpr int SKILL_GRID_Y_OFFSET = 40;  // 相对于窗口顶部
        constexpr int SKILL_GRID_WIDTH = 360;
        constexpr int SKILL_GRID_HEIGHT = 400;
        constexpr int SKILL_ITEM_WIDTH = 40;
        constexpr int SKILL_ITEM_HEIGHT = 40;
        constexpr int SKILL_ITEM_SPACING = 10;
        constexpr int SKILLS_PER_ROW = 7;

        constexpr int PAGE_NAV_Y_OFFSET = -40;  // 相对于窗口底部
        constexpr int PREV_PAGE_BTN_X_OFFSET = 20;
        constexpr int NEXT_PAGE_BTN_X_OFFSET = 100;
        constexpr int PAGE_NAV_BTN_WIDTH = 30;
        constexpr int PAGE_NAV_BTN_HEIGHT = 30;
        constexpr int PAGE_LABEL_X_OFFSET = 60;
        constexpr int PAGE_LABEL_Y_OFFSET = -35;  // 相对于窗口底部

        constexpr int CONFIG_HOTKEYS_BTN_X_OFFSET = -100;  // 相对于窗口右边缘
        constexpr int CONFIG_HOTKEYS_BTN_Y_OFFSET = -40;   // 相对于窗口底部
        constexpr int CONFIG_HOTKEYS_BTN_WIDTH = 80;
        constexpr int CONFIG_HOTKEYS_BTN_HEIGHT = 30;
    }

    // 热键配置界面布局
    namespace HotkeyUI {
        constexpr int WINDOW_WIDTH = 400;
        constexpr int WINDOW_HEIGHT = 300;
        constexpr int WINDOW_X = (SCREEN_WIDTH - WINDOW_WIDTH) / 2;
        constexpr int WINDOW_Y = (SCREEN_HEIGHT - WINDOW_HEIGHT) / 2;

        constexpr int CLOSE_BTN_X_OFFSET = -30;  // 相对于窗口右边缘
        constexpr int CLOSE_BTN_Y_OFFSET = 10;   // 相对于窗口顶部
        constexpr int CLOSE_BTN_WIDTH = 20;
        constexpr int CLOSE_BTN_HEIGHT = 20;

        constexpr int TITLE_X_OFFSET = 20;  // 相对于窗口左边缘
        constexpr int TITLE_Y_OFFSET = 10;  // 相对于窗口顶部

        constexpr int HOTKEY_GRID_X_OFFSET = 20;  // 相对于窗口左边缘
        constexpr int HOTKEY_GRID_Y_OFFSET = 40;  // 相对于窗口顶部
        constexpr int HOTKEY_GRID_WIDTH = 360;
        constexpr int HOTKEY_GRID_HEIGHT = 200;
        constexpr int HOTKEY_ITEM_WIDTH = 40;
        constexpr int HOTKEY_ITEM_HEIGHT = 40;
        constexpr int HOTKEY_ITEM_SPACING = 10;
        constexpr int HOTKEYS_PER_ROW = 7;
    }

    // 物品栏界面布局
    namespace InventoryUI {
        constexpr int WINDOW_WIDTH = 400;
        constexpr int WINDOW_HEIGHT = 500;
        constexpr int WINDOW_X = (SCREEN_WIDTH - WINDOW_WIDTH) / 2;
        constexpr int WINDOW_Y = (SCREEN_HEIGHT - WINDOW_HEIGHT) / 2;

        constexpr int CLOSE_BTN_X_OFFSET = -30;  // 相对于窗口右边缘
        constexpr int CLOSE_BTN_Y_OFFSET = 10;   // 相对于窗口顶部
        constexpr int CLOSE_BTN_WIDTH = 20;
        constexpr int CLOSE_BTN_HEIGHT = 20;

        constexpr int TITLE_X_OFFSET = 20;  // 相对于窗口左边缘
        constexpr int TITLE_Y_OFFSET = 10;  // 相对于窗口顶部

        constexpr int INVENTORY_GRID_X_OFFSET = 20;  // 相对于窗口左边缘
        constexpr int INVENTORY_GRID_Y_OFFSET = 40;  // 相对于窗口顶部
        constexpr int INVENTORY_GRID_WIDTH = 360;
        constexpr int INVENTORY_GRID_HEIGHT = 400;
        constexpr int INVENTORY_ITEM_WIDTH = 40;
        constexpr int INVENTORY_ITEM_HEIGHT = 40;
        constexpr int INVENTORY_ITEM_SPACING = 5;
        constexpr int INVENTORY_ITEMS_PER_ROW = 8;
        constexpr int INVENTORY_ROWS = 10;
    }

    // 聊天界面布局
    namespace ChatUI {
        constexpr int WINDOW_WIDTH = 400;
        constexpr int WINDOW_HEIGHT = 200;
        constexpr int WINDOW_X = 10;
        constexpr int WINDOW_Y = SCREEN_HEIGHT - MainUI::BOTTOM_PANEL_HEIGHT - WINDOW_HEIGHT - 10;

        constexpr int CHAT_HISTORY_X_OFFSET = 10;  // 相对于窗口左边缘
        constexpr int CHAT_HISTORY_Y_OFFSET = 10;  // 相对于窗口顶部
        constexpr int CHAT_HISTORY_WIDTH = 380;
        constexpr int CHAT_HISTORY_HEIGHT = 150;

        constexpr int CHAT_INPUT_X_OFFSET = 10;  // 相对于窗口左边缘
        constexpr int CHAT_INPUT_Y_OFFSET = 170;  // 相对于窗口顶部
        constexpr int CHAT_INPUT_WIDTH = 300;
        constexpr int CHAT_INPUT_HEIGHT = 20;

        constexpr int CHAT_SEND_BTN_X_OFFSET = 320;  // 相对于窗口左边缘
        constexpr int CHAT_SEND_BTN_Y_OFFSET = 170;  // 相对于窗口顶部
        constexpr int CHAT_SEND_BTN_WIDTH = 70;
        constexpr int CHAT_SEND_BTN_HEIGHT = 20;
    }

    // 装备界面布局
    namespace EquipmentPanel {
        constexpr int WINDOW_WIDTH = 350;
        constexpr int WINDOW_HEIGHT = 400;
        constexpr int WINDOW_X = (SCREEN_WIDTH - WINDOW_WIDTH) / 2;
        constexpr int WINDOW_Y = (SCREEN_HEIGHT - WINDOW_HEIGHT) / 2;

        constexpr int CLOSE_BTN_X_OFFSET = -30;  // 相对于窗口右边缘
        constexpr int CLOSE_BTN_Y_OFFSET = 10;   // 相对于窗口顶部
        constexpr int CLOSE_BTN_WIDTH = 20;
        constexpr int CLOSE_BTN_HEIGHT = 20;

        constexpr int TITLE_X_OFFSET = 20;  // 相对于窗口左边缘
        constexpr int TITLE_Y_OFFSET = 10;  // 相对于窗口顶部

        constexpr int CHARACTER_MODEL_X_OFFSET = 100;  // 相对于窗口左边缘
        constexpr int CHARACTER_MODEL_Y_OFFSET = 150;  // 相对于窗口顶部

        constexpr int SLOT_SIZE = 40;  // 装备槽大小

        // 装备槽位置 - 从原始Delphi项目中提取的确切坐标（相对于窗口左上角）
        // 武器槽位置
        constexpr int WEAPON_SLOT_X = 50;
        constexpr int WEAPON_SLOT_Y = 100;

        // 盔甲槽位置
        constexpr int ARMOR_SLOT_X = 100;
        constexpr int ARMOR_SLOT_Y = 100;

        // 头盔槽位置
        constexpr int HELMET_SLOT_X = 100;
        constexpr int HELMET_SLOT_Y = 50;

        // 项链槽位置
        constexpr int NECKLACE_SLOT_X = 150;
        constexpr int NECKLACE_SLOT_Y = 50;

        // 左手槽位置（盾牌或双持）
        constexpr int LEFT_HAND_SLOT_X = 50;
        constexpr int LEFT_HAND_SLOT_Y = 150;

        // 右手槽位置（戒指）
        constexpr int RIGHT_HAND_SLOT_X = 150;
        constexpr int RIGHT_HAND_SLOT_Y = 150;

        // 左手镯槽位置
        constexpr int LEFT_BRACELET_SLOT_X = 50;
        constexpr int LEFT_BRACELET_SLOT_Y = 200;

        // 右手镯槽位置
        constexpr int RIGHT_BRACELET_SLOT_X = 150;
        constexpr int RIGHT_BRACELET_SLOT_Y = 200;

        // 鞋子槽位置
        constexpr int SHOES_SLOT_X = 100;
        constexpr int SHOES_SLOT_Y = 250;

        // 腰带槽位置
        constexpr int BELT_SLOT_X = 100;
        constexpr int BELT_SLOT_Y = 200;

        // 宝石槽位置
        constexpr int STONE_SLOT_X = 200;
        constexpr int STONE_SLOT_Y = 100;

        // 火把槽位置
        constexpr int TORCH_SLOT_X = 200;
        constexpr int TORCH_SLOT_Y = 150;

        // 书籍槽位置
        constexpr int BOOK_SLOT_X = 200;
        constexpr int BOOK_SLOT_Y = 200;
    }

    // 状态界面布局
    namespace StatusUI {
        constexpr int WINDOW_WIDTH = 400;
        constexpr int WINDOW_HEIGHT = 500;
        constexpr int WINDOW_X = (SCREEN_WIDTH - WINDOW_WIDTH) / 2;
        constexpr int WINDOW_Y = (SCREEN_HEIGHT - WINDOW_HEIGHT) / 2;

        constexpr int CLOSE_BTN_X_OFFSET = -30;  // 相对于窗口右边缘
        constexpr int CLOSE_BTN_Y_OFFSET = 10;   // 相对于窗口顶部
        constexpr int CLOSE_BTN_WIDTH = 20;
        constexpr int CLOSE_BTN_HEIGHT = 20;

        constexpr int TITLE_X_OFFSET = 20;  // 相对于窗口左边缘
        constexpr int TITLE_Y_OFFSET = 10;  // 相对于窗口顶部

        constexpr int STATS_X_OFFSET = 200;  // 相对于窗口左边缘
        constexpr int STATS_Y_OFFSET = 100;  // 相对于窗口顶部
        constexpr int STATS_WIDTH = 180;
        constexpr int STATS_HEIGHT = 300;
        constexpr int STATS_LINE_HEIGHT = 20;

        constexpr int CHARACTER_MODEL_X_OFFSET = 100;  // 相对于窗口左边缘
        constexpr int CHARACTER_MODEL_Y_OFFSET = 150;  // 相对于窗口顶部

        // Tab按钮布局
        constexpr int TAB_COUNT = 4;
        constexpr int TAB_WIDTH = 80;
        constexpr int TAB_HEIGHT = 30;
        constexpr int TAB_SPACING = 5;
        constexpr int TAB_START_X = 20;
        constexpr int TAB_Y = 40;

        // 装备面板在状态面板中的位置
        constexpr int EQUIPMENT_PANEL_X_OFFSET = 20;
        constexpr int EQUIPMENT_PANEL_Y_OFFSET = 80;
        constexpr int EQUIPMENT_PANEL_WIDTH_OFFSET = 40;  // 相对于状态面板宽度的偏移量
        constexpr int EQUIPMENT_PANEL_HEIGHT_OFFSET = 100;  // 相对于状态面板高度的偏移量
    }

    // 对话框布局
    namespace DialogUI {
        constexpr int SMALL_DIALOG_WIDTH = 300;
        constexpr int SMALL_DIALOG_HEIGHT = 150;
        constexpr int MEDIUM_DIALOG_WIDTH = 400;
        constexpr int MEDIUM_DIALOG_HEIGHT = 200;
        constexpr int LARGE_DIALOG_WIDTH = 500;
        constexpr int LARGE_DIALOG_HEIGHT = 300;

        constexpr int CLOSE_BTN_X_OFFSET = -30;  // 相对于窗口右边缘
        constexpr int CLOSE_BTN_Y_OFFSET = 10;   // 相对于窗口顶部
        constexpr int CLOSE_BTN_WIDTH = 20;
        constexpr int CLOSE_BTN_HEIGHT = 20;

        constexpr int TITLE_X_OFFSET = 20;  // 相对于窗口左边缘
        constexpr int TITLE_Y_OFFSET = 10;  // 相对于窗口顶部

        constexpr int MESSAGE_X_OFFSET = 20;  // 相对于窗口左边缘
        constexpr int MESSAGE_Y_OFFSET = 40;  // 相对于窗口顶部

        constexpr int BUTTON_WIDTH = 80;
        constexpr int BUTTON_HEIGHT = 30;
        constexpr int BUTTON_SPACING = 20;
        constexpr int BUTTON_Y_OFFSET = -40;  // 相对于窗口底部
    }
}
