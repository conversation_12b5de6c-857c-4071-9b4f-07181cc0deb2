# Guild System Test CMakeLists.txt

cmake_minimum_required(VERSION 3.16)
project(GuildSystemTest)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 添加包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../src)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../src/Common)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../src/GameEngine)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../src/BaseObject)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../src/Protocol)

# 源文件
set(GUILD_SOURCES
    ../src/GameEngine/GuildManager.cpp
    ../src/Common/Utils.cpp
    ../src/Common/Logger.cpp
    ../src/BaseObject/PlayObject.cpp
)

# 测试文件
set(TEST_SOURCES
    GuildSystemTest.cpp
)

# 创建测试可执行文件
add_executable(GuildSystemTest ${TEST_SOURCES} ${GUILD_SOURCES})

# 链接库
if(WIN32)
    target_link_libraries(GuildSystemTest ws2_32)
endif()

# 编译选项
if(MSVC)
    target_compile_options(GuildSystemTest PRIVATE /W4)
else()
    target_compile_options(GuildSystemTest PRIVATE -Wall -Wextra -pedantic)
endif()

# 定义宏
target_compile_definitions(GuildSystemTest PRIVATE 
    _CRT_SECURE_NO_WARNINGS
    WIN32_LEAN_AND_MEAN
)
