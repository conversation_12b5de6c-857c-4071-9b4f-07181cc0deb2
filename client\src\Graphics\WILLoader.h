#pragma once

#include <SDL2/SDL.h>
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>

/**
 * @class WILLoader
 * @brief Loads and manages WIL/WZL image files
 *
 * This class is responsible for loading WIL and WZL files, which are the image formats
 * used by the original Mir client. It extracts individual images from the files
 * and converts them to SDL_Surfaces.
 */
class WILLoader {
public:
    // File type enumeration
    enum class FileType {
        WIL,    ///< Standard WIL format
        WZL     ///< Compressed WZL format
    };

private:
    // WIL file format structures - based on original Delphi project
    struct WMImageHeader {
        char title[40];      ///< 'WEMADE Entertainment inc.'
        int32_t imageCount;  ///< Number of images (Integer in Delphi)
        int32_t colorCount;  ///< Color count (Integer in Delphi)
        int32_t paletteSize; ///< Palette size (Integer in Delphi)
    };

    struct WILHeader {
        WMImageHeader header;///< WIL file header
        int32_t* offsets;    ///< Array of offsets to each image (Integer in Delphi)
    };

    struct WMIndexHeader {
        char title[40];      ///< 'WEMADE Entertainment inc.'
        int32_t indexCount;  ///< Index count (Integer in Delphi)
    };

    struct WMIndexInfo {
        int32_t position;    ///< Position (offset) (Integer in Delphi)
        int32_t size;        ///< Image data size (Integer in Delphi)
    };

    // WZL file format structures - based on original Delphi project
    #pragma pack(1)
    struct WZLHeader {
        char title[44];      ///< Title
        int32_t imageCount;  ///< Image count (Integer in Delphi)
        int32_t i1;          ///< Unknown (Integer in Delphi)
        int32_t i2;          ///< Unknown (Integer in Delphi)
        int32_t i3;          ///< Unknown (Integer in Delphi)
        int32_t i4;          ///< Unknown (Integer in Delphi)
    };
    #pragma pack()

    struct WZLIndexHeader {
        char title[44];      ///< Title
        int32_t indexCount;  ///< Index count (Integer in Delphi)
        int32_t verFlag; ///< Unknown 
    };

    struct WZLImageInfo {
        uint8_t btEn1;       ///< Encoding type (3 for 8-bit, 5 for 16-bit) (Byte in Delphi)
        uint8_t btEn2;       ///< Unknown (Byte in Delphi)
        uint8_t bt2;         ///< Unknown (Byte in Delphi)
        uint8_t bt3;         ///< Unknown (Byte in Delphi)
        int16_t nWidth;      ///< Width (smallint in Delphi)
        int16_t nHeight;     ///< Height (smallint in Delphi)
        int16_t wPx;         ///< X offset (smallint in Delphi)
        int16_t wPy;         ///< Y offset (smallint in Delphi)
        int32_t iLen;        ///< Compressed data length (Cardinal in Delphi, but we use int32_t for consistency)
    };

    // Internal image representation
    struct WILImage {
        int16_t width;       ///< Image width (smallint in Delphi)
        int16_t height;      ///< Image height (smallint in Delphi)
        int16_t offsetX;     ///< X offset for drawing (smallint in Delphi)
        int16_t offsetY;     ///< Y offset for drawing (smallint in Delphi)
        uint8_t* data;       ///< Image pixel data (PByte in Delphi)
    };

    WILHeader m_header;                      ///< WIL file header
    std::string m_filename;                  ///< File name
    std::vector<SDL_Surface*> m_surfaces;    ///< Loaded surfaces
    std::vector<WILImage> m_images;          ///< Loaded images
    bool m_loaded;                           ///< Flag indicating if the file is loaded
    FileType m_fileType;                     ///< Type of the loaded file

    // Palette data
    SDL_Color m_palette[256];                ///< Color palette
    bool m_paletteLoaded;                    ///< Flag indicating if the palette is loaded

    /**
     * @brief Load the palette from a file
     * @param filename Palette file name
     * @return true if successful, false otherwise
     */
    bool LoadPalette(const std::string& filename);

    /**
     * @brief Convert a WIL/WZL image to an SDL_Surface
     * @param index Image index
     * @return SDL_Surface pointer or nullptr if failed
     */
    SDL_Surface* CreateSurfaceFromImage(int index);

    /**
     * @brief Load a WIL file
     * @param file Input file stream
     * @return true if successful, false otherwise
     */
    bool LoadWilFile(std::ifstream& file);

    /**
     * @brief Load a WZL file
     * @param file Input file stream
     * @return true if successful, false otherwise
     */
    bool LoadWzlFile(std::ifstream& file);

    /**
     * @brief Load a WZL image
     * @param file Input file stream
     * @param offset Offset in the file
     * @param index Image index
     */
    void LoadWzlImage(std::ifstream& file, int32_t offset, int32_t index);

    /**
     * @brief Decompress zlib data
     * @param compressedData Compressed data
     * @param compressedSize Size of compressed data
     * @param decompressedData Decompressed data buffer
     * @param decompressedSize Size of decompressed data buffer
     * @return true if successful, false otherwise
     */
    bool DecompressZlibData(const uint8_t* compressedData, size_t compressedSize,
                           uint8_t* decompressedData, size_t decompressedSize);

    /**
     * @brief Get file extension
     * @param filename File name
     * @return File extension
     */
    std::string GetFileExtension(const std::string& filename);

    /**
     * @brief Get file path without extension
     * @param filename File name
     * @return File path without extension
     */
    std::string GetFilePathWithoutExtension(const std::string& filename);

    /**
     * @brief Load index file
     * @param idxfile Index file name
     * @param indexList Vector to store index entries
     * @return true if successful, false otherwise
     */
    bool LoadIndexFile(const std::string& idxfile, std::vector<int32_t>& indexList);

public:
    /**
     * @brief Constructor
     */
    WILLoader();

    /**
     * @brief Destructor
     */
    ~WILLoader();

    /**
     * @brief Load a WIL/WZL file
     * @param filename File name
     * @param paletteFile Palette file name (optional)
     * @return true if successful, false otherwise
     */
    bool Load(const std::string& filename, const std::string& paletteFile = "");

    /**
     * @brief Get a surface by index
     * @param index Image index
     * @return SDL_Surface pointer or nullptr if not found
     */
    SDL_Surface* GetSurface(int index);

    /**
     * @brief Get image offset values
     * @param index Image index
     * @param offsetX X offset (output)
     * @param offsetY Y offset (output)
     * @return true if successful, false otherwise
     */
    bool GetImageOffset(int index, int& offsetX, int& offsetY);

    /**
     * @brief Get the number of images in the file
     * @return Number of images
     */
    int GetCount() const {
        if (m_fileType == FileType::WIL) {
            return m_header.header.imageCount;
        } else {
            // For WZL files
            return m_images.size();
        }
    }

    /**
     * @brief Check if the file is loaded
     * @return true if loaded, false otherwise
     */
    bool IsLoaded() const { return m_loaded; }

    /**
     * @brief Get the file type
     * @return File type
     */
    FileType GetFileType() const { return m_fileType; }

    /**
     * @brief Unload the file and free resources
     */
    void Unload();
};

/**
 * @class WILManager
 * @brief Manages multiple WIL/WZL files
 *
 * This class manages multiple WIL/WZL files and provides a cache for loaded images.
 */
class WILManager {
private:
    std::unordered_map<std::string, std::shared_ptr<WILLoader>> m_loaders;  ///< WIL/WZL loaders by filename
    std::unordered_map<std::string, SDL_Surface*> m_cache;                  ///< Surface cache

    /**
     * @brief Get file extension
     * @param filename File name
     * @return File extension
     */
    std::string GetFileExtension(const std::string& filename);

    /**
     * @brief Get file path without extension
     * @param filename File name
     * @return File path without extension
     */
    std::string GetFilePathWithoutExtension(const std::string& filename);

public:
    /**
     * @brief Constructor
     */
    WILManager();

    /**
     * @brief Destructor
     */
    ~WILManager();

    /**
     * @brief Load a WIL/WZL file
     * @param filename File name
     * @param paletteFile Palette file name (optional)
     * @return true if successful, false otherwise
     */
    bool LoadWIL(const std::string& filename, const std::string& paletteFile = "");

    /**
     * @brief Get a surface from a WIL/WZL file
     * @param filename File name
     * @param index Image index
     * @return SDL_Surface pointer or nullptr if not found
     */
    SDL_Surface* GetSurface(const std::string& filename, int index);

    /**
     * @brief Get image offset values
     * @param filename File name
     * @param index Image index
     * @param offsetX X offset (output)
     * @param offsetY Y offset (output)
     * @return true if successful, false otherwise
     */
    bool GetImageOffset(const std::string& filename, int index, int& offsetX, int& offsetY);

    /**
     * @brief Get a cached surface
     * @param key Cache key
     * @return SDL_Surface pointer or nullptr if not found
     */
    SDL_Surface* GetCachedSurface(const std::string& key);

    /**
     * @brief Add a surface to the cache
     * @param key Cache key
     * @param surface SDL_Surface pointer
     */
    void CacheSurface(const std::string& key, SDL_Surface* surface);

    /**
     * @brief Clear the cache
     */
    void ClearCache();

    /**
     * @brief Unload all WIL/WZL files
     */
    void UnloadAll();
};

