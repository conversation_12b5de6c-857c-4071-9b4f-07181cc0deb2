#include "ChatManager.h"
#include <iostream>
#include <algorithm>

ChatManager::ChatManager(std::shared_ptr<NetworkManager> networkManager, TTF_Font* font)
    : m_networkManager(networkManager)
    , m_font(font)
    , m_guildChatMode(false)
{
}

ChatManager::~ChatManager()
{
}

void ChatManager::Initialize(int x, int y, int width, int height, 
                            int inputX, int inputY, int inputWidth, int inputHeight)
{
    // Create chat window
    m_chatWindow = std::make_shared<ChatWindow>(x, y, width, height, m_font);
    
    // Create chat input
    m_chatInput = std::make_shared<ChatInput>(inputX, inputY, inputWidth, inputHeight, m_font);
    m_chatInput->SetVisible(false);
    m_chatInput->SetOnSubmit([this](const std::string& text) {
        HandleChatInput(text);
        m_chatInput->SetVisible(false);
    });
}

void ChatManager::Update(int deltaTime)
{
    // Update chat window
    if (m_chatWindow) {
        m_chatWindow->Update(deltaTime);
    }
    
    // Update chat input
    if (m_chatInput) {
        m_chatInput->Update(deltaTime);
    }
}

void ChatManager::Render(SDL_Renderer* renderer)
{
    // Render chat window
    if (m_chatWindow) {
        m_chatWindow->Render(renderer);
    }
    
    // Render chat input
    if (m_chatInput && m_chatInput->IsVisible()) {
        m_chatInput->Render(renderer);
    }
}

bool ChatManager::OnKeyDown(const SDL_KeyboardEvent& event)
{
    // Handle chat input key events
    if (m_chatInput && m_chatInput->IsVisible()) {
        return m_chatInput->OnKeyDown(event);
    }
    
    // Handle global key events
    switch (event.keysym.sym) {
        case SDLK_RETURN:
        case SDLK_KP_ENTER:
        case SDLK_SPACE:
            ShowChatInput();
            return true;
            
        case SDLK_SLASH:
            ShowChatInput();
            if (!m_lastWhisperTarget.empty()) {
                m_chatInput->SetText("/" + m_lastWhisperTarget + " ");
            } else {
                m_chatInput->SetText("/");
            }
            return true;
            
        case SDLK_EXCLAIM:
            ShowChatInput();
            m_chatInput->SetText("!");
            return true;
            
        case SDLK_AT:
            ShowChatInput();
            m_chatInput->SetText("@");
            return true;
            
        case SDLK_UP:
            if (m_chatWindow) {
                m_chatWindow->ScrollUp();
                return true;
            }
            break;
            
        case SDLK_DOWN:
            if (m_chatWindow) {
                m_chatWindow->ScrollDown();
                return true;
            }
            break;
            
        case SDLK_PAGEUP:
            if (m_chatWindow) {
                m_chatWindow->ScrollUp(10);
                return true;
            }
            break;
            
        case SDLK_PAGEDOWN:
            if (m_chatWindow) {
                m_chatWindow->ScrollDown(10);
                return true;
            }
            break;
    }
    
    return false;
}

bool ChatManager::OnTextInput(const SDL_TextInputEvent& event)
{
    // Handle chat input text events
    if (m_chatInput && m_chatInput->IsVisible()) {
        return m_chatInput->OnTextInput(event);
    }
    
    return false;
}

bool ChatManager::OnMouseDown(const SDL_MouseButtonEvent& event)
{
    // Handle chat input mouse events
    if (m_chatInput && m_chatInput->IsVisible()) {
        if (m_chatInput->OnMouseDown(event)) {
            return true;
        }
    }
    
    return false;
}

bool ChatManager::OnMouseUp(const SDL_MouseButtonEvent& event)
{
    // Handle chat input mouse events
    if (m_chatInput && m_chatInput->IsVisible()) {
        if (m_chatInput->OnMouseUp(event)) {
            return true;
        }
    }
    
    return false;
}

bool ChatManager::OnMouseMove(const SDL_MouseMotionEvent& event)
{
    // Handle chat input mouse events
    if (m_chatInput && m_chatInput->IsVisible()) {
        if (m_chatInput->OnMouseMove(event)) {
            return true;
        }
    }
    
    return false;
}

bool ChatManager::OnMouseWheel(const SDL_MouseWheelEvent& event)
{
    // Handle chat window mouse wheel events
    if (m_chatWindow) {
        if (m_chatWindow->OnMouseWheel(event)) {
            return true;
        }
    }
    
    return false;
}

void ChatManager::HandleChatInput(const std::string& text)
{
    if (text.empty()) {
        return;
    }
    
    // Check if it's a command
    if (text[0] == '/' || text[0] == '!' || text[0] == '@') {
        if (ProcessCommand(text)) {
            return;
        }
    }
    
    // Send chat message
    if (m_networkManager) {
        m_networkManager->SendChatMessage(text);
    }
}

bool ChatManager::ProcessCommand(const std::string& command)
{
    // Check for whisper command
    if (command[0] == '/' && command.length() > 1 && command[1] != '/') {
        std::string target;
        std::string message;
        
        // Extract target and message
        size_t spacePos = command.find(' ', 1);
        if (spacePos != std::string::npos) {
            target = command.substr(1, spacePos - 1);
            message = command.substr(spacePos + 1);
            
            // Save last whisper target
            m_lastWhisperTarget = target;
            
            // Send whisper message
            if (m_networkManager) {
                m_networkManager->SendWhisperMessage(target, message);
            }
            
            // Add to chat window
            if (m_chatWindow) {
                m_chatWindow->AddMessage(command, ChatMessageType::WHISPER);
            }
            
            return true;
        }
    }
    
    // Check for guild chat command
    if (command[0] == '!' && command.length() > 1) {
        std::string message = command.substr(1);
        
        // Send guild message
        if (m_networkManager) {
            m_networkManager->SendGuildMessage(message);
        }
        
        return true;
    }
    
    // Check for group chat command
    if (command[0] == '@' && command.length() > 1) {
        std::string message = command.substr(1);
        
        // Send group message
        if (m_networkManager) {
            m_networkManager->SendGroupMessage(message);
        }
        
        return true;
    }
    
    // Check for debug commands
    if (command == "/debug") {
        // Toggle debug mode
        // TODO: Implement debug mode
        return true;
    }
    
    // Check for password command
    if (command == "@password") {
        // Toggle password mode
        if (m_chatInput) {
            bool passwordMode = m_chatInput->GetText().empty() || m_chatInput->GetText()[0] != '*';
            m_chatInput->SetPasswordMode(passwordMode);
        }
        return true;
    }
    
    return false;
}

void ChatManager::AddChatMessage(int senderId, const std::string& senderName, const std::string& message)
{
    if (m_chatWindow) {
        m_chatWindow->AddMessage(message, ChatMessageType::NORMAL, senderId, senderName);
    }
}

void ChatManager::AddWhisperMessage(const std::string& senderName, const std::string& message)
{
    if (m_chatWindow) {
        m_chatWindow->AddMessage(message, ChatMessageType::WHISPER, 0, senderName);
    }
}

void ChatManager::AddGuildMessage(const std::string& senderName, const std::string& message)
{
    if (m_chatWindow) {
        m_chatWindow->AddMessage(message, ChatMessageType::GUILD, 0, senderName);
    }
}

void ChatManager::AddGroupMessage(const std::string& senderName, const std::string& message)
{
    if (m_chatWindow) {
        m_chatWindow->AddMessage(message, ChatMessageType::GROUP, 0, senderName);
    }
}

void ChatManager::AddSystemMessage(const std::string& message)
{
    if (m_chatWindow) {
        m_chatWindow->AddMessage(message, ChatMessageType::SYSTEM);
    }
}

void ChatManager::AddCryMessage(const std::string& message)
{
    if (m_chatWindow) {
        m_chatWindow->AddMessage(message, ChatMessageType::CRY);
    }
}

void ChatManager::AddMerchantMessage(int senderId, const std::string& senderName, const std::string& message)
{
    if (m_chatWindow) {
        m_chatWindow->AddMessage(message, ChatMessageType::MERCHANT, senderId, senderName);
    }
}

void ChatManager::ShowChatInput()
{
    if (m_chatInput) {
        m_chatInput->SetVisible(true);
        m_chatInput->Focus();
        
        // Set initial text based on guild chat mode
        if (m_guildChatMode) {
            m_chatInput->SetText("!~");
        } else {
            m_chatInput->SetText("");
        }
    }
}

void ChatManager::HideChatInput()
{
    if (m_chatInput) {
        m_chatInput->SetVisible(false);
        m_chatInput->Unfocus();
    }
}

void ChatManager::ToggleGuildChatMode()
{
    m_guildChatMode = !m_guildChatMode;
}

std::shared_ptr<ChatWindow> ChatManager::GetChatWindow() const
{
    return m_chatWindow;
}

std::shared_ptr<ChatInput> ChatManager::GetChatInput() const
{
    return m_chatInput;
}
