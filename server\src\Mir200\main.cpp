// Mir200 Main Entry Point
// Based on delphi/EM2Engine/M2Server.dpr - Following original project structure
// Phase 1 Implementation - Core server initialization

#include "M2Server.h"
#include "Common/M2Share.h"
#include <iostream>
#include <csignal>
#include <windows.h>

// Global server instance
std::unique_ptr<M2Server> g_server = nullptr;

// Signal handler for graceful shutdown
void SignalHandler(int signal) {
    g_functions::MainOutMessage("Received signal " + std::to_string(signal) + ", shutting down...");
    
    if (g_server) {
        g_server->Stop();
    }
    
    exit(0);
}

// Console control handler for Windows
BOOL WINAPI ConsoleCtrlHandler(DWORD ctrl_type) {
    switch (ctrl_type) {
        case CTRL_C_EVENT:
        case CTRL_BREAK_EVENT:
        case CTRL_CLOSE_EVENT:
        case CTRL_LOGOFF_EVENT:
        case CTRL_SHUTDOWN_EVENT:
            g_functions::MainOutMessage("Console control event received, shutting down...");
            if (g_server) {
                g_server->Stop();
            }
            return TRUE;
        default:
            return FALSE;
    }
}

// Display startup banner
void ShowStartupBanner() {
    std::cout << "========================================" << std::endl;
    std::cout << "  Mir200 - Legend of Mir Private Server" << std::endl;
    std::cout << "  Version: " << G_VERSION << std::endl;
    std::cout << "  Program: " << G_PROGRAM_NAME << std::endl;
    std::cout << "  Website: " << G_WEBSITE << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << std::endl;
}

// Display help information
void ShowHelp() {
    std::cout << "Usage: Mir200.exe [options]" << std::endl;
    std::cout << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  -h, --help          Show this help message" << std::endl;
    std::cout << "  -v, --version       Show version information" << std::endl;
    std::cout << "  -c, --config FILE   Specify configuration file" << std::endl;
    std::cout << "  -d, --daemon        Run as daemon/service" << std::endl;
    std::cout << "  -t, --test          Test mode (no actual server start)" << std::endl;
    std::cout << "  --no-console        Disable console output" << std::endl;
    std::cout << std::endl;
    std::cout << "Examples:" << std::endl;
    std::cout << "  Mir200.exe                           # Start with default config" << std::endl;
    std::cout << "  Mir200.exe -c config/custom.ini     # Start with custom config" << std::endl;
    std::cout << "  Mir200.exe -d                       # Run as service" << std::endl;
    std::cout << std::endl;
}

// Parse command line arguments
struct CommandLineArgs {
    bool show_help = false;
    bool show_version = false;
    bool daemon_mode = false;
    bool test_mode = false;
    bool no_console = false;
    std::string config_file = "config/M2Server.ini";
};

CommandLineArgs ParseCommandLine(int argc, char* argv[]) {
    CommandLineArgs args;
    
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            args.show_help = true;
        }
        else if (arg == "-v" || arg == "--version") {
            args.show_version = true;
        }
        else if (arg == "-d" || arg == "--daemon") {
            args.daemon_mode = true;
        }
        else if (arg == "-t" || arg == "--test") {
            args.test_mode = true;
        }
        else if (arg == "--no-console") {
            args.no_console = true;
        }
        else if ((arg == "-c" || arg == "--config") && i + 1 < argc) {
            args.config_file = argv[++i];
        }
        else {
            std::cout << "Unknown option: " << arg << std::endl;
            std::cout << "Use -h or --help for usage information." << std::endl;
            exit(1);
        }
    }
    
    return args;
}

// Initialize console settings
void InitializeConsole() {
    // Set console title
    SetConsoleTitleA("Mir200 Server Console");
    
    // Set console code page to UTF-8
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
    
    // Enable console colors
    HANDLE console_handle = GetStdHandle(STD_OUTPUT_HANDLE);
    DWORD console_mode;
    GetConsoleMode(console_handle, &console_mode);
    console_mode |= ENABLE_VIRTUAL_TERMINAL_PROCESSING;
    SetConsoleMode(console_handle, console_mode);
}

// Main entry point
int main(int argc, char* argv[]) {
    TRY_BEGIN
        // Parse command line arguments
        CommandLineArgs args = ParseCommandLine(argc, argv);
        
        // Handle help and version requests
        if (args.show_help) {
            ShowHelp();
            return 0;
        }
        
        if (args.show_version) {
            std::cout << "Mir200 Server Version " << G_VERSION << std::endl;
            std::cout << "Build Date: " << __DATE__ << " " << __TIME__ << std::endl;
            return 0;
        }
        
        // Initialize console if not disabled
        if (!args.no_console && !args.daemon_mode) {
            InitializeConsole();
            ShowStartupBanner();
        }
        
        // Initialize M2Share system
        if (!InitializeM2Share()) {
            std::cerr << "Failed to initialize M2Share system" << std::endl;
            return 1;
        }
        
        // Load server configuration
        if (!LoadServerConfig(args.config_file)) {
            g_functions::MainOutMessage("Warning: Failed to load configuration from " + args.config_file);
            g_functions::MainOutMessage("Using default configuration values");
        }
        
        // Set up signal handlers for graceful shutdown
        signal(SIGINT, SignalHandler);
        signal(SIGTERM, SignalHandler);
        SetConsoleCtrlHandler(ConsoleCtrlHandler, TRUE);
        
        // Create and initialize server
        g_server = std::make_unique<M2Server>();
        
        if (!g_server->Initialize(args.config_file)) {
            g_functions::MainOutMessage("Failed to initialize server");
            return 1;
        }
        
        // Test mode - just validate configuration and exit
        if (args.test_mode) {
            g_functions::MainOutMessage("Test mode: Configuration validated successfully");
            g_server->Finalize();
            FinalizeM2Share();
            return 0;
        }
        
        // Start the server
        g_functions::MainOutMessage("Starting Mir200 Server...");
        
        if (!g_server->Start()) {
            g_functions::MainOutMessage("Failed to start server");
            return 1;
        }
        
        g_functions::MainOutMessage("Server started successfully");
        g_functions::MainOutMessage("Server Name: " + g_config::server_name);
        g_functions::MainOutMessage("Gate Address: " + g_config::gate_addr + ":" + std::to_string(g_config::gate_port));
        g_functions::MainOutMessage("Max Users: " + std::to_string(g_config::max_user));
        g_functions::MainOutMessage("Test Server: " + std::string(g_config::test_server ? "Yes" : "No"));
        
        if (!args.daemon_mode && !args.no_console) {
            g_functions::MainOutMessage("Press Ctrl+C to stop the server");
        }
        
        // Main server loop
        g_server->Run();
        
        // Cleanup
        g_functions::MainOutMessage("Shutting down server...");
        g_server->Stop();
        g_server->Finalize();
        g_server.reset();
        
        // Finalize M2Share system
        FinalizeM2Share();
        
        g_functions::MainOutMessage("Server shutdown complete");
        return 0;
        
    TRY_END
    
    // Emergency cleanup
    if (g_server) {
        g_server->Stop();
        g_server->Finalize();
        g_server.reset();
    }
    
    FinalizeM2Share();
    
    std::cerr << "Server terminated due to unhandled exception" << std::endl;
    return 1;
}

// Windows service entry point (for future service mode implementation)
#ifdef _WIN32
void WINAPI ServiceMain(DWORD argc, LPTSTR* argv) {
    // Simplified service implementation for compilation
    (void)argc; // Suppress unused parameter warning
    (void)argv; // Suppress unused parameter warning

    // For now, just run as console application
    std::vector<char*> args;
    args.push_back(const_cast<char*>("Mir200.exe"));

    main(static_cast<int>(args.size()), args.data());
}

// Service control handler
void WINAPI ServiceCtrlHandler(DWORD ctrl_code) {
    switch (ctrl_code) {
        case SERVICE_CONTROL_STOP:
        case SERVICE_CONTROL_SHUTDOWN:
            if (g_server) {
                g_server->Stop();
            }
            break;
        default:
            break;
    }
}
#endif

// DLL entry point (if compiled as DLL)
#ifdef _WIN32
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
        case DLL_PROCESS_ATTACH:
            // DLL initialization
            break;
        case DLL_THREAD_ATTACH:
            // Thread initialization
            break;
        case DLL_THREAD_DETACH:
            // Thread cleanup
            break;
        case DLL_PROCESS_DETACH:
            // DLL cleanup
            if (g_server) {
                g_server->Stop();
                g_server->Finalize();
                g_server.reset();
            }
            FinalizeM2Share();
            break;
    }
    return TRUE;
}
#endif
