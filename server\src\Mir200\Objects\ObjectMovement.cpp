#include "ObjectMovement.h"
#include "BaseObject.h"
#include "../Common/M2Share.h"

// ObjectMovement implementation - Following original ObjBase.pas movement logic exactly

ObjectMovement::ObjectMovement(BaseObject* owner) : m_owner(owner) {
    // Initialize movement state (matching original TBaseObject.Create)
    // Following exact initialization from ObjBase.pas lines 1157-1368
    
    // Movement state initialization (matching original movement field initialization)
    m_walk_speed = 600;                     // m_nWalkSpeed := 600;
    m_walk_step = 1;                        // m_nWalkStep := 1;
    m_walk_count = 0;                       // m_nWalkCount := 0;
    m_walk_wait = 0;                        // m_dwWalkWait := 0;
    m_walk_wait_tick = 0;                   // m_dwWalkWaitTick := 0;
    m_walk_wait_locked = false;             // m_boWalkWaitLocked := False;
    
    // Movement timing initialization (matching original tick initialization)
    m_walk_tick = 0;                        // m_dwWalkTick := 0;
    m_run_tick = 0;                         // m_dwRunTick := 0;
    m_run_time = 300;                       // m_nRunTime := 300;
    m_teleport_tick = 0;                    // m_dwTeleportTick := 0;
    m_map_move_tick = 0;                    // m_dwMapMoveTick := 0;
    
    // Movement configuration initialization
    m_base_walk_speed = 600;
    m_base_run_speed = 300;
    m_walk_interval = 600;
    m_run_interval = 300;
    m_turn_interval = 100;
    m_anti_speed_hack = true;
    
    // Current movement state initialization
    m_is_moving = false;
    m_current_movement_type = MovementType::WALK;
    m_target_position = Point(0, 0);
    m_movement_start_time = 0;
    
    // Clear movement history
    m_movement_history.clear();
}

ObjectMovement::~ObjectMovement() {
    // Clear movement history
    m_movement_history.clear();
}

void ObjectMovement::Initialize() {
    // Reset movement state to initial values
    Reset();
}

void ObjectMovement::Update() {
    // Update movement timing and process movement queue
    UpdateMovementTiming();
    ProcessMovementQueue();
    CleanupMovementHistory();
}

void ObjectMovement::Reset() {
    // Reset movement state
    m_is_moving = false;
    m_current_movement_type = MovementType::WALK;
    m_target_position = Point(0, 0);
    m_movement_start_time = 0;
    
    // Clear movement history
    ClearMovementHistory();
}

// Primary movement methods (matching original ObjBase.pas methods exactly)
bool ObjectMovement::Walk(BYTE direction) {
    return ProcessWalk(direction);
}

bool ObjectMovement::Run(BYTE direction) {
    return ProcessRun(direction);
}

bool ObjectMovement::Turn(BYTE direction) {
    return ProcessTurn(direction);
}

// WalkTo method (following exact logic from ObjBase.pas WalkTo line 1934-2000)
MovementResult ObjectMovement::WalkTo(BYTE direction, bool flag) {
    // function TBaseObject.WalkTo(btDir: Byte; boFlag: Boolean): Boolean; //004C3F64
    if (!m_owner) return MovementResult::INVALID_POSITION;
    
    // Check holy seize state (following original line 1943)
    // if m_boHolySeize then Exit;
    if (m_owner->GetStateManager() && m_owner->GetStateManager()->IsHolySeize()) {
        return MovementResult::PARALYZED;
    }
    
    // Get current position
    Point current_pos = m_owner->GetCurrentPos();
    int old_x = current_pos.x;  // nOX := m_nCurrX;
    int old_y = current_pos.y;  // nOY := m_nCurrY;
    
    // Calculate next position (following original GetNextPosition logic)
    Point next_pos = CalculateNextPosition(direction, MovementType::WALK);
    int new_x = next_pos.x;     // nNX, nNY from GetNextPosition
    int new_y = next_pos.y;
    
    // Validate movement timing (following original walk speed check)
    if (!ValidateMovementTiming(MovementType::WALK)) {
        return MovementResult::TOO_FAST;
    }
    
    // Check if can move (state validation)
    if (!CanMove()) {
        return MovementResult::PARALYZED;
    }
    
    // Check position validity
    if (!IsValidPosition(new_x, new_y)) {
        return MovementResult::INVALID_POSITION;
    }
    
    // Check if position is walkable
    if (!IsWalkable(new_x, new_y)) {
        return MovementResult::BLOCKED;
    }
    
    // Process the actual walk
    if (ProcessWalk(direction)) {
        // Update timing (following original logic)
        m_walk_tick = g_functions::GetCurrentTime(); // m_dwWalkTick := GetTickCount();
        
        // Add to movement history for anti-cheat
        MovementData movement(Point(old_x, old_y), Point(new_x, new_y), direction, MovementType::WALK);
        AddMovementToHistory(movement);
        
        return MovementResult::SUCCESS;
    }
    
    return MovementResult::BLOCKED;
}

// SpaceMove method (following exact logic from ObjBase.pas SpaceMove line 3942-4020)
void ObjectMovement::SpaceMove(const MapName& map, int x, int y, int mode) {
    // procedure TBaseObject.SpaceMove(sMAP: string; nX, nY: Integer; nInt: Integer);
    if (!m_owner) return;
    
    // Store old position and environment
    Point old_pos = m_owner->GetCurrentPos();
    MapName old_map = m_owner->GetMapName();
    
    // Set new position (following original logic)
    m_owner->SetCurrentPos(Point(x, y));
    m_owner->SetMapName(map);
    
    // Update map move tick (following original line 4006)
    m_map_move_tick = g_functions::GetCurrentTime(); // m_dwMapMoveTick := GetTickCount();
    
    // Send space move messages (following original logic lines 4004-4005)
    if (mode == 1) {
        // SendRefMsg(RM_SPACEMOVE_SHOW2, m_btDirection, m_nCurrX, m_nCurrY, 0, '');
        m_owner->SendRefMsg(SM_SPACEMOVE_SHOW2, m_owner->GetDirection(), x, y, 0, "");
    } else {
        // SendRefMsg(RM_SPACEMOVE_SHOW, m_btDirection, m_nCurrX, m_nCurrY, 0, '');
        m_owner->SendRefMsg(SM_SPACEMOVE_SHOW, m_owner->GetDirection(), x, y, 0, "");
    }
    
    // Notify callbacks
    OnPositionChanged(old_pos, Point(x, y));
    if (old_map != map) {
        OnMapChanged(old_map, map);
    }
}

bool ObjectMovement::EnterAnotherMap(Environment* env, int dest_x, int dest_y) {
    if (!m_owner || !env) return false;
    
    // Validate destination position
    if (!IsValidPosition(dest_x, dest_y)) {
        return false;
    }
    
    // Process map change
    Point old_pos = m_owner->GetCurrentPos();
    m_owner->SetCurrentPos(Point(dest_x, dest_y));
    
    // Update environment reference
    m_owner->SetEnvironment(env);
    
    // Notify callbacks
    OnPositionChanged(old_pos, Point(dest_x, dest_y));
    
    return true;
}

// Position and direction methods
Point ObjectMovement::GetCurrentPosition() const {
    return m_owner ? m_owner->GetCurrentPos() : Point(0, 0);
}

Point ObjectMovement::GetFrontPosition() const {
    if (!m_owner) return Point(0, 0);
    
    Point current = m_owner->GetCurrentPos();
    BYTE direction = m_owner->GetDirection();
    
    return g_functions::GetFrontPosition(current.x, current.y, direction);
}

Point ObjectMovement::GetBackPosition() const {
    if (!m_owner) return Point(0, 0);
    
    Point current = m_owner->GetCurrentPos();
    BYTE direction = m_owner->GetDirection();
    
    return g_functions::GetBackPosition(current.x, current.y, direction);
}

bool ObjectMovement::GetFrontPosition(int& x, int& y) const {
    // Following exact logic from ObjBase.pas GetFrontPosition
    if (!m_owner) return false;
    
    Point front = GetFrontPosition();
    x = front.x;
    y = front.y;
    
    return true;
}

bool ObjectMovement::GetBackPosition(int& x, int& y) const {
    // Following exact logic from ObjBase.pas GetBackPosition
    if (!m_owner) return false;
    
    Point back = GetBackPosition();
    x = back.x;
    y = back.y;
    
    return true;
}

BYTE ObjectMovement::GetDirection() const {
    return m_owner ? m_owner->GetDirection() : DR_UP;
}

void ObjectMovement::SetDirection(BYTE direction) {
    if (m_owner && IsValidDirection(direction)) {
        BYTE old_dir = m_owner->GetDirection();
        m_owner->SetDirection(direction);
        OnDirectionChanged(old_dir, direction);
    }
}

// Movement validation
bool ObjectMovement::CanMove() const {
    if (!m_owner) return false;
    
    // Check state manager for movement restrictions
    ObjectState* state = m_owner->GetStateManager();
    if (state && !state->CanMove()) {
        return false;
    }
    
    // Check if dead
    if (m_owner->IsDead()) {
        return false;
    }
    
    return true;
}

bool ObjectMovement::CanWalk(BYTE direction) const {
    if (!CanMove()) return false;
    if (!IsValidDirection(direction)) return false;
    
    // Check timing
    if (!ValidateMovementTiming(MovementType::WALK)) return false;
    
    // Check destination
    Point next_pos = CalculateNextPosition(direction, MovementType::WALK);
    return IsWalkable(next_pos.x, next_pos.y);
}

bool ObjectMovement::CanRun(BYTE direction) const {
    if (!CanMove()) return false;
    if (!IsValidDirection(direction)) return false;
    
    // Check timing
    if (!ValidateMovementTiming(MovementType::RUN)) return false;
    
    // Check destination
    Point next_pos = CalculateNextPosition(direction, MovementType::RUN);
    return IsWalkable(next_pos.x, next_pos.y);
}

bool ObjectMovement::CanTurn(BYTE direction) const {
    if (!m_owner) return false;
    if (!IsValidDirection(direction)) return false;
    
    // Check timing
    return ValidateMovementTiming(MovementType::TURN);
}

bool ObjectMovement::IsValidPosition(int x, int y) const {
    // Check map boundaries
    if (!IsInMap(x, y)) return false;
    
    // Check environment collision
    if (!CheckMapBoundaries(Point(x, y))) return false;
    
    return true;
}

bool ObjectMovement::IsValidDirection(BYTE direction) const {
    return direction <= DR_UPLEFT;
}

// Position utilities
bool ObjectMovement::IsBlocked(int x, int y) const {
    return !IsWalkable(x, y);
}

bool ObjectMovement::IsWalkable(int x, int y) const {
    if (!IsValidPosition(x, y)) return false;
    
    // Check environment collision
    if (CheckEnvironmentCollision(Point(x, y))) return false;
    
    // Check object collision
    if (CheckObjectCollision(Point(x, y))) return false;
    
    return true;
}

bool ObjectMovement::IsFlyable(int x, int y) const {
    // For flying objects, only check map boundaries
    return IsInMap(x, y);
}

bool ObjectMovement::IsInMap(int x, int y) const {
    // Basic map boundary check - would be enhanced with actual map data
    return x >= 0 && y >= 0 && x < 1000 && y < 1000; // Placeholder values
}

bool ObjectMovement::IsInSafeZone(int x, int y) const {
    // Safe zone check - would be enhanced with actual safe zone data
    return false; // Placeholder implementation
}

// Advanced movement methods (following original ObjBase.pas methods)
int ObjectMovement::CharPushed(BYTE direction, int push_count) {
    // Following original CharPushed logic for knockback effects
    if (!m_owner || push_count <= 0) return 0;

    Point current = m_owner->GetCurrentPos();
    Point target = current;

    // Calculate pushed position
    for (int i = 0; i < push_count; ++i) {
        target = g_functions::GetFrontPosition(target.x, target.y, direction);
        if (!IsWalkable(target.x, target.y)) {
            break;
        }
    }

    // Apply the push if valid
    if (target != current) {
        m_owner->SetCurrentPos(target);
        OnPositionChanged(current, target);
        return g_functions::GetDistance(current.x, current.y, target.x, target.y);
    }

    return 0;
}

bool ObjectMovement::GetDropPosition(int org_x, int org_y, int range, int& dest_x, int& dest_y) {
    // Following original sub_4C5370 logic for finding drop positions
    for (int r = 1; r <= range; ++r) {
        for (int dir = 0; dir < 8; ++dir) {
            Point test_pos = g_functions::GetFrontPosition(org_x, org_y, dir);
            for (int step = 0; step < r; ++step) {
                test_pos = g_functions::GetFrontPosition(test_pos.x, test_pos.y, dir);
            }

            if (IsWalkable(test_pos.x, test_pos.y)) {
                dest_x = test_pos.x;
                dest_y = test_pos.y;
                return true;
            }
        }
    }

    return false;
}

void ObjectMovement::MapRandomMove(const MapName& map, int mode) {
    // Following original MapRandomMove logic
    if (!m_owner) return;

    // Generate random position within map bounds
    int x = g_functions::Random(50, 950); // Placeholder map size
    int y = g_functions::Random(50, 950);

    // Find walkable position near random point
    int dest_x, dest_y;
    if (GetDropPosition(x, y, 10, dest_x, dest_y)) {
        SpaceMove(map, dest_x, dest_y, mode);
    }
}

// Direction utilities
BYTE ObjectMovement::GetBackDir(BYTE direction) const {
    return g_functions::GetOppositeDirection(direction);
}

BYTE ObjectMovement::GetAttackDir(BaseObject* target) const {
    if (!m_owner || !target) return DR_UP;

    Point current = m_owner->GetCurrentPos();
    Point target_pos = target->GetCurrentPos();

    return g_functions::GetDirection(current.x, current.y, target_pos.x, target_pos.y);
}

bool ObjectMovement::GetAttackDir(BaseObject* target, BYTE& direction) const {
    if (!target) return false;

    direction = GetAttackDir(target);
    return true;
}

bool ObjectMovement::TargetInSpitRange(BaseObject* target, BYTE& direction) const {
    // Following original TargetInSpitRange logic for ranged attacks
    if (!m_owner || !target) return false;

    Point current = m_owner->GetCurrentPos();
    Point target_pos = target->GetCurrentPos();

    // Check if target is in line of sight
    int distance = g_functions::GetDistance(current.x, current.y, target_pos.x, target_pos.y);
    if (distance > 3) return false; // Max spit range

    direction = g_functions::GetDirection(current.x, current.y, target_pos.x, target_pos.y);
    return true;
}

// Distance and range calculations
int ObjectMovement::GetDistance(BaseObject* target) const {
    if (!m_owner || !target) return 999;

    Point current = m_owner->GetCurrentPos();
    Point target_pos = target->GetCurrentPos();

    return g_functions::GetDistance(current.x, current.y, target_pos.x, target_pos.y);
}

int ObjectMovement::GetDistance(int x, int y) const {
    if (!m_owner) return 999;

    Point current = m_owner->GetCurrentPos();
    return g_functions::GetDistance(current.x, current.y, x, y);
}

bool ObjectMovement::InRange(BaseObject* target, int range) const {
    return GetDistance(target) <= range;
}

bool ObjectMovement::InRange(int x, int y, int range) const {
    return GetDistance(x, y) <= range;
}

// Movement history and anti-cheat
void ObjectMovement::AddMovementToHistory(const MovementData& movement) {
    m_movement_history.push_back(movement);

    // Keep only recent history
    if (m_movement_history.size() > MAX_MOVEMENT_HISTORY) {
        m_movement_history.erase(m_movement_history.begin());
    }
}

bool ObjectMovement::ValidateMovementSpeed(const MovementData& movement) const {
    if (!m_anti_speed_hack) return true;

    // Check movement timing
    DWORD time_diff = g_functions::GetCurrentTime() - movement.timestamp;
    int expected_time = (movement.type == MovementType::RUN) ? m_run_interval : m_walk_interval;

    return time_diff >= expected_time;
}

bool ObjectMovement::DetectSpeedHack() const {
    if (!m_anti_speed_hack || m_movement_history.size() < 3) return false;

    // Check recent movements for speed violations
    int violations = 0;
    for (size_t i = m_movement_history.size() - 3; i < m_movement_history.size(); ++i) {
        if (!ValidateMovementSpeed(m_movement_history[i])) {
            violations++;
        }
    }

    return violations >= 2; // 2 out of 3 recent movements are too fast
}

void ObjectMovement::ClearMovementHistory() {
    m_movement_history.clear();
}

// Movement callbacks and notifications
void ObjectMovement::OnMovementStarted(MovementType type, const Point& target) {
    m_is_moving = true;
    m_current_movement_type = type;
    m_target_position = target;
    m_movement_start_time = g_functions::GetCurrentTime();
}

void ObjectMovement::OnMovementCompleted(MovementType type, const Point& final_pos) {
    m_is_moving = false;
    m_target_position = final_pos;
}

void ObjectMovement::OnMovementFailed(MovementType type, MovementResult result) {
    m_is_moving = false;
    // Log movement failure if needed
}

void ObjectMovement::OnDirectionChanged(BYTE old_dir, BYTE new_dir) {
    // Notify owner of direction change
    if (m_owner) {
        // m_owner->FeatureChanged(); // Would be called in real implementation
    }
}

void ObjectMovement::OnPositionChanged(const Point& old_pos, const Point& new_pos) {
    // Notify owner of position change
    if (m_owner) {
        // m_owner->StatusChanged(); // Would be called in real implementation
    }
}

// Map and environment integration
void ObjectMovement::OnMapChanged(const MapName& old_map, const MapName& new_map) {
    // Handle map change
    ClearMovementHistory(); // Clear history when changing maps
}

void ObjectMovement::OnEnvironmentChanged(Environment* old_env, Environment* new_env) {
    // Handle environment change
    ClearMovementHistory(); // Clear history when changing environments
}

// Synchronization and network
void ObjectMovement::SendMovementToClient(MovementType type, const Point& pos, BYTE direction) {
    if (!m_owner) return;

    // Send movement message to client based on type
    switch (type) {
        case MovementType::WALK:
            m_owner->SendRefMsg(SM_WALK, direction, pos.x, pos.y, 0, "");
            break;
        case MovementType::RUN:
            m_owner->SendRefMsg(SM_RUN, direction, pos.x, pos.y, 0, "");
            break;
        case MovementType::TURN:
            m_owner->SendRefMsg(SM_TURN, direction, pos.x, pos.y, 0, "");
            break;
        default:
            break;
    }
}

void ObjectMovement::SendMovementToNearby(MovementType type, const Point& pos, BYTE direction) {
    if (!m_owner) return;

    // Send movement message to nearby objects
    switch (type) {
        case MovementType::WALK:
            m_owner->SendRefMsg(SM_WALK, direction, pos.x, pos.y, 0, "");
            break;
        case MovementType::RUN:
            m_owner->SendRefMsg(SM_RUN, direction, pos.x, pos.y, 0, "");
            break;
        case MovementType::TURN:
            m_owner->SendRefMsg(SM_TURN, direction, pos.x, pos.y, 0, "");
            break;
        default:
            break;
    }
}

void ObjectMovement::SynchronizePosition() {
    if (!m_owner) return;

    Point current = m_owner->GetCurrentPos();
    BYTE direction = m_owner->GetDirection();

    // Send current position to client and nearby objects
    SendMovementToClient(MovementType::WALK, current, direction);
    SendMovementToNearby(MovementType::WALK, current, direction);
}

// Private internal movement processing methods
bool ObjectMovement::ProcessWalk(BYTE direction) {
    if (!m_owner) return false;

    // Validate direction
    if (!IsValidDirection(direction)) return false;

    // Check if can walk
    if (!CanWalk(direction)) return false;

    // Calculate next position
    Point current = m_owner->GetCurrentPos();
    Point next = CalculateNextPosition(direction, MovementType::WALK);

    // Update position and direction
    m_owner->SetDirection(direction);
    m_owner->SetCurrentPos(next);

    // Send movement messages
    SendMovementToClient(MovementType::WALK, next, direction);
    SendMovementToNearby(MovementType::WALK, next, direction);

    // Notify callbacks
    OnMovementStarted(MovementType::WALK, next);
    OnPositionChanged(current, next);
    OnMovementCompleted(MovementType::WALK, next);

    return true;
}

bool ObjectMovement::ProcessRun(BYTE direction) {
    if (!m_owner) return false;

    // Validate direction
    if (!IsValidDirection(direction)) return false;

    // Check if can run
    if (!CanRun(direction)) return false;

    // Calculate next position (run moves 2 steps)
    Point current = m_owner->GetCurrentPos();
    Point next = CalculateNextPosition(direction, MovementType::RUN);

    // Update position and direction
    m_owner->SetDirection(direction);
    m_owner->SetCurrentPos(next);

    // Send movement messages
    SendMovementToClient(MovementType::RUN, next, direction);
    SendMovementToNearby(MovementType::RUN, next, direction);

    // Notify callbacks
    OnMovementStarted(MovementType::RUN, next);
    OnPositionChanged(current, next);
    OnMovementCompleted(MovementType::RUN, next);

    return true;
}

bool ObjectMovement::ProcessTurn(BYTE direction) {
    if (!m_owner) return false;

    // Validate direction
    if (!IsValidDirection(direction)) return false;

    // Check if can turn
    if (!CanTurn(direction)) return false;

    // Update direction only
    BYTE old_dir = m_owner->GetDirection();
    m_owner->SetDirection(direction);

    // Send turn message
    Point current = m_owner->GetCurrentPos();
    SendMovementToClient(MovementType::TURN, current, direction);
    SendMovementToNearby(MovementType::TURN, current, direction);

    // Notify callbacks
    OnDirectionChanged(old_dir, direction);

    return true;
}

bool ObjectMovement::ProcessTeleport(const Point& target) {
    if (!m_owner) return false;

    // Validate target position
    if (!IsValidPosition(target.x, target.y)) return false;

    // Update position
    Point current = m_owner->GetCurrentPos();
    m_owner->SetCurrentPos(target);

    // Notify callbacks
    OnMovementStarted(MovementType::TELEPORT, target);
    OnPositionChanged(current, target);
    OnMovementCompleted(MovementType::TELEPORT, target);

    return true;
}

bool ObjectMovement::ProcessSpaceMove(const MapName& map, const Point& target, int mode) {
    if (!m_owner) return false;

    // Use the public SpaceMove method
    SpaceMove(map, target.x, target.y, mode);
    return true;
}

// Internal validation methods
bool ObjectMovement::ValidateMovementTiming(MovementType type) const {
    DWORD current_time = g_functions::GetCurrentTime();
    DWORD required_interval;
    DWORD last_move_time;

    switch (type) {
        case MovementType::WALK:
            required_interval = m_walk_interval;
            last_move_time = m_walk_tick;
            break;
        case MovementType::RUN:
            required_interval = m_run_interval;
            last_move_time = m_run_tick;
            break;
        case MovementType::TURN:
            required_interval = m_turn_interval;
            last_move_time = m_walk_tick; // Use walk tick for turn timing
            break;
        default:
            return true; // No timing restriction for other types
    }

    return (current_time - last_move_time) >= required_interval;
}

bool ObjectMovement::ValidateMovementDistance(const Point& from, const Point& to, MovementType type) const {
    int distance = g_functions::GetDistance(from.x, from.y, to.x, to.y);

    switch (type) {
        case MovementType::WALK:
        case MovementType::TURN:
            return distance <= 1; // Walk/turn moves 1 step
        case MovementType::RUN:
            return distance <= 2; // Run moves 2 steps
        case MovementType::TELEPORT:
        case MovementType::SPACE_MOVE:
            return true; // No distance restriction for teleport/space move
        default:
            return false;
    }
}

bool ObjectMovement::ValidateMovementPath(const Point& from, const Point& to) const {
    // Simple line-of-sight check for movement path
    int dx = abs(to.x - from.x);
    int dy = abs(to.y - from.y);

    // Check if movement is in a straight line (8 directions)
    if (dx <= 1 && dy <= 1) return true;

    // For longer movements, check intermediate positions
    if (dx == 0) {
        // Vertical movement
        int step = (to.y > from.y) ? 1 : -1;
        for (int y = from.y + step; y != to.y; y += step) {
            if (!IsWalkable(from.x, y)) return false;
        }
    } else if (dy == 0) {
        // Horizontal movement
        int step = (to.x > from.x) ? 1 : -1;
        for (int x = from.x + step; x != to.x; x += step) {
            if (!IsWalkable(x, from.y)) return false;
        }
    } else if (dx == dy) {
        // Diagonal movement
        int step_x = (to.x > from.x) ? 1 : -1;
        int step_y = (to.y > from.y) ? 1 : -1;
        int x = from.x + step_x;
        int y = from.y + step_y;
        while (x != to.x && y != to.y) {
            if (!IsWalkable(x, y)) return false;
            x += step_x;
            y += step_y;
        }
    } else {
        // Invalid movement path
        return false;
    }

    return true;
}

// Internal utility methods
void ObjectMovement::UpdateMovementState() {
    // Update current movement state based on timing
    if (m_is_moving) {
        DWORD current_time = g_functions::GetCurrentTime();
        DWORD movement_duration = current_time - m_movement_start_time;

        // Check if movement should be completed
        DWORD expected_duration = (m_current_movement_type == MovementType::RUN) ? m_run_interval : m_walk_interval;
        if (movement_duration >= expected_duration) {
            OnMovementCompleted(m_current_movement_type, m_target_position);
        }
    }
}

void ObjectMovement::UpdateMovementTiming() {
    // Update movement timing for anti-cheat validation
    UpdateMovementState();

    // Clean up old movement history
    CleanupMovementHistory();
}

void ObjectMovement::ProcessMovementQueue() {
    // Process any queued movements (placeholder for future implementation)
    // This would handle queued movement commands
}

void ObjectMovement::CleanupMovementHistory() {
    if (m_movement_history.empty()) return;

    DWORD current_time = g_functions::GetCurrentTime();
    const DWORD HISTORY_TIMEOUT = 10000; // 10 seconds

    // Remove old movement history entries
    m_movement_history.erase(
        std::remove_if(m_movement_history.begin(), m_movement_history.end(),
            [current_time, HISTORY_TIMEOUT](const MovementData& movement) {
                return (current_time - movement.timestamp) > HISTORY_TIMEOUT;
            }),
        m_movement_history.end());
}

// Position calculation helpers
Point ObjectMovement::CalculateNextPosition(BYTE direction, MovementType type) const {
    if (!m_owner) return Point(0, 0);

    Point current = m_owner->GetCurrentPos();
    int steps = (type == MovementType::RUN) ? 2 : 1; // Run moves 2 steps, walk moves 1

    Point result = current;
    for (int i = 0; i < steps; ++i) {
        result = g_functions::GetFrontPosition(result.x, result.y, direction);
    }

    return result;
}

Point ObjectMovement::CalculatePositionInDirection(const Point& from, BYTE direction, int distance) const {
    Point result = from;
    for (int i = 0; i < distance; ++i) {
        result = g_functions::GetFrontPosition(result.x, result.y, direction);
    }
    return result;
}

BYTE ObjectMovement::CalculateDirectionToTarget(const Point& target) const {
    if (!m_owner) return DR_UP;

    Point current = m_owner->GetCurrentPos();
    return g_functions::GetDirection(current.x, current.y, target.x, target.y);
}

// Environment interaction
bool ObjectMovement::CheckEnvironmentCollision(const Point& pos) const {
    // Check for environment obstacles (walls, barriers, etc.)
    // This would be implemented with actual environment data
    return false; // Placeholder - no collision detected
}

bool ObjectMovement::CheckObjectCollision(const Point& pos) const {
    // Check for collision with other objects
    // This would be implemented with actual object collision detection
    return false; // Placeholder - no collision detected
}

bool ObjectMovement::CheckMapBoundaries(const Point& pos) const {
    // Check if position is within map boundaries
    return IsInMap(pos.x, pos.y);
}

// Movement effect processing
void ObjectMovement::ApplyMovementEffects(MovementType type) {
    if (!m_owner) return;

    // Apply movement-specific effects
    switch (type) {
        case MovementType::WALK:
            // Walking effects (footsteps, etc.)
            break;
        case MovementType::RUN:
            // Running effects (faster movement, stamina consumption, etc.)
            break;
        case MovementType::TELEPORT:
            // Teleport effects (visual effects, etc.)
            break;
        default:
            break;
    }
}

void ObjectMovement::ProcessMovementSounds(MovementType type) {
    if (!m_owner) return;

    // Process movement sounds
    switch (type) {
        case MovementType::WALK:
            // Walking sound effects
            break;
        case MovementType::RUN:
            // Running sound effects
            break;
        default:
            break;
    }
}

void ObjectMovement::ProcessMovementVisuals(MovementType type) {
    if (!m_owner) return;

    // Process movement visual effects
    switch (type) {
        case MovementType::WALK:
            // Walking visual effects
            break;
        case MovementType::RUN:
            // Running visual effects (dust, etc.)
            break;
        case MovementType::TELEPORT:
            // Teleport visual effects
            break;
        default:
            break;
    }
}

// Anti-cheat and validation
bool ObjectMovement::IsMovementTooFast(const MovementData& movement) const {
    if (!m_anti_speed_hack) return false;

    DWORD time_diff = g_functions::GetCurrentTime() - movement.timestamp;
    int expected_time = (movement.type == MovementType::RUN) ? m_run_interval : m_walk_interval;

    // Allow 10% tolerance for network latency
    int min_time = static_cast<int>(expected_time * 0.9);

    return time_diff < min_time;
}

bool ObjectMovement::IsMovementPatternSuspicious() const {
    if (!m_anti_speed_hack || m_movement_history.size() < 5) return false;

    // Check for suspicious movement patterns
    int rapid_movements = 0;
    DWORD current_time = g_functions::GetCurrentTime();

    for (const auto& movement : m_movement_history) {
        if ((current_time - movement.timestamp) < 1000) { // Within last second
            if (IsMovementTooFast(movement)) {
                rapid_movements++;
            }
        }
    }

    return rapid_movements >= 3; // 3 or more rapid movements in 1 second
}

void ObjectMovement::LogSuspiciousMovement(const MovementData& movement) const {
    if (!m_owner) return;

    // Log suspicious movement for investigation
    std::string log_msg = "Suspicious movement detected for " + m_owner->GetCharName();
    log_msg += " at (" + std::to_string(movement.to_pos.x) + "," + std::to_string(movement.to_pos.y) + ")";
    log_msg += " direction=" + std::to_string(movement.direction);
    log_msg += " type=" + std::to_string(static_cast<int>(movement.type));

    g_functions::MainOutMessage(log_msg);
}
