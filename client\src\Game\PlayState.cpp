#include "PlayState.h"
#include "../Application.h"
#include "MenuState.h"
#include "../UI/Button.h"
#include "../UI/UILayout.h"
#include "../UI/ResourcePaths.h"
#include <iostream>
#include <tuple>
#include <sstream>
#include <fstream>

PlayState::PlayState(Application* app, std::shared_ptr<NetworkManager> networkManager)
    : GameState(app)
    , m_paused(false)
    , m_viewFog(true)
    , m_resourceManager(nullptr)
    , m_networkManager(networkManager)
    , m_chatFont(nullptr)
    , m_uiManager(nullptr)
    , m_skillPanel(nullptr)
    , m_statusPanel(nullptr)
    , m_hotkeyConfigUI(nullptr)
    , m_gameUI(nullptr)
    , m_damageNumberManager(nullptr)
{
    // Register packet handlers if network manager is provided
    if (m_networkManager) {
        // Map related packets
        m_networkManager->RegisterPacketHandler(
            PacketType::MAP_DATA,
            [this](const Packet& packet) { HandleMapData(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::MAP_CHANGE,
            [this](const Packet& packet) { HandleMapChange(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::CHANGE_MAP,
            [this](const Packet& packet) { HandleChangeMap(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::CLEAR_OBJECTS,
            [this](const Packet& packet) { HandleClearObjects(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::AREA_STATE,
            [this](const Packet& packet) { HandleAreaState(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::DAY_CHANGING,
            [this](const Packet& packet) { HandleDayChanging(packet); }
        );

        // Entity spawn packets
        m_networkManager->RegisterPacketHandler(
            PacketType::SPAWN_PLAYER,
            [this](const Packet& packet) { HandleSpawnPlayer(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::SPAWN_MONSTER,
            [this](const Packet& packet) { HandleSpawnMonster(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::SPAWN_NPC,
            [this](const Packet& packet) { HandleSpawnNPC(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::SPAWN_ITEM,
            [this](const Packet& packet) { HandleSpawnItem(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::DESPAWN_ENTITY,
            [this](const Packet& packet) { HandleDespawnEntity(packet); }
        );

        // Entity action packets
        m_networkManager->RegisterPacketHandler(
            PacketType::ENTITY_MOVE,
            [this](const Packet& packet) { HandleEntityMove(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ENTITY_ATTACK,
            [this](const Packet& packet) { HandleEntityAttack(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ENTITY_CAST,
            [this](const Packet& packet) { HandleEntityCast(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ENTITY_EFFECT,
            [this](const Packet& packet) { HandleEntityEffect(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ENTITY_STATE,
            [this](const Packet& packet) { HandleEntityState(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ENTITY_STRUCK,
            [this](const Packet& packet) { HandleEntityStruck(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ENTITY_DEATH,
            [this](const Packet& packet) { HandleEntityDeath(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ENTITY_SKELETON,
            [this](const Packet& packet) { HandleEntitySkeleton(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ENTITY_ALIVE,
            [this](const Packet& packet) { HandleEntityAlive(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ENTITY_LEVEL_UP,
            [this](const Packet& packet) { HandleEntityLevelUp(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ENTITY_CHANGE_NAME_COLOR,
            [this](const Packet& packet) { HandleEntityChangeNameColor(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ENTITY_CHANGE_LIGHT,
            [this](const Packet& packet) { HandleEntityChangeLight(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ENTITY_WIN_EXP,
            [this](const Packet& packet) { HandleEntityWinExp(packet); }
        );

        // Chat packets
        m_networkManager->RegisterPacketHandler(
            PacketType::CHAT_MESSAGE,
            [this](const Packet& packet) { HandleChatMessage(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::WHISPER_MESSAGE,
            [this](const Packet& packet) { HandleWhisperMessage(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::GUILD_MESSAGE,
            [this](const Packet& packet) { HandleGuildMessage(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::SYSTEM_MESSAGE,
            [this](const Packet& packet) { HandleSystemMessage(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::CRY_MESSAGE,
            [this](const Packet& packet) { HandleCryMessage(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::GROUP_MESSAGE,
            [this](const Packet& packet) { HandleGroupMessage(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::MERCHANT_SAY,
            [this](const Packet& packet) { HandleMerchantSay(packet); }
        );

        // Item packets
        m_networkManager->RegisterPacketHandler(
            PacketType::ADD_ITEM,
            [this](const Packet& packet) { HandleAddItem(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::BAG_ITEMS,
            [this](const Packet& packet) { HandleBagItems(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::DELETE_ITEM,
            [this](const Packet& packet) { HandleDeleteItem(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::UPDATE_ITEM,
            [this](const Packet& packet) { HandleUpdateItem(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::DROP_ITEM_RESULT,
            [this](const Packet& packet) { HandleDropItemResult(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::GOLD_CHANGED,
            [this](const Packet& packet) { HandleGoldChanged(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::WEIGHT_CHANGED,
            [this](const Packet& packet) { HandleWeightChanged(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::DURA_CHANGED,
            [this](const Packet& packet) { HandleDuraChanged(packet); }
        );

        // Door packets
        m_networkManager->RegisterPacketHandler(
            PacketType::OPEN_DOOR_RESULT,
            [this](const Packet& packet) { HandleOpenDoorResult(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::CLOSE_DOOR,
            [this](const Packet& packet) { HandleCloseDoor(packet); }
        );

        // Magic packets
        m_networkManager->RegisterPacketHandler(
            PacketType::ADD_MAGIC,
            [this](const Packet& packet) { HandleAddMagic(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::SEND_MY_MAGIC,
            [this](const Packet& packet) { HandleSendMyMagic(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::DELETE_MAGIC,
            [this](const Packet& packet) { HandleDeleteMagic(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::MAGIC_FIRE,
            [this](const Packet& packet) { HandleMagicFire(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::MAGIC_FIRE_FAIL,
            [this](const Packet& packet) { HandleMagicFireFail(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::MAGIC_LEVEL_EXP,
            [this](const Packet& packet) { HandleMagicLevelExp(packet); }
        );

        // Status packets
        m_networkManager->RegisterPacketHandler(
            PacketType::USER_STATE,
            [this](const Packet& packet) { HandleUserState(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::HEALTH_SPELL_CHANGED,
            [this](const Packet& packet) { HandleHealthSpellChanged(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ABILITY,
            [this](const Packet& packet) { HandleAbility(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::SUBABILITY,
            [this](const Packet& packet) { HandleSubAbility(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::ADJUST_BONUS,
            [this](const Packet& packet) { HandleAdjustBonus(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::OPEN_HEALTH,
            [this](const Packet& packet) { HandleOpenHealth(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::CLOSE_HEALTH,
            [this](const Packet& packet) { HandleCloseHealth(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::CHANGE_FACE,
            [this](const Packet& packet) { HandleChangeFace(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::BREAK_WEAPON,
            [this](const Packet& packet) { HandleBreakWeapon(packet); }
        );

        // Game flow packet handlers
        m_networkManager->RegisterPacketHandler(
            PacketType::START_PLAY,
            [this](const Packet& packet) { HandleStartPlay(packet); }
        );
        m_networkManager->RegisterPacketHandler(
            PacketType::RECONNECT,
            [this](const Packet& packet) { HandleReconnect(packet); }
        );
    }
}

PlayState::~PlayState()
{
    // Unregister packet handlers if network manager is provided
    if (m_networkManager) {
        // Map related packets
        m_networkManager->UnregisterPacketHandler(PacketType::MAP_DATA);
        m_networkManager->UnregisterPacketHandler(PacketType::MAP_CHANGE);
        m_networkManager->UnregisterPacketHandler(PacketType::CHANGE_MAP);
        m_networkManager->UnregisterPacketHandler(PacketType::CLEAR_OBJECTS);
        m_networkManager->UnregisterPacketHandler(PacketType::AREA_STATE);
        m_networkManager->UnregisterPacketHandler(PacketType::DAY_CHANGING);

        // Entity spawn packets
        m_networkManager->UnregisterPacketHandler(PacketType::SPAWN_PLAYER);
        m_networkManager->UnregisterPacketHandler(PacketType::SPAWN_MONSTER);
        m_networkManager->UnregisterPacketHandler(PacketType::SPAWN_NPC);
        m_networkManager->UnregisterPacketHandler(PacketType::SPAWN_ITEM);
        m_networkManager->UnregisterPacketHandler(PacketType::DESPAWN_ENTITY);

        // Entity action packets
        m_networkManager->UnregisterPacketHandler(PacketType::ENTITY_MOVE);
        m_networkManager->UnregisterPacketHandler(PacketType::ENTITY_ATTACK);
        m_networkManager->UnregisterPacketHandler(PacketType::ENTITY_CAST);
        m_networkManager->UnregisterPacketHandler(PacketType::ENTITY_EFFECT);
        m_networkManager->UnregisterPacketHandler(PacketType::ENTITY_STATE);
        m_networkManager->UnregisterPacketHandler(PacketType::ENTITY_STRUCK);
        m_networkManager->UnregisterPacketHandler(PacketType::ENTITY_DEATH);
        m_networkManager->UnregisterPacketHandler(PacketType::ENTITY_SKELETON);
        m_networkManager->UnregisterPacketHandler(PacketType::ENTITY_ALIVE);
        m_networkManager->UnregisterPacketHandler(PacketType::ENTITY_LEVEL_UP);
        m_networkManager->UnregisterPacketHandler(PacketType::ENTITY_CHANGE_NAME_COLOR);
        m_networkManager->UnregisterPacketHandler(PacketType::ENTITY_CHANGE_LIGHT);
        m_networkManager->UnregisterPacketHandler(PacketType::ENTITY_WIN_EXP);

        // Chat packets
        m_networkManager->UnregisterPacketHandler(PacketType::CHAT_MESSAGE);
        m_networkManager->UnregisterPacketHandler(PacketType::WHISPER_MESSAGE);
        m_networkManager->UnregisterPacketHandler(PacketType::GUILD_MESSAGE);
        m_networkManager->UnregisterPacketHandler(PacketType::SYSTEM_MESSAGE);
        m_networkManager->UnregisterPacketHandler(PacketType::CRY_MESSAGE);
        m_networkManager->UnregisterPacketHandler(PacketType::GROUP_MESSAGE);
        m_networkManager->UnregisterPacketHandler(PacketType::MERCHANT_SAY);

        // Item packets
        m_networkManager->UnregisterPacketHandler(PacketType::ADD_ITEM);
        m_networkManager->UnregisterPacketHandler(PacketType::BAG_ITEMS);
        m_networkManager->UnregisterPacketHandler(PacketType::DELETE_ITEM);
        m_networkManager->UnregisterPacketHandler(PacketType::UPDATE_ITEM);
        m_networkManager->UnregisterPacketHandler(PacketType::DROP_ITEM_RESULT);
        m_networkManager->UnregisterPacketHandler(PacketType::GOLD_CHANGED);
        m_networkManager->UnregisterPacketHandler(PacketType::WEIGHT_CHANGED);
        m_networkManager->UnregisterPacketHandler(PacketType::DURA_CHANGED);

        // Door packets
        m_networkManager->UnregisterPacketHandler(PacketType::OPEN_DOOR_RESULT);
        m_networkManager->UnregisterPacketHandler(PacketType::CLOSE_DOOR);

        // Magic packets
        m_networkManager->UnregisterPacketHandler(PacketType::ADD_MAGIC);
        m_networkManager->UnregisterPacketHandler(PacketType::SEND_MY_MAGIC);
        m_networkManager->UnregisterPacketHandler(PacketType::DELETE_MAGIC);
        m_networkManager->UnregisterPacketHandler(PacketType::MAGIC_FIRE);
        m_networkManager->UnregisterPacketHandler(PacketType::MAGIC_FIRE_FAIL);
        m_networkManager->UnregisterPacketHandler(PacketType::MAGIC_LEVEL_EXP);

        // Status packets
        m_networkManager->UnregisterPacketHandler(PacketType::USER_STATE);
        m_networkManager->UnregisterPacketHandler(PacketType::HEALTH_SPELL_CHANGED);
        m_networkManager->UnregisterPacketHandler(PacketType::ABILITY);
        m_networkManager->UnregisterPacketHandler(PacketType::SUBABILITY);
        m_networkManager->UnregisterPacketHandler(PacketType::ADJUST_BONUS);
        m_networkManager->UnregisterPacketHandler(PacketType::OPEN_HEALTH);
        m_networkManager->UnregisterPacketHandler(PacketType::CLOSE_HEALTH);
        m_networkManager->UnregisterPacketHandler(PacketType::CHANGE_FACE);
        m_networkManager->UnregisterPacketHandler(PacketType::BREAK_WEAPON);

        // Game flow packet handlers
        m_networkManager->UnregisterPacketHandler(PacketType::START_PLAY);
        m_networkManager->UnregisterPacketHandler(PacketType::RECONNECT);
    }
}

void PlayState::Enter()
{
    try {
        std::cout << "PlayState::Enter - Starting initialization..." << std::endl;

        // Create WIL manager
        std::cout << "PlayState::Enter - Creating WILManager..." << std::endl;
        m_wilManager = std::make_shared<WILManager>();

        // Initialize resource manager
        std::cout << "PlayState::Enter - Initializing ResourceManager..." << std::endl;
        m_resourceManager = ResourceManager::GetInstance();
        if (!m_resourceManager->Initialize(m_wilManager)) {
            std::cerr << "Failed to initialize resource manager" << std::endl;
        }

        // Register standard resource paths
        // Note: Default paths are already set in ResourceManager constructor
        std::cout << "PlayState::Enter - Resource paths initialized" << std::endl;

        // Create map manager
        std::cout << "PlayState::Enter - Creating MapManager..." << std::endl;
        m_mapManager = std::make_shared<MapManager>(m_wilManager, m_app->GetRenderer());

        // Create actor manager
        std::cout << "PlayState::Enter - Creating ActorManager..." << std::endl;
        m_actorManager = std::make_shared<ActorManager>(m_wilManager, m_app->GetRenderer());

        // 创建光照管理器
        std::cout << "PlayState::Enter - Creating LightManager..." << std::endl;
        m_lightManager = std::make_shared<LightManager>(m_app->GetRenderer());
        if (!m_lightManager->Initialize()) {
            std::cerr << "Failed to initialize light manager" << std::endl;
        }

        // 加载光照效果
        if (!m_lightManager->LoadLightEffects()) {
            std::cerr << "Failed to load light effects" << std::endl;
        }

        // 设置默认的雾效可见性
        m_viewFog = true;  // 默认显示雾效
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in PlayState::Enter (initialization): " << e.what() << std::endl;
        throw; // Re-throw to allow proper cleanup
    }
    catch (...) {
        std::cerr << "Unknown exception in PlayState::Enter (initialization)" << std::endl;
        throw; // Re-throw to allow proper cleanup
    }

    try {

        // Initialize skill manager
        std::cout << "PlayState::Enter - Initializing SkillManager..." << std::endl;
        SkillManager* skillManager = SkillManager::GetInstance();
        if (!skillManager->Initialize()) {
            std::cerr << "Failed to initialize skill manager" << std::endl;
        }

        // Initialize effect manager
        std::cout << "PlayState::Enter - Initializing EffectManager..." << std::endl;
        EffectManager* effectManager = EffectManager::GetInstance();
        if (!effectManager->Initialize(m_wilManager, m_app->GetRenderer())) {
            std::cerr << "Failed to initialize effect manager" << std::endl;
        }

        // 初始化聊天字体
        std::cout << "PlayState::Enter - Loading chat font..." << std::endl;
        m_chatFont = TTF_OpenFont("Fonts/arial.ttf", 14);
        if (!m_chatFont) {
            std::cerr << "Failed to load chat font: " << TTF_GetError() << std::endl;
            // 尝试加载备用字体
            m_chatFont = TTF_OpenFont("C:/Windows/Fonts/simhei.ttf", 14);
            if (!m_chatFont) {
                std::cerr << "Failed to load backup font: " << TTF_GetError() << std::endl;
            }
        }

        // Create chat manager
        std::cout << "PlayState::Enter - Creating ChatManager..." << std::endl;
        m_chatManager = std::make_shared<ChatManager>(m_networkManager, m_chatFont);

        // Initialize chat UI using UILayout constants
        std::cout << "PlayState::Enter - Initializing chat UI..." << std::endl;
        m_chatManager->Initialize(
            UILayout::ChatUI::WINDOW_X,
            UILayout::ChatUI::WINDOW_Y,
            UILayout::ChatUI::WINDOW_WIDTH,
            UILayout::ChatUI::WINDOW_HEIGHT,
            UILayout::ChatUI::WINDOW_X + UILayout::ChatUI::CHAT_INPUT_X_OFFSET,
            UILayout::ChatUI::WINDOW_Y + UILayout::ChatUI::CHAT_INPUT_Y_OFFSET,
            UILayout::ChatUI::CHAT_INPUT_WIDTH,
            UILayout::ChatUI::CHAT_INPUT_HEIGHT
        );

        // Set chat font for actor manager
        if (m_actorManager && m_chatFont) {
            m_actorManager->SetChatFont(m_chatFont);
        }

        // Create UI manager
        std::cout << "PlayState::Enter - Creating UIManager..." << std::endl;
        m_uiManager = std::make_shared<UIManager>(m_app->GetRenderer());
        m_uiManager->SetWILManager(m_wilManager);

        // Create damage number manager
        std::cout << "PlayState::Enter - Creating DamageNumberManager..." << std::endl;
        m_damageNumberManager = std::make_shared<DamageNumberManager>(m_app->GetRenderer());

        // Create skill panel
        std::cout << "PlayState::Enter - Creating UI panels..." << std::endl;
        auto player = m_actorManager->GetLocalPlayer();
        // Create a shared_ptr from the singleton instance
        auto skillManagerInstance = std::shared_ptr<SkillManager>(SkillManager::GetInstance(), [](SkillManager*){/* Don't delete singleton */});

        // Position skill panel in the center of the screen using UILayout constants
        m_skillPanel = std::make_shared<SkillPanel>(
            UILayout::SkillUI::WINDOW_X,
            UILayout::SkillUI::WINDOW_Y,
            UILayout::SkillUI::WINDOW_WIDTH,
            UILayout::SkillUI::WINDOW_HEIGHT,
            player, skillManagerInstance
        );
        m_skillPanel->SetWILManager(m_wilManager);
        m_skillPanel->Hide(); // Hide by default when entering the game
        m_uiManager->AddControl(m_skillPanel);

        // Create status panel using UILayout constants
        m_statusPanel = std::make_shared<StatusPanel>(
            UILayout::StatusUI::WINDOW_X,
            UILayout::StatusUI::WINDOW_Y,
            UILayout::StatusUI::WINDOW_WIDTH,
            UILayout::StatusUI::WINDOW_HEIGHT,
            player
        );
        m_statusPanel->SetWILManager(m_wilManager);
        m_statusPanel->Hide(); // Hide by default when entering the game
        m_uiManager->AddControl(m_statusPanel);

        // Create hotkey config UI using UILayout constants
        m_hotkeyConfigUI = std::make_shared<HotkeyConfigUI>(
            UILayout::HotkeyUI::WINDOW_X,
            UILayout::HotkeyUI::WINDOW_Y,
            UILayout::HotkeyUI::WINDOW_WIDTH,
            UILayout::HotkeyUI::WINDOW_HEIGHT,
            player, skillManagerInstance
        );
        m_hotkeyConfigUI->SetWILManager(m_wilManager);
        m_hotkeyConfigUI->Hide(); // Hide by default when entering the game
        m_uiManager->AddControl(m_hotkeyConfigUI);

        // Create main game UI
        m_gameUI = std::make_shared<GameUI>(
            0, 0, UILayout::SCREEN_WIDTH, UILayout::MainUI::BOTTOM_PANEL_HEIGHT,
            std::dynamic_pointer_cast<Player>(player)
        );
        m_gameUI->SetWILManager(m_wilManager);

        // Set up callbacks for GameUI buttons
        m_gameUI->SetOnStatusButtonClick([this]() {
            if (m_statusPanel) {
                if (m_statusPanel->IsVisible()) {
                    m_statusPanel->Hide();
                } else {
                    m_statusPanel->Show();
                }
            }
        });

        m_gameUI->SetOnSkillButtonClick([this]() {
            if (m_skillPanel) {
                if (m_skillPanel->IsVisible()) {
                    m_skillPanel->Hide();
                } else {
                    m_skillPanel->Show();
                }
            }
        });

        m_uiManager->AddControl(m_gameUI);

        // Preload common resources
        std::cout << "PlayState::Enter - Loading common resources..." << std::endl;

        // 创建必要的目录，与原始Delphi项目保持一致
        std::cout << "PlayState::Enter - Checking resource directories..." << std::endl;

        // 创建Data目录 - 用于存放资源文件
        if (system("mkdir -p Data") != 0) {
            std::cerr << "Failed to create Data directory" << std::endl;
        }

        // 创建Map目录 - 用于存放地图文件
        if (system("mkdir -p Map") != 0) {
            std::cerr << "Failed to create Map directory" << std::endl;
        }

        // 创建Config目录 - 用于存放配置文件
        if (system("mkdir -p Config") != 0) {
            std::cerr << "Failed to create Config directory" << std::endl;
        }

        // 创建Fonts目录 - 用于存放字体文件
        if (system("mkdir -p Fonts") != 0) {
            std::cerr << "Failed to create Fonts directory" << std::endl;
        }

        // 加载所有必要的资源，参考原始Delphi项目的MShare.pas中的资源加载
        std::cout << "PlayState::Enter - Loading all necessary resources..." << std::endl;

        // 主要UI资源
        std::cout << "PlayState::Enter - Loading MAIN resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::MAIN)) {
            std::cerr << "WARNING: Failed to load MAIN resources (" << ResourcePaths::MAIN << ")" << std::endl;
        }

        std::cout << "PlayState::Enter - Loading MAIN2 resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::MAIN2)) {
            std::cerr << "WARNING: Failed to load MAIN2 resources (" << ResourcePaths::MAIN2 << ")" << std::endl;
        }

        std::cout << "PlayState::Enter - Loading MAIN3 resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::MAIN3)) {
            std::cerr << "WARNING: Failed to load MAIN3 resources (" << ResourcePaths::MAIN3 << ")" << std::endl;
        }

        // 角色选择界面资源
        std::cout << "PlayState::Enter - Loading CHR_SELECT resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::CHR_SELECT)) {
            std::cerr << "WARNING: Failed to load CHR_SELECT resources (" << ResourcePaths::CHR_SELECT << ")" << std::endl;
        }

        // 小地图资源
        std::cout << "PlayState::Enter - Loading MINIMAP resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::MINIMAP)) {
            std::cerr << "WARNING: Failed to load MINIMAP resources (" << ResourcePaths::MINIMAP << ")" << std::endl;
        }

        // 地图资源
        std::cout << "PlayState::Enter - Loading TILES resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::TILES)) {
            std::cerr << "WARNING: Failed to load TILES resources (" << ResourcePaths::TILES << ")" << std::endl;
        }

        std::cout << "PlayState::Enter - Loading SMALL_TILES resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::SMALL_TILES)) {
            std::cerr << "WARNING: Failed to load SMALL_TILES resources (" << ResourcePaths::SMALL_TILES << ")" << std::endl;
        }

        std::cout << "PlayState::Enter - Loading OBJECTS resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::OBJECTS)) {
            std::cerr << "WARNING: Failed to load OBJECTS resources (" << ResourcePaths::OBJECTS << ")" << std::endl;
        }

        // 角色相关资源
        std::cout << "PlayState::Enter - Loading HUM_EFFECT resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::HUM_EFFECT)) {
            std::cerr << "WARNING: Failed to load HUM_EFFECT resources (" << ResourcePaths::HUM_EFFECT << ")" << std::endl;
        }

        std::cout << "PlayState::Enter - Loading HUMAN resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::HUMAN)) {
            std::cerr << "WARNING: Failed to load HUMAN resources (" << ResourcePaths::HUMAN << ")" << std::endl;
        }

        std::cout << "PlayState::Enter - Loading HAIR resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::HAIR)) {
            std::cerr << "WARNING: Failed to load HAIR resources (" << ResourcePaths::HAIR << ")" << std::endl;
        }

        std::cout << "PlayState::Enter - Loading WEAPON resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::WEAPON)) {
            std::cerr << "WARNING: Failed to load WEAPON resources (" << ResourcePaths::WEAPON << ")" << std::endl;
        }

        // 物品相关资源
        std::cout << "PlayState::Enter - Loading ITEMS resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::ITEMS)) {
            std::cerr << "WARNING: Failed to load ITEMS resources (" << ResourcePaths::ITEMS << ")" << std::endl;
        }

        std::cout << "PlayState::Enter - Loading STATE_ITEMS resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::STATE_ITEMS)) {
            std::cerr << "WARNING: Failed to load STATE_ITEMS resources (" << ResourcePaths::STATE_ITEMS << ")" << std::endl;
        }

        std::cout << "PlayState::Enter - Loading DROP_ITEMS resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::DROP_ITEMS)) {
            std::cerr << "WARNING: Failed to load DROP_ITEMS resources (" << ResourcePaths::DROP_ITEMS << ")" << std::endl;
        }

        // 魔法相关资源
        std::cout << "PlayState::Enter - Loading MAGIC_ICON resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::MAGIC_ICON)) {
            std::cerr << "WARNING: Failed to load MAGIC_ICON resources (" << ResourcePaths::MAGIC_ICON << ")" << std::endl;
        }

        // NPC资源
        std::cout << "PlayState::Enter - Loading NPC resources..." << std::endl;
        if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::NPC)) {
            std::cerr << "WARNING: Failed to load NPC resources (" << ResourcePaths::NPC << ")" << std::endl;
        }

        // In the original project, the map is loaded when the server sends the map name
        // We don't load a map here - it will be loaded when we receive the MAP_DATA or MAP_CHANGE packet
        std::cout << "PlayState::Enter - Waiting for server to send map data..." << std::endl;

        // Create player
        std::cout << "PlayState::Enter - Creating player..." << std::endl;
        if (!CreatePlayer()) {
            std::cerr << "Failed to create player" << std::endl;
        }

        // Spawn monsters
        std::cout << "PlayState::Enter - Spawning monsters..." << std::endl;
        if (!SpawnMonsters()) {
            std::cerr << "Failed to spawn monsters" << std::endl;
        }

        // Add welcome message to chat
        std::cout << "PlayState::Enter - Adding welcome message..." << std::endl;
        if (m_chatManager) {
            m_chatManager->AddSystemMessage("Welcome to the game!");
        }

        // Make player say a welcome message
        if (m_actorManager && m_actorManager->GetLocalPlayer()) {
            m_actorManager->ActorSay(m_actorManager->GetLocalPlayer()->GetId(), "Hello, world!");
        }

        std::cout << "PlayState::Enter - Initialization complete!" << std::endl;
        // Play game music
        // TODO: Play game music
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in PlayState::Enter (UI setup): " << e.what() << std::endl;
        throw; // Re-throw to allow proper cleanup
    }
    catch (...) {
        std::cerr << "Unknown exception in PlayState::Enter (UI setup)" << std::endl;
        throw; // Re-throw to allow proper cleanup
    }
}

void PlayState::Exit()
{
    // Clear managers
    m_chatManager.reset();
    m_skillPanel.reset();
    m_statusPanel.reset();
    m_hotkeyConfigUI.reset();
    m_gameUI.reset();
    m_uiManager.reset();
    m_damageNumberManager.reset();
    m_actorManager.reset();
    m_mapManager.reset();
    m_lightManager.reset();

    // Close chat font
    if (m_chatFont) {
        TTF_CloseFont(m_chatFont);
        m_chatFont = nullptr;
    }

    // Unload all resources
    if (m_resourceManager) {
        m_resourceManager->UnloadAllResources();
    }

    m_wilManager.reset();

    // Disconnect from server if connected
    if (m_networkManager && m_networkManager->IsConnected()) {
        m_networkManager->Disconnect();
    }

    // Release singleton managers
    EffectManager::ReleaseInstance();
    SkillManager::ReleaseInstance();
    // Note: We don't release ResourceManager here as it might be used by other states

    // Stop music
    // TODO: Stop game music
}

void PlayState::Update(float deltaTime)
{
    if (m_paused) {
        return;
    }

    // Update network manager
    if (m_networkManager) {
        m_networkManager->Update();
    }

    // Update chat manager
    if (m_chatManager) {
        m_chatManager->Update(static_cast<int>(deltaTime * 1000));
    }

    // Update UI manager
    if (m_uiManager) {
        m_uiManager->Update(static_cast<int>(deltaTime * 1000));
    }

    // Update damage number manager
    if (m_damageNumberManager) {
        m_damageNumberManager->Update(static_cast<int>(deltaTime * 1000));
    }

    // Update actors
    if (m_actorManager) {
        m_actorManager->Update(static_cast<int>(deltaTime * 1000));
    }

    // Update map
    if (m_mapManager) {
        // Update map animations
        m_mapManager->UpdateAnimations(static_cast<int>(deltaTime * 1000));

        // Center camera on player if available
        if (m_actorManager) {
            std::shared_ptr<Player> player = m_actorManager->GetLocalPlayer();
            if (player) {
                int playerX = player->GetX();
                int playerY = player->GetY();

                // Convert map coordinates to screen coordinates
                int screenX, screenY;
                m_mapManager->MapToScreen(playerX, playerY, screenX, screenY);

                // Center camera on player
                int cameraX = screenX - m_app->GetScreenWidth() / 2;
                int cameraY = screenY - m_app->GetScreenHeight() / 2;

                m_mapManager->SetCamera(cameraX, cameraY);
            } else {
                // If no player exists yet, center camera on the middle of the map
                if (m_mapManager->GetWidth() > 0 && m_mapManager->GetHeight() > 0) {
                    int mapCenterX = m_mapManager->GetWidth() / 2;
                    int mapCenterY = m_mapManager->GetHeight() / 2;

                    // Convert map coordinates to screen coordinates
                    int screenX, screenY;
                    m_mapManager->MapToScreen(mapCenterX, mapCenterY, screenX, screenY);

                    // Center camera on map center
                    int cameraX = screenX - m_app->GetScreenWidth() / 2;
                    int cameraY = screenY - m_app->GetScreenHeight() / 2;

                    m_mapManager->SetCamera(cameraX, cameraY);
                }
            }
        }
    }

    // Update light manager
    if (m_lightManager && m_actorManager && m_mapManager) {
        // Update fog visibility
        m_lightManager->SetViewFog(m_viewFog);

        // Clear light map
        if (m_lightManager->IsViewFog()) {
            m_lightManager->ClearLightMap();

            // Get player
            std::shared_ptr<Player> player = m_actorManager->GetLocalPlayer();

            // Determine center position for lighting
            int centerX, centerY;
            int shiftX = 0, shiftY = 0;
            bool isPlayer = false;

            if (player) {
                // Use player position as center
                centerX = player->GetX();
                centerY = player->GetY();
                shiftX = player->GetShiftX();
                shiftY = player->GetShiftY();
                isPlayer = true;
            } else {
                // Use map center as fallback
                centerX = m_mapManager->GetWidth() / 2;
                centerY = m_mapManager->GetHeight() / 2;
            }

            // Add lights from map cells
            // Get cells within a certain range of the center position
            int lightRange = 15; // Adjust this value based on visibility range

            auto cellsInRange = m_mapManager->GetCellsInRange(centerX, centerY, lightRange);
            for (const auto& cellTuple : cellsInRange) {
                int x = std::get<0>(cellTuple);
                int y = std::get<1>(cellTuple);
                MapCell* cell = std::get<2>(cellTuple);

                // Check if cell has light
                if (cell && cell->GetLight() > 0) {
                    // Add light to light manager
                    m_lightManager->AddLight(
                        x,
                        y,
                        0,  // No shift for map cells
                        0,
                        cell->GetLight(),
                        false  // Use overlap check
                    );
                }
            }

            // Add lights from actors
            for (const auto& actor : m_actorManager->GetActors()) {
                if (actor->IsVisible() && actor->GetLight() > 0) {
                    m_lightManager->AddLight(
                        actor->GetX(),
                        actor->GetY(),
                        actor->GetShiftX(),
                        actor->GetShiftY(),
                        actor->GetLight(),
                        isPlayer && (actor == player)  // nocheck for player
                    );
                }
            }

            // Add lights from effects
            auto effectManager = EffectManager::GetInstance();
            if (effectManager) {
                // Update effects
                effectManager->Update(static_cast<int>(deltaTime * 1000));

                // TODO: Add lights from effects when implemented in EffectManager
            }

            // Apply light map
            m_lightManager->ApplyLightMap(
                centerX,
                centerY,
                shiftX,
                shiftY,
                0,  // mapLeft
                0   // mapTop
            );
        }
    }
}

void PlayState::Render()
{
    // Render map
    if (m_mapManager) {
        m_mapManager->Render(m_app->GetRenderer());
    }

    // Render actors
    if (m_actorManager) {
        m_actorManager->Render();
    }

    // Render skill effects
    auto effectManager = EffectManager::GetInstance();
    if (effectManager) {
        effectManager->Render();
    }

    // Render fog/lighting effects
    if (m_lightManager && m_lightManager->IsViewFog()) {
        // Get the current render target
        SDL_Texture* target = SDL_GetRenderTarget(m_app->GetRenderer());

        // Render fog
        m_lightManager->RenderFog(target);
    }

    // Render chat UI
    if (m_chatManager) {
        m_chatManager->Render(m_app->GetRenderer());
    }

    // Render damage numbers
    if (m_damageNumberManager) {
        m_damageNumberManager->Render();
    }

    // Render UI elements
    if (m_uiManager) {
        m_uiManager->Render();
    }
}

void PlayState::HandleEvents(SDL_Event& event)
{
    // Handle chat events first
    if (m_chatManager) {
        bool handled = false;

        switch (event.type) {
            case SDL_KEYDOWN:
                handled = m_chatManager->OnKeyDown(event.key);
                break;
            case SDL_TEXTINPUT:
                handled = m_chatManager->OnTextInput(event.text);
                break;
            case SDL_MOUSEBUTTONDOWN:
                handled = m_chatManager->OnMouseDown(event.button);
                break;
            case SDL_MOUSEBUTTONUP:
                handled = m_chatManager->OnMouseUp(event.button);
                break;
            case SDL_MOUSEMOTION:
                handled = m_chatManager->OnMouseMove(event.motion);
                break;
            case SDL_MOUSEWHEEL:
                handled = m_chatManager->OnMouseWheel(event.wheel);
                break;
        }

        // If chat handled the event, don't process it further
        if (handled) {
            return;
        }
    }

    // Handle UI events
    if (m_uiManager) {
        bool handled = m_uiManager->HandleEvent(event);

        // If UI handled the event, don't process it further
        if (handled) {
            return;
        }
    }

    // Handle game events
    if (event.type == SDL_KEYDOWN) {
        switch (event.key.keysym.sym) {
            case SDLK_ESCAPE:
                // Open menu
                m_app->ChangeState(std::make_unique<MenuState>(m_app, this));
                break;
            case SDLK_p:
                // Toggle pause
                if (m_paused) {
                    Resume();
                } else {
                    Pause();
                }
                break;
            case SDLK_SPACE:
                // If chat input is not visible, toggle door
                if (!m_chatManager || !m_chatManager->GetChatInput()->IsVisible()) {
                    if (m_actorManager && m_mapManager) {
                        std::shared_ptr<Player> player = m_actorManager->GetLocalPlayer();
                        if (player) {
                            int playerX = player->GetX();
                            int playerY = player->GetY();

                            // Check adjacent cells for doors
                            for (int dy = -1; dy <= 1; dy++) {
                                for (int dx = -1; dx <= 1; dx++) {
                                    if (dx == 0 && dy == 0) continue;  // Skip player's cell

                                    int doorX = playerX + dx;
                                    int doorY = playerY + dy;

                                    // Try to toggle door
                                    if (m_mapManager->ToggleDoor(doorX, doorY)) {
                                        // Door toggled successfully
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                break;
            case SDLK_f:
                // Toggle fog
                SetViewFog(!IsViewFog());
                break;
            case SDLK_RETURN:
            case SDLK_KP_ENTER:
                // Show chat input if not already visible
                if (m_chatManager && !m_chatManager->GetChatInput()->IsVisible()) {
                    m_chatManager->ShowChatInput();
                }
                break;
            case SDLK_F10:
                // Toggle status panel (F10 key)
                if (m_statusPanel) {
                    if (m_statusPanel->IsVisible()) {
                        m_statusPanel->Hide();
                    } else {
                        m_statusPanel->Show();
                    }
                }
                break;
            case SDLK_F11:
                // Toggle skill UI (F11 key)
                if (m_skillPanel) {
                    if (m_skillPanel->IsVisible()) {
                        m_skillPanel->Hide();
                    } else {
                        m_skillPanel->Show();
                    }
                }
                break;
            case SDLK_k:
                // Toggle skill UI (alternative key)
                if (m_skillPanel) {
                    if (m_skillPanel->IsVisible()) {
                        m_skillPanel->Hide();
                    } else {
                        m_skillPanel->Show();
                    }
                }
                break;
            case SDLK_h:
                // Toggle hotkey config UI
                if (m_hotkeyConfigUI) {
                    if (m_hotkeyConfigUI->IsVisible()) {
                        m_hotkeyConfigUI->Hide();
                    } else {
                        m_hotkeyConfigUI->Show();
                    }
                }
                break;
        }
    }
}

void PlayState::Pause()
{
    m_paused = true;
}

void PlayState::Resume()
{
    m_paused = false;
}

bool PlayState::LoadMap(const std::string& mapName)
{
    try {
        if (!m_mapManager) {
            std::cerr << "LoadMap: MapManager is null" << std::endl;
            return false;
        }

        // 使用与原始Delphi项目相同的路径格式
        // 原始项目使用 Map/ 目录存放地图文件
        std::string mapFile = ResourcePaths::MAP_DIR + mapName + ".map";

        // 构建tileset名称 - 使用Data目录，与原始项目保持一致
        // 原始项目中，tileset文件名格式为 Data/mapName_background.wil, Data/mapName_middle.wil, Data/mapName_object.wil
        std::string tilesetName = "Data/" + mapName;

        std::cout << "LoadMap: Loading map file: " << mapFile << std::endl;
        std::cout << "LoadMap: Using tileset: " << tilesetName << std::endl;

        // 检查地图文件是否存在
        std::ifstream mapFileCheck(mapFile);
        if (!mapFileCheck.good()) {
            std::cerr << "LoadMap: Map file does not exist: " << mapFile << std::endl;
            std::cerr << "LoadMap: Creating an empty map instead" << std::endl;

            // 创建一个空地图作为替代
            bool result = m_mapManager->CreateEmptyMap(100, 100, tilesetName);
            if (!result) {
                std::cerr << "LoadMap: Failed to create empty map" << std::endl;
                return false;
            }

            std::cout << "LoadMap: Successfully created empty map" << std::endl;
            return true;
        }

        // 地图文件存在，尝试加载
        bool result = m_mapManager->LoadMap(mapFile, tilesetName);
        if (!result) {
            std::cerr << "LoadMap: Failed to load map: " << mapName << std::endl;

            // 创建一个空地图作为替代
            std::cerr << "LoadMap: Creating an empty map as fallback" << std::endl;
            result = m_mapManager->CreateEmptyMap(100, 100, tilesetName);
            if (!result) {
                std::cerr << "LoadMap: Failed to create empty map" << std::endl;
                return false;
            }

            std::cout << "LoadMap: Successfully created empty map as fallback" << std::endl;
            return true;
        } else {
            std::cout << "LoadMap: Successfully loaded map: " << mapName << std::endl;

            // 在原始项目中，加载地图后会更新地图名称
            m_mapManager->SetMapName(mapName);
        }
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in LoadMap: " << e.what() << std::endl;
        return false;
    }
    catch (...) {
        std::cerr << "Unknown exception in LoadMap" << std::endl;
        return false;
    }
}

bool PlayState::CreatePlayer()
{
    try {
        if (!m_actorManager) {
            std::cerr << "CreatePlayer: ActorManager is null" << std::endl;
            return false;
        }

        std::cout << "CreatePlayer: In the original project, player data is sent by the server" << std::endl;
        std::cout << "CreatePlayer: Waiting for server to send player data..." << std::endl;

        // In the original project, player data is sent by the server
        // We don't create a player here - it will be created when we receive the appropriate packet

        // Preload player-related resources
        if (m_resourceManager) {
            std::cout << "CreatePlayer: Preloading player resources..." << std::endl;
            // Load human images if not already loaded
            if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::HUMAN)) {
                std::cerr << "CreatePlayer: Failed to load HUMAN resources" << std::endl;
            }

            // Load weapon images if not already loaded
            if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::WEAPON)) {
                std::cerr << "CreatePlayer: Failed to load WEAPON resources" << std::endl;
            }

            // Load hair images if not already loaded
            if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::HAIR)) {
                std::cerr << "CreatePlayer: Failed to load HAIR resources" << std::endl;
            }
        } else {
            std::cerr << "CreatePlayer: ResourceManager is null" << std::endl;
        }

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in CreatePlayer: " << e.what() << std::endl;
        return false;
    }
    catch (...) {
        std::cerr << "Unknown exception in CreatePlayer" << std::endl;
        return false;
    }
}

bool PlayState::SpawnMonsters()
{
    try {
        if (!m_actorManager || !m_mapManager) {
            std::cerr << "SpawnMonsters: ActorManager or MapManager is null" << std::endl;
            return false;
        }

        std::cout << "SpawnMonsters: In the original project, monsters are spawned by the server" << std::endl;
        std::cout << "SpawnMonsters: Waiting for server to send monster spawn packets..." << std::endl;

        // In the original project, monsters are spawned by the server
        // We don't spawn monsters here - they will be spawned when we receive SPAWN_MONSTER packets

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception in SpawnMonsters: " << e.what() << std::endl;
        return false;
    }
    catch (...) {
        std::cerr << "Unknown exception in SpawnMonsters" << std::endl;
        return false;
    }
}

void PlayState::SetViewFog(bool viewFog)
{
    m_viewFog = viewFog;

    if (m_lightManager) {
        m_lightManager->SetViewFog(viewFog);
    }
}

bool PlayState::IsViewFog() const
{
    if (m_lightManager) {
        return m_lightManager->IsViewFog();
    }

    return m_viewFog;
}

void PlayState::AddLight(int x, int y, int shiftX, int shiftY, int light, bool nocheck)
{
    // 检查光照管理器是否存在
    if (!m_lightManager) {
        return;
    }

    // 调用光照管理器的AddLight方法
    m_lightManager->AddLight(x, y, shiftX, shiftY, light, nocheck);
}

// Map packet handlers
void PlayState::HandleMapData(const Packet& packet)
{
    // 从数据包中提取地图数据
    std::string mapName;
    int32_t mapWidth, mapHeight;

    packet >> mapName >> mapWidth >> mapHeight;

    // 加载地图
    if (m_mapManager) {
        // 使用我们的LoadMap方法，它会正确构建文件路径
        LoadMap(mapName);

        // 设置地图尺寸（如果需要）
        // 在原始Delphi项目中，地图尺寸是从地图文件中读取的
        // 但我们也可以从服务器接收到的数据中设置
        if (mapWidth > 0 && mapHeight > 0) {
            std::cout << "HandleMapData: Setting map dimensions to " << mapWidth << "x" << mapHeight << std::endl;
            // 如果MapManager有设置尺寸的方法，可以在这里调用
        }

        // 设置地图名称
        m_mapManager->SetMapName(mapName);

        // 在原始Delphi项目中，这里会更新全局变量g_sMapTitle
        std::cout << "HandleMapData: Map title set to " << mapName << std::endl;

        // 在原始Delphi项目中，这里会初始化光照信息
        // 在MapManager的InitializeLightMap方法中已经处理了光照信息的初始化
        // 但我们需要在这里处理光照效果的应用
        if (m_lightManager) {
            // 清除光照图
            m_lightManager->ClearLightMap();

            // 添加玩家光照
            if (m_actorManager && m_actorManager->GetLocalPlayer()) {
                int playerX = m_actorManager->GetLocalPlayer()->GetX();
                int playerY = m_actorManager->GetLocalPlayer()->GetY();
                AddLight(playerX, playerY, 0, 0, 3, true); // 玩家光照级别为3
            }
        }
    }
}

void PlayState::HandleMapChange(const Packet& packet)
{
    // 从数据包中提取地图名称和位置
    std::string mapName;
    int32_t x, y;

    packet >> mapName >> x >> y;

    // 加载新地图并设置玩家位置
    if (m_mapManager) {
        std::cout << "HandleMapChange: Changing map to " << mapName << " at position " << x << "," << y << std::endl;

        // 使用我们的LoadMap方法，它会正确构建文件路径
        LoadMap(mapName);

        // 设置玩家位置（如果角色管理器存在）
        if (m_actorManager && m_actorManager->GetLocalPlayer()) {
            m_actorManager->GetLocalPlayer()->SetPosition(x, y);

            // 在原始Delphi项目中，这里会更新玩家位置并清除其他对象
            std::cout << "HandleMapChange: Player position set to " << x << "," << y << std::endl;
        }

        // 在原始Delphi项目中，这里会更新全局变量g_sMapTitle
        std::cout << "HandleMapChange: Map title set to " << mapName << std::endl;

        // 处理光照信息
        if (m_lightManager) {
            // 清除光照图
            m_lightManager->ClearLightMap();

            // 添加玩家光照
            if (m_actorManager && m_actorManager->GetLocalPlayer()) {
                AddLight(x, y, 0, 0, 3, true); // 玩家光照级别为3
            }
        }
    }
}

void PlayState::HandleChangeMap(const Packet& packet)
{
    // 与HandleMapChange类似，但参数可能不同
    // 在原始Delphi项目中，这两个处理函数处理不同的数据包类型
    std::string mapName;
    int32_t x, y;

    packet >> mapName >> x >> y;

    // 加载新地图并设置玩家位置
    if (m_mapManager) {
        std::cout << "HandleChangeMap: Changing map to " << mapName << " at position " << x << "," << y << std::endl;

        // 使用我们的LoadMap方法，它会正确构建文件路径
        LoadMap(mapName);

        // 设置玩家位置（如果角色管理器存在）
        if (m_actorManager && m_actorManager->GetLocalPlayer()) {
            m_actorManager->GetLocalPlayer()->SetPosition(x, y);

            // 在原始Delphi项目中，这里会更新玩家位置并清除其他对象
            std::cout << "HandleChangeMap: Player position set to " << x << "," << y << std::endl;
        }

        // 在原始Delphi项目中，这里会更新全局变量g_sMapTitle
        std::cout << "HandleChangeMap: Map title set to " << mapName << std::endl;

        // 处理光照信息
        if (m_lightManager) {
            // 清除光照图
            m_lightManager->ClearLightMap();

            // 添加玩家光照
            if (m_actorManager && m_actorManager->GetLocalPlayer()) {
                AddLight(x, y, 0, 0, 3, true); // 玩家光照级别为3
            }
        }
    }
}

void PlayState::HandleClearObjects(const Packet& packet)
{
    // Clear all objects from the map
    if (m_actorManager) {
        m_actorManager->ClearNonPlayerActors();
    }
}

void PlayState::HandleAreaState(const Packet& packet)
{
    // Extract area state from packet
    int32_t x, y, state;

    packet >> x >> y >> state;

    // Update area state in map manager
    if (m_mapManager) {
        m_mapManager->SetAreaState(x, y, state);
    }
}

void PlayState::HandleDayChanging(const Packet& packet)
{
    // Extract day/night state from packet
    int32_t dayState;

    packet >> dayState;

    // Update day/night state
    if (m_lightManager) {
        m_lightManager->SetDayState(dayState);
    }
}

// Entity spawn packet handlers
void PlayState::HandleSpawnPlayer(const Packet& packet)
{
    // Extract player data from packet
    int32_t id, x, y, direction, appearance;
    std::string name;

    packet >> id >> x >> y >> direction >> appearance >> name;

    // Spawn player in actor manager
    if (m_actorManager) {
        // Convert appearance to PlayerClass
        PlayerClass playerClass = PlayerClass::WARRIOR; // Default
        switch (appearance) {
            case 0: playerClass = PlayerClass::WARRIOR; break;
            case 1: playerClass = PlayerClass::WIZARD; break;
            case 2: playerClass = PlayerClass::TAOIST; break;
            case 3: playerClass = PlayerClass::ASSASSIN; break;
        }

        m_actorManager->SpawnPlayer(id, name, playerClass, x, y);
    }
}

void PlayState::HandleSpawnMonster(const Packet& packet)
{
    // Extract monster data from packet
    int32_t id, x, y, direction, appearance;
    std::string name;

    packet >> id >> x >> y >> direction >> appearance >> name;

    // Spawn monster in actor manager
    if (m_actorManager) {
        // Convert appearance to MonsterType
        MonsterType monsterType = static_cast<MonsterType>(appearance);
        int level = 1; // Default level, could be extracted from packet if available

        m_actorManager->SpawnMonster(id, name, monsterType, level, x, y);
    }
}

void PlayState::HandleSpawnNPC(const Packet& packet)
{
    // Extract NPC data from packet
    int32_t id, x, y, direction, appearance;
    std::string name;

    packet >> id >> x >> y >> direction >> appearance >> name;

    // Spawn NPC in actor manager
    if (m_actorManager) {
        m_actorManager->SpawnNPC(id, name, appearance, x, y);
    }
}

void PlayState::HandleSpawnItem(const Packet& packet)
{
    // Extract item data from packet
    int32_t id, x, y, itemType, itemLook;

    packet >> id >> x >> y >> itemType >> itemLook;

    // Spawn item in actor manager
    if (m_actorManager) {
        m_actorManager->SpawnItem(id, itemType, itemLook, x, y);
    }
}

void PlayState::HandleDespawnEntity(const Packet& packet)
{
    // Extract entity ID from packet
    int32_t id;

    packet >> id;

    // Despawn entity in actor manager
    if (m_actorManager) {
        m_actorManager->DespawnActor(id);
    }
}

// Entity action packet handlers
void PlayState::HandleEntityMove(const Packet& packet)
{
    // Extract entity movement data from packet
    int32_t id, x, y, direction, moveType;

    packet >> id >> x >> y >> direction >> moveType;

    // Move entity in actor manager
    if (m_actorManager) {
        m_actorManager->MoveActor(id, x, y, direction, moveType);
    }
}

void PlayState::HandleEntityAttack(const Packet& packet)
{
    // Extract entity attack data from packet
    int32_t id, targetId, attackType;

    packet >> id >> targetId >> attackType;

    // Perform attack in actor manager
    if (m_actorManager) {
        m_actorManager->ActorAttack(id, targetId, attackType);
    }
}

void PlayState::HandleEntityCast(const Packet& packet)
{
    // Extract entity cast data from packet
    int32_t id, targetId, skillId, skillLevel;

    packet >> id >> targetId >> skillId >> skillLevel;

    // Perform cast in actor manager
    if (m_actorManager) {
        m_actorManager->ActorCast(id, targetId, skillId, skillLevel);
    }
}

void PlayState::HandleEntityEffect(const Packet& packet)
{
    // Extract entity effect data from packet
    int32_t id, effectId;

    packet >> id >> effectId;

    // Apply effect to entity in actor manager
    if (m_actorManager) {
        m_actorManager->ApplyEffectToActor(id, effectId);
    }
}

// Chat packet handlers
void PlayState::HandleChatMessage(const Packet& packet)
{
    // Extract chat message data from packet
    int32_t id;
    std::string message;

    packet >> id >> message;

    // Display chat message in chat window
    if (m_chatManager) {
        // Get actor name if available
        std::string senderName = "";
        if (m_actorManager) {
            std::shared_ptr<Actor> actor = m_actorManager->GetActor(id);
            if (actor) {
                senderName = actor->GetName();
            }
        }

        m_chatManager->AddChatMessage(id, senderName, message);
    }

    // Also display chat bubble above actor
    if (m_actorManager) {
        m_actorManager->ActorSay(id, message);
    }
}

void PlayState::HandleWhisperMessage(const Packet& packet)
{
    // Extract whisper message data from packet
    std::string sender, message;

    packet >> sender >> message;

    // Display whisper message in chat window
    if (m_chatManager) {
        m_chatManager->AddWhisperMessage(sender, message);
    }
}

void PlayState::HandleSystemMessage(const Packet& packet)
{
    // Extract system message data from packet
    std::string message;

    packet >> message;

    // Display system message in chat window
    if (m_chatManager) {
        m_chatManager->AddSystemMessage(message);
    }
}

// Chat message handlers
void PlayState::HandleGuildMessage(const Packet& packet) {
    // Extract guild message data from packet
    std::string sender, message;
    packet >> sender >> message;

    // Display guild message in chat window
    if (m_chatManager) {
        m_chatManager->AddGuildMessage(sender, message);
    }
}

void PlayState::HandleCryMessage(const Packet& packet) {
    // Extract cry message data from packet
    std::string message;
    packet >> message;

    // Display cry message in chat window
    if (m_chatManager) {
        m_chatManager->AddCryMessage(message);
    }
}

void PlayState::HandleGroupMessage(const Packet& packet) {
    // Extract group message data from packet
    std::string sender, message;
    packet >> sender >> message;

    // Display group message in chat window
    if (m_chatManager) {
        m_chatManager->AddGroupMessage(sender, message);
    }
}

void PlayState::HandleMerchantSay(const Packet& packet) {
    // Extract merchant say data from packet
    int32_t id;
    std::string message;
    packet >> id >> message;

    // Display merchant message in chat window
    if (m_chatManager) {
        // Get merchant name if available
        std::string senderName = "";
        if (m_actorManager) {
            std::shared_ptr<Actor> actor = m_actorManager->GetActor(id);
            if (actor) {
                senderName = actor->GetName();
            }
        }

        m_chatManager->AddMerchantMessage(id, senderName, message);
    }

    // Also display chat bubble above merchant
    if (m_actorManager) {
        m_actorManager->ActorSay(id, message);
    }
}

// Entity state handlers
void PlayState::HandleEntityState(const Packet& packet) {
    // Extract entity state data from packet
    int32_t id, feature, status;
    packet >> id >> feature >> status;

    // Update entity state in actor manager
    if (m_actorManager) {
        std::shared_ptr<Actor> actor = m_actorManager->GetActor(id);
        if (actor) {
            // Update actor appearance and status
            actor->SetAppearance(feature);
            actor->SetStatus(status);
        }
    }
}

void PlayState::HandleEntityStruck(const Packet& packet) {
    // Extract entity struck data from packet
    int32_t id, attackerId, damage, currentHP, maxHP;
    packet >> id >> attackerId >> damage >> currentHP >> maxHP;

    // Apply damage to entity in actor manager
    if (m_actorManager) {
        std::shared_ptr<Actor> actor = m_actorManager->GetActor(id);
        if (actor) {
            // Update actor health
            actor->SetHealth(currentHP);
            actor->SetMaxHealth(maxHP);

            // Apply damage effect
            actor->TakeDamage(damage);

            // If this is the local player, play hit sound
            if (actor == m_actorManager->GetLocalPlayer()) {
                // TODO: Play hit sound
            }

            // Show damage number
            if (m_damageNumberManager) {
                // Get actor position
                int x = actor->GetX();
                int y = actor->GetY();

                // Convert map coordinates to screen coordinates
                int screenX, screenY;
                if (m_mapManager) {
                    m_mapManager->MapToScreen(x, y, screenX, screenY);

                    // Adjust position to be above the actor
                    screenY -= 30;  // Offset above the actor

                    // Determine damage type
                    DamageType damageType = DamageType::NORMAL;

                    // Check if it's a critical hit (for example, if damage > 100)
                    if (damage > 100) {
                        damageType = DamageType::CRITICAL;
                    }
                    // Check if it's a miss (damage = 0)
                    else if (damage == 0) {
                        damageType = DamageType::MISS;
                    }
                    // Check if it's healing (negative damage)
                    else if (damage < 0) {
                        damageType = DamageType::HEAL;
                        damage = -damage;  // Convert to positive for display
                    }

                    // Add damage number
                    m_damageNumberManager->AddDamageNumber(screenX, screenY, damage, damageType);
                }
            }
        }
    }
}

void PlayState::HandleEntityDeath(const Packet& packet) {
    // Extract entity death data from packet
    int32_t id, x, y, damage, feature, status;
    packet >> id >> x >> y >> damage >> feature >> status;

    // Handle entity death in actor manager
    if (m_actorManager) {
        std::shared_ptr<Actor> actor = m_actorManager->GetActor(id);
        if (actor) {
            // Set actor position
            actor->SetPosition(x, y);

            // Update appearance and status
            actor->SetAppearance(feature);
            actor->SetStatus(status);

            // Set health to 0
            actor->SetHealth(0);

            // Set dying state
            actor->SetState(ActorState::DYING);

            // Play death sound
            // TODO: Play death sound
        }
    }
}

void PlayState::HandleEntitySkeleton(const Packet& packet) {
    // Extract entity skeleton data from packet
    int32_t id, hp, maxHP, damage, feature, status;
    packet >> id >> hp >> maxHP >> damage >> feature >> status;

    // Handle entity becoming a skeleton in actor manager
    if (m_actorManager) {
        // Create a new skeleton actor or update existing one
        std::shared_ptr<Actor> actor = m_actorManager->GetActor(id);
        if (!actor) {
            // Create new skeleton actor
            // TODO: Implement skeleton actor creation
        } else {
            // Update existing actor to skeleton
            actor->SetAppearance(feature);
            actor->SetStatus(status);
            actor->SetHealth(hp);
            actor->SetMaxHealth(maxHP);
            actor->SetState(ActorState::DEAD); // Use DEAD state for skeletons
        }
    }
}

void PlayState::HandleEntityAlive(const Packet& packet) {
    // Extract entity alive data from packet
    int32_t id, feature, status;
    packet >> id >> feature >> status;

    // Handle entity coming back to life in actor manager
    if (m_actorManager) {
        std::shared_ptr<Actor> actor = m_actorManager->GetActor(id);
        if (actor) {
            // Update appearance and status
            actor->SetAppearance(feature);
            actor->SetStatus(status);

            // Set alive state
            actor->SetState(ActorState::IDLE);

            // Play resurrection effect
            // TODO: Play resurrection effect
        }
    }
}

void PlayState::HandleEntityLevelUp(const Packet& packet) {
    // Extract entity level up data from packet
    int32_t id;
    packet >> id;

    // Handle entity level up in actor manager
    if (m_actorManager) {
        std::shared_ptr<Actor> actor = m_actorManager->GetActor(id);
        if (actor) {
            // Play level up effect
            auto effectManager = EffectManager::GetInstance();
            if (effectManager) {
                // TODO: Create level up effect
                // effectManager->CreateLevelUpEffect(actor->GetX(), actor->GetY());
            }

            // Play level up sound
            // TODO: Play level up sound

            // If this is the local player, show level up message
            if (actor == m_actorManager->GetLocalPlayer()) {
                // TODO: Show level up message
            }
        }
    }
}

void PlayState::HandleEntityChangeNameColor(const Packet& packet) {
    // Extract entity name color change data from packet
    int32_t id, color;
    packet >> id >> color;

    // Handle entity name color change in actor manager
    if (m_actorManager) {
        std::shared_ptr<Actor> actor = m_actorManager->GetActor(id);
        if (actor) {
            // Update name color
            actor->SetNameColor(color);
        }
    }
}

void PlayState::HandleEntityChangeLight(const Packet& packet) {
    // Extract entity light change data from packet
    int32_t id, light;
    packet >> id >> light;

    // Handle entity light change in actor manager
    if (m_actorManager) {
        std::shared_ptr<Actor> actor = m_actorManager->GetActor(id);
        if (actor) {
            // Update light level
            actor->SetLight(light);
        }
    }
}

void PlayState::HandleEntityWinExp(const Packet& packet) {
    // Extract entity win exp data from packet
    int32_t id, exp;
    packet >> id >> exp;

    // Handle entity winning experience in actor manager
    if (m_actorManager) {
        std::shared_ptr<Actor> actor = m_actorManager->GetActor(id);
        if (actor) {
            // If this is the local player, update experience
            if (actor == m_actorManager->GetLocalPlayer()) {
                auto player = std::dynamic_pointer_cast<Player>(actor);
                if (player) {
                    // Update player experience
                    bool leveledUp = player->AddExperience(exp);

                    // Show experience gain message
                    if (m_chatManager) {
                        std::stringstream ss;
                        ss << "获得经验值: " << exp;
                        m_chatManager->AddSystemMessage(ss.str());

                        // Show level up message if leveled up
                        if (leveledUp) {
                            ss.str("");
                            ss << "恭喜你升级了! 当前等级: " << player->GetLevel();
                            m_chatManager->AddSystemMessage(ss.str());

                            // Play level up effect
                            auto effectManager = EffectManager::GetInstance();
                            if (effectManager) {
                                // TODO: Create level up effect
                                // effectManager->CreateLevelUpEffect(player->GetX(), player->GetY());
                            }

                            // Play level up sound
                            // TODO: Play level up sound
                        }
                    }
                }
            }
        }
    }
}
void PlayState::HandleAddItem(const Packet& packet) {
    // Extract add item data from packet
    int32_t slot, itemType, itemLook, identified;
    std::string itemName;
    std::vector<uint8_t> values;
    int32_t makeIndex, durability, maxDurability;

    packet >> slot >> itemType >> itemLook >> identified >> itemName >> makeIndex >> durability >> maxDurability;

    // Read additional values (up to 14 values as in the original TUserItem)
    for (int i = 0; i < 14; i++) {
        uint8_t value;
        packet >> value;
        values.push_back(value);
    }

    // Add item to player's inventory
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = std::dynamic_pointer_cast<Player>(m_actorManager->GetLocalPlayer());
        if (player) {
            // Create a new item
            auto item = std::make_shared<Item>(itemType, static_cast<ItemType>(itemType), itemLook, itemName, identified == 1);
            item->SetMakeIndex(makeIndex);
            item->SetDurability(durability);
            item->SetMaxDurability(maxDurability);
            item->SetValues(values);

            // Add the item to the inventory
            player->AddItem(item, slot);

            // Play item pickup sound
            // TODO: Play item pickup sound
        }
    }
}

void PlayState::HandleBagItems(const Packet& packet) {
    // Extract bag items data from packet
    int32_t itemCount;
    packet >> itemCount;

    // Get the player
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = std::dynamic_pointer_cast<Player>(m_actorManager->GetLocalPlayer());
        if (player) {
            // Get the inventory
            auto inventory = player->GetInventory();
            if (inventory) {
                // Clear current inventory
                inventory->Clear();

                // Read each item
                for (int i = 0; i < itemCount; i++) {
                    int32_t slot, itemType, itemLook, identified;
                    std::string itemName;
                    std::vector<uint8_t> values;
                    int32_t makeIndex, durability, maxDurability;

                    packet >> slot >> itemType >> itemLook >> identified >> itemName >> makeIndex >> durability >> maxDurability;

                    // Read additional values (up to 14 values as in the original TUserItem)
                    for (int j = 0; j < 14; j++) {
                        uint8_t value;
                        packet >> value;
                        values.push_back(value);
                    }

                    // Create a new item
                    auto item = std::make_shared<Item>(itemType, static_cast<ItemType>(itemType), itemLook, itemName, identified == 1);
                    item->SetMakeIndex(makeIndex);
                    item->SetDurability(durability);
                    item->SetMaxDurability(maxDurability);
                    item->SetValues(values);

                    // Add the item to the inventory
                    inventory->AddItem(item, slot);
                }
            }
        }
    }
}

void PlayState::HandleDeleteItem(const Packet& packet) {
    // Extract delete item data from packet
    int32_t slot;
    packet >> slot;

    // Get the player
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = std::dynamic_pointer_cast<Player>(m_actorManager->GetLocalPlayer());
        if (player) {
            // Remove item from inventory
            player->RemoveItem(slot);
        }
    }
}

void PlayState::HandleUpdateItem(const Packet& packet) {
    // Extract update item data from packet
    int32_t slot, itemType, itemLook, identified;
    std::string itemName;
    std::vector<uint8_t> values;
    int32_t makeIndex, durability, maxDurability;

    packet >> slot >> itemType >> itemLook >> identified >> itemName >> makeIndex >> durability >> maxDurability;

    // Read additional values (up to 14 values as in the original TUserItem)
    for (int i = 0; i < 14; i++) {
        uint8_t value;
        packet >> value;
        values.push_back(value);
    }

    // Get the player
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = std::dynamic_pointer_cast<Player>(m_actorManager->GetLocalPlayer());
        if (player) {
            // Create a new item
            auto item = std::make_shared<Item>(itemType, static_cast<ItemType>(itemType), itemLook, itemName, identified == 1);
            item->SetMakeIndex(makeIndex);
            item->SetDurability(durability);
            item->SetMaxDurability(maxDurability);
            item->SetValues(values);

            // Update the item in the inventory
            if (player->GetInventory()) {
                player->GetInventory()->UpdateItem(slot, item);
            }
        }
    }
}

void PlayState::HandleDropItemResult(const Packet& packet) {
    // Extract drop item result data from packet
    int32_t success;
    packet >> success;

    // Handle drop item result
    if (success) {
        // Item dropped successfully
        // TODO: Play drop sound
    } else {
        // Failed to drop item
        // TODO: Show error message
    }
}

void PlayState::HandleGoldChanged(const Packet& packet) {
    // Extract gold changed data from packet
    int32_t gold;
    packet >> gold;

    // Update player gold
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = std::dynamic_pointer_cast<Player>(m_actorManager->GetLocalPlayer());
        if (player && player->GetInventory()) {
            player->GetInventory()->SetGold(gold);

            // Play gold sound
            // TODO: Play gold sound
        }
    }
}

void PlayState::HandleWeightChanged(const Packet& packet) {
    // Extract weight changed data from packet
    int32_t weight, maxWeight;
    packet >> weight >> maxWeight;

    // Update player weight
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = std::dynamic_pointer_cast<Player>(m_actorManager->GetLocalPlayer());
        if (player && player->GetInventory()) {
            player->GetInventory()->SetWeight(weight);
            player->GetInventory()->SetMaxWeight(maxWeight);
        }
    }
}

void PlayState::HandleDuraChanged(const Packet& packet) {
    // Extract durability changed data from packet
    int32_t slot, durability, maxDurability;
    packet >> slot >> durability >> maxDurability;

    // Update item durability
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = std::dynamic_pointer_cast<Player>(m_actorManager->GetLocalPlayer());
        if (player) {
            // Get the item from the inventory
            auto item = player->GetItem(slot);
            if (item) {
                // Update durability
                item->SetDurability(durability);
                item->SetMaxDurability(maxDurability);

                // Check if the item is about to break
                if (durability < maxDurability / 5) {
                    // TODO: Show warning message
                }
            }
        }
    }
}
void PlayState::HandleOpenDoorResult(const Packet& packet) {
    // Extract open door result data from packet
    int32_t x, y, doorIndex, doorOffset;
    packet >> x >> y >> doorIndex >> doorOffset;

    // Update door state in map manager
    if (m_mapManager) {
        m_mapManager->SetDoorState(x, y, doorIndex, doorOffset, true);

        // Play door open sound
        // TODO: Play door open sound
    }
}

void PlayState::HandleCloseDoor(const Packet& packet) {
    // Extract close door data from packet
    int32_t x, y;
    packet >> x >> y;

    // Update door state in map manager
    if (m_mapManager) {
        m_mapManager->SetDoorState(x, y, 0, 0, false);

        // Play door close sound
        // TODO: Play door close sound
    }
}

void PlayState::HandleAddMagic(const Packet& packet) {
    // Extract add magic data from packet
    int32_t magicId, level, key;
    std::string name;

    packet >> magicId >> level >> key >> name;

    // Add magic to player's spell list
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = std::dynamic_pointer_cast<Player>(m_actorManager->GetLocalPlayer());
        if (player) {
            // Get the skill from the skill manager
            auto skillManager = SkillManager::GetInstance();
            auto skill = skillManager->GetSkill(magicId);

            if (skill) {
                // Set the skill level and key
                skill->SetLevel(level);
                skill->SetKey(key);

                // Add the skill to the player
                player->AddSkill(skill);

                // Play magic learn sound
                // TODO: Play magic learn sound

                // Show notification
                if (m_chatManager) {
                    m_chatManager->AddSystemMessage("学会了新魔法: " + name);
                }

                // Refresh skill UI if visible
                if (m_skillPanel && m_skillPanel->IsVisible()) {
                    m_skillPanel->RefreshSkillList();
                }
            }
        }
    }
}

void PlayState::HandleSendMyMagic(const Packet& packet) {
    // Extract send my magic data from packet
    int32_t magicCount;
    packet >> magicCount;

    // Get the player
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = std::dynamic_pointer_cast<Player>(m_actorManager->GetLocalPlayer());
        if (player) {
            // Clear current spell list
            player->ClearSkills();

            // Read each magic
            for (int i = 0; i < magicCount; i++) {
                int32_t magicId, level, key, experience;
                std::string name;

                packet >> magicId >> level >> key >> experience >> name;

                // Get the skill from the skill manager
                auto skillManager = SkillManager::GetInstance();
                auto skill = skillManager->GetSkill(magicId);

                if (skill) {
                    // Set the skill properties
                    skill->SetLevel(level);
                    skill->SetKey(key);
                    skill->SetExperience(experience);

                    // Add the skill to the player
                    player->AddSkill(skill);

                    // Set hotkey if key is set
                    if (key > 0 && key <= 10) {
                        player->SetHotkey(key - 1, magicId);
                    }
                }
            }

            // Show notification
            if (m_chatManager) {
                m_chatManager->AddSystemMessage("已接收所有魔法信息");
            }

            // Refresh skill UI if visible
            if (m_skillPanel && m_skillPanel->IsVisible()) {
                m_skillPanel->RefreshSkillList();
            }

            // Refresh hotkey config UI if visible
            if (m_hotkeyConfigUI && m_hotkeyConfigUI->IsVisible()) {
                m_hotkeyConfigUI->RefreshHotkeys();
            }
        }
    }
}

void PlayState::HandleDeleteMagic(const Packet& packet) {
    // Extract delete magic data from packet
    int32_t magicId;
    packet >> magicId;

    // Remove magic from player's spell list
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = std::dynamic_pointer_cast<Player>(m_actorManager->GetLocalPlayer());
        if (player) {
            // Get the skill name before removing it
            std::string skillName = "";
            auto skill = player->GetSkill(magicId);
            if (skill) {
                skillName = skill->GetName();
            }

            // Remove the skill
            player->RemoveSkill(magicId);

            // Show notification
            if (m_chatManager && !skillName.empty()) {
                m_chatManager->AddSystemMessage("失去了魔法: " + skillName);
            }

            // Refresh skill UI if visible
            if (m_skillPanel && m_skillPanel->IsVisible()) {
                m_skillPanel->RefreshSkillList();
            }

            // Refresh hotkey config UI if visible
            if (m_hotkeyConfigUI && m_hotkeyConfigUI->IsVisible()) {
                m_hotkeyConfigUI->RefreshHotkeys();
            }
        }
    }
}

void PlayState::HandleMagicFire(const Packet& packet) {
    // Extract magic fire data from packet
    int32_t actorId, effectType, effectId, targetX, targetY, targetId;
    packet >> actorId >> effectType >> effectId >> targetX >> targetY >> targetId;

    // Get the actor
    if (m_actorManager) {
        auto actor = m_actorManager->GetActor(actorId);
        if (actor) {
            // Set actor to casting state
            actor->SetState(ActorState::SPELL);

            // Get the skill
            auto skillManager = SkillManager::GetInstance();
            auto skill = skillManager->GetSkill(effectId);

            // Create magic effect
            auto effectManager = EffectManager::GetInstance();
            if (effectManager && skill) {
                // Set the skill's effect type
                skill->SetEffectType(static_cast<EffectType>(effectType));

                // Play the skill effect
                effectManager->PlaySkillEffect(skill, actor->GetX(), actor->GetY(), targetX, targetY);

                // Play magic sound
                // TODO: Play magic sound based on effect type

                // If this is the local player, update mana
                if (actor == m_actorManager->GetLocalPlayer()) {
                    auto player = std::dynamic_pointer_cast<Player>(actor);
                    if (player) {
                        // Consume mana
                        player->SetMana(player->GetMana() - skill->GetManaCost());
                    }
                }

                // If there's a target actor, handle effects on target
                if (targetId > 0) {
                    auto targetActor = m_actorManager->GetActor(targetId);
                    if (targetActor) {
                        // Apply effect to target (damage, heal, etc.)
                        switch (skill->GetEffectType()) {
                            case EffectType::HEAL:
                                // Healing effect
                                if (targetActor == m_actorManager->GetLocalPlayer()) {
                                    auto player = std::dynamic_pointer_cast<Player>(targetActor);
                                    if (player) {
                                        int healAmount = skill->CalculateEffect(actor->GetLevel(), skill->GetLevel());
                                        player->Heal(healAmount);
                                    }
                                }
                                break;
                            case EffectType::BUFF:
                                // Buff effect
                                // TODO: Implement buff effects
                                break;
                            case EffectType::DEBUFF:
                                // Debuff effect
                                // TODO: Implement debuff effects
                                break;
                            default:
                                // Damage effect
                                // Note: Actual damage is handled by HandleEntityStruck
                                break;
                        }
                    }
                }
            }
        }
    }
}

void PlayState::HandleMagicFireFail(const Packet& packet) {
    // Extract magic fire fail data from packet
    int32_t actorId;
    packet >> actorId;

    // Handle magic fire fail
    if (m_actorManager) {
        std::shared_ptr<Actor> actor = m_actorManager->GetActor(actorId);
        if (actor) {
            // Cancel casting animation
            actor->SetState(ActorState::IDLE);

            // If this is the local player, show fail message
            if (actor == m_actorManager->GetLocalPlayer()) {
                if (m_chatManager) {
                    m_chatManager->AddSystemMessage("魔法施放失败");
                }

                // Play fail sound
                // TODO: Play magic fail sound
            }
        }
    }
}

void PlayState::HandleMagicLevelExp(const Packet& packet) {
    // Extract magic level exp data from packet
    int32_t magicId, level, experience;
    packet >> magicId >> level >> experience;

    // Update magic level and experience
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = std::dynamic_pointer_cast<Player>(m_actorManager->GetLocalPlayer());
        if (player) {
            // Get the skill
            auto skill = player->GetSkill(magicId);
            if (skill) {
                // Save old level
                int oldLevel = skill->GetLevel();

                // Update level and experience
                skill->SetLevel(level);
                skill->SetExperience(experience);

                // If level increased, show message
                if (level > oldLevel) {
                    if (m_chatManager) {
                        m_chatManager->AddSystemMessage(
                            skill->GetName() + " 升级到了 " + std::to_string(level) + " 级"
                        );
                    }

                    // Play level up sound
                    // TODO: Play magic level up sound

                    // Create level up effect
                    auto effectManager = EffectManager::GetInstance();
                    if (effectManager) {
                        // Create a special effect for skill level up
                        effectManager->AddEffect(
                            player->GetX(), player->GetY(),
                            player->GetX(), player->GetY(),
                            EffectType::BUFF, 1, 1500
                        );
                    }

                    // Refresh skill UI if visible
                    if (m_skillPanel && m_skillPanel->IsVisible()) {
                        m_skillPanel->RefreshSkillList();
                    }
                }
            }
        }
    }
}
void PlayState::HandleUserState(const Packet& packet) {
    // Extract user state data from packet
    int32_t state;
    packet >> state;

    // Update player state
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        m_actorManager->GetLocalPlayer()->SetUserState(state);

        // Handle specific state changes
        // TODO: Implement state-specific effects (poisoned, etc.)
    }
}

void PlayState::HandleHealthSpellChanged(const Packet& packet) {
    // Extract health and spell data from packet
    int32_t hp, maxHP, mp, maxMP;
    packet >> hp >> maxHP >> mp >> maxMP;

    // Update player health and mana
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        m_actorManager->GetLocalPlayer()->SetHealth(hp);
        m_actorManager->GetLocalPlayer()->SetMaxHealth(maxHP);
        m_actorManager->GetLocalPlayer()->SetMana(mp);
        m_actorManager->GetLocalPlayer()->SetMaxMana(maxMP);
    }
}

void PlayState::HandleAbility(const Packet& packet) {
    // Extract ability data from packet
    int32_t level, experience, maxExperience,
            strength, dexterity, vitality, energy, magic,
            attackMin, attackMax, accuracy, agility, defense;

    packet >> level >> experience >> maxExperience
           >> strength >> dexterity >> vitality >> energy >> magic
           >> attackMin >> attackMax >> accuracy >> agility >> defense;

    // Update player abilities
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = m_actorManager->GetLocalPlayer();

        player->SetLevel(level);
        player->SetExperience(experience);
        player->SetMaxExperience(maxExperience);

        player->SetStrength(strength);
        player->SetDexterity(dexterity);
        player->SetVitality(vitality);
        player->SetEnergy(energy);
        player->SetMagic(magic);

        player->SetAttackMin(attackMin);
        player->SetAttackMax(attackMax);
        player->SetAccuracy(accuracy);
        player->SetAgility(agility);
        player->SetDefense(defense);
    }
}

void PlayState::HandleSubAbility(const Packet& packet) {
    // Extract sub-ability data from packet
    int32_t ac, mac, dc, mc, sc, handWeight, wearWeight, bagWeight;

    packet >> ac >> mac >> dc >> mc >> sc >> handWeight >> wearWeight >> bagWeight;

    // Update player sub-abilities
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = m_actorManager->GetLocalPlayer();

        player->SetAC(ac);
        player->SetMAC(mac);
        player->SetDC(dc);
        player->SetMC(mc);
        player->SetSC(sc);

        player->SetHandWeight(handWeight);
        player->SetWearWeight(wearWeight);
        player->SetBagWeight(bagWeight);
    }
}

void PlayState::HandleAdjustBonus(const Packet& packet) {
    // Extract adjust bonus data from packet
    int32_t bonusType, bonusValue;
    packet >> bonusType >> bonusValue;

    // Update player bonus
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        auto player = m_actorManager->GetLocalPlayer();

        // Apply bonus based on type
        switch (bonusType) {
            case 0: // Strength
                player->SetStrength(player->GetStrength() + bonusValue);
                break;
            case 1: // Dexterity
                player->SetDexterity(player->GetDexterity() + bonusValue);
                break;
            case 2: // Vitality
                player->SetVitality(player->GetVitality() + bonusValue);
                break;
            case 3: // Energy
                player->SetEnergy(player->GetEnergy() + bonusValue);
                break;
            case 4: // Magic
                player->SetMagic(player->GetMagic() + bonusValue);
                break;
            // Add other bonus types as needed
        }
    }
}

void PlayState::HandleOpenHealth(const Packet& packet) {
    // Extract open health data from packet
    int32_t id, hp, maxHP;
    packet >> id >> hp >> maxHP;

    // Update entity health and show health bar
    if (m_actorManager) {
        std::shared_ptr<Actor> actor = m_actorManager->GetActor(id);
        if (actor) {
            actor->SetHealth(hp);
            actor->SetMaxHealth(maxHP);
            actor->SetShowHealthBar(true);
        }
    }
}

void PlayState::HandleCloseHealth(const Packet& packet) {
    // Extract close health data from packet
    int32_t id;
    packet >> id;

    // Hide health bar
    if (m_actorManager) {
        std::shared_ptr<Actor> actor = m_actorManager->GetActor(id);
        if (actor) {
            actor->SetShowHealthBar(false);
        }
    }
}

void PlayState::HandleChangeFace(const Packet& packet) {
    // Extract change face data from packet
    int32_t face;
    packet >> face;

    // Update player face
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        m_actorManager->GetLocalPlayer()->SetFace(face);
    }
}

void PlayState::HandleBreakWeapon(const Packet& packet) {
    // Extract break weapon data from packet
    int32_t weaponType;
    packet >> weaponType;

    // Handle weapon break
    if (m_actorManager && m_actorManager->GetLocalPlayer()) {
        // TODO: Implement weapon break logic

        // Play weapon break sound
        // TODO: Play weapon break sound

        // Show weapon break message
        // TODO: Show weapon break message
    }
}

// Game flow packet handlers
void PlayState::HandleStartPlay(const Packet& packet) {
    // Extract server address and port from packet
    std::string serverAddress;
    int32_t serverPort;

    packet >> serverAddress >> serverPort;

    std::cout << "HandleStartPlay: Received server address: " << serverAddress << ", port: " << serverPort << std::endl;

    // In the original Delphi project, this would:
    // 1. Disconnect from the current server (login server)
    // 2. Connect to the game server using the provided address and port
    // 3. Send a login request to the game server

    if (m_networkManager) {
        // Disconnect from current server
        if (m_networkManager->IsConnected()) {
            std::cout << "HandleStartPlay: Disconnecting from current server..." << std::endl;
            m_networkManager->Disconnect();
        }

        // Connect to game server
        std::cout << "HandleStartPlay: Connecting to game server at " << serverAddress << ":" << serverPort << "..." << std::endl;
        bool connected = m_networkManager->Connect(serverAddress, serverPort);

        if (connected) {
            std::cout << "HandleStartPlay: Connected to game server successfully" << std::endl;

            // Clear inventory and chat
            // TODO: Implement ClearBag and ClearChatBoard

            // Hide any UI panels that should be hidden when entering the game
            if (m_statusPanel) m_statusPanel->Hide();
            if (m_skillPanel) m_skillPanel->Hide();
            if (m_hotkeyConfigUI) m_hotkeyConfigUI->Hide();

            // Send login request to game server
            // TODO: Implement SendRunLogin

            // Add system message
            if (m_chatManager) {
                m_chatManager->AddSystemMessage("Connected to game server");
            }
        } else {
            std::cerr << "HandleStartPlay: Failed to connect to game server" << std::endl;

            // Add error message
            if (m_chatManager) {
                m_chatManager->AddSystemMessage("Failed to connect to game server");
            }
        }
    }
}

void PlayState::HandleReconnect(const Packet& packet) {
    // Extract server address and port from packet
    std::string serverAddress;
    int32_t serverPort;

    packet >> serverAddress >> serverPort;

    std::cout << "HandleReconnect: Received server address: " << serverAddress << ", port: " << serverPort << std::endl;

    // In the original Delphi project, this would:
    // 1. Save the player's inventory
    // 2. Disconnect from the current server
    // 3. Connect to the new server using the provided address and port
    // 4. Send a login request to the new server

    if (m_networkManager) {
        // TODO: Save player inventory

        // Disconnect from current server
        if (m_networkManager->IsConnected()) {
            std::cout << "HandleReconnect: Disconnecting from current server..." << std::endl;
            m_networkManager->Disconnect();
        }

        // Connect to new server
        std::cout << "HandleReconnect: Connecting to new server at " << serverAddress << ":" << serverPort << "..." << std::endl;
        bool connected = m_networkManager->Connect(serverAddress, serverPort);

        if (connected) {
            std::cout << "HandleReconnect: Connected to new server successfully" << std::endl;

            // Clear inventory and chat
            // TODO: Implement ClearBag and ClearChatBoard

            // Send login request to new server
            // TODO: Implement SendRunLogin

            // Add system message
            if (m_chatManager) {
                m_chatManager->AddSystemMessage("Connected to new server");
            }
        } else {
            std::cerr << "HandleReconnect: Failed to connect to new server" << std::endl;

            // Add error message
            if (m_chatManager) {
                m_chatManager->AddSystemMessage("Failed to connect to new server");
            }
        }
    }
}
