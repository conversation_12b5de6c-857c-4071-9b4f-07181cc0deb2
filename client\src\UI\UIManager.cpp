#include "UIManager.h"
#include <algorithm>
#include <iostream>
#include "TextInput.h"
// #include "UIContainer.h" // 移除无用的包含

UIManager::UIManager(SDL_Renderer* renderer)
    : m_renderer(renderer)
    , m_wil<PERSON>anager(nullptr)
{
}

UIManager::~UIManager()
{
    ClearControls();
}

void UIManager::Update(int deltaTime)
{
    // Update all controls
    for (auto& control : m_controls) {
        if (control->IsVisible()) {
            control->Update(deltaTime);
        }
    }
}

void UIManager::Render()
{
    // Render all controls
    for (auto& control : m_controls) {
        if (control->IsVisible()) {
            control->Ren<PERSON>(m_renderer);
        }
    }
}

// 递归查找被点中的控件
static std::shared_ptr<UIControl> FindControlAt(const std::shared_ptr<UIControl>& control, int x, int y) {
    if (!control->IsVisible() || !control->IsEnabled()) return nullptr;
    // 先递归 children（Z序高的先）
    const auto& children = control->GetChildren();
    for (auto it = children.rbegin(); it != children.rend(); ++it) {
        auto found = FindControlAt(*it, x, y);
        if (found) return found;
    }
    // 再判断自己
    if (control->IsPointInside(x, y)) return control;
    return nullptr;
}

bool UIManager::HandleEvent(const SDL_Event& event)
{
    if (event.type == SDL_MOUSEBUTTONDOWN && event.button.button == SDL_BUTTON_LEFT) {
        int mouseX = event.button.x;
        int mouseY = event.button.y;
        // 递归查找所有控件
        for (auto it = m_controls.rbegin(); it != m_controls.rend(); ++it) {
            auto found = FindControlAt(*it, mouseX, mouseY);
            if (found && found->HandleEvent(event)) {
                return true;
                // if (found->HandleEvent(event)) {
                //     // 如果是 TextInput，设置为 focusedControl
                //     auto textInput = std::dynamic_pointer_cast<TextInput>(found);
                //     if (textInput) {
                //         SetFocusedControl(textInput);
                //     } else {
                //         ClearFocusedControl();
                //     }
                //     return true;
                // }
            }
        }
        // 没有控件被点中，清除焦点
        ClearFocusedControl();
        return false;
    }
    // 键盘和文本输入事件优先分发给 focusedControl
    auto focused = m_focusedControl.lock();
    if (focused && focused->IsVisible() && focused->IsEnabled()) {
        if (focused->HandleEvent(event)) {
            return true;
        }
    }
    // 其它事件分发给所有控件
    for (auto it = m_controls.rbegin(); it != m_controls.rend(); ++it) {
        if (focused && *it == focused) continue;
        if ((*it)->IsVisible() && (*it)->IsEnabled()) {
            if ((*it)->HandleEvent(event)) {
                return true;
            }
        }
    }
    return false;
}

void UIManager::AddControl(std::shared_ptr<UIControl> control)
{
    // Set WIL manager if available
    if (m_wilManager) {
        control->SetWILManager(m_wilManager);
    }
    // Set UIManager
    control->SetUIManager(this);

    // Add to controls list
    m_controls.push_back(control);

    // Add to named controls map if it has a name
    if (!control->GetName().empty()) {
        m_namedControls[control->GetName()] = control;
    }
}

bool UIManager::RemoveControl(UIControl* control)
{
    // Find the control
    auto it = std::find_if(m_controls.begin(), m_controls.end(),
        [control](const std::shared_ptr<UIControl>& c) {
            return c.get() == control;
        });

    if (it != m_controls.end()) {
        // Remove from named controls map if it has a name
        if (!(*it)->GetName().empty()) {
            m_namedControls.erase((*it)->GetName());
        }

        // Remove from controls list
        m_controls.erase(it);
        return true;
    }

    return false;
}

std::shared_ptr<UIControl> UIManager::GetControl(const std::string& name)
{
    auto it = m_namedControls.find(name);
    if (it != m_namedControls.end()) {
        return it->second;
    }

    return nullptr;
}

void UIManager::ClearControls()
{
    m_controls.clear();
    m_namedControls.clear();
}

void UIManager::SetWILManager(std::shared_ptr<WILManager> wilManager)
{
    m_wilManager = wilManager;

    // Set WIL manager for all controls
    for (auto& control : m_controls) {
        control->SetWILManager(wilManager);
    }
}

void UIManager::SetFocusedControl(std::shared_ptr<UIControl> control) {
    if (control == m_focusedControl.lock()) {
        return;
    }
    if(!m_focusedControl.expired()){
        auto prev = m_focusedControl.lock();
        if (prev && prev != control) {
            prev->Unfocus();
        }
    }
    m_focusedControl = control;
    if (control) {
        control->Focus();
    }
}

void UIManager::ClearFocusedControl() {
    if (m_focusedControl.expired()) {
        return;
    }
    auto prev = m_focusedControl.lock();
    if (prev) {
        prev->Unfocus();
    }
    m_focusedControl.reset();
}

std::shared_ptr<UIControl> UIManager::GetFocusedControl() {
    return m_focusedControl.lock();
}

void UIManager::AddContainer(std::shared_ptr<UIContainer> container)
{
    container->SetUIManager(this);
    m_controls.push_back(container);
    if (!container->GetName().empty()) {
        m_namedControls[container->GetName()] = container;
    }
}
