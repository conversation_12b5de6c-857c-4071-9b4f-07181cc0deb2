#pragma once

#include "Texture.h"
#include <memory>
#include <vector>

/**
 * @class Sprite
 * @brief Represents a game sprite
 *
 * This class represents a game sprite, which is a graphical object that can be
 * rendered on the screen. It can be animated or static.
 */
class Sprite {
protected:
    int m_x;                             ///< X position
    int m_y;                             ///< Y position

private:
    std::shared_ptr<Texture> m_texture;  ///< Texture containing the sprite
    SDL_Rect m_clip;                     ///< Clip rectangle for the sprite
    int m_offsetX;                       ///< X offset for drawing
    int m_offsetY;                       ///< Y offset for drawing
    double m_angle;                      ///< Rotation angle
    SDL_Point m_center;                  ///< Rotation center
    SDL_RendererFlip m_flip;             ///< Flip mode
    bool m_visible;                      ///< Visibility flag

public:
    /**
     * @brief Constructor
     * @param texture Texture containing the sprite
     */
    Sprite(std::shared_ptr<Texture> texture);

    /**
     * @brief Destructor
     */
    ~Sprite();

    /**
     * @brief Set the clip rectangle
     * @param x X coordinate of the clip
     * @param y Y coordinate of the clip
     * @param w Width of the clip
     * @param h Height of the clip
     */
    void SetClip(int x, int y, int w, int h);

    /**
     * @brief Set the position
     * @param x X coordinate
     * @param y Y coordinate
     */
    void SetPosition(int x, int y);

    /**
     * @brief Set the offset
     * @param x X offset
     * @param y Y offset
     */
    void SetOffset(int x, int y);

    /**
     * @brief Set the rotation angle
     * @param angle Angle in degrees
     */
    void SetAngle(double angle);

    /**
     * @brief Set the rotation center
     * @param x X coordinate
     * @param y Y coordinate
     */
    void SetCenter(int x, int y);

    /**
     * @brief Set the flip mode
     * @param flip SDL flip mode
     */
    void SetFlip(SDL_RendererFlip flip);

    /**
     * @brief Set visibility
     * @param visible Visibility flag
     */
    void SetVisible(bool visible);

    /**
     * @brief Get the X position
     * @return X position
     */
    int GetX() const { return m_x; }

    /**
     * @brief Get the Y position
     * @return Y position
     */
    int GetY() const { return m_y; }

    /**
     * @brief Get the width
     * @return Width in pixels
     */
    int GetWidth() const { return m_clip.w; }

    /**
     * @brief Get the height
     * @return Height in pixels
     */
    int GetHeight() const { return m_clip.h; }

    /**
     * @brief Check if the sprite is visible
     * @return true if visible, false otherwise
     */
    bool IsVisible() const { return m_visible; }

    /**
     * @brief Render the sprite
     */
    void Render();
};

/**
 * @class AnimatedSprite
 * @brief Represents an animated sprite
 *
 * This class represents an animated sprite, which is a sprite that can be
 * animated by cycling through a series of frames.
 */
class AnimatedSprite : public Sprite {
private:
    std::vector<SDL_Rect> m_frames;      ///< Animation frames
    int m_currentFrame;                  ///< Current frame index
    int m_frameCount;                    ///< Total number of frames
    int m_frameDelay;                    ///< Delay between frames
    int m_frameTimer;                    ///< Timer for frame animation
    bool m_looping;                      ///< Looping flag
    bool m_playing;                      ///< Playing flag

public:
    /**
     * @brief Constructor
     * @param texture Texture containing the sprite
     */
    AnimatedSprite(std::shared_ptr<Texture> texture);

    /**
     * @brief Destructor
     */
    ~AnimatedSprite();

    /**
     * @brief Add a frame to the animation
     * @param x X coordinate of the frame
     * @param y Y coordinate of the frame
     * @param w Width of the frame
     * @param h Height of the frame
     */
    void AddFrame(int x, int y, int w, int h);

    /**
     * @brief Set the frame delay
     * @param delay Delay in milliseconds
     */
    void SetFrameDelay(int delay);

    /**
     * @brief Set looping
     * @param looping Looping flag
     */
    void SetLooping(bool looping);

    /**
     * @brief Start the animation
     */
    void Play();

    /**
     * @brief Stop the animation
     */
    void Stop();

    /**
     * @brief Reset the animation to the first frame
     */
    void Reset();

    /**
     * @brief Set the current frame
     * @param frame Frame index
     */
    void SetFrame(int frame);

    /**
     * @brief Get the current frame
     * @return Current frame index
     */
    int GetCurrentFrame() const { return m_currentFrame; }

    /**
     * @brief Get the frame count
     * @return Total number of frames
     */
    int GetFrameCount() const { return m_frameCount; }

    /**
     * @brief Check if the animation is playing
     * @return true if playing, false otherwise
     */
    bool IsPlaying() const { return m_playing; }

    /**
     * @brief Get the position of the sprite
     * @param x Reference to store X position
     * @param y Reference to store Y position
     */
    void GetPosition(int& x, int& y) const { x = m_x; y = m_y; }

    /**
     * @brief Get the size of the sprite
     * @param width Reference to store width
     * @param height Reference to store height
     */
    void GetSize(int& width, int& height) const { width = GetWidth(); height = GetHeight(); }

    /**
     * @brief Update the animation
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    void Update(int deltaTime);

    /**
     * @brief Render the sprite
     */
    void Render();
};
