; DBServer Configuration File
; 数据库服务器配置文件

[DB]
; 角色数据库目录
Dir=./FDB/
; 备份目录
Backup=./Backup/
; 游戏数据库文件路径（SQLite）
GameDB=./GameData/GameData.db

[Setup]
; 服务器监听端口
ServerPort=6000
; 服务器监听地址（0.0.0.0表示监听所有地址）
ServerAddr=0.0.0.0
; 服务器名称
ServerName=DBServer01
; 查看黑客消息
ViewHackMsg=0
; 动态IP模式
DynamicIPMode=0

[Server]
; ID服务器地址
IDSAddr=127.0.0.1
; ID服务器端口
IDSPort=5600

[Options]
; 自动备份间隔（分钟，0表示禁用）
AutoBackupInterval=60
; 日志级别 (0=Debug, 1=Info, 2=Warning, 3=Error)
LogLevel=1
; 会话验证超时时间（秒）
SessionTimeout=5
; 是否启用IP白名单
EnableIPWhitelist=true

[Performance]
; 数据库索引缓存大小（MB）
IndexCacheSize=32
; 最大并发查询数
MaxConcurrentQueries=100
; 数据保存队列大小
SaveQueueSize=1000
; 数据库连接池大小
ConnectionPoolSize=10
; 查询缓存大小（MB）
QueryCacheSize=64
; 是否启用WAL模式（SQLite）
EnableWAL=1
; 数据库同步模式（0=OFF, 1=NORMAL, 2=FULL）
SyncMode=1
; 忙等待超时（毫秒）
BusyTimeout=5000

[DBClear]
; 自动清理间隔（毫秒）
Interval=3000
; 等级限制1
Level1=1
; 等级限制2
Level2=7
; 等级限制3
Level3=14
; 天数限制1
Day1=14
; 天数限制2
Day2=62
; 天数限制3
Day3=124
; 月份限制1
Month1=0
; 月份限制2
Month2=0
; 月份限制3
Month3=0

[GameData]
; 是否自动导入游戏数据
AutoImport=1
; 游戏数据导入目录
ImportDir=./GameData/Import/
; 物品数据文件
ItemsFile=StdItems.csv
; 魔法数据文件
MagicsFile=Magic.csv
; 导入时是否覆盖已存在的数据
OverwriteExisting=0
; 是否在启动时验证数据完整性
ValidateOnStartup=1

[Security]
; 允许的服务器IP列表文件
ServerIPList=!AddrTable.txt
; 网关ID配置文件
GateIDFile=SelectID.txt
; 是否启用IP白名单
EnableIPWhitelist=1
; IP白名单文件
IPWhitelistFile=./config/!AddrTable.txt
; 是否记录所有查询
LogAllQueries=0
; 敏感操作日志文件
SecurityLogFile=./Log/Security.log 