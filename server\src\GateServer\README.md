# GateServer - 网关服务器

## 概述

GateServer是传奇游戏服务器架构中的网关服务器，负责客户端连接的代理、消息转发、安全控制和防作弊检测。它基于原始Delphi版本的ERunGate进行重构，使用现代C++实现，提供完整的功能兼容性。

## 主要功能

### 1. 连接代理
- **客户端连接管理**: 支持最多1000个并发客户端会话
- **消息转发**: 在客户端和游戏服务器之间透明转发游戏数据
- **会话维护**: 跟踪客户端会话状态，处理连接和断开事件
- **心跳检测**: 定期检测客户端和服务器连接状态

### 2. 安全控制
- **IP过滤**: 支持IP阻止列表，自动阻止恶意IP地址
- **连接限制**: 限制每个IP地址的最大连接数
- **攻击检测**: 自动检测和阻止连接攻击
- **三种阻止模式**:
  - `DISCONNECT (0)`: 仅断开连接
  - `BLOCK (1)`: 临时阻止IP
  - `BLOCKLIST (2)`: 永久加入阻止列表

### 3. 防作弊系统
- **速度检测**: 监控玩家的游戏动作速度
- **支持的动作类型**:
  - 攻击速度 (HitSpeed)
  - 魔法速度 (SpellSpeed)
  - 跑步速度 (RunSpeed)
  - 走路速度 (WalkSpeed)
  - 转向速度 (TurnSpeed)
- **三种控制模式**:
  - `0`: 仅记录日志
  - `1`: 发送警告消息
  - `2`: 断开连接

### 4. 消息过滤
- **敏感词过滤**: 自动过滤聊天中的不当词汇
- **自动替换**: 将敏感词替换为星号(*)
- **可配置词库**: 通过WordFilter.txt文件配置敏感词列表

### 5. 网络优化
- **数据包分块**: 大数据包自动分块发送
- **发送队列**: 智能管理发送队列，避免网络拥塞
- **流量控制**: 控制发送速度，防止客户端过载
- **缓冲管理**: 高效的接收和发送缓冲区管理

## 网关协议

GateServer使用专门的网关协议与游戏服务器通信：

```cpp
enum GateMessageType {
    GM_OPEN = 1,              // 打开连接
    GM_CLOSE = 2,             // 关闭连接
    GM_CHECKSERVER = 3,       // 服务器检查信号
    GM_CHECKCLIENT = 4,       // 客户端检查信号
    GM_DATA = 5,              // 转发游戏数据
    GM_SERVERUSERINDEX = 6,   // 用户索引管理
    GM_RECEIVE_OK = 7,        // 接收确认
    GM_TEST = 20              // 测试消息
};
```

## 配置文件

### config/GateServer.ini

```ini
[Server]
# 服务器连接配置
ServerAddr=127.0.0.1        # 游戏服务器地址
ServerPort=5000             # 游戏服务器端口

# 网关监听配置
GateAddr=0.0.0.0           # 网关监听地址
GatePort=7200              # 网关监听端口

# 连接限制配置
MaxConnOfIPaddr=50         # 每IP最大连接数
MaxClientPacketSize=8000   # 最大客户端数据包大小
ClientSendBlockSize=1000   # 客户端发送块大小
ClientTimeOutTime=5000     # 客户端超时时间(毫秒)
SessionTimeOutTime=3600000 # 会话超时时间(毫秒)

# 防攻击配置
AttackTick=300             # 攻击检测时间间隔
AttackCount=5              # 攻击次数阈值
BlockMethod=0              # 阻止方法(0=断开,1=临时阻止,2=永久阻止)

[Setup]
# 速度控制配置
HitSpeed=1                 # 启用攻击速度检测
SpellSpeed=1               # 启用魔法速度检测
RunSpeed=1                 # 启用跑步速度检测
WalkSpeed=1                # 启用走路速度检测
TurnSpeed=1                # 启用转向速度检测

# 时间间隔配置（毫秒）
HitTime=500                # 攻击时间间隔
SpellTime=500              # 魔法时间间隔
RunTime=300                # 跑步时间间隔
WalkTime=300               # 走路时间间隔
TurnTime=200               # 转向时间间隔

# 计数限制配置
HitCount=3                 # 攻击计数限制
SpellCount=3               # 魔法计数限制
RunCount=3                 # 跑步计数限制
WalkCount=3                # 走路计数限制
TurnCount=3                # 转向计数限制

# 速度控制模式
SpeedControlMode=1         # 0=仅记录, 1=警告, 2=断开连接

# 提示信息配置
HintSpeed=1                # 显示速度提示
HintSpeedMsg=系统提示: 请按照游戏节奏,不要使用非法外挂!
```

### WordFilter.txt - 敏感词过滤列表

```
# 敏感词过滤文件
# 一行一个敏感词，系统会自动替换为*号

# 脏话类
傻逼
白痴
智障
垃圾

# 外挂类
外挂
作弊
加速器
变速齿轮

# 广告类
QQ群
微信群
代练
出售
收购
```

### BlockIPList.txt - IP阻止列表

```
# IP阻止列表文件
# 一行一个IP地址，这些IP将被拒绝连接
# 示例：
# *************
# *********
```

## 编译和运行

### 编译要求
- C++17 或更高版本
- CMake 3.10 或更高版本
- Windows: Visual Studio 2019+ 或 MinGW-w64
- Linux: GCC 8+ 或 Clang 8+

### 编译步骤

```bash
# 在server目录下
mkdir build
cd build
cmake ..
cmake --build . --target GateServer

# 或者使用Visual Studio
cmake .. -G "Visual Studio 16 2019"
# 然后在Visual Studio中编译GateServer项目
```

### 运行

```bash
# 确保配置文件存在
cp config/GateServer.ini ./
cp ../WordFilter.txt ./
cp ../BlockIPList.txt ./

# 运行GateServer
./bin/GateServer
```

## 架构设计

### 多线程架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Main Thread   │    │ Process Thread  │    │  Check Thread   │
│                 │    │                 │    │                 │
│ - 主循环        │    │ - 消息处理      │    │ - 连接检查      │
│ - 状态监控      │    │ - 数据转发      │    │ - 超时检测      │
│ - 信号处理      │    │ - 队列管理      │    │ - 心跳发送      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 数据流

```
[客户端] ←─→ [GateServer] ←─→ [游戏服务器]
              │
              ├── IP过滤
              ├── 速度检测
              ├── 消息过滤
              ├── 会话管理
              └── 数据转发
```

## 监控和调试

### 日志级别
- `0`: 错误 - 严重错误信息
- `1`: 警告 - 警告信息
- `2`: 信息 - 一般信息
- `3`: 调试 - 调试信息
- `4`: 详细 - 详细调试信息

### 运行时状态
GateServer每30秒会显示当前状态：
```
GateServer Status - Active Connections: 25, Sessions: 25
```

### 重要事件日志
- 客户端连接和断开
- IP阻止事件
- 速度检测警告
- 服务器连接状态变化
- 配置文件重载

## 性能特性

- **高并发**: 支持1000个并发客户端连接
- **低延迟**: 高效的消息转发机制
- **内存优化**: 智能的缓冲区管理
- **线程安全**: 完整的线程安全设计
- **异步处理**: 非阻塞的网络I/O

## 兼容性

- 完全兼容原版Delphi客户端
- 支持所有原版网络协议
- 保持原版的数据包格式
- 兼容原版的配置文件格式

## 故障排除

### 常见问题

1. **无法启动**
   - 检查端口是否被占用
   - 确认配置文件路径正确
   - 检查防火墙设置

2. **客户端无法连接**
   - 确认GatePort配置正确
   - 检查IP是否在阻止列表中
   - 验证网络连通性

3. **无法连接游戏服务器**
   - 检查ServerAddr和ServerPort配置
   - 确认游戏服务器正在运行
   - 验证网络连通性

4. **性能问题**
   - 调整ClientSendBlockSize
   - 优化防作弊检测参数
   - 检查系统资源使用情况

## 开发和扩展

### 添加新的防作弊检测
1. 在`GateConfig`结构中添加新的配置项
2. 在`CheckDefaultMessage`函数中实现检测逻辑
3. 更新配置文件解析代码
4. 添加相应的日志记录

### 自定义消息过滤
1. 修改`FilterSayMsg`函数
2. 扩展敏感词匹配算法
3. 支持正则表达式匹配
4. 添加白名单功能

### 性能优化
1. 调整线程数量和处理频率
2. 优化数据结构和算法
3. 使用内存池管理
4. 实现零拷贝数据转发

## 版本历史

- **v1.0.0** (2024-12-19): 初始版本，完整实现所有核心功能
  - 基础连接代理功能
  - 完整的安全控制系统
  - 防作弊检测机制
  - 敏感词过滤系统
  - 多线程架构设计 