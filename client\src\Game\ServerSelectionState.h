#pragma once

#include "../GameState.h"
#include "../Graphics/Texture.h"
#include "../UI/Button.h"
#include "../UI/Label.h"
#include "../UI/ListView.h"
#include "../UI/DialogManager.h"
#include "../UI/MessageDialog.h"
#include "../Network/NetworkManager.h"
#include "../Data/ServerInfo.h"
#include <memory>
#include <vector>
#include <string>

/**
 * @class ServerSelectionState
 * @brief Game state for the server selection screen
 *
 * This class represents the server selection screen of the game, where the player can
 * select a server to connect to.
 */
class ServerSelectionState : public GameState {
private:
    std::shared_ptr<Texture> m_backgroundTexture;  ///< Background texture

    // UI controls
    std::shared_ptr<Label> m_titleLabel;           ///< Title label
    std::shared_ptr<ListView> m_serverListView;    ///< Server list view
    std::shared_ptr<Button> m_connectButton;       ///< Connect button
    std::shared_ptr<Button> m_backButton;          ///< Back button
    std::shared_ptr<Label> m_statusLabel;          ///< Status label

    // Network manager
    std::shared_ptr<NetworkManager> m_networkManager;  ///< Network manager
    std::shared_ptr<DialogManager> m_dialogManager;    ///< Dialog manager

    // Server list
    std::vector<ServerInfo> m_servers;             ///< Server list
    int m_selectedServerIndex;                     ///< Selected server index

    // Connection state
    bool m_connecting;                             ///< Whether we're currently connecting

    /**
     * @brief Create UI controls
     */
    void CreateControls();

    /**
     * @brief Handle connect button click
     */
    void OnConnectButtonClick();

    /**
     * @brief Handle back button click
     */
    void OnBackButtonClick();

    /**
     * @brief Handle server list response
     * @param packet Server list response packet
     */
    void OnServerListResponse(const Packet& packet);

    /**
     * @brief Handle server connect response
     * @param packet Server connect response packet
     */
    void OnServerConnectResponse(const Packet& packet);

public:
    /**
     * @brief Constructor
     * @param app Pointer to the application
     * @param networkManager Network manager
     */
    ServerSelectionState(Application* app, std::shared_ptr<NetworkManager> networkManager);

    /**
     * @brief Destructor
     */
    virtual ~ServerSelectionState();

    /**
     * @brief Called when entering the state
     */
    virtual void Enter() override;

    /**
     * @brief Called when exiting the state
     */
    virtual void Exit() override;

    /**
     * @brief Update the state
     * @param deltaTime Time elapsed since last frame in seconds
     */
    virtual void Update(float deltaTime) override;

    /**
     * @brief Render the state
     */
    virtual void Render() override;

    /**
     * @brief Handle SDL events
     * @param event SDL event to handle
     */
    virtual void HandleEvents(SDL_Event& event) override;
};
