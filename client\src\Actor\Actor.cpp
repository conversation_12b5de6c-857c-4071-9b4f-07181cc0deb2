#include "Actor.h"
#include <cmath>

Actor::Actor(int id, const std::string& name)
    : m_id(id)
    , m_name(name)
    , m_x(0)
    , m_y(0)
    , m_direction(Direction::DOWN)
    , m_state(ActorState::IDLE)
    , m_moveSpeed(100)  // 100 pixels per second
    , m_attackSpeed(60)  // 60 attacks per minute
    , m_visible(true)
    , m_targetX(0)
    , m_targetY(0)
    , m_moving(false)
    , m_moveProgress(0.0f)
    , m_animationTimer(0)
    , m_light(0)        // Default light level
    , m_shiftX(0)       // Default X shift
    , m_shiftY(0)       // Default Y shift
    , m_health(100)     // Default health
    , m_maxHealth(100)  // Default max health
    , m_chatDuration(0)
    , m_chatTimer(0)
    , m_chatFont(nullptr)
{
}

Actor::~Actor()
{
}

void Actor::Update(int deltaTime)
{
    // Update movement
    UpdateMovement(deltaTime);

    // Update animation
    UpdateAnimation(deltaTime);

    // Update chat bubble
    UpdateChatBubble(deltaTime);
}

void Actor::Render()
{
    if (!m_visible) {
        return;
    }

    // Get the current sprite
    std::shared_ptr<AnimatedSprite> sprite = GetCurrentSprite();
    if (!sprite) {
        return;
    }

    // Render the sprite
    sprite->Render();

    // Render chat bubble
    if (m_chatTimer > 0 && m_chatTexture) {
        SDL_Renderer* renderer = m_chatTexture->GetRenderer();
        if (renderer) {
            RenderChatBubble(renderer);
        }
    }
}

void Actor::SetPosition(int x, int y)
{
    m_x = x;
    m_y = y;

    // Update sprite position
    std::shared_ptr<AnimatedSprite> sprite = GetCurrentSprite();
    if (sprite) {
        int screenX, screenY;
        // TODO: Convert map coordinates to screen coordinates
        screenX = m_x * 48;  // Assuming tile width is 48
        screenY = m_y * 32;  // Assuming tile height is 32
        sprite->SetPosition(screenX, screenY);
    }
}

void Actor::SetDirection(Direction direction)
{
    m_direction = direction;

    // Update sprite direction
    // This depends on how the sprites are organized
    // For example, if each direction has its own animation frames
    std::shared_ptr<AnimatedSprite> sprite = GetCurrentSprite();
    if (sprite) {
        // Set the appropriate frame based on direction
        int directionIndex = static_cast<int>(m_direction);
        sprite->SetFrame(directionIndex);
    }
}

void Actor::SetState(ActorState state)
{
    // Don't change state if it's the same
    if (m_state == state) {
        return;
    }

    // Change state
    m_state = state;

    // Get the sprite for the new state
    std::shared_ptr<AnimatedSprite> sprite = GetCurrentSprite();
    if (sprite) {
        // Reset and play the animation
        sprite->Reset();
        sprite->Play();
    }
}

void Actor::SetVisible(bool visible)
{
    m_visible = visible;

    // Update sprite visibility
    std::shared_ptr<AnimatedSprite> sprite = GetCurrentSprite();
    if (sprite) {
        sprite->SetVisible(visible);
    }
}

bool Actor::MoveTo(int x, int y)
{
    // Don't move if already at the target
    if (m_x == x && m_y == y) {
        return false;
    }

    // Set target position
    m_targetX = x;
    m_targetY = y;
    m_moving = true;
    m_moveProgress = 0.0f;

    // Calculate direction
    int dx = m_targetX - m_x;
    int dy = m_targetY - m_y;

    if (dx > 0 && dy == 0) {
        SetDirection(Direction::RIGHT);
    } else if (dx < 0 && dy == 0) {
        SetDirection(Direction::LEFT);
    } else if (dx == 0 && dy > 0) {
        SetDirection(Direction::DOWN);
    } else if (dx == 0 && dy < 0) {
        SetDirection(Direction::UP);
    } else if (dx > 0 && dy > 0) {
        SetDirection(Direction::DOWN_RIGHT);
    } else if (dx > 0 && dy < 0) {
        SetDirection(Direction::UP_RIGHT);
    } else if (dx < 0 && dy > 0) {
        SetDirection(Direction::DOWN_LEFT);
    } else if (dx < 0 && dy < 0) {
        SetDirection(Direction::UP_LEFT);
    }

    // Set state to walking
    SetState(ActorState::WALKING);

    return true;
}

void Actor::StopMovement()
{
    m_moving = false;
    m_moveProgress = 0.0f;

    // Set state to idle
    SetState(ActorState::IDLE);
}

bool Actor::Attack()
{
    // Don't attack if already attacking
    if (m_state == ActorState::ATTACKING) {
        return false;
    }

    // Set state to attacking
    SetState(ActorState::ATTACKING);

    return true;
}

bool Actor::CastSpell(int spellId)
{
    // Don't cast if already casting
    if (m_state == ActorState::CASTING) {
        return false;
    }

    // Set state to casting
    SetState(ActorState::CASTING);

    return true;
}

bool Actor::TakeDamage(int damage)
{
    // Set state to hit
    SetState(ActorState::HIT);

    // Reduce health
    m_health = std::max(0, m_health - damage);

    // Check if dead
    if (m_health <= 0) {
        SetState(ActorState::DYING);
        return false;  // Dead
    }

    return true;  // Still alive
}

void Actor::AddSprite(ActorState state, std::shared_ptr<AnimatedSprite> sprite)
{
    m_sprites[state] = sprite;
}

void Actor::UpdateMovement(int deltaTime)
{
    if (!m_moving) {
        return;
    }

    // Calculate movement progress
    float moveDistance = m_moveSpeed * deltaTime / 1000.0f;  // Pixels per millisecond
    m_moveProgress += moveDistance;

    // Calculate total distance
    float totalDistance = std::sqrt(
        std::pow(m_targetX - m_x, 2) +
        std::pow(m_targetY - m_y, 2)
    );

    // Check if movement is complete
    if (m_moveProgress >= totalDistance) {
        // Reached the target
        m_x = m_targetX;
        m_y = m_targetY;
        StopMovement();
    } else {
        // Calculate new position
        float progress = m_moveProgress / totalDistance;
        m_x = m_x + (m_targetX - m_x) * progress;
        m_y = m_y + (m_targetY - m_y) * progress;
    }

    // Update sprite position
    std::shared_ptr<AnimatedSprite> sprite = GetCurrentSprite();
    if (sprite) {
        int screenX, screenY;
        // TODO: Convert map coordinates to screen coordinates
        screenX = m_x * 48;  // Assuming tile width is 48
        screenY = m_y * 32;  // Assuming tile height is 32
        sprite->SetPosition(screenX, screenY);
    }
}

void Actor::UpdateAnimation(int deltaTime)
{
    // Get the current sprite
    std::shared_ptr<AnimatedSprite> sprite = GetCurrentSprite();
    if (!sprite) {
        return;
    }

    // Update the animation
    sprite->Update(deltaTime);

    // Check if the animation is complete
    if (!sprite->IsPlaying()) {
        // Animation complete, handle state transitions
        switch (m_state) {
            case ActorState::ATTACKING:
            case ActorState::CASTING:
            case ActorState::HIT:
                // Return to idle state
                SetState(ActorState::IDLE);
                break;
            case ActorState::DYING:
                // Transition to dead state
                SetState(ActorState::DEAD);
                break;
            default:
                // No state transition
                break;
        }
    }
}

std::shared_ptr<AnimatedSprite> Actor::GetCurrentSprite()
{
    // Find the sprite for the current state
    auto it = m_sprites.find(m_state);
    if (it != m_sprites.end()) {
        return it->second;
    }

    // If not found, try to find the idle sprite
    it = m_sprites.find(ActorState::IDLE);
    if (it != m_sprites.end()) {
        return it->second;
    }

    // No sprite found
    return nullptr;
}

void Actor::Say(const std::string& message, int duration)
{
    // Don't process empty messages
    if (message.empty()) {
        return;
    }

    // Set chat message and duration
    m_chatMessage = message;
    m_chatDuration = duration;
    m_chatTimer = duration;

    // Clear previous texture
    m_chatTexture.reset();

    // Create new texture if font is available
    if (m_chatFont) {
        // Create text surface
        SDL_Color textColor = {255, 255, 255, 255}; // White text
        SDL_Surface* textSurface = TTF_RenderText_Blended_Wrapped(m_chatFont, message.c_str(), textColor, 200); // Max width 200px

        if (textSurface) {
            // Create background surface (slightly larger than text)
            int padding = 5;
            int bgWidth = textSurface->w + padding * 2;
            int bgHeight = textSurface->h + padding * 2;

            SDL_Surface* bgSurface = SDL_CreateRGBSurface(0, bgWidth, bgHeight, 32, 0, 0, 0, 0);
            if (bgSurface) {
                // Fill background with semi-transparent black
                SDL_FillRect(bgSurface, NULL, SDL_MapRGBA(bgSurface->format, 0, 0, 0, 180));

                // Create rounded corners (simple version)
                // This is a simplified approach - for better results, use SDL_gfx or similar
                SDL_Rect cornerRect = {0, 0, 2, 2};
                SDL_FillRect(bgSurface, &cornerRect, SDL_MapRGBA(bgSurface->format, 0, 0, 0, 0));

                cornerRect.x = bgWidth - 2;
                SDL_FillRect(bgSurface, &cornerRect, SDL_MapRGBA(bgSurface->format, 0, 0, 0, 0));

                cornerRect.y = bgHeight - 2;
                SDL_FillRect(bgSurface, &cornerRect, SDL_MapRGBA(bgSurface->format, 0, 0, 0, 0));

                cornerRect.x = 0;
                SDL_FillRect(bgSurface, &cornerRect, SDL_MapRGBA(bgSurface->format, 0, 0, 0, 0));

                // Blit text onto background
                SDL_Rect textRect = {padding, padding, textSurface->w, textSurface->h};
                SDL_BlitSurface(textSurface, NULL, bgSurface, &textRect);

                // Create texture from combined surface
                // Get renderer from sprite
                SDL_Renderer* renderer = nullptr;
                std::shared_ptr<AnimatedSprite> sprite = GetCurrentSprite();
                if (sprite) {
                    // Since AnimatedSprite doesn't have GetRenderer, we'll get it from elsewhere
                    // For now, we'll use a global renderer or pass it from elsewhere
                    // This is a temporary solution
                    renderer = SDL_GetRenderer(SDL_GetWindowFromID(1)); // Assuming window ID 1
                }

                if (renderer) {
                    m_chatTexture = std::make_shared<Texture>(renderer);
                    if (m_chatTexture) {
                        m_chatTexture->LoadFromSurface(bgSurface);
                    }
                }

                SDL_FreeSurface(bgSurface);
            }

            SDL_FreeSurface(textSurface);
        }
    }
}

void Actor::UpdateChatBubble(int deltaTime)
{
    // Update chat timer
    if (m_chatTimer > 0) {
        m_chatTimer -= deltaTime;
        if (m_chatTimer <= 0) {
            // Chat duration expired, clear message and texture
            m_chatMessage.clear();
            m_chatTexture.reset();
        }
    }
}

void Actor::RenderChatBubble(SDL_Renderer* renderer)
{
    if (!m_chatTexture || m_chatTimer <= 0) {
        return;
    }

    // Get current sprite for position reference
    std::shared_ptr<AnimatedSprite> sprite = GetCurrentSprite();
    if (!sprite) {
        return;
    }

    // Calculate position above the actor
    int spriteX = sprite->GetX();
    int spriteY = sprite->GetY();
    int spriteWidth = sprite->GetWidth();
    int spriteHeight = sprite->GetHeight();

    int chatX = spriteX + (spriteWidth - m_chatTexture->GetWidth()) / 2;
    int chatY = spriteY - m_chatTexture->GetHeight() - 5; // 5 pixels above the actor

    // Ensure chat bubble is on screen
    if (chatY < 0) {
        chatY = 0;
    }

    // Render chat bubble with fade-out effect near the end
    if (m_chatTimer < 1000) { // Last second fade out
        // Calculate alpha based on remaining time
        Uint8 alpha = static_cast<Uint8>((m_chatTimer * 255) / 1000);
        m_chatTexture->SetAlpha(alpha);
    } else {
        m_chatTexture->SetAlpha(255);
    }

    // Render the chat bubble
    m_chatTexture->Render(chatX, chatY);
}
