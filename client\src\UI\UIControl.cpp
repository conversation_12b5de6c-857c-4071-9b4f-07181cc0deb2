#include "UIControl.h"
#include "../Graphics/WILLoader.h"
#include <algorithm>
#include <iostream>
#include "UIManager.h"

UIControl::UIControl(int x, int y, int width, int height, const std::string& name, bool useRelativePosition)
    : m_x(x)
    , m_y(y)
    , m_width(width)
    , m_height(height)
    , m_visible(true)
    , m_enabled(true)
    , m_name(name)
    , m_useRelativePosition(useRelativePosition)
    , m_resourceFile("")
    , m_normalImageIndex(-1)
    , m_hoverImageIndex(-1)
    , m_pressedImageIndex(-1)
    , m_disabledImageIndex(-1)
    , m_wilManager(nullptr)
    , m_userData(0)
    , m_parent(nullptr)
    , m_mouseOver(false)
    , m_mouseDown(false)
{
}

UIControl::UIControl(int x, int y, const std::string& name, bool useRelativePosition)
    : m_x(x)
    , m_y(y)
    , m_width(0)  // Width will be determined by image dimensions
    , m_height(0) // Height will be determined by image dimensions
    , m_visible(true)
    , m_enabled(true)
    , m_name(name)
    , m_useRelativePosition(useRelativePosition)
    , m_resourceFile("")
    , m_normalImageIndex(-1)
    , m_hoverImageIndex(-1)
    , m_pressedImageIndex(-1)
    , m_disabledImageIndex(-1)
    , m_wilManager(nullptr)
    , m_userData(0)
    , m_parent(nullptr)
    , m_mouseOver(false)
    , m_mouseDown(false)
{
}

UIControl::~UIControl()
{
}

void UIControl::Update(int deltaTime)
{
    // Update children
    for (auto& child : m_children) {
        if (child->IsVisible()) {
            child->Update(deltaTime);
        }
    }
}

void UIControl::Render(SDL_Renderer* renderer)
{
    // Skip if not visible
    if (!m_visible) {
        return;
    }

    // Get absolute position
    int absoluteX = GetAbsoluteX();
    int absoluteY = GetAbsoluteY();

    // Render control background based on state
    if (!m_resourceFile.empty() && m_wilManager) {
        // Determine which image index to use based on control state
        int imageIndex = -1;

        if (!m_enabled && m_disabledImageIndex >= 0 && m_disabledImageIndex != NO_IMAGE) {
            // Disabled state
            imageIndex = m_disabledImageIndex;
        } else if (m_mouseDown && m_pressedImageIndex >= 0 && m_pressedImageIndex != NO_IMAGE) {
            // Pressed state
            imageIndex = m_pressedImageIndex;
        } else if (m_mouseOver && m_hoverImageIndex >= 0 && m_hoverImageIndex != NO_IMAGE) {
            // Hover state
            imageIndex = m_hoverImageIndex;
        } else if (m_normalImageIndex >= 0 && m_normalImageIndex != NO_IMAGE) {
            // Normal state
            imageIndex = m_normalImageIndex;
        }

        // Render the image if we have a valid index
        if (imageIndex >= 0) {
            // Get the surface from the WIL manager
            SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, imageIndex);

            if (surface) {
                // Create a temporary texture from the surface
                SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);

                if (texture) {
                    // Get texture dimensions
                    int texWidth, texHeight;
                    SDL_QueryTexture(texture, nullptr, nullptr, &texWidth, &texHeight);

                    // Create rendering rectangle
                    SDL_Rect destRect = {absoluteX, absoluteY, m_width, m_height};

                    // If width or height is 0, use the texture dimensions
                    if (m_width == 0) {
                        destRect.w = texWidth;
                        // Update the actual width property for hit testing
                        m_width = texWidth;
                    }
                    if (m_height == 0) {
                        destRect.h = texHeight;
                        // Update the actual height property for hit testing
                        m_height = texHeight;
                    }

                    SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                    // Free the texture
                    SDL_DestroyTexture(texture);
                }
            }
        } else {
            // When image is missing, render as transparent (no rendering)
            // This allows parent elements to show through
        }
    } else {
        // When no resource file or WIL manager, render as transparent (no rendering)
        // This allows parent elements to show through
    }

    // Render children
    for (auto& child : m_children) {
        if (child->IsVisible()) {
            child->Render(renderer);
        }
    }
}

bool UIControl::HandleMouseMotion(int x, int y)
{
    // 先递归 children（从后往前，Z序高的先处理）
    for (auto it = m_children.rbegin(); it != m_children.rend(); ++it) {
        if ((*it)->IsVisible() && (*it)->IsEnabled()) {
            if ((*it)->HandleMouseMotion(x, y)) {
                return true;
            }
        }
    }

    // 再处理自己
    bool wasMouseOver = m_mouseOver;
    m_mouseOver = IsPointInside(x, y);

    if (m_mouseOver && !wasMouseOver) {
        if (m_onMouseEnter) {
            m_onMouseEnter(this);
        }
    } else if (!m_mouseOver && wasMouseOver) {
        if (m_onMouseLeave) {
            m_onMouseLeave(this);
        }
    }
    return m_mouseOver;
}

bool UIControl::HandleMouseButton(Uint8 button, bool pressed, int x, int y)
{
    // 先递归 children（从后往前，Z序高的先处理）
    for (auto it = m_children.rbegin(); it != m_children.rend(); ++it) {
        if ((*it)->IsVisible() && (*it)->IsEnabled()) {
            if ((*it)->HandleMouseButton(button, pressed, x, y)) {
                return true;
            }
        }
    }

    // 再处理自己
    bool isInside = IsPointInside(x, y);
    if (button == SDL_BUTTON_LEFT) {
        if (pressed) {
            m_mouseDown = isInside;
        } else if (m_mouseDown && isInside) {
            if (m_onClick) {
                m_onClick(this);
            }
            m_mouseDown = false;
        } else {
            m_mouseDown = false;
        }
    }
    return isInside && m_enabled;
}

bool UIControl::HandleKey(SDL_Keycode key, bool pressed)
{
    // 先递归 children（从后往前，Z序高的先处理）
    for (auto it = m_children.rbegin(); it != m_children.rend(); ++it) {
        if ((*it)->IsVisible() && (*it)->IsEnabled()) {
            if ((*it)->HandleKey(key, pressed)) {
                return true;
            }
        }
    }
    // 默认控件不处理键盘事件
    return false;
}

bool UIControl::HandleEvent(const SDL_Event& event)
{
    switch (event.type) {
        case SDL_MOUSEMOTION:
            return HandleMouseMotion(event.motion.x, event.motion.y);

        case SDL_MOUSEBUTTONDOWN:
            return HandleMouseButton(event.button.button, true, event.button.x, event.button.y);

        case SDL_MOUSEBUTTONUP:
            return HandleMouseButton(event.button.button, false, event.button.x, event.button.y);

        case SDL_KEYDOWN:
            return HandleKey(event.key.keysym.sym, true);

        case SDL_KEYUP:
            return HandleKey(event.key.keysym.sym, false);

        default:
            return false;
    }
}

void UIControl::SetPosition(int x, int y)
{
    int dx = x - m_x;
    int dy = y - m_y;

    m_x = x;
    m_y = y;

    // Update children positions only for non-relative positioned children
    for (auto& child : m_children) {
        if (!child->IsUsingRelativePosition()) {
            child->SetPosition(child->GetX() + dx, child->GetY() + dy);
        }
    }
}

void UIControl::SetSize(int width, int height)
{
    m_width = width;
    m_height = height;
}

void UIControl::SetVisible(bool visible)
{
    m_visible = visible;
}

void UIControl::SetEnabled(bool enabled)
{
    m_enabled = enabled;
}

void UIControl::SetName(const std::string& name)
{
    m_name = name;
}

void UIControl::SetParent(UIControl* parent)
{
    m_parent = parent;
}

void UIControl::AddChild(std::shared_ptr<UIControl> child, bool useRelativePosition)
{
    child->SetParent(this);
    child->SetUseRelativePosition(useRelativePosition);

    // Pass WIL manager to child
    if (m_wilManager) {
        child->SetWILManager(m_wilManager);
    }

    m_children.push_back(child);
}

void UIControl::SetUseRelativePosition(bool useRelativePosition)
{
    m_useRelativePosition = useRelativePosition;
}

int UIControl::GetAbsoluteX() const
{
    if (m_useRelativePosition && m_parent) {
        return m_parent->GetAbsoluteX() + m_x;
    }
    return m_x;
}

int UIControl::GetAbsoluteY() const
{
    if (m_useRelativePosition && m_parent) {
        return m_parent->GetAbsoluteY() + m_y;
    }
    return m_y;
}

bool UIControl::RemoveChild(UIControl* child)
{
    auto it = std::find_if(m_children.begin(), m_children.end(),
        [child](const std::shared_ptr<UIControl>& c) {
            return c.get() == child;
        });

    if (it != m_children.end()) {
        (*it)->SetParent(nullptr);
        m_children.erase(it);
        return true;
    }

    return false;
}

void UIControl::SetOnClick(std::function<void(UIControl*)> handler)
{
    m_onClick = handler;
}

void UIControl::SetOnMouseEnter(std::function<void(UIControl*)> handler)
{
    m_onMouseEnter = handler;
}

void UIControl::SetOnMouseLeave(std::function<void(UIControl*)> handler)
{
    m_onMouseLeave = handler;
}

void UIControl::SetResourceFile(const std::string& filename)
{
    m_resourceFile = filename;

    // Try to update size from normal image index first
    // if (m_normalImageIndex >= 0 && m_normalImageIndex != NO_IMAGE) {
    //     UpdateSizeFromImage(m_normalImageIndex);
    // }
}

void UIControl::SetNormalImageIndex(int index)
{
    m_normalImageIndex = index;
    UpdateSizeFromImage(index);
}

void UIControl::SetHoverImageIndex(int index)
{
    m_hoverImageIndex = index;
    UpdateSizeFromImage(index);
}

void UIControl::SetPressedImageIndex(int index)
{
    m_pressedImageIndex = index;
    UpdateSizeFromImage(index);
}

void UIControl::SetDisabledImageIndex(int index)
{
    m_disabledImageIndex = index;
    UpdateSizeFromImage(index);
}

void UIControl::SetWILManager(std::shared_ptr<WILManager> wilManager)
{
    m_wilManager = wilManager;

    // Propagate to children
    for (auto& child : m_children) {
        child->SetWILManager(wilManager);
    }

    // Try to update size from normal image index first
    if (m_normalImageIndex >= 0 && m_normalImageIndex != NO_IMAGE) {
        UpdateSizeFromImage(m_normalImageIndex);
    }
}

void UIControl::SetResourceType(ResourceManager::ResourceType resourceType)
{
    // Get resource manager
    ResourceManager* resourceManager = ResourceManager::GetInstance();
    if (!resourceManager) {
        std::cerr << "ResourceManager is not initialized" << std::endl;
        return;
    }

    // Get resource path
    std::string resourcePath = resourceManager->GetResourcePath(resourceType);
    if (resourcePath.empty()) {
        std::cerr << "Resource path not found for type: " << ResourceManager::ResourceTypeToString(resourceType) << std::endl;
        return;
    }

    // Set resource file - this will also try to update size from normal image index
    SetResourceFile(resourcePath);
}

bool UIControl::IsPointInside(int x, int y) const
{
    int absoluteX = GetAbsoluteX();
    int absoluteY = GetAbsoluteY();
    return x >= absoluteX && x < absoluteX + m_width && y >= absoluteY && y < absoluteY + m_height;
}

void UIControl::UpdateSizeFromImage(int imageIndex)
{
    // Skip if width and height are already set
    if (m_width != 0 && m_height != 0) {
        return;
    }

    // Skip if no resource file or WIL manager
    if (m_resourceFile.empty() || !m_wilManager) {
        return;
    }

    // Skip if image index is invalid
    if (imageIndex < 0 || imageIndex == NO_IMAGE) {
        return;
    }

    // Get the surface from the WIL manager
    SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, imageIndex);
    if (!surface) {
        return;
    }

    // Update width and height if they are 0
    if (m_width == 0) {
        m_width = surface->w;
    }
    if (m_height == 0) {
        m_height = surface->h;
    }
}

void UIContainer::SetUIManager(UIManager* manager) {
    m_uiManager = manager;
    for (auto& child : m_children) {
        child->SetUIManager(manager);
    }
}

UIManager* UIContainer::GetUIManager() {
    if (m_uiManager) return m_uiManager;
    if (m_parent) return m_parent->GetUIManager();
    return nullptr;
}

// UIContainer 实现

UIContainer::~UIContainer() {}
