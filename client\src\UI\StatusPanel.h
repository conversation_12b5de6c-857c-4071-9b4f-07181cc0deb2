#pragma once

#include "UIControl.h"
#include "EquipmentPanel.h"
#include "../Actor/Player.h"
#include <memory>
#include <vector>
#include <string>
#include <SDL2/SDL.h>

/**
 * @class StatusPanel
 * @brief UI for displaying character status information
 *
 * This panel displays character stats, equipment, guild information, and settings.
 * It is toggled with the F10 key or by clicking the status button in the main UI.
 * This follows the original Delphi project's structure where statusPanel and skillPanel
 * are in the same parent interface.
 */
class StatusPanel : public UIControl {
public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param player Player reference
     */
    StatusPanel(int x, int y, int width, int height, std::shared_ptr<Player> player);

    /**
     * @brief Destructor
     */
    virtual ~StatusPanel();

    /**
     * @brief Update the UI
     * @param deltaTime Time since last update in milliseconds
     */
    virtual void Update(int deltaTime) override;

    /**
     * @brief Render the UI
     * @param renderer SDL renderer
     */
    virtual void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Handle mouse button events
     * @param button Mouse button
     * @param pressed True if pressed, false if released
     * @param x Mouse x position
     * @param y Mouse y position
     * @return True if handled, false otherwise
     */
    virtual bool HandleMouseButton(Uint8 button, bool pressed, int x, int y) override;

    /**
     * @brief Handle mouse motion events
     * @param x Mouse x position
     * @param y Mouse y position
     * @return True if handled, false otherwise
     */
    virtual bool HandleMouseMotion(int x, int y) override;

    /**
     * @brief Handle key events
     * @param key Key code
     * @param pressed True if pressed, false if released
     * @return True if handled, false otherwise
     */
    virtual bool HandleKey(SDL_Keycode key, bool pressed) override;

    /**
     * @brief Show the UI
     */
    void Show();

    /**
     * @brief Hide the UI
     */
    void Hide();

    /**
     * @brief Refresh the status display
     */
    void RefreshStatus();

    /**
     * @brief Set the font for text rendering
     * @param font Font to use
     */
    void SetFont(std::shared_ptr<Font> font) {
        m_font = font;
        if (m_equipmentUI) {
            m_equipmentUI->SetFont(font);
        }
    }

private:
    /**
     * @brief Create UI controls
     */
    void CreateControls();

    /**
     * @brief Render character stats
     * @param renderer SDL renderer
     */
    void RenderStats(SDL_Renderer* renderer);

    /**
     * @brief Render character model with equipment
     * @param renderer SDL renderer
     */
    void RenderCharacterModel(SDL_Renderer* renderer);

    /**
     * @brief Handle close button click
     * @param control Control that was clicked
     */
    void OnCloseButtonClick(UIControl* control);

    /**
     * @brief Handle tab button click
     * @param control Control that was clicked (can be nullptr)
     * @param tabIndex Index of the tab that was clicked
     */
    void OnTabButtonClick(UIControl* control, int tabIndex);

    std::shared_ptr<Player> m_player;                  ///< Player reference
    std::shared_ptr<Button> m_closeButton;             ///< Close button
    std::shared_ptr<Label> m_titleLabel;               ///< Title label
    std::shared_ptr<EquipmentPanel> m_equipmentUI;     ///< Equipment panel
    std::vector<std::shared_ptr<Button>> m_tabButtons; ///< Tab buttons
    std::shared_ptr<Font> m_font;                      ///< Font for rendering text

    SDL_Color m_textColor;                             ///< Text color
    SDL_Color m_textShadowColor;                       ///< Text shadow color

    int m_currentTab;                                  ///< Current tab index
    std::string m_resourceFile;                        ///< Resource file for UI images
};

