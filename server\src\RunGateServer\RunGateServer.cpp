#include "RunGateServer.h"
#include "../Common/Logger.h"
#include "../Common/Utils.h"
#include "../Protocol/PacketTypes.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <regex>
#include <chrono>
#include <iomanip>
#include <cstring>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#else
#include <arpa/inet.h>
#include <netinet/in.h>
#endif

namespace MirServer {

RunGateServer::RunGateServer()
    : m_sessionCount(0), m_isRunning(false), m_serverReady(false), 
      m_gateReady(false), m_checkServerFail(false),
      m_serverAddr("127.0.0.1"), m_serverPort(5000),
      m_gateAddr("0.0.0.0"), m_gatePort(7200),
      m_maxConnOfIP(50), m_maxClientPacketSize(8000), m_nomClientPacketSize(200),
      m_maxClientMsgCount(20), m_clientSendBlockSize(16), m_clientCheckTimeOut(50),
      m_maxOverNomSizeCount(2), m_checkServerTimeOutTime(180000), // 3分钟
      m_blockMethod(RUNGATE_DISCONNECT), m_attackTick(300), m_attackCount(5),
      m_sayMsgMaxLen(100), m_sayMsgTime(1000), m_replaceWord("*"),
      m_reviceMsgSize(0), m_deCodeMsgSize(0), m_sendBlockSize(0),
      m_processMsgSize(0), m_humLogonMsgSize(0), m_humPlayMsgSize(0),
      m_dwProcessReviceMsgTimeLimit(30), m_dwProcessSendMsgTimeLimit(30),
      m_dwLoopTime(0), m_dwProcessServerMsgTime(0), m_dwProcessClientMsgTime(0),
      m_dwReConnectServerTime(0), m_dwRefConsoleMsgTick(0), m_boDecodeMsgLock(false) {
    
    // 初始化INI文件处理器
    m_iniFile = std::make_unique<IniFile>();
    
    // 初始化时间戳
    auto now = std::chrono::steady_clock::now();
    m_dwCheckClientTick = m_dwProcessPacketTick = m_dwRefConsolMsgTick = 
    m_dwLoopCheckTick = m_dwCheckServerTick = now;
    
    // 初始化会话数组
    RestSessionArray();
}

RunGateServer::~RunGateServer() {
    Stop();
}

bool RunGateServer::Initialize(const std::string& configFile) {
    try {
        m_configFile = configFile;
        
        // 加载配置文件到INI处理器
        m_iniFile->LoadFromFile(configFile);
        
        // 加载配置
        LoadConfig();
        
        // 加载敏感词列表
        LoadAbuseFile();
        
        // 加载阻止IP列表
        LoadBlockIPFile();
        
        // 创建客户端网络管理器
        m_clientManager = std::make_unique<NetworkManager>();
        
        AddMainLogMsg("RunGateServer initialized successfully", 0);
        return true;
        
    } catch (const std::exception& e) {
        AddMainLogMsg("Failed to initialize RunGateServer: " + std::string(e.what()), 1);
        return false;
    }
}

bool RunGateServer::Start() {
    try {
        if (m_isRunning) {
            AddMainLogMsg("RunGateServer is already running", 1);
            return false;
        }
        
        // 启动客户端监听
        if (!m_clientManager->StartServer(m_gateAddr, m_gatePort)) {
            AddMainLogMsg("Failed to start client listener on " + m_gateAddr + ":" + std::to_string(m_gatePort), 1);
            return false;
        }
        
        // 设置客户端事件回调
        m_clientManager->SetOnClientConnect([this](std::shared_ptr<ClientConnection> conn) {
            OnClientConnect(conn);
        });
        
        m_clientManager->SetOnClientDisconnect([this](std::shared_ptr<ClientConnection> conn) {
            OnClientDisconnect(conn);
        });
        
        m_clientManager->SetOnClientMessage([this](std::shared_ptr<ClientConnection> conn, const std::vector<BYTE>& data) {
            OnClientMessage(conn, data);
        });
        
        // 连接到游戏服务器
        m_serverConnection = m_clientManager->ConnectToServer(m_serverAddr, m_serverPort);
        
        if (!m_serverConnection) {
            AddMainLogMsg("Failed to connect to game server " + m_serverAddr + ":" + std::to_string(m_serverPort), 1);
            return false;
        }
        
        // 设置服务器事件回调（暂时注释掉，等待实现）
        /*
        m_serverConnection->SetOnConnect([this]() {
            OnServerConnect();
        });
        
        m_serverConnection->SetOnDisconnect([this]() {
            OnServerDisconnect();
        });
        
        m_serverConnection->SetOnMessage([this](const std::vector<BYTE>& data) {
            OnServerMessage(data);
        });
        */
        
        m_isRunning = true;
        
        // 启动工作线程
        m_processThread = std::thread(&RunGateServer::ProcessThread, this);
        m_checkThread = std::thread(&RunGateServer::CheckThread, this);
        m_decodeThread = std::thread(&RunGateServer::DecodeThread, this);
        
        AddMainLogMsg("RunGateServer started successfully", 0);
        AddMainLogMsg("Listening on " + m_gateAddr + ":" + std::to_string(m_gatePort), 0);
        AddMainLogMsg("Connected to game server " + m_serverAddr + ":" + std::to_string(m_serverPort), 0);
        
        return true;
        
    } catch (const std::exception& e) {
        AddMainLogMsg("Failed to start RunGateServer: " + std::string(e.what()), 1);
        return false;
    }
}

void RunGateServer::Stop() {
    if (!m_isRunning) return;
    
    AddMainLogMsg("Stopping RunGateServer...", 0);
    
    m_isRunning = false;
    
    // 关闭所有客户端连接
    CloseAllUser();
    
    // 停止客户端监听
    if (m_clientManager) {
        m_clientManager->StopServer();
    }
    
    // 断开服务器连接
    if (m_serverConnection) {
        m_serverConnection->Disconnect();
    }
    
    // 等待工作线程结束
    if (m_processThread.joinable()) {
        m_processThread.join();
    }
    if (m_checkThread.joinable()) {
        m_checkThread.join();
    }
    if (m_decodeThread.joinable()) {
        m_decodeThread.join();
    }
    
    // 保存配置和阻止IP列表
    SaveConfig();
    SaveBlockIPFile();
    
    AddMainLogMsg("RunGateServer stopped", 0);
}

void RunGateServer::LoadConfig() {
    AddMainLogMsg("加载配置文件...", 3);
    
    try {
        // 从INI文件读取服务器配置
        m_serverAddr = m_iniFile->GetString("Server", "ServerAddr", "127.0.0.1");
        m_serverPort = m_iniFile->GetInt("Server", "ServerPort", 5000);
        m_gateAddr = m_iniFile->GetString("Server", "GateAddr", "0.0.0.0");
        m_gatePort = m_iniFile->GetInt("Server", "GatePort", 7200);
        m_maxConnOfIP = m_iniFile->GetInt("Server", "MaxConnOfIPaddr", 50);
        m_maxClientPacketSize = m_iniFile->GetInt("Server", "MaxClientPacketSize", 8000);
        m_nomClientPacketSize = m_iniFile->GetInt("Server", "NomClientPacketSize", 200);
        m_maxClientMsgCount = m_iniFile->GetInt("Server", "MaxClientMsgCount", 20);
        m_clientSendBlockSize = m_iniFile->GetInt("Server", "ClientSendBlockSize", 16);
        m_clientCheckTimeOut = m_iniFile->GetInt("Server", "ClientCheckTimeOut", 50);
        m_maxOverNomSizeCount = m_iniFile->GetInt("Server", "MaxOverNomSizeCount", 2);
        m_checkServerTimeOutTime = m_iniFile->GetInt("Server", "CheckServerTimeOutTime", 180000);
        
        int blockMethod = m_iniFile->GetInt("Server", "BlockMethod", 0);
        if (blockMethod >= 0 && blockMethod <= 2) {
            m_blockMethod = static_cast<RunGateBlockIPMethod>(blockMethod);
        }
        
        m_attackTick = m_iniFile->GetInt("Server", "AttackTick", 300);
        m_attackCount = m_iniFile->GetInt("Server", "AttackCount", 5);
        m_sayMsgMaxLen = m_iniFile->GetInt("Server", "SayMsgMaxLen", 100);
        m_sayMsgTime = m_iniFile->GetInt("Server", "SayMsgTime", 1000);
        m_replaceWord = m_iniFile->GetString("Server", "ReplaceWord", "*");
        
        // 从INI文件读取速度控制配置
        m_speedConfig.boHit = m_iniFile->GetBool("Setup", "HitSpeed", true);
        m_speedConfig.boSpell = m_iniFile->GetBool("Setup", "SpellSpeed", true);
        m_speedConfig.boRun = m_iniFile->GetBool("Setup", "RunSpeed", true);
        m_speedConfig.boWalk = m_iniFile->GetBool("Setup", "WalkSpeed", true);
        m_speedConfig.boTurn = m_iniFile->GetBool("Setup", "TurnSpeed", true);
        
        m_speedConfig.nHitTime = m_iniFile->GetInt("Setup", "HitTime", 500);
        m_speedConfig.nSpellTime = m_iniFile->GetInt("Setup", "SpellTime", 500);
        m_speedConfig.nRunTime = m_iniFile->GetInt("Setup", "RunTime", 300);
        m_speedConfig.nWalkTime = m_iniFile->GetInt("Setup", "WalkTime", 300);
        m_speedConfig.nTurnTime = m_iniFile->GetInt("Setup", "TurnTime", 200);
        
        m_speedConfig.nHitCount = m_iniFile->GetInt("Setup", "HitCount", 3);
        m_speedConfig.nSpellCount = m_iniFile->GetInt("Setup", "SpellCount", 3);
        m_speedConfig.nRunCount = m_iniFile->GetInt("Setup", "RunCount", 3);
        m_speedConfig.nWalkCount = m_iniFile->GetInt("Setup", "WalkCount", 3);
        m_speedConfig.nTurnCount = m_iniFile->GetInt("Setup", "TurnCount", 3);
        
        BYTE speedMode = static_cast<BYTE>(m_iniFile->GetInt("Setup", "SpeedControlMode", 1));
        if (speedMode <= 2) {
            m_speedConfig.btSpeedControlMode = speedMode;
        }
        
        m_speedConfig.boSpeedShowMsg = m_iniFile->GetBool("Setup", "HintSpeed", true);
        m_speedConfig.sSpeedShowMsg = m_iniFile->GetString("Setup", "HintSpeedMsg", 
            "系统提示: 请按照游戏节奏,不要使用非法外挂!");
        
        BYTE msgColor = static_cast<BYTE>(m_iniFile->GetInt("Setup", "MsgColor", 0));
        if (msgColor <= 2) {
            m_speedConfig.btMsgColor = msgColor;
        }
        
        AddMainLogMsg("配置文件加载完成", 3);
        
        // 输出关键配置信息
        AddMainLogMsg("服务器地址: " + m_serverAddr + ":" + std::to_string(m_serverPort), 3);
        AddMainLogMsg("网关地址: " + m_gateAddr + ":" + std::to_string(m_gatePort), 3);
        AddMainLogMsg("每IP最大连接: " + std::to_string(m_maxConnOfIP), 3);
        AddMainLogMsg("最大包大小: " + std::to_string(m_maxClientPacketSize), 3);
        AddMainLogMsg("阻止方法: " + std::to_string(static_cast<int>(m_blockMethod)), 3);
        
    } catch (const std::exception& e) {
        AddMainLogMsg("加载配置文件时发生错误: " + std::string(e.what()), 1);
        AddMainLogMsg("将使用默认配置", 2);
    }
}

void RunGateServer::SaveConfig() {
    try {
        // 网络配置
        SetConfigString("Server", "ServerAddr", m_serverAddr);
        SetConfigInt("Server", "ServerPort", m_serverPort);
        SetConfigString("Gate", "GateAddr", m_gateAddr);
        SetConfigInt("Gate", "GatePort", m_gatePort);
        
        // 连接限制配置
        SetConfigInt("Limit", "MaxConnOfIP", m_maxConnOfIP);
        SetConfigInt("Limit", "MaxClientPacketSize", m_maxClientPacketSize);
        SetConfigInt("Limit", "NomClientPacketSize", m_nomClientPacketSize);
        SetConfigInt("Limit", "MaxClientMsgCount", m_maxClientMsgCount);
        SetConfigInt("Limit", "ClientSendBlockSize", m_clientSendBlockSize);
        SetConfigInt("Limit", "MaxOverNomSizeCount", m_maxOverNomSizeCount);
        
        // 超时配置
        SetConfigInt("Timeout", "ClientCheckTimeOut", m_clientCheckTimeOut);
        SetConfigInt("Timeout", "CheckServerTimeOut", m_checkServerTimeOutTime);
        
        // 攻击防护配置
        SetConfigInt("Attack", "AttackTick", m_attackTick);
        SetConfigInt("Attack", "AttackCount", m_attackCount);
        SetConfigInt("Attack", "BlockMethod", static_cast<int>(m_blockMethod));
        
        // 聊天配置
        SetConfigInt("Chat", "SayMsgMaxLen", m_sayMsgMaxLen);
        SetConfigInt("Chat", "SayMsgTime", m_sayMsgTime);
        SetConfigString("Chat", "ReplaceWord", m_replaceWord);
        
        // 速度控制配置
        SetConfigBool("Speed", "CheckHit", m_speedConfig.boHit);
        SetConfigBool("Speed", "CheckSpell", m_speedConfig.boSpell);
        SetConfigBool("Speed", "CheckRun", m_speedConfig.boRun);
        SetConfigBool("Speed", "CheckWalk", m_speedConfig.boWalk);
        SetConfigBool("Speed", "CheckTurn", m_speedConfig.boTurn);
        
        SetConfigInt("Speed", "HitTime", m_speedConfig.nHitTime);
        SetConfigInt("Speed", "SpellTime", m_speedConfig.nSpellTime);
        SetConfigInt("Speed", "RunTime", m_speedConfig.nRunTime);
        SetConfigInt("Speed", "WalkTime", m_speedConfig.nWalkTime);
        SetConfigInt("Speed", "TurnTime", m_speedConfig.nTurnTime);
        
        SetConfigInt("Speed", "HitCount", m_speedConfig.nHitCount);
        SetConfigInt("Speed", "SpellCount", m_speedConfig.nSpellCount);
        SetConfigInt("Speed", "RunCount", m_speedConfig.nRunCount);
        SetConfigInt("Speed", "WalkCount", m_speedConfig.nWalkCount);
        SetConfigInt("Speed", "TurnCount", m_speedConfig.nTurnCount);
        
        SetConfigInt("Speed", "SpeedControlMode", m_speedConfig.btSpeedControlMode);
        SetConfigBool("Speed", "SpeedShowMsg", m_speedConfig.boSpeedShowMsg);
        SetConfigString("Speed", "SpeedShowMsg", m_speedConfig.sSpeedShowMsg);
        SetConfigInt("Speed", "MsgColor", m_speedConfig.btMsgColor);
        
        // 保存INI文件到磁盘
        m_iniFile->SaveToFile();
        
        AddMainLogMsg("Configuration saved successfully", 0);
        
    } catch (const std::exception& e) {
        AddMainLogMsg("Failed to save configuration: " + std::string(e.what()), 1);
    }
}

void RunGateServer::ReloadConfig() {
    AddMainLogMsg("Reloading configuration...", 0);
    LoadConfig();
    LoadAbuseFile();
    LoadBlockIPFile();
    AddMainLogMsg("Configuration reloaded", 0);
}

void RunGateServer::OnClientConnect(std::shared_ptr<ClientConnection> connection) {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    std::string remoteAddr = connection->GetRemoteIP();
    int socketHandle = static_cast<int>(connection->GetSocket());
    
    // 检查IP是否被阻止
    if (IsBlockedIP(remoteAddr)) {
        AddMainLogMsg("Blocked IP attempted to connect: " + remoteAddr, 1);
        connection->Disconnect();
        return;
    }
    
    // 检查连接数限制
    if (IsConnectionLimited(remoteAddr, socketHandle)) {
        AddMainLogMsg("Connection limit exceeded for IP: " + remoteAddr, 1);
        connection->Disconnect();
        return;
    }
    
    // 查找空闲会话槽
    int sessionIdx = -1;
    for (int i = 0; i < RUNGATE_MAX_SESSION; ++i) {
        if (!m_sessions[i].socket) {
            sessionIdx = i;
            break;
        }
    }
    
    if (sessionIdx == -1) {
        AddMainLogMsg("No available session slots for " + remoteAddr, 1);
        connection->Disconnect();
        return;
    }
    
    // 初始化会话信息
    auto& session = m_sessions[sessionIdx];
    session.socket = connection;
    session.nSckHandle = socketHandle;
    session.sRemoteAddr = remoteAddr;
    session.nUserListIndex = 0;
    session.nPacketIdx = -1;
    session.nPacketErrCount = 0;
    session.boStartLogon = true;
    session.boSendLock = false;
    session.boOverNomSize = false;
    session.nOverNomSizeCount = 0;
    session.nCheckSendLength = 0;
    session.boSendAvailable = true;
    session.boSendCheck = false;
    session.nReceiveLength = 0;
    session.sSocData.clear();
    session.sSendData.clear();
    session.gameSpeed.Reset();
    
    auto now = std::chrono::steady_clock::now();
    session.dwSendLatestTime = session.dwTimeOutTime = session.dwReceiveLengthTick = 
    session.dwReceiveTick = session.dwSayMsgTick = now;
    
    ++m_sessionCount;
    
    // 通知游戏服务器有新连接
    SendServerMsg(GM_OPEN, sessionIdx, socketHandle, 0, 0, nullptr);
    
    AddMainLogMsg("Client connected: " + remoteAddr + " (Session: " + std::to_string(sessionIdx) + ")", 0);
}

void RunGateServer::OnClientDisconnect(std::shared_ptr<ClientConnection> connection) {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    int socketHandle = static_cast<int>(connection->GetSocket());
    std::string remoteAddr = connection->GetRemoteIP();
    
    // 查找并清理会话
    for (int i = 0; i < RUNGATE_MAX_SESSION; ++i) {
        auto& session = m_sessions[i];
        if (session.socket && session.nSckHandle == socketHandle) {
            // 通知游戏服务器连接断开
            SendServerMsg(GM_CLOSE, i, socketHandle, session.nUserListIndex, 0, nullptr);
            
            // 清理会话
            session.socket.reset();
            session.nSckHandle = -1;
            session.sRemoteAddr.clear();
            session.sSocData.clear();
            session.sSendData.clear();
            session.gameSpeed.Reset();
            
            --m_sessionCount;
            
            AddMainLogMsg("Client disconnected: " + remoteAddr + " (Session: " + std::to_string(i) + ")", 0);
            break;
        }
    }
}

void RunGateServer::OnClientMessage(std::shared_ptr<ClientConnection> connection, const std::vector<BYTE>& data) {
    int socketHandle = static_cast<int>(connection->GetSocket());
    
    // 查找对应的会话
    int sessionIdx = -1;
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        for (int i = 0; i < RUNGATE_MAX_SESSION; ++i) {
            if (m_sessions[i].socket && m_sessions[i].nSckHandle == socketHandle) {
                sessionIdx = i;
                break;
            }
        }
    }
    
    if (sessionIdx == -1) {
        AddMainLogMsg("Received message from unknown client", 1);
        return;
    }
    
    // 检查消息大小
    if (data.size() > static_cast<size_t>(m_maxClientPacketSize)) {
        AddMainLogMsg("Oversized packet from client: " + std::to_string(data.size()) + " bytes", 1);
        
        auto& session = m_sessions[sessionIdx];
        session.boOverNomSize = true;
        ++session.nOverNomSizeCount;
        
        if (session.nOverNomSizeCount >= m_maxOverNomSizeCount) {
            AddMainLogMsg("Too many oversized packets from " + session.sRemoteAddr + ", disconnecting", 1);
            connection->Disconnect();
            return;
        }
    }
    
    // 将消息加入处理队列
    std::string msgData(reinterpret_cast<const char*>(data.data()), data.size());
    RunGateSendUserData userData(sessionIdx, socketHandle, msgData);
    
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_receiveQueue.push(userData);
    }
    
    m_reviceMsgSize += data.size();
}

void RunGateServer::OnServerConnect() {
    m_serverReady = true;
    m_gateReady = true;
    m_checkServerFail = false;
    AddMainLogMsg("Connected to game server successfully", 0);
}

void RunGateServer::OnServerDisconnect() {
    m_serverReady = false;
    m_gateReady = false;
    m_checkServerFail = true;
    AddMainLogMsg("Disconnected from game server", 1);
    
    // 尝试重新连接
    if (m_isRunning && m_clientManager) {
        AddMainLogMsg("Attempting to reconnect to game server...", 0);
        std::this_thread::sleep_for(std::chrono::seconds(5));
        m_serverConnection = m_clientManager->ConnectToServer(m_serverAddr, m_serverPort);
        if (m_serverConnection) {
            OnServerConnect();
        } else if (m_clientManager) {
            // 尝试重新连接
            m_serverConnection = m_clientManager->ConnectToServer(m_serverAddr, m_serverPort);
            if (m_serverConnection) {
                OnServerConnect();
            }
        }
    }
}

void RunGateServer::OnServerMessage(const std::vector<BYTE>& data) {
    // 解析服务器消息
    if (data.size() < sizeof(RunGateMessageHeader)) {
        AddMainLogMsg("Invalid server message size", 1);
        return;
    }
    
    const RunGateMessageHeader* header = reinterpret_cast<const RunGateMessageHeader*>(data.data());
    
    if (header->dwCode != RUNGATE_CODE) {
        AddMainLogMsg("Invalid server message code", 1);
        return;
    }
    
    // 处理不同类型的服务器消息
    switch (header->wIdent) {
        case GM_DATA: {
            // 转发数据到客户端
            std::lock_guard<std::mutex> lock(m_sessionMutex);
            int sessionIdx = header->wGSocketIdx;
            
            if (sessionIdx >= 0 && sessionIdx < RUNGATE_MAX_SESSION) {
                auto& session = m_sessions[sessionIdx];
                if (session.socket && session.nSckHandle == header->nSocket) {
                    // 提取消息数据
                    if (data.size() > sizeof(RunGateMessageHeader)) {
                        std::string msgData(
                            reinterpret_cast<const char*>(data.data() + sizeof(RunGateMessageHeader)),
                            data.size() - sizeof(RunGateMessageHeader)
                        );
                        
                        // 加入发送队列
                        RunGateSendUserData userData(sessionIdx, header->nSocket, msgData);
                        {
                            std::lock_guard<std::mutex> qlock(m_queueMutex);
                            m_sendQueue.push(userData);
                        }
                    }
                }
            }
            break;
        }
        
        case GM_SERVERUSERINDEX: {
            // 更新用户索引
            std::lock_guard<std::mutex> lock(m_sessionMutex);
            int sessionIdx = header->wGSocketIdx;
            
            if (sessionIdx >= 0 && sessionIdx < RUNGATE_MAX_SESSION) {
                auto& session = m_sessions[sessionIdx];
                if (session.socket && session.nSckHandle == header->nSocket) {
                    session.nUserListIndex = header->wUserListIndex;
                }
            }
            break;
        }
        
        case GM_CHECKSERVER: {
            // 服务器检查响应
            SendServerMsg(GM_CHECKCLIENT, 0, 0, 0, 0, nullptr);
            break;
        }
        
        default:
            AddMainLogMsg("Unknown server message type: " + std::to_string(header->wIdent), 1);
            break;
    }
}

// 工作线程实现
void RunGateServer::ProcessThread() {
    AddMainLogMsg("Process thread started", 0);
    
    while (m_isRunning) {
        try {
            // 处理发送队列
            {
                std::lock_guard<std::mutex> lock(m_queueMutex);
                while (!m_sendQueue.empty()) {
                    auto userData = m_sendQueue.front();
                    m_sendQueue.pop();
                    ProcessPacket(userData);
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            
        } catch (const std::exception& e) {
            AddMainLogMsg("Process thread error: " + std::string(e.what()), 1);
        }
    }
    
    AddMainLogMsg("Process thread stopped", 0);
}

void RunGateServer::CheckThread() {
    AddMainLogMsg("Check thread started", 0);
    
    while (m_isRunning) {
        try {
            auto now = std::chrono::steady_clock::now();
            
            // 定期检查服务器连接
            if (std::chrono::duration_cast<std::chrono::milliseconds>(now - m_dwCheckServerTick).count() >= 30000) { // 30秒
                m_dwCheckServerTick = now;
                
                if (m_serverReady) {
                    SendServerMsg(GM_CHECKSERVER, 0, 0, 0, 0, nullptr);
                } else if (m_clientManager) {
                    // 尝试重新连接到游戏服务器
                    m_serverConnection = m_clientManager->ConnectToServer(m_serverAddr, m_serverPort);
                    if (m_serverConnection) {
                        OnServerConnect();
                    }
                }
            }
            
            // 检查客户端连接超时
            {
                std::lock_guard<std::mutex> lock(m_sessionMutex);
                for (int i = 0; i < RUNGATE_MAX_SESSION; ++i) {
                    auto& session = m_sessions[i];
                    if (session.socket) {
                        // 检查接收超时
                        auto lastReceive = std::chrono::duration_cast<std::chrono::milliseconds>(
                            now - session.dwReceiveTick).count();
                        
                        if (lastReceive > 300000) { // 5分钟无数据，断开连接
                            AddMainLogMsg("Client timeout: " + session.sRemoteAddr, 1);
                            session.socket->Disconnect();
                        }
                    }
                }
            }
            
            std::this_thread::sleep_for(std::chrono::seconds(5));
            
        } catch (const std::exception& e) {
            AddMainLogMsg("Check thread error: " + std::string(e.what()), 1);
        }
    }
    
    AddMainLogMsg("Check thread stopped", 0);
}

void RunGateServer::DecodeThread() {
    AddMainLogMsg("Decode thread started", 0);
    
    while (m_isRunning) {
        try {
            // 防止重入
            if (m_boDecodeMsgLock) {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                continue;
            }
            
            m_boDecodeMsgLock = true;
            
            auto dwLoopCheckTick = std::chrono::steady_clock::now();
            
            try {
                // 显示主日志消息（如果需要的话）
                // ShowMainLogMsg();
                
                // 更新控制台信息
                if (std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::steady_clock::now() - m_dwRefConsolMsgTick).count() >= 1000) {
                    m_dwRefConsolMsgTick = std::chrono::steady_clock::now();
                    
                    // 重置统计数据
                    m_reviceMsgSize = 0;
                    m_deCodeMsgSize = 0;
                    m_sendBlockSize = 0;
                    m_processMsgSize = 0;
                    m_humLogonMsgSize = 0;
                    m_humPlayMsgSize = 0;
                }
                
                // 处理接收队列
                auto dwProcessReviceMsgLimiTick = std::chrono::steady_clock::now();
                while (true) {
                    std::unique_lock<std::mutex> lock(m_queueMutex);
                    if (m_receiveQueue.empty()) {
                        lock.unlock();
                        break;
                    }
                    auto userData = m_receiveQueue.front();
                    m_receiveQueue.pop();
                    lock.unlock();
                    
                    ProcessUserPacket(userData);
                    
                    if (std::chrono::duration_cast<std::chrono::milliseconds>(
                        std::chrono::steady_clock::now() - dwProcessReviceMsgLimiTick).count() > m_dwProcessReviceMsgTimeLimit) {
                        break;
                    }
                }
                
                // 处理发送队列
                dwProcessReviceMsgLimiTick = std::chrono::steady_clock::now();
                while (true) {
                    std::unique_lock<std::mutex> lock(m_queueMutex);
                    if (m_sendQueue.empty()) {
                        lock.unlock();
                        break;
                    }
                    auto userData = m_sendQueue.front();
                    m_sendQueue.pop();
                    lock.unlock();
                    
                    ProcessPacket(userData);
                    
                    if (std::chrono::duration_cast<std::chrono::milliseconds>(
                        std::chrono::steady_clock::now() - dwProcessReviceMsgLimiTick).count() > m_dwProcessSendMsgTimeLimit) {
                        break;
                    }
                }
                
                // 处理会话发送数据
                if (std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::steady_clock::now() - m_dwProcessPacketTick).count() > 300) {
                    m_dwProcessPacketTick = std::chrono::steady_clock::now();
                    
                    // 动态调整处理时间限制
                    {
                        std::lock_guard<std::mutex> lock(m_queueMutex);
                        if (!m_receiveQueue.empty()) {
                            if (m_dwProcessReviceMsgTimeLimit < 300) m_dwProcessReviceMsgTimeLimit++;
                        } else {
                            if (m_dwProcessReviceMsgTimeLimit > 30) m_dwProcessReviceMsgTimeLimit--;
                        }
                        
                        if (!m_sendQueue.empty()) {
                            if (m_dwProcessSendMsgTimeLimit < 300) m_dwProcessSendMsgTimeLimit++;
                        } else {
                            if (m_dwProcessSendMsgTimeLimit > 30) m_dwProcessSendMsgTimeLimit--;
                        }
                    }
                    
                    // 处理会话的发送数据
                    std::lock_guard<std::mutex> sessionLock(m_sessionMutex);
                    for (int i = 0; i < RUNGATE_MAX_SESSION; i++) {
                        auto& session = m_sessions[i];
                        if (session.socket && !session.sSendData.empty()) {
                            RunGateSendUserData userData(i, session.nSckHandle, "");
                            ProcessPacket(userData);
                            
                            if (std::chrono::duration_cast<std::chrono::milliseconds>(
                                std::chrono::steady_clock::now() - dwProcessReviceMsgLimiTick).count() > 20) {
                                break;
                            }
                        }
                    }
                }
                
                // 检查服务器连接
                if (std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::steady_clock::now() - m_dwCheckClientTick).count() > 2000) {
                    m_dwCheckClientTick = std::chrono::steady_clock::now();
                    
                    if (m_gateReady) {
                        SendServerMsg(GM_CHECKCLIENT, 0, 0, 0, 0, nullptr);
                    }
                    
                    if (std::chrono::duration_cast<std::chrono::milliseconds>(
                        std::chrono::steady_clock::now() - m_dwCheckServerTick).count() > m_checkServerTimeOutTime) {
                        m_checkServerFail = true;
                        if (m_serverConnection) {
                            m_serverConnection->Disconnect();
                        }
                    }
                    
                    // 减少循环时间
                    if (m_dwLoopTime > 30) m_dwLoopTime -= 20;
                    if (m_dwProcessServerMsgTime > 1) m_dwProcessServerMsgTime--;
                    if (m_dwProcessClientMsgTime > 1) m_dwProcessClientMsgTime--;
                }
                
            } catch (const std::exception& e) {
                AddMainLogMsg("[Exception] DecodeTimer inner: " + std::string(e.what()), 1);
            }
            
            m_boDecodeMsgLock = false;
            
            // 计算循环时间
            auto dwLoopProcessTime = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - dwLoopCheckTick).count();
            if (m_dwLoopTime < dwLoopProcessTime) {
                m_dwLoopTime = dwLoopProcessTime;
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            
        } catch (const std::exception& e) {
            AddMainLogMsg("Decode thread error: " + std::string(e.what()), 1);
            m_boDecodeMsgLock = false;
        }
    }
    
    AddMainLogMsg("Decode thread stopped", 0);
}

void RunGateServer::ProcessUserPacket(const RunGateSendUserData& userData) {
    if (userData.nSocketIdx < 0 || userData.nSocketIdx >= RUNGATE_MAX_SESSION) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    auto& session = m_sessions[userData.nSocketIdx];
    
    if (!session.socket || session.nSckHandle != userData.nSocketHandle) {
        return;
    }
    
    try {
        m_processMsgSize += userData.sMsg.length();
        
        if (session.nPacketErrCount < 10) {
            // 检查缓冲区大小
            if (session.sSocData.length() > RUNGATE_MSG_MAX_LENGTH) {
                session.sSocData.clear();
                session.nPacketErrCount = 99;
                return;
            }
            
            std::string sMsg = session.sSocData + userData.sMsg;
            
            while (true) {
                std::string sData;
                sMsg = ArrestStringEx(sMsg, '#', '!', sData);
                
                if (sData.length() > 2) {
                    int nPacketIdx = Str_ToInt(sData[0], 99);
                    
                    if (session.nPacketIdx == nPacketIdx) {
                        // 重复数据包，增加错误计数
                        session.nPacketErrCount++;
                    } else {
                        session.nPacketIdx = nPacketIdx;
                        sData = sData.substr(1); // 去掉包索引
                        
                        int nDataLen = sData.length();
                        if (nDataLen >= RUNGATE_DEF_BLOCK_SIZE) {
                            if (session.boStartLogon) {
                                // 第一个登录数据包
                                m_humLogonMsgSize += sData.length();
                                session.boStartLogon = false;
                                
                                // 发送数据到游戏服务器
                                std::string sendData = "#" + std::to_string(nPacketIdx) + sData + "!";
                                SendServerMsg(GM_DATA, userData.nSocketIdx, session.nSckHandle, 
                                            session.nUserListIndex, sendData.length(), sendData.c_str());
                            } else {
                                // 普通游戏数据包
                                m_humPlayMsgSize += sData.length();
                                
                                // 解码DefaultMessage并进行速度检查
                                if (sData.length() >= sizeof(Protocol::DefaultMessage)) {
                                    Protocol::DefaultMessage defMsg;
                                    std::string extraData = DecodeMessage(sData, defMsg);
                                    
                                    int nCheckResult = CheckDefMsg(&defMsg, &session);
                                    if (nCheckResult == 0) {
                                        // 通过检查，发送到游戏服务器
                                        std::string sendData = "#" + std::to_string(nPacketIdx) + sData + "!";
                                        SendServerMsg(GM_DATA, userData.nSocketIdx, session.nSckHandle,
                                                    session.nUserListIndex, sendData.length(), sendData.c_str());
                                    } else if (nCheckResult == 1) {
                                        // 发送警告
                                        SendWarnMsg(&session);
                                    } else if (nCheckResult == 2) {
                                        // 断开连接
                                        session.socket->Disconnect();
                                        break;
                                    }
                                }
                            }
                        }
                    }
                } else {
                    break; // 没有更多完整的数据包
                }
            }
            
            session.sSocData = sMsg; // 保存剩余数据
        }
        
    } catch (const std::exception& e) {
        AddMainLogMsg("[Exception] ProcessUserPacket: " + std::string(e.what()), 1);
    }
}

void RunGateServer::ProcessPacket(const RunGateSendUserData& userData) {
    if (userData.nSocketIdx < 0 || userData.nSocketIdx >= RUNGATE_MAX_SESSION) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    auto& session = m_sessions[userData.nSocketIdx];
    
    if (!session.socket || session.nSckHandle != userData.nSocketHandle) {
        return;
    }
    
    try {
        m_deCodeMsgSize += userData.sMsg.length();
        
        // 将数据添加到发送缓冲区
        session.sSendData += userData.sMsg;
        
        // 分块发送数据
        while (!session.sSendData.empty()) {
            std::string sSendBlock;
            
            if (session.sSendData.length() > static_cast<size_t>(m_clientSendBlockSize)) {
                sSendBlock = session.sSendData.substr(0, m_clientSendBlockSize);
                session.sSendData.erase(0, m_clientSendBlockSize);
            } else {
                sSendBlock = session.sSendData;
                session.sSendData.clear();
            }
            
            // 检查发送流量控制
            auto now = std::chrono::steady_clock::now();
            if (!session.boSendAvailable) {
                if (std::chrono::duration_cast<std::chrono::milliseconds>(now - session.dwTimeOutTime).count() > 0) {
                    session.boSendAvailable = true;
                    session.nCheckSendLength = 0;
                }
            }
            
            if (session.boSendAvailable) {
                // 检查发送量
                if (session.nCheckSendLength >= RUNGATE_SEND_CHECK_SIZE) {
                    if (!session.boSendCheck) {
                        session.boSendCheck = true;
                        sSendBlock = "*" + sSendBlock; // 添加流量控制标记
                    }
                    
                    if (session.nCheckSendLength >= RUNGATE_SEND_CHECK_SIZE_MAX) {
                        session.boSendAvailable = false;
                        session.dwTimeOutTime = now + std::chrono::milliseconds(m_clientCheckTimeOut);
                    }
                }
                
                // 发送数据
                if (session.socket && session.socket->IsConnected()) {
                    session.socket->Send(sSendBlock.c_str(), sSendBlock.length());
                    session.nCheckSendLength += sSendBlock.length();
                    m_sendBlockSize += sSendBlock.length();
                }
            } else {
                // 无法发送，重新放回缓冲区
                session.sSendData = sSendBlock + session.sSendData;
                break;
            }
        }
        
    } catch (const std::exception& e) {
        AddMainLogMsg("ProcessPacket error for " + session.sRemoteAddr + ": " + e.what(), 1);
    }
}

void RunGateServer::SendServerMsg(int nIdent, WORD wSocketIndex, int nSocket, 
                                 int nUserListIndex, int nLen, const char* data) {
    if (!m_serverConnection || !m_serverConnection->IsConnected()) {
        return;
    }
    
    try {
        RunGateMessageHeader header;
        header.dwCode = RUNGATE_CODE;
        header.nSocket = nSocket;
        header.wGSocketIdx = wSocketIndex;
        header.wIdent = nIdent;
        header.wUserListIndex = nUserListIndex;
        header.nLength = nLen;
        
        std::vector<BYTE> sendBuffer;
        sendBuffer.resize(sizeof(RunGateMessageHeader) + nLen);
        
        // 复制消息头
        memcpy(sendBuffer.data(), &header, sizeof(RunGateMessageHeader));
        
        // 复制消息数据
        if (data && nLen > 0) {
            memcpy(sendBuffer.data() + sizeof(RunGateMessageHeader), data, nLen);
        }
        
        m_serverConnection->Send(sendBuffer.data(), sendBuffer.size());
        
    } catch (const std::exception& e) {
        AddMainLogMsg("SendServerMsg error: " + std::string(e.what()), 1);
    }
}

void RunGateServer::SendSocket(const char* buffer, int nLen) {
    SendServerMsg(GM_DATA, 0, 0, 0, nLen, buffer);
}

int RunGateServer::CheckDefMsg(const Protocol::DefaultMessage* defMsg, RunGateSessionInfo* sessionInfo) {
    if (!defMsg || !sessionInfo) {
        return 0;
    }
    
    int result = 0;
    bool boSpeedShowMsg = false;
    auto now = std::chrono::steady_clock::now();
    
    switch (defMsg->wIdent) {
        case Protocol::CM_WALK: {
            if (m_speedConfig.boWalk) {
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now - sessionInfo->gameSpeed.dwWalkTimeTick);
                
                if (elapsed.count() > m_speedConfig.nWalkTime) {
                    sessionInfo->gameSpeed.dwWalkTimeTick = now;
                    if (sessionInfo->gameSpeed.nWalkCount > 0) {
                        sessionInfo->gameSpeed.nWalkCount--;
                    }
                } else {
                    sessionInfo->gameSpeed.dwWalkTimeTick = now;
                    sessionInfo->gameSpeed.nWalkCount++;
                    if (sessionInfo->gameSpeed.nWalkCount >= m_speedConfig.nWalkCount) {
                        boSpeedShowMsg = true;
                    }
                    result = m_speedConfig.btSpeedControlMode;
                }
            }
            break;
        }
        
        case Protocol::CM_RUN: {
            if (m_speedConfig.boRun) {
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now - sessionInfo->gameSpeed.dwRunTimeTick);
                
                if (elapsed.count() > m_speedConfig.nRunTime) {
                    sessionInfo->gameSpeed.dwRunTimeTick = now;
                    if (sessionInfo->gameSpeed.nRunCount > 0) {
                        sessionInfo->gameSpeed.nRunCount--;
                    }
                } else {
                    sessionInfo->gameSpeed.dwRunTimeTick = now;
                    sessionInfo->gameSpeed.nRunCount++;
                    if (sessionInfo->gameSpeed.nRunCount >= m_speedConfig.nRunCount) {
                        boSpeedShowMsg = true;
                    }
                    result = m_speedConfig.btSpeedControlMode;
                }
            }
            break;
        }
        
        case Protocol::CM_HIT:
        case Protocol::CM_HEAVYHIT:
        case Protocol::CM_BIGHIT:
        case Protocol::CM_POWERHIT:
        case Protocol::CM_LONGHIT:
        case Protocol::CM_WIDEHIT:
        case Protocol::CM_FIREHIT:
        case Protocol::CM_CRSHIT:
        case Protocol::CM_TWINHIT: {
            if (m_speedConfig.boHit) {
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now - sessionInfo->gameSpeed.dwHitTimeTick);
                
                if (elapsed.count() > m_speedConfig.nHitTime) {
                    sessionInfo->gameSpeed.dwHitTimeTick = now;
                    if (sessionInfo->gameSpeed.nHitCount > 0) {
                        sessionInfo->gameSpeed.nHitCount--;
                    }
                } else {
                    sessionInfo->gameSpeed.dwHitTimeTick = now;
                    sessionInfo->gameSpeed.nHitCount++;
                    if (sessionInfo->gameSpeed.nHitCount >= m_speedConfig.nHitCount) {
                        boSpeedShowMsg = true;
                    }
                    result = m_speedConfig.btSpeedControlMode;
                }
            }
            break;
        }
        
        case Protocol::CM_SPELL: {
            if (m_speedConfig.boSpell) {
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now - sessionInfo->gameSpeed.dwSpellTimeTick);
                
                if (elapsed.count() > m_speedConfig.nSpellTime) {
                    sessionInfo->gameSpeed.dwSpellTimeTick = now;
                    if (sessionInfo->gameSpeed.nSpellCount > 0) {
                        sessionInfo->gameSpeed.nSpellCount--;
                    }
                } else {
                    sessionInfo->gameSpeed.dwSpellTimeTick = now;
                    sessionInfo->gameSpeed.nSpellCount++;
                    if (sessionInfo->gameSpeed.nSpellCount >= m_speedConfig.nSpellCount) {
                        boSpeedShowMsg = true;
                    }
                    result = m_speedConfig.btSpeedControlMode;
                }
            }
            break;
        }
        
        case Protocol::CM_TURN: {
            if (m_speedConfig.boTurn) {
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now - sessionInfo->gameSpeed.dwTurnTimeTick);
                
                if (elapsed.count() > m_speedConfig.nTurnTime) {
                    sessionInfo->gameSpeed.dwTurnTimeTick = now;
                    if (sessionInfo->gameSpeed.nTurnCount > 0) {
                        sessionInfo->gameSpeed.nTurnCount--;
                    }
                } else {
                    sessionInfo->gameSpeed.dwTurnTimeTick = now;
                    sessionInfo->gameSpeed.nTurnCount++;
                    if (sessionInfo->gameSpeed.nTurnCount >= m_speedConfig.nTurnCount) {
                        boSpeedShowMsg = true;
                    }
                    result = m_speedConfig.btSpeedControlMode;
                }
            }
            break;
        }
        
        case Protocol::CM_SAY: {
            // 检查聊天消息频率
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                now - sessionInfo->dwSayMsgTick);
            
            if (elapsed.count() < m_sayMsgTime) {
                result = 1; // 警告
            } else {
                sessionInfo->dwSayMsgTick = now;
            }
            break;
        }
    }
    
    // 如果需要显示速度提示消息
    if (boSpeedShowMsg && m_speedConfig.boSpeedShowMsg) {
        // 在下一次循环中发送警告消息（避免在当前处理中发送）
        if (result == 1) {
            // 发送警告但不断开连接
        }
    }
    
    return result;
}

void RunGateServer::SendWarnMsg(RunGateSessionInfo* sessionInfo) {
    if (!sessionInfo || !sessionInfo->socket || !m_speedConfig.boSpeedShowMsg) {
        return;
    }
    
    try {
        // 创建警告消息
        Protocol::DefaultMessage warnMsg;
        warnMsg.wIdent = Protocol::SM_HEAR; // 系统消息
        warnMsg.nRecog = 0;
        warnMsg.wParam = 0;
        warnMsg.wTag = m_speedConfig.btMsgColor;
        warnMsg.wSeries = 0;
        
        // 发送警告消息给客户端
        std::string msgData = EncodeMessage(warnMsg) + m_speedConfig.sSpeedShowMsg;
        std::string fullMsg = "#0" + msgData + "!";
        
        RunGateSendUserData userData;
        userData.nSocketIdx = -1; // 直接发送，不通过队列
        userData.nSocketHandle = sessionInfo->nSckHandle;
        userData.sMsg = fullMsg;
        
        if (sessionInfo->socket && sessionInfo->socket->IsConnected()) {
            sessionInfo->socket->Send(fullMsg.c_str(), fullMsg.length());
        }
        
    } catch (const std::exception& e) {
        AddMainLogMsg("[Exception] SendWarnMsg: " + std::string(e.what()), 1);
    }
}

void RunGateServer::SendActionFail(std::shared_ptr<ClientConnection> connection) {
    if (!connection) return;
    
    Protocol::DefaultMessage msg;
    msg.nRecog = 0;
    msg.wIdent = 999; // 暂时使用一个数值，因为SM_ACTIONFAIL可能不存在
    msg.wParam = 0;
    msg.wTag = 0;
    msg.wSeries = 0;
    
    // 修复Send方法调用
    std::vector<BYTE> sendData(reinterpret_cast<const BYTE*>(&msg), 
                              reinterpret_cast<const BYTE*>(&msg) + sizeof(Protocol::DefaultMessage));
    connection->Send(sendData.data(), sendData.size());
}

// IP管理功能实现
bool RunGateServer::IsBlockedIP(const std::string& ipAddress) {
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    int ipInt = IPStringToInt(ipAddress);
    
    // 检查永久阻止列表
    for (const auto& addr : m_blockIPList) {
        if (addr.nIPaddr == ipInt) {
            return true;
        }
    }
    
    // 检查临时阻止列表
    auto now = std::chrono::steady_clock::now();
    for (auto it = m_tempBlockIPList.begin(); it != m_tempBlockIPList.end();) {
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - it->dwStartAttackTick).count();
        
        if (elapsed > 300000) { // 5分钟后移除临时阻止
            it = m_tempBlockIPList.erase(it);
        } else {
            if (it->nIPaddr == ipInt) {
                return true;
            }
            ++it;
        }
    }
    
    return false;
}

bool RunGateServer::IsConnectionLimited(const std::string& ipAddress, int socketHandle) {
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    auto& ipList = m_currentIPList[ipAddress];
    
    // 清理过期的连接记录
    auto now = std::chrono::steady_clock::now();
    for (auto it = ipList.begin(); it != ipList.end();) {
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - it->dwStartAttackTick).count();
        
        if (elapsed > 600000) { // 10分钟后清理记录
            it = ipList.erase(it);
        } else {
            ++it;
        }
    }
    
    // 检查连接数限制
    if (static_cast<int>(ipList.size()) >= m_maxConnOfIP) {
        return true;
    }
    
    // 添加新连接记录
    ipList.emplace_back(IPStringToInt(ipAddress), socketHandle);
    
    return false;
}

bool RunGateServer::AddAttackIP(const std::string& ipAddress) {
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    int ipInt = IPStringToInt(ipAddress);
    auto now = std::chrono::steady_clock::now();
    
    // 查找现有攻击记录
    for (auto& addr : m_attackIPList) {
        if (addr.nIPaddr == ipInt) {
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                now - addr.dwStartAttackTick).count();
            
            if (elapsed < m_attackTick) {
                ++addr.nAttackCount;
                if (addr.nAttackCount >= m_attackCount) {
                    // 达到攻击阈值，添加到阻止列表
                    switch (m_blockMethod) {
                        case RUNGATE_DISCONNECT:
                            CloseConnectByIP(ipAddress);
                            break;
                        case RUNGATE_BLOCK:
                            AddTempBlockIP(ipAddress);
                            break;
                        case RUNGATE_BLOCKLIST:
                            AddBlockIP(ipAddress);
                            break;
                    }
                    return true;
                }
            } else {
                // 重置攻击计数
                addr.dwStartAttackTick = now;
                addr.nAttackCount = 1;
            }
            return false;
        }
    }
    
    // 添加新的攻击记录
    m_attackIPList.emplace_back(ipInt, 0);
    m_attackIPList.back().nAttackCount = 1;
    
    return false;
}

int RunGateServer::AddBlockIP(const std::string& ipAddress) {
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    int ipInt = IPStringToInt(ipAddress);
    
    // 检查是否已存在
    for (const auto& addr : m_blockIPList) {
        if (addr.nIPaddr == ipInt) {
            return 0; // 已存在
        }
    }
    
    m_blockIPList.emplace_back(ipInt, 0);
    AddMainLogMsg("Added IP to block list: " + ipAddress, 1);
    
    // 断开该IP的所有连接
    CloseConnectByIP(ipAddress);
    
    return 1;
}

int RunGateServer::AddTempBlockIP(const std::string& ipAddress) {
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    int ipInt = IPStringToInt(ipAddress);
    
    // 检查是否已存在
    for (const auto& addr : m_tempBlockIPList) {
        if (addr.nIPaddr == ipInt) {
            return 0; // 已存在
        }
    }
    
    m_tempBlockIPList.emplace_back(ipInt, 0);
    AddMainLogMsg("Added IP to temp block list: " + ipAddress, 1);
    
    // 断开该IP的所有连接
    CloseConnectByIP(ipAddress);
    
    return 1;
}

int RunGateServer::GetConnectCountOfIP(const std::string& ipAddress) {
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    auto it = m_currentIPList.find(ipAddress);
    return (it != m_currentIPList.end()) ? static_cast<int>(it->second.size()) : 0;
}

int RunGateServer::GetAttackIPCount(const std::string& ipAddress) {
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    int ipInt = IPStringToInt(ipAddress);
    
    for (const auto& addr : m_attackIPList) {
        if (addr.nIPaddr == ipInt) {
            return addr.nAttackCount;
        }
    }
    
    return 0;
}

void RunGateServer::CloseConnectByIP(const std::string& ipAddress) {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    for (auto& session : m_sessions) {
        if (session.socket && session.sRemoteAddr == ipAddress) {
            session.socket->Disconnect();
        }
    }
    
    AddMainLogMsg("Closed all connections for IP: " + ipAddress, 1);
}

void RunGateServer::CloseAllUser() {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    for (auto& session : m_sessions) {
        if (session.socket) {
            session.socket->Disconnect();
        }
    }
    
    AddMainLogMsg("Closed all user connections", 0);
}

int RunGateServer::GetActiveConnections() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_sessionMutex));
    
    int count = 0;
    for (const auto& session : m_sessions) {
        if (session.socket && session.socket->IsConnected()) {
            ++count;
        }
    }
    
    return count;
}

std::string RunGateServer::GetVersionInfo() const {
    return "MirServer RunGateServer v1.0.0 - HAPPYM2.NET Compatible";
}

void RunGateServer::FilterSayMsg(std::string& msg) {
    if (msg == "OoOoOoOoOoQ") {
        // 特殊调试命令，关闭所有连接
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_abuseMutex);
    
    for (const auto& filterWord : m_abuseList) {
        if (filterWord.empty()) continue;
        
        size_t pos = 0;
        while ((pos = msg.find(filterWord, pos)) != std::string::npos) {
            std::string replacement(filterWord.length(), m_replaceWord[0]);
            msg.replace(pos, filterWord.length(), replacement);
            pos += replacement.length();
        }
    }
    
    // 检查消息长度限制
    if (static_cast<int>(msg.length()) > m_sayMsgMaxLen) {
        msg = msg.substr(0, m_sayMsgMaxLen);
    }
}

void RunGateServer::LoadAbuseFile() {
    std::lock_guard<std::mutex> lock(m_abuseMutex);
    
    m_abuseList.clear();
    
    try {
        std::ifstream file("config/abuse.txt");
        if (file.is_open()) {
            std::string line;
            while (std::getline(file, line)) {
                // 去除行末的回车换行符
                line.erase(line.find_last_not_of("\r\n") + 1);
                if (!line.empty()) {
                    m_abuseList.push_back(line);
                }
            }
            file.close();
            
            AddMainLogMsg("Loaded " + std::to_string(m_abuseList.size()) + " abuse words", 0);
        }
    } catch (const std::exception& e) {
        AddMainLogMsg("Failed to load abuse file: " + std::string(e.what()), 1);
    }
}

void RunGateServer::LoadBlockIPFile() {
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    m_blockIPList.clear();
    
    try {
        std::ifstream file("config/blockip.txt");
        if (file.is_open()) {
            std::string line;
            while (std::getline(file, line)) {
                line.erase(line.find_last_not_of("\r\n") + 1);
                if (!line.empty()) {
                    int ipInt = IPStringToInt(line);
                    if (ipInt != 0) {
                        m_blockIPList.emplace_back(ipInt, 0);
                    }
                }
            }
            file.close();
            
            AddMainLogMsg("Loaded " + std::to_string(m_blockIPList.size()) + " blocked IPs", 0);
        }
    } catch (const std::exception& e) {
        AddMainLogMsg("Failed to load block IP file: " + std::string(e.what()), 1);
    }
}

void RunGateServer::SaveBlockIPFile() {
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    try {
        std::ofstream file("config/blockip.txt");
        if (file.is_open()) {
            for (const auto& addr : m_blockIPList) {
                file << IPIntToString(addr.nIPaddr) << std::endl;
            }
            file.close();
            
            AddMainLogMsg("Saved " + std::to_string(m_blockIPList.size()) + " blocked IPs", 0);
        }
    } catch (const std::exception& e) {
        AddMainLogMsg("Failed to save block IP file: " + std::string(e.what()), 1);
    }
}

void RunGateServer::RestSessionArray() {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    for (int i = 0; i < RUNGATE_MAX_SESSION; i++) {
        auto& session = m_sessions[i];
        session.socket = nullptr;
        session.sSocData.clear();
        session.sSendData.clear();
        session.nUserListIndex = 0;
        session.nPacketIdx = -1;
        session.nPacketErrCount = 0;
        session.boStartLogon = true;
        session.boSendLock = false;
        session.boOverNomSize = false;
        session.nOverNomSizeCount = 0;
        session.nCheckSendLength = 0;
        session.boSendAvailable = true;
        session.boSendCheck = false;
        session.nReceiveLength = 0;
        session.nSckHandle = -1;
        session.sRemoteAddr.clear();
        session.gameSpeed.Reset();
        
        auto now = std::chrono::steady_clock::now();
        session.dwSendLatestTime = session.dwTimeOutTime = session.dwReceiveLengthTick = 
        session.dwReceiveTick = session.dwSayMsgTick = now;
    }
    
    m_sessionCount = 0;
}

// 辅助函数实现
int RunGateServer::IPStringToInt(const std::string& ipStr) {
    struct sockaddr_in sa;
    int result = inet_pton(AF_INET, ipStr.c_str(), &(sa.sin_addr));
    return (result == 1) ? sa.sin_addr.s_addr : 0;
}

std::string RunGateServer::IPIntToString(int ipInt) {
    struct in_addr addr;
    addr.s_addr = ipInt;
    char str[INET_ADDRSTRLEN];
    if (inet_ntop(AF_INET, &addr, str, INET_ADDRSTRLEN)) {
        return std::string(str);
    }
    return "0.0.0.0";
}

// 配置文件操作实现（使用INI文件库）
std::string RunGateServer::GetConfigString(const std::string& section, const std::string& key, const std::string& defaultValue) {
    return m_iniFile->GetString(section, key, defaultValue);
}

int RunGateServer::GetConfigInt(const std::string& section, const std::string& key, int defaultValue) {
    return m_iniFile->GetInt(section, key, defaultValue);
}

bool RunGateServer::GetConfigBool(const std::string& section, const std::string& key, bool defaultValue) {
    return m_iniFile->GetBool(section, key, defaultValue);
}

void RunGateServer::SetConfigString(const std::string& section, const std::string& key, const std::string& value) {
    m_iniFile->SetString(section, key, value);
}

void RunGateServer::SetConfigInt(const std::string& section, const std::string& key, int value) {
    m_iniFile->SetInt(section, key, value);
}

void RunGateServer::SetConfigBool(const std::string& section, const std::string& key, bool value) {
    m_iniFile->SetBool(section, key, value);
}

void RunGateServer::AddMainLogMsg(const std::string& msg, int level) {
    // 这里应该使用Common/Logger.h中的日志系统
    std::string levelStr;
    switch (level) {
        case 0: levelStr = "[INFO]"; break;
        case 1: levelStr = "[ERROR]"; break;
        case 2: levelStr = "[WARN]"; break;
        default: levelStr = "[DEBUG]"; break;
    }
    
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);
    
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") << " " << levelStr << " " << msg;
    
    std::cout << oss.str() << std::endl;
}

// 实现工具函数
std::string RunGateServer::EncodeString(const std::string& str) const {
    // 对应原ERunGate的EncodeString函数
    std::string result;
    for (char c : str) {
        // 简单的字符编码，可以根据原版需要调整
        result += static_cast<char>(c ^ 0x5A);
    }
    return result;
}

std::string RunGateServer::DecodeString(const std::string& str) const {
    // 对应原ERunGate的DecodeString函数
    std::string result;
    for (char c : str) {
        // 解码，与编码对应
        result += static_cast<char>(c ^ 0x5A);
    }
    return result;
}

bool RunGateServer::CheckAbuseWord(const std::string& msg) const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(m_abuseMutex));
    
    for (const std::string& abuseWord : m_abuseList) {
        if (!abuseWord.empty() && msg.find(abuseWord) != std::string::npos) {
            return true;
        }
    }
    return false;
}


// 字符串处理方法（对应原项目EDcode.pas和Main.pas）
std::string RunGateServer::ArrestStringEx(const std::string& sSource, char cStart, char cEnd, std::string& sResult) {
    // 从字符串中提取由cStart和cEnd包围的部分
    size_t startPos = sSource.find(cStart);
    if (startPos == std::string::npos) {
        sResult = "";
        return sSource;
    }
    
    size_t endPos = sSource.find(cEnd, startPos + 1);
    if (endPos == std::string::npos) {
        sResult = "";
        return sSource;
    }
    
    sResult = sSource.substr(startPos + 1, endPos - startPos - 1);
    return sSource.substr(endPos + 1);
}

std::string RunGateServer::EncodeMessage(const Protocol::DefaultMessage& defMsg) {
    // 编码DefaultMessage为字符串（对应原项目的EncodeMessage）
    std::string result;
    result.resize(RUNGATE_DEF_BLOCK_SIZE);
    std::memcpy(&result[0], &defMsg, sizeof(Protocol::DefaultMessage));
    return result;
}

std::string RunGateServer::DecodeMessage(const std::string& encodedMsg, Protocol::DefaultMessage& defMsg) {
    // 解码字符串为DefaultMessage（对应原项目的DecodeMessage）
    if (encodedMsg.length() < sizeof(Protocol::DefaultMessage)) {
        std::memset(&defMsg, 0, sizeof(Protocol::DefaultMessage));
        return "";
    }
    
    std::memcpy(&defMsg, encodedMsg.data(), sizeof(Protocol::DefaultMessage));
    return encodedMsg.substr(sizeof(Protocol::DefaultMessage));
}

int RunGateServer::Str_ToInt(char c, int nDefault) {
    // 字符转整数（对应原项目的Str_ToInt）
    if (c >= '0' && c <= '9') {
        return c - '0';
    }
    return nDefault;
}

// 改进的数据包处理方法（完全对应原项目逻辑）
void RunGateServer::ProcReceiveBuffer(const char* tBuffer, int nMsgLen) {
    // 对应原项目Main.pas的ProcReceiveBuffer方法
    try {
        static char* SocketBuffer = nullptr;
        static int nBuffLen = 0;
        
        // 重新分配缓冲区
        SocketBuffer = static_cast<char*>(std::realloc(SocketBuffer, nBuffLen + nMsgLen));
        std::memcpy(SocketBuffer + nBuffLen, tBuffer, nMsgLen);
        
        int nLen = nBuffLen + nMsgLen;
        char* Buff = SocketBuffer;
        
        if (nLen >= sizeof(RunGateMessageHeader)) {
            while (true) {
                RunGateMessageHeader* pMsg = reinterpret_cast<RunGateMessageHeader*>(Buff);
                if (pMsg->dwCode == RUNGATE_CODE) {
                    if ((std::abs(pMsg->nLength) + sizeof(RunGateMessageHeader)) > nLen) {
                        break; // 消息不完整
                    }
                    
                    char* MsgBuff = Buff + sizeof(RunGateMessageHeader);
                    
                    // 处理不同类型的消息
                    switch (pMsg->wIdent) {
                        case GM_CHECKSERVER: {
                            m_checkServerFail = false;
                            m_dwCheckServerTick = std::chrono::steady_clock::now();
                            break;
                        }
                        
                        case GM_SERVERUSERINDEX: {
                            std::lock_guard<std::mutex> lock(m_sessionMutex);
                            if (pMsg->wGSocketIdx < RUNGATE_MAX_SESSION && 
                                pMsg->nSocket == m_sessions[pMsg->wGSocketIdx].nSckHandle) {
                                m_sessions[pMsg->wGSocketIdx].nUserListIndex = pMsg->wUserListIndex;
                            }
                            break;
                        }
                        
                        case GM_RECEIVE_OK: {
                            // 发送确认响应
                            SendServerMsg(GM_RECEIVE_OK, 0, 0, 0, 0, nullptr);
                            break;
                        }
                        
                        case GM_DATA: {
                            ProcessMakeSocketStr(pMsg->nSocket, pMsg->wGSocketIdx, MsgBuff, pMsg->nLength);
                            break;
                        }
                        
                        case GM_TEST: {
                            // 测试消息，暂时不处理
                            break;
                        }
                    }
                    
                    Buff += sizeof(RunGateMessageHeader) + std::abs(pMsg->nLength);
                    nLen -= (std::abs(pMsg->nLength) + sizeof(RunGateMessageHeader));
                } else {
                    ++Buff;
                    --nLen;
                }
                
                if (nLen < sizeof(RunGateMessageHeader)) {
                    break;
                }
            }
        }
        
        // 保存剩余数据
        if (nLen > 0) {
            char* TempBuff = static_cast<char*>(std::malloc(nLen));
            std::memcpy(TempBuff, Buff, nLen);
            std::free(SocketBuffer);
            SocketBuffer = TempBuff;
            nBuffLen = nLen;
        } else {
            std::free(SocketBuffer);
            SocketBuffer = nullptr;
            nBuffLen = 0;
        }
        
    } catch (const std::exception& e) {
        AddMainLogMsg("[Exception] ProcReceiveBuffer: " + std::string(e.what()), 1);
    }
}

void RunGateServer::ProcessMakeSocketStr(int nSocket, int nSocketIndex, const char* Buffer, int nMsgLen) {
    // 对应原项目Main.pas的ProcessMakeSocketStr方法
    try {
        std::string sSendMsg;
        Protocol::DefaultMessage* pDefMsg = nullptr;
        
        if (nMsgLen < 0) {
            // 纯字符串消息
            sSendMsg = "#" + std::string(Buffer) + "!";
        } else {
            // 包含DefaultMessage的消息
            if (nMsgLen >= sizeof(Protocol::DefaultMessage)) {
                pDefMsg = reinterpret_cast<Protocol::DefaultMessage*>(const_cast<char*>(Buffer));
                if (nMsgLen > sizeof(Protocol::DefaultMessage)) {
                    sSendMsg = "#" + EncodeMessage(*pDefMsg) + 
                              std::string(Buffer + sizeof(Protocol::DefaultMessage), 
                                        nMsgLen - sizeof(Protocol::DefaultMessage)) + "!";
                } else {
                    sSendMsg = "#" + EncodeMessage(*pDefMsg) + "!";
                }
            }
        }
        
        if (nSocketIndex >= 0 && nSocketIndex < RUNGATE_MAX_SESSION && !sSendMsg.empty()) {
            RunGateSendUserData userData(nSocketIndex, nSocket, sSendMsg);
            std::lock_guard<std::mutex> lock(m_queueMutex);
            m_sendQueue.push(userData);
        }
        
    } catch (const std::exception& e) {
        AddMainLogMsg("[Exception] ProcessMakeSocketStr: " + std::string(e.what()), 1);
    }
}

} // namespace MirServer 