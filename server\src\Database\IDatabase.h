// IDatabase.h - 数据库抽象接口
#pragma once

#include <string>
#include <vector>
#include <memory>
#include <map>
#include <any>
#include <functional>

namespace MirServer {

// 数据库查询结果行
using ResultRow = std::map<std::string, std::any>;
using ResultSet = std::vector<ResultRow>;

// 数据库类型枚举
enum class DatabaseType {
    SQLite,
    MySQL,
    PostgreSQL,
    MSSQL
};

// 数据库查询回调
using QueryCallback = std::function<void(const ResultSet&)>;

// 数据库事务接口
class ITransaction {
public:
    virtual ~ITransaction() = default;
    
    virtual bool Commit() = 0;
    virtual bool Rollback() = 0;
    virtual bool Execute(const std::string& sql) = 0;
    virtual bool Execute(const std::string& sql, const std::vector<std::any>& params) = 0;
};

// 数据库抽象接口
class IDatabase {
public:
    virtual ~IDatabase() = default;
    
    // 连接管理
    virtual bool Connect(const std::string& connectionString) = 0;
    virtual bool Disconnect() = 0;
    virtual bool IsConnected() const = 0;
    
    // 基本操作
    virtual bool Execute(const std::string& sql) = 0;
    virtual bool Execute(const std::string& sql, const std::vector<std::any>& params) = 0;
    
    // 查询操作
    virtual ResultSet Query(const std::string& sql) = 0;
    virtual ResultSet Query(const std::string& sql, const std::vector<std::any>& params) = 0;
    virtual void QueryAsync(const std::string& sql, QueryCallback callback) = 0;
    
    // 事务管理
    virtual std::unique_ptr<ITransaction> BeginTransaction() = 0;
    
    // 批量操作
    virtual bool ExecuteBatch(const std::vector<std::string>& sqls) = 0;
    
    // 工具方法
    virtual int64_t GetLastInsertId() = 0;
    virtual int GetAffectedRows() = 0;
    virtual std::string GetLastError() const = 0;
    virtual DatabaseType GetType() const = 0;
    
    // 预处理语句
    virtual bool Prepare(const std::string& name, const std::string& sql) = 0;
    virtual bool ExecutePrepared(const std::string& name, const std::vector<std::any>& params) = 0;
    virtual ResultSet QueryPrepared(const std::string& name, const std::vector<std::any>& params) = 0;
};

// 数据库工厂
class DatabaseFactory {
public:
    static std::unique_ptr<IDatabase> Create(DatabaseType type);
    static std::unique_ptr<IDatabase> CreateFromConfig(const std::string& configFile);
};

} // namespace MirServer 