#!/bin/bash

# 编译PlayObject组队和行会功能测试

echo "编译PlayObject组队和行会功能测试..."

# 设置编译参数
CXX=g++
CXXFLAGS="-std=c++17 -Wall -Wextra -g -O0"
INCLUDES="-I../src -I../src/Common -I../src/BaseObject -I../src/GameEngine"
LIBS="-lgtest -lgtest_main -pthread"

# 源文件列表
SOURCES=(
    "test_playobject_group_guild.cpp"
    "../src/BaseObject/PlayObject.cpp"
    "../src/BaseObject/BaseObject.cpp"
    "../src/GameEngine/GroupManager.cpp"
    "../src/GameEngine/GuildManager.cpp"
    "../src/Common/Logger.cpp"
    "../src/Common/Types.cpp"
    "../src/Common/Utils.cpp"
)

# 检查源文件是否存在
for src in "${SOURCES[@]}"; do
    if [ ! -f "$src" ]; then
        echo "警告: 源文件 $src 不存在，跳过..."
    fi
done

# 编译
echo "正在编译..."
$CXX $CXXFLAGS $INCLUDES "${SOURCES[@]}" $LIBS -o test_playobject_group_guild

if [ $? -eq 0 ]; then
    echo "编译成功！"
    echo "运行测试: ./test_playobject_group_guild"
else
    echo "编译失败！"
    exit 1
fi
