# 传奇私服行会系统完整指南

## 概述

本文档描述了从Delphi重构到C++的传奇私服行会系统的完整功能实现。该系统遵循原项目的设计模式，提供了完整的行会管理功能。

## 系统架构

### 核心类

1. **Guild** - 行会类，对应原项目的TGuild
2. **GuildManager** - 行会管理器，对应原项目的TGuildManager
3. **GuildProtocolHandler** - 行会协议处理器

### 数据结构

- `GuildMember` - 行会成员信息
- `GuildWarInfo` - 行会战争信息
- `GuildAllyInfo` - 行会联盟信息
- `GuildDonationRecord` - 捐献记录
- `GuildSkillInfo` - 技能信息
- `GuildTerritoryInfo` - 领地信息

## 功能模块

### 1. 基础行会管理

#### 行会创建
```cpp
auto& guildManager = GuildManager::GetInstance();
bool result = guildManager.CreateGuild("行会名称", chiefPlayer);
```

#### 成员管理
- 添加成员：`guild->AddMember(player, rank)`
- 移除成员：`guild->RemoveMember(playerName)`
- 职位管理：`guild->UpdateMemberRank(playerName, newRank, rankName)`

#### 行会属性
- 建设度 (BuildPoint)
- 灵气值 (Aurae)
- 安定度 (Stability)
- 繁荣度 (Flourishing)
- 行会等级 (GuildLevel)
- 行会经验 (GuildExp)
- 行会金币 (GuildGold)

### 2. 行会捐献系统

#### 金币捐献
```cpp
bool result = guild->DonateGold(player, amount);
```
- 自动扣除玩家金币
- 增加行会金币
- 记录捐献历史
- 获得行会经验

#### 物品捐献
```cpp
bool result = guild->DonateItem(player, itemName, count);
```
- 物品存入行会仓库
- 记录捐献历史
- 获得行会经验

#### 捐献统计
- 查看个人总捐献：`guild->GetTotalDonation(playerName)`
- 捐献历史记录

### 3. 行会升级系统

#### 升级机制
- 经验获取：捐献金币、物品，完成任务
- 自动升级：达到经验要求时自动升级
- 升级奖励：提升各项属性

#### 升级公式
```cpp
int requiredExp = guildLevel * 1000;  // 升级所需经验
```

#### 升级奖励
- 建设度 +100
- 灵气值 +50
- 安定度 +50
- 繁荣度 +50

### 4. 行会技能系统

#### 可学习技能
1. 行会攻击力提升
2. 行会防御力提升
3. 行会经验加成
4. 行会金币加成
5. 行会传送
6. 行会复活
7. 行会治疗
8. 行会护盾

#### 技能管理
```cpp
// 学习技能
guild->LearnGuildSkill("技能名称");

// 升级技能
guild->UpgradeGuildSkill("技能名称");

// 查询技能等级
int level = guild->GetGuildSkillLevel("技能名称");
```

#### 技能要求
- 学习技能：行会等级≥2，消耗10000金币
- 升级技能：消耗 (技能等级+1) × 5000 金币

### 5. 行会仓库系统

#### 权限控制
- 会长、副会长、队长可以访问仓库
- 普通成员无法访问

#### 仓库操作
```cpp
// 存入物品
guild->DepositItem(player, itemName, count);

// 取出物品
guild->WithdrawItem(player, itemName, count);

// 查看仓库物品
auto items = guild->GetWarehouseItems();
```

### 6. 行会排名系统

#### 排名计算公式
```cpp
int score = guildLevel * 100 + memberCount * 10 +
           buildPoint + aurae + stability + flourishing +
           activeWarBonus;
```

#### 排名功能
```cpp
// 更新所有行会排名
guildManager.UpdateAllGuildRankings();

// 获取排行榜
auto rankings = guildManager.GetGuildRankings(10);

// 查询特定行会排名
int rank = guildManager.GetGuildRank("行会名称");
```

### 7. 行会战争系统

#### 宣战
```cpp
bool result = guild->StartWar(targetGuild, duration);
```

#### 停战
```cpp
bool result = guild->EndWar(targetGuild);
```

#### 战争状态检查
```cpp
bool isWar = guild->IsWarWith(targetGuild);
```

### 8. 行会联盟系统

#### 结盟
```cpp
bool result = guild->AddAlly(targetGuild);
```

#### 解除联盟
```cpp
bool result = guild->RemoveAlly(targetGuild);
```

#### 联盟状态检查
```cpp
bool isAlly = guild->IsAlly(targetGuild);
```

### 9. 行会领地系统

#### 占领领地
```cpp
bool result = guild->ClaimTerritory("领地名称");
```

#### 失去领地
```cpp
bool result = guild->LoseTerritory("领地名称");
```

#### 领地收入
```cpp
int income = guild->GetTerritoryIncome();  // 每个领地1000金币/天
```

### 10. 行会配置系统

#### 配置管理
```cpp
// 设置配置
guild->SetConfigBool("Permissions.AllowMemberInvite", true);
guild->SetConfigInt("General.MaxMembers", 300);
guild->SetConfigString("War.DefaultDuration", "7200000");

// 读取配置
bool allowInvite = guild->GetConfigBool("Permissions.AllowMemberInvite");
int maxMembers = guild->GetConfigInt("General.MaxMembers");
std::string warDuration = guild->GetConfigString("War.DefaultDuration");
```

#### 配置文件格式
行会配置保存在 `GuildBase/行会名称.ini` 文件中：

```ini
; Guild Configuration File for 行会名称
; Generated at 时间戳

[General]
AutoSave=1
SaveInterval=300000
MaxNotices=10
MaxMembers=200

[Permissions]
AllowMemberInvite=0
AllowMemberKick=0
AllowMemberNotice=0
AllowMemberWarehouse=0
RequireApproval=1

[War]
DefaultDuration=10800000
AllowAllyHelp=1
AutoAcceptWar=0
WarCooldown=86400000

[Skills]
AutoUpgrade=0
MaxSkillLevel=10
UpgradeCostMultiplier=1.0

[Warehouse]
MaxItems=1000
LogOperations=1
AccessLevel=2

[Donation]
MinGoldAmount=1000
MaxGoldAmount=1000000
ExpRewardRate=0.01
ShowDonationRank=1
```

#### 主要配置项说明

**[General] - 基本设置**
- `AutoSave`: 是否自动保存
- `SaveInterval`: 保存间隔时间(毫秒)
- `MaxNotices`: 最大公告数量
- `MaxMembers`: 最大成员数量

**[Permissions] - 权限设置**
- `AllowMemberInvite`: 是否允许普通成员邀请新成员
- `AllowMemberKick`: 是否允许普通成员踢出其他成员
- `AllowMemberNotice`: 是否允许普通成员发布公告
- `AllowMemberWarehouse`: 是否允许普通成员访问仓库
- `RequireApproval`: 加入行会是否需要审批

**[War] - 战争设置**
- `DefaultDuration`: 默认战争持续时间(毫秒)
- `AllowAllyHelp`: 是否允许联盟行会帮助
- `AutoAcceptWar`: 是否自动接受宣战
- `WarCooldown`: 战争冷却时间(毫秒)

### 11. 文件操作系统

#### 文件格式
行会数据保存在 `GuildBase/行会名称.txt` 文件中：

```
# Guild Data File for 行会名称
# Generated at 时间戳

行会名称
等级,经验,金币,建设度,灵气值,安定度,繁荣度
MEMBER:玩家名,职位,职位名称,加入时间,最后在线时间
NOTICE:公告内容
WAR:行会1,行会2,开始时间,持续时间,是否激活
ALLY:联盟行会,结盟时间,是否激活
DONATION:玩家名,捐献金额
WAREHOUSE:物品名,数量
SKILL:技能名,等级
TERRITORY:领地名称
```

#### 文件操作
```cpp
// 保存行会数据和配置
guild->SaveToFile();

// 加载行会数据和配置
guild->LoadFromFile();

// 备份行会文件
guild->BackupGuildFile();
```

## 配置参数

```cpp
static constexpr int MAX_GUILD_MEMBERS = 200;       // 最大成员数
static constexpr int MAX_NOTICES = 10;              // 最大公告数
static constexpr DWORD SAVE_INTERVAL = 30000;       // 保存间隔(毫秒)
static constexpr DWORD DEFAULT_WAR_DURATION = 10800000; // 默认战争时间(3小时)
```

## 使用示例

参见 `server/examples/GuildSystemExample.cpp` 文件，包含完整的功能演示。

## 测试

运行测试：
```bash
cd server/tests
mkdir build && cd build
cmake ..
make
./GuildSystemTest
```

## 线程安全

所有Guild类的方法都使用互斥锁保护，确保多线程环境下的数据安全。

## 日志记录

系统会记录所有重要操作的日志，便于调试和监控：
- 行会创建/删除
- 成员加入/离开
- 职位变更
- 捐献记录
- 技能学习/升级
- 战争/联盟状态变化

## 兼容性

该实现完全兼容原Delphi项目的数据格式和业务逻辑，可以无缝替换原有系统。
