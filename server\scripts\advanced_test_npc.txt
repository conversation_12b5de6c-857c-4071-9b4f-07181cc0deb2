[@main]
欢迎来到高级功能测试NPC！\\
我可以帮助你测试新增的脚本引擎功能。\\
\\
请选择测试类型：\\
<时间条件测试/@timetest> 测试时间相关条件\\
<装备条件测试/@equiptest> 测试装备相关条件\\
<怪物管理测试/@monstertest> 测试怪物管理功能\\
<变量系统测试/@vartest> 测试变量系统\\
<列表管理测试/@listtest> 测试列表管理\\
<特殊功能测试/@specialtest> 测试特殊功能\\
<退出/@exit> 退出\\

[@timetest]
时间条件测试菜单：\\
<检查当前时间/@checktime> 检查当前时间\\
<检查日期/@checkdate> 检查当前日期\\
<检查小时/@checkhour> 检查当前小时\\
<白天夜晚判断/@daynight> 白天夜晚判断\\
<返回主菜单/@main> 返回主菜单\\

[@checktime]
#IF
CHECKTIME > 1200
CHECKTIME < 1800
#ACT
SENDMSG 5 现在是下午时间（12:00-18:00）！
#ELSEACT
SENDMSG 5 现在不是下午时间！
GOTO timetest

[@checkdate]
#ACT
SENDMSG 5 今天的日期检查完成！
GOTO timetest

[@checkhour]
#IF
CHECKHOUR > 8
CHECKHOUR < 22
#ACT
SENDMSG 5 现在是营业时间（8:00-22:00）！
#ELSEACT
SENDMSG 5 现在是休息时间，请稍后再来！
GOTO timetest

[@daynight]
#IF
CHECKHOUR > 6
CHECKHOUR < 18
#ACT
SENDMSG 5 现在是白天时间！
DAYCHANGECOLOR 255
#ELSEACT
SENDMSG 5 现在是夜晚时间！
NIGHTCHANGECOLOR 128
GOTO timetest

[@equiptest]
装备条件测试菜单：\\
<检查武器/@checkweapon> 检查当前武器\\
<检查盔甲/@checkarmor> 检查当前盔甲\\
<检查戒指/@checkring> 检查戒指\\
<装备管理/@equipmanage> 装备管理\\
<返回主菜单/@main> 返回主菜单\\

[@checkweapon]
#IF
CHECKWEAPON 木剑
#ACT
SENDMSG 5 你装备了木剑！
TAKEW 0
GIVE 铁剑 1
SENDMSG 5 给你升级为铁剑！
#ELSEACT
SENDMSG 5 你没有装备木剑！
GOTO equiptest

[@checkarmor]
#IF
CHECKARMOR 布衣
#ACT
SENDMSG 5 你穿着布衣！
#ELSEACT
SENDMSG 5 你没有穿布衣！
GOTO equiptest

[@checkring]
#IF
CHECKRING_L 铜戒指
#ACT
SENDMSG 5 你的左手戴着铜戒指！
#ELSEACT
SENDMSG 5 你的左手没有戴铜戒指！
GOTO equiptest

[@equipmanage]
#ACT
SENDMSG 5 开始装备管理...
TAKEON 铁剑
SENDMSG 5 自动装备铁剑！
GOTO equiptest

[@monstertest]
怪物管理测试菜单：\\
<生成怪物/@genmonster> 生成怪物\\
<清除怪物/@clearmonster> 清除怪物\\
<怪物统计/@countmonster> 怪物统计\\
<宠物管理/@petmanage> 宠物管理\\
<返回主菜单/@main> 返回主菜单\\

[@genmonster]
#IF
CHECKLEVEL > 10
#ACT
MONGEN 鸡 5 3
SENDMSG 5 在你周围生成了5只鸡！
MONGENEX 鹿 比奇县 330 330 3
SENDMSG 5 在比奇县指定位置生成了3只鹿！
#ELSEACT
SENDMSG 5 等级不够，无法召唤怪物！
GOTO monstertest

[@clearmonster]
#ACT
CLEARMON 比奇县
SENDMSG 5 清除了比奇县的所有怪物！
GOTO monstertest

[@countmonster]
#ACT
MOBCOUNT 比奇县
SENDMSG 5 统计比奇县怪物数量完成！
GOTO monstertest

[@petmanage]
#IF
CHECKSLAVECOUNT > 0
#ACT
RECALLSLAVE
SENDMSG 5 召回了所有宠物！
#ELSEACT
SENDMSG 5 你没有宠物！
GOTO monstertest

[@vartest]
变量系统测试菜单：\\
<设置变量/@setvar> 设置变量\\
<计算变量/@calcvar> 计算变量\\
<检查变量/@checkvar> 检查变量\\
<保存变量/@savevar> 保存变量\\
<返回主菜单/@main> 返回主菜单\\

[@setvar]
#ACT
SETVAR PlayerScore 100
SETVAR PlayerLevel 25
SENDMSG 5 设置变量：PlayerScore=100, PlayerLevel=25
GOTO vartest

[@calcvar]
#ACT
CALCVAR PlayerScore + 50
CALCVAR PlayerLevel * 2
SENDMSG 5 计算变量：PlayerScore+50, PlayerLevel*2
GOTO vartest

[@checkvar]
#IF
CHECKVAR PlayerScore > 100
#ACT
SENDMSG 5 PlayerScore大于100！
#ELSEACT
SENDMSG 5 PlayerScore不大于100！
GOTO vartest

[@savevar]
#ACT
SAVEVAR PlayerScore
SAVEVAR PlayerLevel
SENDMSG 5 保存变量完成！
GOTO vartest

[@listtest]
列表管理测试菜单：\\
<名单管理/@namelist> 名单管理\\
<IP列表管理/@iplist> IP列表管理\\
<账号列表管理/@accountlist> 账号列表管理\\
<返回主菜单/@main> 返回主菜单\\

[@namelist]
#ACT
ADDNAMELIST VIPList
SENDMSG 5 将你添加到VIP名单！
GOTO listtest

[@iplist]
#ACT
ADDIPLIST BanList *************
SENDMSG 5 添加IP到封禁列表！
GOTO listtest

[@accountlist]
#ACT
ADDACCOUNTLIST AdminList TestAccount
SENDMSG 5 添加账号到管理员列表！
GOTO listtest

[@specialtest]
特殊功能测试菜单：\\
<发型修改/@hair> 发型修改\\
<消息发送/@sendmsg> 消息发送\\
<特效测试/@effects> 特效测试\\
<音效测试/@sound> 音效测试\\
<返回主菜单/@main> 返回主菜单\\

[@hair]
#ACT
HAIR 5
SENDMSG 5 修改发型为样式5！
GOTO specialtest

[@sendmsg]
#ACT
SENDMSGUSER TestPlayer 这是一条测试消息！
SENDMSGMAP 比奇县 地图广播消息！
SENDMSGALL 全服公告：测试消息！
GOTO specialtest

[@effects]
#ACT
FIREBURN 330 330 10
LIGHTING 335 335 5
SENDMSG 5 在指定位置创建了火焰和闪电特效！
GOTO specialtest

[@sound]
#ACT
PLAYBGM background.mp3
PLAYWAV effect.wav
SENDMSG 5 播放背景音乐和音效！
GOTO specialtest

[@exit]
感谢使用高级功能测试！\\
再见！\\
#ACT
CLOSE
