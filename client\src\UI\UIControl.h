#pragma once

#include <SDL2/SDL.h>
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include "../Graphics/ResourceManager.h"

// Forward declarations
class WILManager;
class UIManager;

// Special constant to indicate that an image should not be rendered
constexpr int NO_IMAGE = -2;

/**
 * @class UIControl
 * @brief Base class for UI controls
 *
 * This class represents a UI control, which is a visual element that can be
 * rendered on the screen and can respond to user input.
 */
class UIControl {
protected:
    int m_x;                        ///< X position
    int m_y;                        ///< Y position
    int m_width;                    ///< Width
    int m_height;                   ///< Height
    bool m_visible;                 ///< Visibility flag
    bool m_enabled;                 ///< Enabled flag
    std::string m_name;             ///< Control name
    bool m_useRelativePosition;     ///< Whether to use relative positioning to parent

    // Resource indices for WIL files
    std::string m_resourceFile;     ///< Resource file name (WIL file)
    int m_normalImageIndex;         ///< Normal state image index
    int m_hoverImageIndex;          ///< Hover state image index
    int m_pressedImageIndex;        ///< Pressed state image index
    int m_disabledImageIndex;       ///< Disabled state image index

    // WIL manager for loading images
    std::shared_ptr<WILManager> m_wilManager;  ///< WIL manager
    int m_userData;                          ///< User data (for storing arbitrary integer data)

    UIControl* m_parent;            ///< Parent control
    std::vector<std::shared_ptr<UIControl>> m_children;  ///< Child controls

    bool m_mouseOver;               ///< Whether the mouse is over the control
    bool m_mouseDown;               ///< Whether the mouse button is down on the control

    // Event handlers
    std::function<void(UIControl*)> m_onClick;        ///< Click event handler
    std::function<void(UIControl*)> m_onMouseEnter;   ///< Mouse enter event handler
    std::function<void(UIControl*)> m_onMouseLeave;   ///< Mouse leave event handler



public:
    /**
     * @brief Constructor with width and height
     * @param x X position
     * @param y Y position
     * @param width Width (default: 0, will use image width if available)
     * @param height Height (default: 0, will use image height if available)
     * @param name Control name
     * @param useRelativePosition Whether to use relative positioning to parent
     */
    UIControl(int x, int y, int width, int height, const std::string& name = "", bool useRelativePosition = false);

    /**
     * @brief Constructor without width and height (will use image dimensions)
     * @param x X position
     * @param y Y position
     * @param name Control name
     * @param useRelativePosition Whether to use relative positioning to parent
     */
    UIControl(int x, int y, const std::string& name = "", bool useRelativePosition = false);

    /**
     * @brief Virtual destructor
     */
    virtual ~UIControl();
    /**
     * @brief Check if a point is inside the control
     * @param x X coordinate
     * @param y Y coordinate
     * @return true if inside, false otherwise
     */
    bool IsPointInside(int x, int y) const;
    /**
     * @brief Update the control
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    virtual void Update(int deltaTime);

    /**
     * @brief Render the control
     * @param renderer SDL renderer
     */
    virtual void Render(SDL_Renderer* renderer);

    /**
     * @brief Handle mouse motion event
     * @param x Mouse X coordinate
     * @param y Mouse Y coordinate
     * @return true if handled, false otherwise
     */
    virtual bool HandleMouseMotion(int x, int y);

    /**
     * @brief Handle mouse button event
     * @param button Mouse button
     * @param pressed Whether the button was pressed or released
     * @param x Mouse X coordinate
     * @param y Mouse Y coordinate
     * @return true if handled, false otherwise
     */
    virtual bool HandleMouseButton(Uint8 button, bool pressed, int x, int y);

    /**
     * @brief Handle key event
     * @param key Key code
     * @param pressed Whether the key was pressed or released
     * @return true if handled, false otherwise
     */
    virtual bool HandleKey(SDL_Keycode key, bool pressed);

    /**
     * @brief Handle SDL event
     * @param event SDL event
     * @return true if handled, false otherwise
     */
    virtual bool HandleEvent(const SDL_Event& event);

    /**
     * @brief Set the position
     * @param x X position
     * @param y Y position
     */
    virtual void SetPosition(int x, int y);

    /**
     * @brief Set the size
     * @param width Width
     * @param height Height
     */
    virtual void SetSize(int width, int height);

    /**
     * @brief Set visibility
     * @param visible Visibility flag
     */
    virtual void SetVisible(bool visible);

    /**
     * @brief Show the control
     */
    virtual void Show() { SetVisible(true); }

    /**
     * @brief Hide the control
     */
    virtual void Hide() { SetVisible(false); }

    /**
     * @brief Set enabled state
     * @param enabled Enabled flag
     */
    virtual void SetEnabled(bool enabled);

    /**
     * @brief Set the name
     * @param name Control name
     */
    virtual void SetName(const std::string& name);

    /**
     * @brief Set the parent control
     * @param parent Parent control
     */
    virtual void SetParent(UIControl* parent);

    /**
     * @brief Add a child control
     * @param child Child control
     * @param useRelativePosition Whether the child should use relative positioning
     */
    virtual void AddChild(std::shared_ptr<UIControl> child, bool useRelativePosition = false);

    /**
     * @brief Remove a child control
     * @param child Child control
     * @return true if successful, false otherwise
     */
    virtual bool RemoveChild(UIControl* child);

    /**
     * @brief Set whether to use relative positioning to parent
     * @param useRelativePosition Whether to use relative positioning
     */
    virtual void SetUseRelativePosition(bool useRelativePosition);

    /**
     * @brief Get whether the control uses relative positioning
     * @return true if using relative positioning, false otherwise
     */
    bool IsUsingRelativePosition() const { return m_useRelativePosition; }

    /**
     * @brief Get the absolute X position (considering parent position if using relative positioning)
     * @return Absolute X position
     */
    int GetAbsoluteX() const;

    /**
     * @brief Get the absolute Y position (considering parent position if using relative positioning)
     * @return Absolute Y position
     */
    int GetAbsoluteY() const;

    /**
     * @brief Set the click event handler
     * @param handler Click event handler
     */
    virtual void SetOnClick(std::function<void(UIControl*)> handler);

    /**
     * @brief Set the mouse enter event handler
     * @param handler Mouse enter event handler
     */
    virtual void SetOnMouseEnter(std::function<void(UIControl*)> handler);

    /**
     * @brief Set the mouse leave event handler
     * @param handler Mouse leave event handler
     */
    virtual void SetOnMouseLeave(std::function<void(UIControl*)> handler);

    /**
     * @brief Set the resource file
     * @param filename Resource file name (WIL file)
     */
    virtual void SetResourceFile(const std::string& filename);

    /**
     * @brief Set the normal image index
     * @param index Normal state image index
     */
    virtual void SetNormalImageIndex(int index);

    /**
     * @brief Set the hover image index
     * @param index Hover state image index
     */
    virtual void SetHoverImageIndex(int index);

    /**
     * @brief Set the pressed image index
     * @param index Pressed state image index
     */
    virtual void SetPressedImageIndex(int index);

    /**
     * @brief Set the disabled image index
     * @param index Disabled state image index
     */
    virtual void SetDisabledImageIndex(int index);

    /**
     * @brief Set the WIL manager
     * @param wilManager WIL manager
     */
    virtual void SetWILManager(std::shared_ptr<WILManager> wilManager);

    /**
     * @brief Set the resource type using ResourceManager
     * @param resourceType Resource type from ResourceManager
     */
    virtual void SetResourceType(ResourceManager::ResourceType resourceType);

    /**
     * @brief Get the X position
     * @return X position
     */
    int GetX() const { return m_x; }

    /**
     * @brief Get the Y position
     * @return Y position
     */
    int GetY() const { return m_y; }

    /**
     * @brief Get the width
     * @return Width
     */
    int GetWidth() const { return m_width; }

    /**
     * @brief Get the height
     * @return Height
     */
    int GetHeight() const { return m_height; }

    /**
     * @brief Check if the control is visible
     * @return true if visible, false otherwise
     */
    bool IsVisible() const { return m_visible; }

    /**
     * @brief Check if the control is enabled
     * @return true if enabled, false otherwise
     */
    bool IsEnabled() const { return m_enabled; }

    /**
     * @brief Get the name
     * @return Control name
     */
    const std::string& GetName() const { return m_name; }

    /**
     * @brief Get the parent control
     * @return Parent control
     */
    UIControl* GetParent() const { return m_parent; }

    /**
     * @brief Get the child controls
     * @return Vector of child controls
     */
    const std::vector<std::shared_ptr<UIControl>>& GetChildren() const { return m_children; }

    /**
     * @brief Check if the mouse is over the control
     * @return true if mouse over, false otherwise
     */
    bool IsMouseOver() const { return m_mouseOver; }

    /**
     * @brief Check if the mouse button is down on the control
     * @return true if mouse down, false otherwise
     */
    bool IsMouseDown() const { return m_mouseDown; }

    /**
     * @brief Get the resource file
     * @return Resource file name
     */
    const std::string& GetResourceFile() const { return m_resourceFile; }

    /**
     * @brief Get the normal image index
     * @return Normal state image index
     */
    int GetNormalImageIndex() const { return m_normalImageIndex; }

    /**
     * @brief Get the hover image index
     * @return Hover state image index
     */
    int GetHoverImageIndex() const { return m_hoverImageIndex; }

    /**
     * @brief Get the pressed image index
     * @return Pressed state image index
     */
    int GetPressedImageIndex() const { return m_pressedImageIndex; }

    /**
     * @brief Get the disabled image index
     * @return Disabled state image index
     */
    int GetDisabledImageIndex() const { return m_disabledImageIndex; }

    /**
     * @brief Get the WIL manager
     * @return WIL manager
     */
    std::shared_ptr<WILManager> GetWILManager() const { return m_wilManager; }

    /**
     * @brief Set user data
     * @param userData User data
     */
    void SetUserData(int userData) { m_userData = userData; }

    /**
     * @brief Get user data
     * @return User data
     */
    int GetUserData() const { return m_userData; }

    // 添加虚函数Unfocus，便于焦点管理
    virtual void Unfocus() {}

    // 新增：UIManager 相关虚函数
    virtual void SetUIManager(UIManager* manager) {}
    virtual UIManager* GetUIManager() {
        if (m_parent) return m_parent->GetUIManager();
        return nullptr;
    }

    virtual void Focus() {}

protected:
    /**
     * @brief Update control size based on image dimensions if width or height is 0
     * @param imageIndex Image index to get dimensions from
     */
    void UpdateSizeFromImage(int imageIndex);
};

class UIContainer : public UIControl {
protected:
    UIManager* m_uiManager = nullptr;
public:
    UIContainer(int x, int y, int width, int height, const std::string& name = "", bool useRelativePosition = false)
        : UIControl(x, y, width, height, name, useRelativePosition) {}
    UIContainer(int x, int y, const std::string& name = "", bool useRelativePosition = false)
        : UIControl(x, y, name, useRelativePosition) {}

    void SetUIManager(UIManager* manager) override;
    UIManager* GetUIManager() override;
    ~UIContainer() override;
};

