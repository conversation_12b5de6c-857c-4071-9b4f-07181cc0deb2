#include "Dialog.h"
#include "UIConstants.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>

Dialog::Dialog(int x, int y, int width, int height, const std::string& title, const std::string& message, DialogButtons buttons, const std::string& name)
    : UIContainer(x, y, width, height, name)
    , m_title(title)
    , m_message(message)
    , m_buttons(buttons)
    , m_result(DialogResult::NONE)
    , m_dialogSize(DialogSize::NORMAL)
    , m_wilManager(nullptr)
    , m_resourceFile("")
    , m_backgroundImageIndex(UIConstants::DIALOG_BG_INDEX) // Default dialog background index
    , m_messageX(39)  // Default message position from original Delphi project
    , m_messageY(38)
    , m_dragging(false)
    , m_dragOffsetX(0)
    , m_dragOffsetY(0)   
{
    // <PERSON>reate controls
    CreateControls();
}

Dialog::Dialog(int x, int y, const std::string& title, const std::string& message, DialogButtons buttons, const std::string& name)
    : UIContainer(x, y, name)
    , m_title(title)
    , m_message(message)
    , m_buttons(buttons)
    , m_result(DialogResult::NONE)
    , m_dialogSize(DialogSize::NORMAL)
    , m_wilManager(nullptr)
    , m_resourceFile("")
    , m_backgroundImageIndex(UIConstants::DIALOG_BG_INDEX) // Default dialog background index
    , m_messageX(39)  // Default message position from original Delphi project
    , m_messageY(38)
    , m_dragging(false)
    , m_dragOffsetX(0)
    , m_dragOffsetY(0)
{
    // Create controls
    CreateControls();
}

void Dialog::SetWILManager(std::shared_ptr<WILManager> wilManager)
{
    m_wilManager = wilManager;
}

void Dialog::SetResourceFile(const std::string& resourceFile)
{
    m_resourceFile = resourceFile;
}

void Dialog::SetBackgroundImageIndex(int index)
{
    m_backgroundImageIndex = index;

    // Update background texture if WIL manager and resource file are set
    if (m_wilManager && !m_resourceFile.empty()) {
        UpdateSizeFromImage(index);
        // Load background texture from WIL resource
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_backgroundImageIndex);
        if (surface) {
            m_backgroundTexture = std::make_shared<Texture>(nullptr); // We'll set the renderer later
            m_backgroundTexture->LoadFromSurface(surface);
        }
    }
}

void Dialog::SetDialogSize(DialogSize size)
{
    m_dialogSize = size;

    // Update background image index based on dialog size
    switch (size) {
        case DialogSize::SMALL:
            m_backgroundImageIndex = UIConstants::DIALOG_BG_SMALL_INDEX;
            m_messageX = 39;
            m_messageY = 38;
            break;
        case DialogSize::NORMAL:
            m_backgroundImageIndex = UIConstants::DIALOG_BG_NORMAL_INDEX;
            m_messageX = 39;
            m_messageY = 38;
            break;
        case DialogSize::LARGE:
            m_backgroundImageIndex = UIConstants::DIALOG_BG_LARGE_INDEX;
            m_messageX = 23;
            m_messageY = 20;
            break;
    }

    // Update background texture if WIL manager and resource file are set
    if (m_wilManager && !m_resourceFile.empty()) {
        // Load background texture from WIL resource
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_backgroundImageIndex);
        if (surface) {
            // Update dialog size based on background image
            SetSize(surface->w, surface->h);

            // Center dialog on screen
            // TODO: Get actual screen dimensions instead of hardcoded values
            int screenWidth = 800;
            int screenHeight = 600;
            SetPosition((screenWidth - surface->w) / 2, (screenHeight - surface->h) / 2);

            // Update background texture
            m_backgroundTexture = std::make_shared<Texture>(nullptr); // We'll set the renderer later
            m_backgroundTexture->LoadFromSurface(surface);

            // Recreate controls with new positions
            CreateControls();
        }
    }
}

Dialog::~Dialog()
{
}

void Dialog::Initialize(SDL_Renderer* renderer)
{
    // Initialize background texture if WIL manager and resource file are set
    if (m_wilManager && !m_resourceFile.empty()) {
        // Load background texture from WIL resource
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_backgroundImageIndex);
        if (surface) {
            m_backgroundTexture = std::make_shared<Texture>(renderer);
            m_backgroundTexture->LoadFromSurface(surface);
        }
    }
}

void Dialog::Update(int deltaTime)
{
    // Update controls
    UIControl::Update(deltaTime);
}

void Dialog::Render(SDL_Renderer* renderer)
{
    // Try to load background texture if not loaded yet
    if (!m_backgroundTexture && m_wilManager && !m_resourceFile.empty()) {
        // Load background texture from WIL resource
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_backgroundImageIndex);
        if (surface) {
            m_backgroundTexture = std::make_shared<Texture>(renderer);
            m_backgroundTexture->LoadFromSurface(surface);
        }
    }

    // Render background
    if (m_backgroundTexture) {
        // If the background texture is smaller than the dialog, tile it
        int bgWidth = m_backgroundTexture->GetWidth();
        int bgHeight = m_backgroundTexture->GetHeight();

        if (bgWidth >= m_width && bgHeight >= m_height) {
            // Background is large enough, just render it
            m_backgroundTexture->Render(m_x, m_y);
        } else {
            // Tile the background
            for (int y = m_y; y < m_y + m_height; y += bgHeight) {
                for (int x = m_x; x < m_x + m_width; x += bgWidth) {
                    m_backgroundTexture->Render(x, y);
                }
            }
        }

        // Draw a border around the dialog
        SDL_Rect rect = {m_x, m_y, m_width, m_height};
        SDL_SetRenderDrawColor(renderer, 200, 200, 200, 255);  // Light gray
        SDL_RenderDrawRect(renderer, &rect);
    } else if (m_normalImageIndex >= 0 && m_normalImageIndex != NO_IMAGE && m_wilManager && !m_resourceFile.empty()) {
        // Try to use the resource indices from UIControl
        UIControl::SetNormalImageIndex(m_backgroundImageIndex);
        UIControl::SetResourceFile(m_resourceFile);
        UIControl::SetWILManager(m_wilManager);
        UIControl::Render(renderer);
    } else {
        // Render a default background
        SDL_Rect rect = {m_x, m_y, m_width, m_height};
        SDL_SetRenderDrawColor(renderer, 50, 50, 50, 200);  // Dark gray with alpha
        SDL_RenderFillRect(renderer, &rect);

        SDL_SetRenderDrawColor(renderer, 200, 200, 200, 255);  // Light gray
        SDL_RenderDrawRect(renderer, &rect);

        // Render title bar
        SDL_Rect titleRect = {m_x, m_y, m_width, 30};
        SDL_SetRenderDrawColor(renderer, 100, 100, 100, 255);  // Gray
        SDL_RenderFillRect(renderer, &titleRect);
    }

    // Render controls
    UIControl::Render(renderer);
}

bool Dialog::HandleMouseMotion(int x, int y)
{
    // Handle dragging
    if (m_dragging) {
        SetPosition(x - m_dragOffsetX, y - m_dragOffsetY);
        return true;
    }

    // Handle controls
    return UIControl::HandleMouseMotion(x, y);
}

bool Dialog::HandleMouseButton(Uint8 button, bool pressed, int x, int y)
{
    // Handle dragging
    if (button == SDL_BUTTON_LEFT) {
        if (pressed) {
            // Check if clicked on title bar
            if (x >= m_x && x < m_x + m_width && y >= m_y && y < m_y + 30) {
                m_dragging = true;
                m_dragOffsetX = x - m_x;
                m_dragOffsetY = y - m_y;
                return true;
            }
        } else {
            m_dragging = false;
        }
    }

    // Handle controls
    return UIControl::HandleMouseButton(button, pressed, x, y);
}

bool Dialog::HandleKey(SDL_Keycode key, bool pressed)
{
    // First let the base class handle the key
    if (UIControl::HandleKey(key, pressed)) {
        return true;
    }

    // Only handle key press events
    if (!pressed) {
        return false;
    }

    // Handle keyboard input based on the original Delphi implementation
    // In the original, Enter key triggers OK/Yes button, Esc key triggers Cancel button

    // Handle Enter key
    if (key == SDLK_RETURN || key == SDLK_KP_ENTER) {
        // Check if OK button is visible and is the only button
        bool hasOkButton = false;
        bool hasOtherButtons = false;

        for (const auto& button : m_buttonControls) {
            if (button->GetText() == "OK") {
                hasOkButton = true;
            } else {
                hasOtherButtons = true;
            }
        }

        // If OK is the only button, close with OK result
        if (hasOkButton && !hasOtherButtons) {
            Close(DialogResult::OK);
            return true;
        }

        // Check if Yes button is visible and is the only button
        bool hasYesButton = false;
        hasOtherButtons = false;

        for (const auto& button : m_buttonControls) {
            if (button->GetText() == "Yes") {
                hasYesButton = true;
            } else {
                hasOtherButtons = true;
            }
        }

        // If Yes is the only button, close with Yes result
        if (hasYesButton && !hasOtherButtons) {
            Close(DialogResult::YES);
            return true;
        }
    }

    // Handle Escape key
    if (key == SDLK_ESCAPE) {
        // Check if Cancel button is visible
        bool hasCancelButton = false;

        for (const auto& button : m_buttonControls) {
            if (button->GetText() == "Cancel") {
                hasCancelButton = true;
                break;
            }
        }

        // If Cancel button is visible, close with Cancel result
        if (hasCancelButton) {
            Close(DialogResult::CANCEL);
            return true;
        }
    }

    return false;
}

void Dialog::SetBackgroundTexture(std::shared_ptr<Texture> texture)
{
    m_backgroundTexture = texture;
}

void Dialog::SetTitle(const std::string& title)
{
    m_title = title;

    // Update title label
    if (m_titleLabel) {
        m_titleLabel->SetText(title);
    }
}

void Dialog::SetMessage(const std::string& message)
{
    m_message = message;

    // Update message label
    if (m_messageLabel) {
        m_messageLabel->SetText(message);
    }
}

void Dialog::SetButtons(DialogButtons buttons)
{
    m_buttons = buttons;

    // Recreate controls
    CreateControls();
}

void Dialog::SetOnResult(std::function<void(DialogResult)> callback)
{
    m_onResult = callback;
}

void Dialog::Show()
{
    SetVisible(true);
}

void Dialog::Hide()
{
    SetVisible(false);
}

void Dialog::Close(DialogResult result)
{
    m_result = result;
    Hide();

    // Call result callback
    if (m_onResult) {
        m_onResult(result);
    }
}

void Dialog::CreateControls()
{
    // Clear existing controls
    m_children.clear();
    m_titleLabel.reset();
    m_messageLabel.reset();
    m_buttonControls.clear();
    SetNormalImageIndex(NO_IMAGE);
    SetHoverImageIndex(NO_IMAGE);
    SetPressedImageIndex(NO_IMAGE);
    SetDisabledImageIndex(NO_IMAGE);
    // Create title label
    m_titleLabel = std::make_shared<Label>(m_x + 10, m_y + 5, 0, 0, m_title);
    AddChild(m_titleLabel);

    // Create message label - use the message position from the original Delphi project
    m_messageLabel = std::make_shared<Label>(m_x + m_messageX, m_y + m_messageY, 0, 0, m_message);
    AddChild(m_messageLabel);

    // Create buttons - use the button positions from the original Delphi project
    // In the original, buttons are positioned from right to left with 110px spacing
    // Base X position is 324 (XBase) for normal size dialog
    int buttonY = m_y + 126; // Default Y position for buttons in normal size dialog

    // Adjust button position based on dialog size
    switch (m_dialogSize) {
        case DialogSize::SMALL:
            buttonY = m_y + 36;
            break;
        case DialogSize::NORMAL:
            buttonY = m_y + 126;
            break;
        case DialogSize::LARGE:
            buttonY = m_y + 305;
            break;
    }

    // Base X position for buttons (rightmost button)
    int baseX = m_x + 324; // XBase from original Delphi project
    int buttonSpacing = 110; // Spacing between buttons in original

    // In the original Delphi project, buttons are positioned from right to left
    // and only the necessary buttons are shown based on DlgButtons parameter
    int buttonX = baseX;

    // Create buttons based on dialog buttons type
    // Following the original Delphi implementation in DMessageDlg function

    // First check if Cancel button should be shown
    if (m_buttons == DialogButtons::OK_CANCEL ||
        m_buttons == DialogButtons::YES_NO_CANCEL ||
        m_buttons == DialogButtons::RETRY_CANCEL) {

        // Create Cancel button
        auto cancelButton = std::make_shared<Button>(buttonX, buttonY, 0, 0, "Cancel");
        cancelButton->SetWILManager(m_wilManager);
        cancelButton->SetResourceFile(m_resourceFile);
        cancelButton->SetNormalImageIndex(UIConstants::DIALOG_CANCEL_BTN_NORMAL);
        cancelButton->SetOnClick(CreateButtonClickHandler(cancelButton));
        AddChild(cancelButton);
        m_buttonControls.push_back(cancelButton);

        // Move to next button position (left)
        buttonX -= buttonSpacing;
    }

    // Check if No button should be shown
    if (m_buttons == DialogButtons::YES_NO ||
        m_buttons == DialogButtons::YES_NO_CANCEL) {

        // Create No button
        auto noButton = std::make_shared<Button>(buttonX, buttonY, 0, 0, "No");
        noButton->SetWILManager(m_wilManager);
        noButton->SetResourceFile(m_resourceFile);
        noButton->SetNormalImageIndex(UIConstants::DIALOG_NO_BTN_NORMAL);
        noButton->SetOnClick(CreateButtonClickHandler(noButton));
        AddChild(noButton);
        m_buttonControls.push_back(noButton);

        // Move to next button position (left)
        buttonX -= buttonSpacing;
    }

    // Check if Yes button should be shown
    if (m_buttons == DialogButtons::YES_NO ||
        m_buttons == DialogButtons::YES_NO_CANCEL) {

        // Create Yes button
        auto yesButton = std::make_shared<Button>(buttonX, buttonY, 0, 0, "Yes");
        yesButton->SetWILManager(m_wilManager);
        yesButton->SetResourceFile(m_resourceFile);
        yesButton->SetNormalImageIndex(UIConstants::DIALOG_YES_BTN_NORMAL);
        yesButton->SetOnClick(CreateButtonClickHandler(yesButton));
        AddChild(yesButton);
        m_buttonControls.push_back(yesButton);

        // Move to next button position (left)
        buttonX -= buttonSpacing;
    }

    // Check if OK button should be shown (either explicitly or as default)
    if (m_buttons == DialogButtons::OK ||
        m_buttons == DialogButtons::OK_CANCEL ||
        buttonX == baseX) { // No buttons added yet, show OK as default

        // Create OK button
        auto okButton = std::make_shared<Button>(buttonX, buttonY, 0, 0, "OK");
        okButton->SetWILManager(m_wilManager);
        okButton->SetResourceFile(m_resourceFile);
        okButton->SetNormalImageIndex(UIConstants::DIALOG_OK_BTN_NORMAL);
        okButton->SetOnClick(CreateButtonClickHandler(okButton));
        AddChild(okButton);
        m_buttonControls.push_back(okButton);
    }

    // Note: The original implementation also handles RETRY_CANCEL and ABORT_RETRY_IGNORE
    // but these are not shown in the code snippets provided. We can add them if needed.
}

std::function<void()> Dialog::CreateButtonClickHandler(std::shared_ptr<Button> button)
{
    return [this, button]() {
        HandleButtonClick(button.get());
    };
}

void Dialog::HandleButtonClick(UIControl* button)
{
    // Find which button was clicked
    for (size_t i = 0; i < m_buttonControls.size(); i++) {
        if (m_buttonControls[i].get() == button) {
            // Determine result based on button index and type
            DialogResult result = DialogResult::NONE;

            switch (m_buttons) {
                case DialogButtons::OK:
                    result = DialogResult::OK;
                    break;
                case DialogButtons::OK_CANCEL:
                    result = (i == 0) ? DialogResult::OK : DialogResult::CANCEL;
                    break;
                case DialogButtons::YES_NO:
                    result = (i == 0) ? DialogResult::YES : DialogResult::NO;
                    break;
                case DialogButtons::YES_NO_CANCEL:
                    if (i == 0) result = DialogResult::YES;
                    else if (i == 1) result = DialogResult::NO;
                    else result = DialogResult::CANCEL;
                    break;
                case DialogButtons::RETRY_CANCEL:
                    result = (i == 0) ? DialogResult::RETRY : DialogResult::CANCEL;
                    break;
                case DialogButtons::ABORT_RETRY_IGNORE:
                    if (i == 0) result = DialogResult::ABORT;
                    else if (i == 1) result = DialogResult::RETRY;
                    else result = DialogResult::NONE;  // Ignore
                    break;
            }

            // Close dialog with result
            Close(result);
            break;
        }
    }
}

