#include "LoginState.h"
#include "ServerSelectionState.h"
#include "../Application.h"
#include "../UI/DialogManager.h"
#include "../UI/ResourcePaths.h"
#include "../UI/UIConstants.h"
#include "../UI/UIControl.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>

LoginState::LoginState(Application* app)
    : GameState(app)
    , m_loggingIn(false)
    , m_serverAddress("127.0.0.1")  // Default server address
    , m_serverPort(7000)            // Default server port
    , m_backgroundIndex(83)         // Login background image index from original Delphi code (g_WMainImages.Images[83])
    , m_logoIndex(103)              // Logo image index from original Delphi code (g_WMainImages.Images[103])
    , m_font(nullptr)               // Initialize font to nullptr
{
    // Create WIL manager
    m_wilManager = std::make_shared<WILManager>();

    // Initialize resource manager
    m_resourceManager = ResourceManager::GetInstance();
    if (!m_resourceManager->Initialize(m_wilManager)) {
        std::cerr << "Failed to initialize resource manager" << std::endl;
    }

    // Create UI manager
    m_uiManager = std::make_shared<UIManager>(m_app->GetRenderer());
    m_uiManager->SetWILManager(m_wilManager);

    // Create dialog manager
    m_dialogManager = std::make_shared<DialogManager>(m_app->GetRenderer());
    m_dialogManager->SetWILManager(m_wilManager);
    m_dialogManager->SetResourceFile("data/dialog.wil");

    // Create network manager
    m_networkManager = std::make_shared<NetworkManager>();
    if (!m_networkManager->Initialize()) {
        std::cerr << "Failed to initialize network manager" << std::endl;
    }

    // Register packet handlers
    m_networkManager->RegisterPacketHandler(
        PacketType::LOGIN_RESPONSE,
        [this](const Packet& packet) { OnLoginResponse(packet); }
    );

    m_networkManager->RegisterPacketHandler(
        PacketType::CHARACTER_CREATE_RESPONSE,
        [this](const Packet& packet) { OnNewAccountResponse(packet); }
    );

    m_networkManager->RegisterPacketHandler(
        PacketType::PASSWORD_CHANGED,
        [this](const Packet& packet) { OnChangePasswordResponse(packet); }
    );
}

LoginState::~LoginState()
{
    // Unregister packet handlers
    if (m_networkManager) {
        m_networkManager->UnregisterPacketHandler(PacketType::LOGIN_RESPONSE);
        m_networkManager->UnregisterPacketHandler(PacketType::CHARACTER_CREATE_RESPONSE);
        m_networkManager->UnregisterPacketHandler(PacketType::PASSWORD_CHANGED);
    }

    // Clean up font
    if (m_font) {
        TTF_CloseFont(m_font);
        m_font = nullptr;
    }
}

void LoginState::Enter()
{
    // Load required resources
    if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::MAIN)) {
        std::cerr << "Failed to load main resources" << std::endl;
    }

    if (!m_resourceManager->LoadResource(ResourceManager::ResourceType::MAIN2)) {
        std::cerr << "Failed to load main2 resources" << std::endl;
    }

    // Create background texture from WIL resource
    SDL_Surface* backgroundSurface = m_resourceManager->GetSurface(ResourceManager::ResourceType::MAIN, m_backgroundIndex);
    if (backgroundSurface) {
        m_backgroundTexture = std::make_shared<Texture>(m_app->GetRenderer());
        m_backgroundTexture->LoadFromSurface(backgroundSurface);
    } else {
        std::cerr << "Failed to load background surface from WIL" << std::endl;

        // Fallback to a solid color background
        m_backgroundTexture = std::make_shared<Texture>(m_app->GetRenderer());
        m_backgroundTexture->CreateBlank(m_app->GetScreenWidth(), m_app->GetScreenHeight(), SDL_TEXTUREACCESS_TARGET);

        // Set the renderer target to the texture
        SDL_SetRenderTarget(m_app->GetRenderer(), m_backgroundTexture->GetSDLTexture());

        // Fill with a dark blue color
        SDL_SetRenderDrawColor(m_app->GetRenderer(), 0, 0, 64, 255);
        SDL_RenderClear(m_app->GetRenderer());

        // Reset the renderer target
        SDL_SetRenderTarget(m_app->GetRenderer(), nullptr);
    }

    // Create logo texture from WIL resource
    SDL_Surface* logoSurface = m_resourceManager->GetSurface(ResourceManager::ResourceType::MAIN, m_logoIndex);
    if (logoSurface) {
        m_logoTexture = std::make_shared<Texture>(m_app->GetRenderer());
        m_logoTexture->LoadFromSurface(logoSurface);

        // Set color key for transparency (black)
        m_logoTexture->SetColorKey(0, 0, 0);
    } else {
        std::cerr << "Failed to load logo surface from WIL" << std::endl;

        // Try to load from file as fallback
        m_logoTexture = std::make_shared<Texture>(m_app->GetRenderer());
        if (!m_logoTexture->LoadFromFile("assets/data/logo.png")) {
            std::cerr << "Failed to load logo texture from file" << std::endl;
        }
    }

    // Create UI controls
    CreateControls();

    // Set the login window position and appearance
    // In the original Delphi code, the login window (DLogin) is positioned at the center of the screen

    // Play login music
    // TODO: Play login music (bmg_intro in original Delphi code)
}

void LoginState::Exit()
{
    // Clear textures
    m_backgroundTexture.reset();
    m_logoTexture.reset();

    // Clear UI controls through the UI manager
    m_uiManager->ClearControls();

    // Reset control pointers
    // m_titleLabel.reset();
    m_usernameLabel.reset();
    m_passwordLabel.reset();
    m_usernameInput.reset();
    m_passwordInput.reset();
    m_loginButton.reset();
    m_exitButton.reset();
    m_statusLabel.reset();

    // Clean up font
    if (m_font) {
        TTF_CloseFont(m_font);
        m_font = nullptr;
    }

    // Stop login music
    // TODO: Stop login music
}

void LoginState::Update(float deltaTime)
{
    // Update network manager
    if (m_networkManager) {
        m_networkManager->Update();
    }

    // Update all UI controls using the UI manager
    m_uiManager->Update(static_cast<int>(deltaTime * 1000));

    // Update dialog manager
    m_dialogManager->Update(static_cast<int>(deltaTime * 1000));

    // Update dialogs
    if (m_newAccountDialog && m_newAccountDialog->IsVisible()) {
        m_newAccountDialog->Update(static_cast<int>(deltaTime * 1000));
    }

    if (m_changePasswordDialog && m_changePasswordDialog->IsVisible()) {
        m_changePasswordDialog->Update(static_cast<int>(deltaTime * 1000));
    }
}

void LoginState::Render()
{
    // Render background
    if (m_backgroundTexture) {
        m_backgroundTexture->Render(0, 0);
    }

    // Render logo
    if (m_logoTexture) {
        int logoX = (m_app->GetScreenWidth() - m_logoTexture->GetWidth()) / 2;
        int logoY = 50;
        m_logoTexture->Render(logoX, logoY);
    }

    // Render all UI controls using the UI manager
    m_uiManager->Render();

    // Render dialog manager
    m_dialogManager->Render();

    // Render dialogs
    if (m_newAccountDialog && m_newAccountDialog->IsVisible()) {
        m_newAccountDialog->Render(m_app->GetRenderer());
    }

    if (m_changePasswordDialog && m_changePasswordDialog->IsVisible()) {
        m_changePasswordDialog->Render(m_app->GetRenderer());
    }
}

void LoginState::HandleEvents(SDL_Event& event)
{
    // Check if dialog manager has any active dialogs and let it handle events first
    if (m_dialogManager->HasActiveDialogs()) {
        if (m_dialogManager->HandleEvent(event)) {
            return;
        }
    }

    // Check if dialogs are visible and let them handle events first
    bool dialogHandledEvent = false;

    if (m_newAccountDialog && m_newAccountDialog->IsVisible()) {
        dialogHandledEvent = m_newAccountDialog->HandleEvent(event);
    }

    if (!dialogHandledEvent && m_changePasswordDialog && m_changePasswordDialog->IsVisible()) {
        dialogHandledEvent = m_changePasswordDialog->HandleEvent(event);
    }

    // If a dialog handled the event, don't process it further
    if (dialogHandledEvent) {
        return;
    }

    // Debug output for text input events
    if (event.type == SDL_TEXTINPUT) {
        std::cout << "LoginState received text input: '" << event.text.text << "'" << std::endl;
    }

    // Let the UI manager handle events
    bool handled = m_uiManager->HandleEvent(event);

    // Debug output for event handling
    if (event.type == SDL_TEXTINPUT) {
        std::cout << "UI manager handled text input: " << (handled ? "true" : "false") << std::endl;
    }

    // Handle keyboard events
    if (event.type == SDL_KEYDOWN) {
        if (event.key.keysym.sym == SDLK_RETURN) {
            // Enter key pressed, attempt login
            OnLoginButtonClick();
        } else if (event.key.keysym.sym == SDLK_ESCAPE) {
            // Escape key pressed, exit
            OnExitButtonClick();
        } else if (event.key.keysym.sym == SDLK_TAB) {
            // Tab key pressed, switch focus between username and password
            if (m_usernameInput && m_passwordInput) {
                if (m_usernameInput->IsFocused()) {
                    m_usernameInput->Unfocus();
                    m_passwordInput->Focus();
                } else {
                    m_usernameInput->Focus();
                    m_passwordInput->Unfocus();
                }
            }
        }
    }
}

void LoginState::CreateControls()
{

    // Clear the UI manager
    m_uiManager->ClearControls();

    // Calculate screen center (like in original Delphi code)
    int screenWidth = m_app->GetScreenWidth();
    int screenHeight = m_app->GetScreenHeight();
    int centerX = screenWidth / 2;
    int centerY = screenHeight / 2;

    // Create a login panel as a parent container - size will be determined by the background image
    // First get the background image to determine its size
    SDL_Surface* loginBgSurface = m_resourceManager->GetSurface(ResourceManager::ResourceType::MAIN, 60);
    int loginBgWidth = 400;  // Default width if image not found
    int loginBgHeight = 300; // Default height if image not found

    if (loginBgSurface) {
        loginBgWidth = loginBgSurface->w;
        loginBgHeight = loginBgSurface->h;
    }

    // Create the login panel with the size of the background image and centered on screen
    auto loginContainer = std::make_shared<UIContainer>(
        centerX - loginBgWidth / 2,
        centerY - loginBgHeight / 2,
        loginBgWidth,
        loginBgHeight,
        "LoginPanel"
    );
    loginContainer->SetWILManager(m_wilManager);
    loginContainer->SetUIManager(m_uiManager.get());
    loginContainer->SetResourceFile(ResourcePaths::MAIN);
    loginContainer->SetNormalImageIndex(60); // Login background image index from original Delphi code
    m_uiManager->AddContainer(loginContainer);

    // Create username input - positioned according to original Delphi code (m_EdId)
    // In IntroScn.pas, m_EdId coordinates are set in OpenScene method:
    // Left := SCREENWIDTH div 2 - 68 + 18 (line 498)
    // Top := SCREENHEIGHT div 2 - 8 - 34 (line 499)
    // Width := 137 (line 501)
    // Height := 16 (line 500)
    int usernameX = centerX - 68 + 18; // Absolute coordinates - centered
    int usernameY = centerY - 8 - 34; // Absolute coordinates - above center
    m_usernameInput = std::make_shared<TextInput>(usernameX, usernameY, 137, 16, "", "UsernameInput"); // Larger size for better visibility
    // m_usernameInput->SetFont(font);
    m_usernameInput->SetTextColor({255, 255, 0, 255}); // Yellow text for better visibility
    m_usernameInput->SetBackgroundColor({0, 0, 128, 255}); // Dark blue background
    m_usernameInput->SetBorderColor({255, 255, 255, 255}); // White border for better visibility
    m_usernameInput->SetMaxLength(10); // MaxLength from original Delphi code
    loginContainer->AddChild(m_usernameInput);

    // Create password input - positioned according to original Delphi code (m_EdPasswd)
    // In IntroScn.pas, m_EdPasswd coordinates are set in OpenScene method:
    // Left := SCREENWIDTH div 2 - 68 + 18 (line 505)
    // Top := SCREENHEIGHT div 2 - 8 - 2 (line 506)
    // Width := 137 (line 508)
    // Height := 16 (line 507)
    int passwordX = centerX - 68 + 18; // Absolute coordinates - centered
    int passwordY = centerY - 8 - 2; // Absolute coordinates - below center
    m_passwordInput = std::make_shared<TextInput>(passwordX, passwordY, 137, 16, "", "PasswordInput"); // Larger size for better visibility
    // m_passwordInput->SetFont(font);
    m_passwordInput->SetTextColor({255, 255, 0, 255}); // Yellow text for better visibility
    m_passwordInput->SetBackgroundColor({0, 0, 128, 255}); // Dark blue background
    m_passwordInput->SetBorderColor({255, 255, 255, 255}); // White border for better visibility
    m_passwordInput->SetMaxLength(10); // MaxLength from original Delphi code
    m_passwordInput->SetPasswordMode(true);
    loginContainer->AddChild(m_passwordInput);

    // Create login button - positioned relative to the login panel using original Delphi coordinates
    // No need to specify width and height, it will use the image dimensions automatically
    m_loginButton = std::make_shared<Button>(171, 165, "Login");
    // m_loginButton->SetFont(font);
    m_loginButton->SetTextColor({255, 255, 255, 255});
    m_loginButton->SetOnClick([this]() { OnLoginButtonClick(); });
    m_loginButton->SetWILManager(m_wilManager);
    m_loginButton->SetResourceFile(ResourcePaths::MAIN);
    // Set button image indices based on original Delphi code
    m_loginButton->SetNormalImageIndex(NO_IMAGE);  // DLoginOk image index from original Delphi code
    m_loginButton->SetHoverImageIndex(NO_IMAGE);   // Original doesn't use hover state
    m_loginButton->SetPressedImageIndex(62); // Same as normal in original
    // Add as child with relative positioning
    loginContainer->AddChild(m_loginButton, true);

    // Create exit button - positioned relative to the login panel using original Delphi coordinates
    // No need to specify width and height, it will use the image dimensions automatically
    m_exitButton = std::make_shared<Button>(252, 28, "X");
    // m_exitButton->SetFont(font);
    m_exitButton->SetTextColor({255, 255, 255, 255});
    m_exitButton->SetOnClick([this]() { OnExitButtonClick(); });
    m_exitButton->SetWILManager(m_wilManager);
    m_exitButton->SetResourceFile(ResourcePaths::MAIN);
    // Set button image indices based on original Delphi code
    m_exitButton->SetNormalImageIndex(NO_IMAGE);  // DLoginClose image index from original Delphi code
    m_exitButton->SetHoverImageIndex(NO_IMAGE);   // Original doesn't use hover state
    m_exitButton->SetPressedImageIndex(64);       // Same as normal in original
    // Add as child with relative positioning
    loginContainer->AddChild(m_exitButton, true);

    // Create new account button - positioned relative to the login panel using original Delphi coordinates
    // No need to specify width and height, it will use the image dimensions automatically
    m_newAccountButton = std::make_shared<Button>(24, 207, "New Account");
    // m_newAccountButton->SetFont(font);
    m_newAccountButton->SetTextColor({255, 255, 255, 255});
    m_newAccountButton->SetOnClick([this]() { OnNewAccountButtonClick(); });
    m_newAccountButton->SetWILManager(m_wilManager);
    m_newAccountButton->SetResourceFile(ResourcePaths::MAIN);
    // Set button image indices based on original Delphi code
    m_newAccountButton->SetNormalImageIndex(NO_IMAGE);  // DLoginNew image index from original Delphi code
    m_newAccountButton->SetHoverImageIndex(NO_IMAGE);   // Original doesn't use hover state
    m_newAccountButton->SetPressedImageIndex(61); // Same as normal in original
    // Add as child with relative positioning
    loginContainer->AddChild(m_newAccountButton, true);

    // Create change password button - positioned relative to the login panel using original Delphi coordinates
    // No need to specify width and height, it will use the image dimensions automatically
    m_changePasswordButton = std::make_shared<Button>(130, 207, "Change Password");
    // m_changePasswordButton->SetFont(font);
    m_changePasswordButton->SetTextColor({255, 255, 255, 255});
    m_changePasswordButton->SetOnClick([this]() { OnChangePasswordButtonClick(); });
    m_changePasswordButton->SetWILManager(m_wilManager);
    m_changePasswordButton->SetResourceFile(ResourcePaths::MAIN);
    // Set button image indices based on original Delphi code
    m_changePasswordButton->SetNormalImageIndex(NO_IMAGE);  // DLoginChgPw image index from original Delphi code
    m_changePasswordButton->SetHoverImageIndex(NO_IMAGE);   // Original doesn't use hover state
    m_changePasswordButton->SetPressedImageIndex(53); // Same as normal in original
    // Add as child with relative positioning
    loginContainer->AddChild(m_changePasswordButton, true);

    // Create status label - this will remain a direct child of the UI manager
    m_statusLabel = std::make_shared<Label>(0, centerY, 800, 30, "");
    // m_statusLabel->SetFont(font);
    m_statusLabel->SetTextColor({255, 0, 0, 255});
    m_statusLabel->SetAlignment(TextAlignment::CENTER);
    m_statusLabel->SetWILManager(m_wilManager);
    m_uiManager->AddControl(m_statusLabel);

    // // Add input controls to UI manager last so they're rendered on top
    // m_uiManager->AddControl(m_usernameInput);
    // m_uiManager->AddControl(m_passwordInput);

    // Store the font for later use instead of closing it
    // m_font = font;

    // Explicitly start text input mode
    SDL_StartTextInput();

    // Set initial focus to username input and make sure text input is started
    m_usernameInput->Focus();
}

void LoginState::OnLoginButtonClick()
{
    // Check if we're already logging in
    if (m_loggingIn) {
        return;
    }

    // Get username and password
    std::string username = m_usernameInput->GetText();
    std::string password = m_passwordInput->GetText();

    // Validate username and password
    if (username.empty()) {
        m_statusLabel->SetText("Please enter a username");
        m_dialogManager->ShowMessageDialog("Login Error", "Please enter a username", MessageType::ERROR);
        return;
    }

    if (password.empty()) {
        m_statusLabel->SetText("Please enter a password");
        m_dialogManager->ShowMessageDialog("Login Error", "Please enter a password", MessageType::ERROR);
        return;
    }

    // Set logging in state
    m_loggingIn = true;
    m_statusLabel->SetText("Connecting to server...");

    // Connect to server
    if (!m_networkManager->Connect(m_serverAddress, m_serverPort)) {
        m_statusLabel->SetText("Failed to connect to server");
        m_dialogManager->ShowMessageDialog("Connection Error", "Failed to connect to server. Please check your network connection and try again.", MessageType::ERROR);
        m_loggingIn = false;
        return;
    }

    // Send login request
    m_statusLabel->SetText("Logging in...");

    Packet packet(PacketType::LOGIN_REQUEST);
    packet << username << password;
    m_networkManager->QueuePacket(packet);
}

void LoginState::OnExitButtonClick()
{
    // Exit the application
    m_app->Quit();
}

void LoginState::OnNewAccountButtonClick()
{
    // Show the new account dialog
    if (m_newAccountDialog) {
        m_newAccountDialog->SetVisible(true);
    } else {
        // Create the dialog if it doesn't exist
        int screenWidth = m_app->GetScreenWidth();
        int screenHeight = m_app->GetScreenHeight();
        SDL_Surface* newAccountBgSurface = m_resourceManager->GetSurface(ResourceManager::ResourceType::MAIN, UIConstants::LOGIN_NEWACCOUNT_BG_INDEX);
        int dialogX = (screenWidth - newAccountBgSurface->w) / 2;
        int dialogY = (screenHeight - newAccountBgSurface->h) / 2;

        m_newAccountDialog = std::make_shared<NewAccountDialog>(dialogX, dialogY, newAccountBgSurface->w, newAccountBgSurface->h ,"Create New Account", m_wilManager);
        // Set dialog appearance
        m_newAccountDialog->SetBackgroundImageIndex(UIConstants::LOGIN_NEWACCOUNT_BG_INDEX);
        m_newAccountDialog->Initialize(m_app->GetRenderer());
        m_uiManager->AddControl(m_newAccountDialog);



        // Set callbacks
        m_newAccountDialog->SetOnOkCallback([this](const std::string& accountName, const std::string& password,
                                                  const std::string& userName, const std::string& question1,
                                                  const std::string& answer1, const std::string& question2,
                                                  const std::string& answer2, const std::string& email) {
            // Send new account request to server
            m_statusLabel->SetText("Creating new account...");

            // Connect to server if not already connected
            if (!m_networkManager->IsConnected()) {
                if (!m_networkManager->Connect(m_serverAddress, m_serverPort)) {
                    m_statusLabel->SetText("Failed to connect to server");
                    m_dialogManager->ShowMessageDialog("Connection Error", "Failed to connect to server. Please check your network connection and try again.", MessageType::ERROR);
                    return;
                }
            }

            // Create and send new account request packet
            Packet packet(PacketType::CHARACTER_CREATE_REQUEST);
            packet << accountName << password << userName << question1 << answer1 << question2 << answer2 << email;
            m_networkManager->QueuePacket(packet);
        });

        m_newAccountDialog->SetOnCancelCallback([this]() {
            // Hide the dialog
            m_newAccountDialog->SetVisible(false);
        });

        // Show the dialog
        m_newAccountDialog->SetVisible(true);
    }
}

void LoginState::OnChangePasswordButtonClick()
{
    // Show the change password dialog
    if (m_changePasswordDialog) {
        m_changePasswordDialog->SetVisible(true);
    } else {
        // Create the dialog if it doesn't exist
        int screenWidth = m_app->GetScreenWidth();
        int screenHeight = m_app->GetScreenHeight();
        int dialogWidth = 400;
        int dialogHeight = 250;
        int dialogX = (screenWidth - dialogWidth) / 2;
        int dialogY = (screenHeight - dialogHeight) / 2;

        m_changePasswordDialog = std::make_shared<ChangePasswordDialog>(dialogX, dialogY, dialogWidth, dialogHeight, "Change Password", m_wilManager);
        m_changePasswordDialog->Initialize(m_app->GetRenderer());

        // Set dialog appearance
        m_changePasswordDialog->SetBackgroundImageIndex(UIConstants::DIALOG_BG_INDEX);

        // Set callbacks
        m_changePasswordDialog->SetOnOkCallback([this](const std::string& accountName, const std::string& currentPassword, const std::string& newPassword) {
            // Send change password request to server
            m_statusLabel->SetText("Changing password...");

            // Connect to server if not already connected
            if (!m_networkManager->IsConnected()) {
                if (!m_networkManager->Connect(m_serverAddress, m_serverPort)) {
                    m_statusLabel->SetText("Failed to connect to server");
                    m_dialogManager->ShowMessageDialog("Connection Error", "Failed to connect to server. Please check your network connection and try again.", MessageType::ERROR);
                    return;
                }
            }

            // Create and send change password request packet
            Packet packet(PacketType::CHANGE_PASSWORD);
            packet << accountName << currentPassword << newPassword;
            m_networkManager->QueuePacket(packet);
        });

        m_changePasswordDialog->SetOnCancelCallback([this]() {
            // Hide the dialog
            m_changePasswordDialog->SetVisible(false);
        });

        // Show the dialog
        m_changePasswordDialog->SetVisible(true);
    }
}

void LoginState::OnLoginResponse(const Packet& packet)
{
    // Reset logging in state
    m_loggingIn = false;

    // Read response
    uint8_t success;
    std::string message;
    packet >> success >> message;

    if (success) {
        // Login successful
        m_statusLabel->SetText("Login successful");

        // Show success message
        m_dialogManager->ShowMessageDialog("Login Successful", "You have successfully logged in.", MessageType::INFORMATION, DialogButtons::OK, [this](DialogResult result) {
            // Transition to server selection state
            m_app->ChangeState(std::make_unique<ServerSelectionState>(m_app, m_networkManager));
        });
    } else {
        // Login failed
        m_statusLabel->SetText(message);

        // Show error message
        m_dialogManager->ShowMessageDialog("Login Failed", message, MessageType::ERROR);

        // Disconnect from server
        m_networkManager->Disconnect();
    }
}

void LoginState::OnNewAccountResponse(const Packet& packet)
{
    // Read response
    uint8_t success;
    std::string message;
    packet >> success >> message;

    if (success) {
        // Account creation successful
        m_statusLabel->SetText("Account created successfully");

        // Show success message
        m_dialogManager->ShowMessageDialog("Account Created", "Your account has been created successfully. You can now log in with your new account.", MessageType::INFORMATION);

        // Hide the new account dialog
        if (m_newAccountDialog) {
            m_newAccountDialog->SetVisible(false);
        }
    } else {
        // Account creation failed
        m_statusLabel->SetText(message);

        // Show error message
        m_dialogManager->ShowMessageDialog("Account Creation Failed", message, MessageType::ERROR);
    }
}

void LoginState::OnChangePasswordResponse(const Packet& packet)
{
    // Read response
    uint8_t success;
    std::string message;
    packet >> success >> message;

    if (success) {
        // Password change successful
        m_statusLabel->SetText("Password changed successfully");

        // Show success message
        m_dialogManager->ShowMessageDialog("Password Changed", "Your password has been changed successfully. You can now log in with your new password.", MessageType::INFORMATION);

        // Hide the change password dialog
        if (m_changePasswordDialog) {
            m_changePasswordDialog->SetVisible(false);
        }
    } else {
        // Password change failed
        m_statusLabel->SetText(message);

        // Show error message
        m_dialogManager->ShowMessageDialog("Password Change Failed", message, MessageType::ERROR);
    }
}

