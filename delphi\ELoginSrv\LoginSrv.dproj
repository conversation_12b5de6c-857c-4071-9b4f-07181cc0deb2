﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectGuid>{1e680208-bfcc-4bfb-a024-c6480a42dde9}</ProjectGuid>
    <MainSource>LoginSrv.dpr</MainSource>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
    <DCC_DependencyCheckOutputName>..\MirServer\LoginSrv\LoginSrv.exe</DCC_DependencyCheckOutputName>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_DebugInformation>False</DCC_DebugInformation>
    <DCC_LocalDebugSymbols>False</DCC_LocalDebugSymbols>
    <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
    <DCC_ExeOutput>..\MirServer</DCC_ExeOutput>
    <DCC_DcuOutput>..\Build\LoginSrv</DCC_DcuOutput>
    <DCC_ObjOutput>..\Build\LoginSrv</DCC_ObjOutput>
    <DCC_HppOutput>..\Build\LoginSrv</DCC_HppOutput>
    <DCC_UnitSearchPath>D:\EGameOfMir\Component\JSocket</DCC_UnitSearchPath>
    <DCC_ResourcePath>D:\EGameOfMir\Component\JSocket</DCC_ResourcePath>
    <DCC_ObjPath>D:\EGameOfMir\Component\JSocket</DCC_ObjPath>
    <DCC_IncludePath>D:\EGameOfMir\Component\JSocket</DCC_IncludePath>
    <DCC_Define>RELEASE</DCC_Define>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_ExeOutput>..\MirServer\LoginSrv</DCC_ExeOutput>
    <DCC_DcuOutput>..\Build\LoginSrv</DCC_DcuOutput>
    <DCC_ObjOutput>..\Build\LoginSrv</DCC_ObjOutput>
    <DCC_HppOutput>..\Build\LoginSrv</DCC_HppOutput>
    <DCC_UnitSearchPath>..\Component\JSocket</DCC_UnitSearchPath>
    <DCC_ResourcePath>..\Component\JSocket</DCC_ResourcePath>
    <DCC_ObjPath>..\Component\JSocket</DCC_ObjPath>
    <DCC_IncludePath>..\Component\JSocket</DCC_IncludePath>
    <DCC_Define>DEBUG</DCC_Define>
  </PropertyGroup>
  <ProjectExtensions>
    <Borland.Personality>Delphi.Personality</Borland.Personality>
    <Borland.ProjectType>VCLApplication</Borland.ProjectType>
    <BorlandProject>
<BorlandProject xmlns=""> <Delphi.Personality>   <Parameters>
      <Parameters Name="UseLauncher">False</Parameters>
      <Parameters Name="LoadAllSymbols">True</Parameters>
      <Parameters Name="LoadUnspecifiedSymbols">False</Parameters>
    </Parameters>
    <VersionInfo>
      <VersionInfo Name="IncludeVerInfo">False</VersionInfo>
      <VersionInfo Name="AutoIncBuild">False</VersionInfo>
      <VersionInfo Name="MajorVer">2</VersionInfo>
      <VersionInfo Name="MinorVer">0</VersionInfo>
      <VersionInfo Name="Release">0</VersionInfo>
      <VersionInfo Name="Build">0</VersionInfo>
      <VersionInfo Name="Debug">False</VersionInfo>
      <VersionInfo Name="PreRelease">False</VersionInfo>
      <VersionInfo Name="Special">False</VersionInfo>
      <VersionInfo Name="Private">False</VersionInfo>
      <VersionInfo Name="DLL">False</VersionInfo>
      <VersionInfo Name="Locale">2052</VersionInfo>
      <VersionInfo Name="CodePage">936</VersionInfo>
    </VersionInfo>
    <VersionInfoKeys>
      <VersionInfoKeys Name="CompanyName"></VersionInfoKeys>
      <VersionInfoKeys Name="FileDescription"></VersionInfoKeys>
      <VersionInfoKeys Name="FileVersion"></VersionInfoKeys>
      <VersionInfoKeys Name="InternalName"></VersionInfoKeys>
      <VersionInfoKeys Name="LegalCopyright"></VersionInfoKeys>
      <VersionInfoKeys Name="LegalTrademarks"></VersionInfoKeys>
      <VersionInfoKeys Name="OriginalFilename"></VersionInfoKeys>
      <VersionInfoKeys Name="ProductName"></VersionInfoKeys>
      <VersionInfoKeys Name="ProductVersion"></VersionInfoKeys>
      <VersionInfoKeys Name="Comments"></VersionInfoKeys>
    </VersionInfoKeys>
    <Source>
      <Source Name="MainSource">LoginSrv.dpr</Source>
    </Source>
  </Delphi.Personality> </BorlandProject></BorlandProject>
  </ProjectExtensions>
  <ItemGroup />
  <ItemGroup>
    <DelphiCompile Include="LoginSrv.dpr">
      <MainSource>MainSource</MainSource>
    </DelphiCompile>
    <DCCReference Include="..\Common\Common.pas" />
    <DCCReference Include="..\Common\Grobal2.pas" />
    <DCCReference Include="..\Common\HUtil32.pas" />
    <DCCReference Include="..\Common\MudUtil.pas" />
    <DCCReference Include="BasicSet.pas">
      <Form>FrmBasicSet</Form>
    </DCCReference>
    <DCCReference Include="EDCode.pas" />
    <DCCReference Include="EditUserInfo.pas">
      <Form>FrmUserInfoEdit</Form>
    </DCCReference>
    <DCCReference Include="FAccountView.pas">
      <Form>FrmAccountView</Form>
    </DCCReference>
    <DCCReference Include="FrmFindId.pas">
      <Form>FrmFindUserId</Form>
    </DCCReference>
    <DCCReference Include="GateSet.pas">
      <Form>FrmGateSetting</Form>
    </DCCReference>
    <DCCReference Include="GrobalSession.pas">
      <Form>frmGrobalSession</Form>
    </DCCReference>
    <DCCReference Include="IDDB.pas" />
    <DCCReference Include="LMain.pas">
      <Form>FrmMain</Form>
    </DCCReference>
    <DCCReference Include="LSShare.pas" />
    <DCCReference Include="MasSock.pas">
      <Form>FrmMasSoc</Form>
    </DCCReference>
    <DCCReference Include="MonSoc.pas">
      <Form>FrmMonSoc</Form>
    </DCCReference>
    <DCCReference Include="Parse.pas" />
    <DCCReference Include="SDK.pas" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Borland.Delphi.Targets" />
</Project>