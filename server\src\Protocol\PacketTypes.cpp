// PacketTypes.cpp - 协议类型实现
#include "PacketTypes.h"
#include <unordered_map>
#include <string>

namespace MirServer {
namespace Protocol {

// 协议名称映射表（用于调试和日志）
static const std::unordered_map<PacketType, std::string> PacketTypeNames = {
    // 客户端到服务器
    {CM_PROTOCOL, "CM_PROTOCOL"},
    {CM_IDPASSWORD, "CM_IDPASSWORD"},
    {CM_ADDNEWUSER, "CM_ADDNEWUSER"},
    {CM_CHANGEPASSWORD, "CM_CHANGEPASSWORD"},
    {CM_UPDATEUSER, "CM_UPDATEUSER"},
    {CM_GETBACKPASSWORD, "CM_GETBACKPASSWORD"},
    {CM_QUERYCHR, "CM_QUERYCHR"},
    {CM_NEWCHR, "CM_NEWCHR"},
    {CM_DELCHR, "CM_DELCHR"},
    {CM_SELCHR, "CM_SELCHR"},
    {CM_SELECTSERVER, "CM_SELECTSERVER"},
    {CM_QUERYUSERNAME, "CM_QUERYUSERNAME"},
    {CM_QUERYBAGITEMS, "CM_QUERYBAGITEMS"},
    {CM_DROPITEM, "CM_DROPITEM"},
    {CM_PICKUP, "CM_PICKUP"},
    {CM_USEBAGITEM, "CM_USEBAGITEM"},
    {CM_TAKEONITEM, "CM_TAKEONITEM"},
    {CM_TAKEOFFITEM, "CM_TAKEOFFITEM"},
    {CM_EAT, "CM_EAT"},
    {CM_BUTCH, "CM_BUTCH"},
    {CM_MAGICKEYCHANGE, "CM_MAGICKEYCHANGE"},
    {CM_CLICKNPC, "CM_CLICKNPC"},
    {CM_MERCHANTDLGSELECT, "CM_MERCHANTDLGSELECT"},
    {CM_MERCHANTQUERYSELLPRICE, "CM_MERCHANTQUERYSELLPRICE"},
    {CM_USERSELLITEM, "CM_USERSELLITEM"},
    {CM_USERBUYITEM, "CM_USERBUYITEM"},
    {CM_USERGETDETAILITEM, "CM_USERGETDETAILITEM"},
    {CM_DROPGOLD, "CM_DROPGOLD"},
    {CM_LOGINNOTICEOK, "CM_LOGINNOTICEOK"},
    {CM_GROUPMODE, "CM_GROUPMODE"},
    {CM_CREATEGROUP, "CM_CREATEGROUP"},
    {CM_ADDGROUPMEMBER, "CM_ADDGROUPMEMBER"},
    {CM_DELGROUPMEMBER, "CM_DELGROUPMEMBER"},
    {CM_USERREPAIRITEM, "CM_USERREPAIRITEM"},
    {CM_MERCHANTQUERYREPAIRCOST, "CM_MERCHANTQUERYREPAIRCOST"},
    {CM_DEALTRY, "CM_DEALTRY"},
    {CM_DEALADDITEM, "CM_DEALADDITEM"},
    {CM_DEALDELITEM, "CM_DEALDELITEM"},
    {CM_DEALCANCEL, "CM_DEALCANCEL"},
    {CM_DEALCHGGOLD, "CM_DEALCHGGOLD"},
    {CM_DEALEND, "CM_DEALEND"},
    {CM_USERSTORAGEITEM, "CM_USERSTORAGEITEM"},
    {CM_USERTAKEBACKSTORAGEITEM, "CM_USERTAKEBACKSTORAGEITEM"},
    {CM_WANTMINIMAP, "CM_WANTMINIMAP"},
    {CM_USERMAKEDRUGITEM, "CM_USERMAKEDRUGITEM"},
    {CM_WALK, "CM_WALK"},
    {CM_RUN, "CM_RUN"},
    {CM_HIT, "CM_HIT"},
    {CM_HEAVYHIT, "CM_HEAVYHIT"},
    {CM_BIGHIT, "CM_BIGHIT"},
    {CM_SPELL, "CM_SPELL"},
    {CM_POWERHIT, "CM_POWERHIT"},
    {CM_LONGHIT, "CM_LONGHIT"},
    {CM_WIDEHIT, "CM_WIDEHIT"},
    {CM_FIREHIT, "CM_FIREHIT"},
    {CM_SAY, "CM_SAY"},
    {CM_WHISPER, "CM_WHISPER"},
    {CM_GM, "CM_GM"},
    {CM_TURN, "CM_TURN"},
    {CM_LOGINOUT, "CM_LOGINOUT"},
    {CM_LOGINGAME, "CM_LOGINGAME"},
    {CM_ALIVE, "CM_ALIVE"},
    {CM_RUSH, "CM_RUSH"},
    {CM_RUSHKUNG, "CM_RUSHKUNG"},
    {CM_BACKSTEP, "CM_BACKSTEP"},
    {CM_DIGUP, "CM_DIGUP"},
    {CM_DIGDOWN, "CM_DIGDOWN"},
    {CM_FLYAXE, "CM_FLYAXE"},
    {CM_LIGHTING, "CM_LIGHTING"},
    {CM_SUBABILITY, "CM_SUBABILITY"},
    {CM_HORSERUN, "CM_HORSERUN"},
    {CM_CRSHIT, "CM_CRSHIT"},
    {CM_TWINHIT, "CM_TWINHIT"},
    {CM_RIDE, "CM_RIDE"},
    
    // 服务器到客户端
    {SM_OUTOFCONNECTION, "SM_OUTOFCONNECTION"},
    {SM_PASSOK_SELECTSERVER, "SM_PASSOK_SELECTSERVER"},
    {SM_SELECTSERVER_OK, "SM_SELECTSERVER_OK"},
    {SM_CERTIFICATION_SUCCESS, "SM_CERTIFICATION_SUCCESS"},
    {SM_CERTIFICATION_FAIL, "SM_CERTIFICATION_FAIL"},
    {SM_ID_NOTFOUND, "SM_ID_NOTFOUND"},
    {SM_PASSWD_FAIL, "SM_PASSWD_FAIL"},
    {SM_NEWID_SUCCESS, "SM_NEWID_SUCCESS"},
    {SM_NEWID_FAIL, "SM_NEWID_FAIL"},
    {SM_CHGPASSWD_SUCCESS, "SM_CHGPASSWD_SUCCESS"},
    {SM_CHGPASSWD_FAIL, "SM_CHGPASSWD_FAIL"},
    {SM_GETBACKPASSWD_SUCCESS, "SM_GETBACKPASSWD_SUCCESS"},
    {SM_GETBACKPASSWD_FAIL, "SM_GETBACKPASSWD_FAIL"},
    {SM_QUERYCHR, "SM_QUERYCHR"},
    {SM_NEWCHR_SUCCESS, "SM_NEWCHR_SUCCESS"},
    {SM_NEWCHR_FAIL, "SM_NEWCHR_FAIL"},
    {SM_DELCHR_SUCCESS, "SM_DELCHR_SUCCESS"},
    {SM_DELCHR_FAIL, "SM_DELCHR_FAIL"},
    {SM_STARTPLAY, "SM_STARTPLAY"},
    {SM_STARTFAIL, "SM_STARTFAIL"},
    {SM_QUERYCHR_FAIL, "SM_QUERYCHR_FAIL"},
    {SM_OUTOFCONNECTION2, "SM_OUTOFCONNECTION2"},
    {SM_MAPCHANGED, "SM_MAPCHANGED"},
    {SM_LOGON, "SM_LOGON"},
    {SM_MAPDESCRIPTION, "SM_MAPDESCRIPTION"},
    {SM_ABILITY, "SM_ABILITY"},
    {SM_HEALTHSPELLCHANGED, "SM_HEALTHSPELLCHANGED"},
    {SM_DAYCHANGING, "SM_DAYCHANGING"},
    {SM_LOGINSTATUS, "SM_LOGINSTATUS"},
    {SM_NEWMAP, "SM_NEWMAP"},
    {SM_RECONNECT, "SM_RECONNECT"},
    {SM_GHOST, "SM_GHOST"},
    {SM_SHOWEVENT, "SM_SHOWEVENT"},
    {SM_HIDEEVENT, "SM_HIDEEVENT"},
    {SM_SPACEMOVE_HIDE, "SM_SPACEMOVE_HIDE"},
    {SM_SPACEMOVE_SHOW, "SM_SPACEMOVE_SHOW"},
    {SM_RECONNECT2, "SM_RECONNECT2"},
    {SM_MOVEMODE_CHANGED, "SM_MOVEMODE_CHANGED"},
    {SM_SPACEMOVE_HIDE2, "SM_SPACEMOVE_HIDE2"},
    {SM_SPACEMOVE_SHOW2, "SM_SPACEMOVE_SHOW2"},
    {SM_MOVEFAIL, "SM_MOVEFAIL"},
    {SM_WALK, "SM_WALK"},
    {SM_RUN, "SM_RUN"},
    {SM_HIT, "SM_HIT"},
    {SM_HEAVYHIT, "SM_HEAVYHIT"},
    {SM_BIGHIT, "SM_BIGHIT"},
    {SM_SPELL, "SM_SPELL"},
    {SM_POWERHIT, "SM_POWERHIT"},
    {SM_LONGHIT, "SM_LONGHIT"},
    {SM_WIDEHIT, "SM_WIDEHIT"},
    {SM_FIREHIT, "SM_FIREHIT"},
    {SM_SAY, "SM_SAY"},
    {SM_TURN, "SM_TURN"},
    {SM_ADDITEM, "SM_ADDITEM"},
    {SM_BAGITEMS, "SM_BAGITEMS"},
    {SM_DELITEM, "SM_DELITEM"},
    {SM_UPDATEITEM, "SM_UPDATEITEM"},
    {SM_ADDMAGIC, "SM_ADDMAGIC"},
    {SM_SENDMYMAGIC, "SM_SENDMYMAGIC"},
    {SM_DELMAGIC, "SM_DELMAGIC"},
    {SM_MAGIC_LVEXP, "SM_MAGIC_LVEXP"},
    {SM_DURACHANGE, "SM_DURACHANGE"},
    {SM_MERCHANTSAY, "SM_MERCHANTSAY"},
    {SM_MERCHANTDLG, "SM_MERCHANTDLG"},
    {SM_SENDGOODSLIST, "SM_SENDGOODSLIST"},
    {SM_SENDUSERSELL, "SM_SENDUSERSELL"},
    {SM_SENDBUYPRICE, "SM_SENDBUYPRICE"},
    {SM_USERSELLITEM_OK, "SM_USERSELLITEM_OK"},
    {SM_USERSELLITEM_FAIL, "SM_USERSELLITEM_FAIL"},
    {SM_BUYITEM_SUCCESS, "SM_BUYITEM_SUCCESS"},
    {SM_BUYITEM_FAIL, "SM_BUYITEM_FAIL"},
    {SM_SENDDETAILGOODSLIST, "SM_SENDDETAILGOODSLIST"},
    {SM_GOLDCHANGED, "SM_GOLDCHANGED"},
    {SM_CHANGELIGHT, "SM_CHANGELIGHT"},
    {SM_LAMPCHANGEDURA, "SM_LAMPCHANGEDURA"},
    {SM_CHANGENAMECOLOR, "SM_CHANGENAMECOLOR"},
    {SM_TAKEONITEM, "SM_TAKEONITEM"},
    {SM_TAKEOFFITEM, "SM_TAKEOFFITEM"},
    {SM_SENDREPAIRCOST, "SM_SENDREPAIRCOST"},
    {SM_USERREPAIRITEM_OK, "SM_USERREPAIRITEM_OK"},
    {SM_USERREPAIRITEM_FAIL, "SM_USERREPAIRITEM_FAIL"},
    {SM_STORAGE_OK, "SM_STORAGE_OK"},
    {SM_STORAGE_FULL, "SM_STORAGE_FULL"},
    {SM_STORAGE_FAIL, "SM_STORAGE_FAIL"},
    {SM_SAVEITEMLIST, "SM_SAVEITEMLIST"},
    {SM_TAKEBACKSTORAGEITEM_OK, "SM_TAKEBACKSTORAGEITEM_OK"},
    {SM_TAKEBACKSTORAGEITEM_FAIL, "SM_TAKEBACKSTORAGEITEM_FAIL"},
    {SM_TAKEBACKSTORAGEITEM_FULLBAG, "SM_TAKEBACKSTORAGEITEM_FULLBAG"},
    {SM_STORAGEPASSWORD_OK, "SM_STORAGEPASSWORD_OK"},
    {SM_STORAGEPASSWORD_FAIL, "SM_STORAGEPASSWORD_FAIL"},
    {SM_MAKEDRUG_SUCCESS, "SM_MAKEDRUG_SUCCESS"},
    {SM_MAKEDRUG_FAIL, "SM_MAKEDRUG_FAIL"},
    {SM_DEALTRY_FAIL, "SM_DEALTRY_FAIL"},
    {SM_DEALMENU, "SM_DEALMENU"},
    {SM_DEALCANCEL, "SM_DEALCANCEL"},
    {SM_DEALADDITEM_OK, "SM_DEALADDITEM_OK"},
    {SM_DEALADDITEM_FAIL, "SM_DEALADDITEM_FAIL"},
    {SM_DEALDELITEM_OK, "SM_DEALDELITEM_OK"},
    {SM_DEALDELITEM_FAIL, "SM_DEALDELITEM_FAIL"},
    {SM_DEALREMOTEADDITEM, "SM_DEALREMOTEADDITEM"},
    {SM_DEALREMOTEDELITEM, "SM_DEALREMOTEDELITEM"},
    {SM_DEALCHGGOLD_OK, "SM_DEALCHGGOLD_OK"},
    {SM_DEALCHGGOLD_FAIL, "SM_DEALCHGGOLD_FAIL"},
    {SM_DEALREMOTECHGGOLD, "SM_DEALREMOTECHGGOLD"},
    {SM_DEALSUCCESS, "SM_DEALSUCCESS"},
    {SM_READMINIMAP_OK, "SM_READMINIMAP_OK"},
    {SM_READMINIMAP_FAIL, "SM_READMINIMAP_FAIL"},
    {SM_SERVERCONFIG, "SM_SERVERCONFIG"},
    {SM_GAMEGOLDNAME, "SM_GAMEGOLDNAME"},
    {SM_PASSWORD, "SM_PASSWORD"},
    {SM_RIDEHORSE, "SM_RIDEHORSE"},
    {SM_MONSTERSAY, "SM_MONSTERSAY"},
    {SM_ALIVE, "SM_ALIVE"},
    {SM_INSTANCEHEALGUAGE, "SM_INSTANCEHEALGUAGE"},
    {SM_BREAKWEAPON, "SM_BREAKWEAPON"},
    {SM_HIDE, "SM_HIDE"},
    {SM_NOWDEATH, "SM_NOWDEATH"},
    {SM_41, "SM_41"},
    {SM_FEATURECHANGED, "SM_FEATURECHANGED"},
    {SM_USERNAME, "SM_USERNAME"},
    {SM_MYSTATUS, "SM_MYSTATUS"},
    {SM_RUSHKUNG, "SM_RUSHKUNG"},
    {SM_RUSH, "SM_RUSH"},
    {SM_BACKSTEP, "SM_BACKSTEP"},
    {SM_HORSERUN, "SM_HORSERUN"},
    {SM_CRSHIT, "SM_CRSHIT"},
    {SM_TWINHIT, "SM_TWINHIT"},
    {SM_DIGUP, "SM_DIGUP"},
    {SM_DIGDOWN, "SM_DIGDOWN"},
    {SM_FLYAXE, "SM_FLYAXE"},
    {SM_LIGHTING, "SM_LIGHTING"}
};

// 获取协议名称（用于调试）
std::string GetPacketTypeName(PacketType type) {
    auto it = PacketTypeNames.find(type);
    if (it != PacketTypeNames.end()) {
        return it->second;
    }
    return "UNKNOWN_PACKET_" + std::to_string(static_cast<int>(type));
}

// 判断是否是客户端协议
bool IsClientPacket(PacketType type) {
    // CM_ 开头的协议都是客户端到服务器
    return type >= 80 && type <= 4999;
}

// 判断是否是服务器协议
bool IsServerPacket(PacketType type) {
    // SM_ 开头的协议都是服务器到客户端
    return (type >= 1 && type < 80) || (type >= 500 && type <= 9999);
}

// 判断是否需要登录后才能处理的协议
bool RequiresLogin(PacketType type) {
    switch (type) {
        case CM_PROTOCOL:
        case CM_IDPASSWORD:
        case CM_ADDNEWUSER:
        case CM_CHANGEPASSWORD:
        case CM_GETBACKPASSWORD:
        case CM_QUERYCHR:
        case CM_NEWCHR:
        case CM_DELCHR:
        case CM_SELCHR:
        case CM_SELECTSERVER:
            return false;
        default:
            return true;
    }
}

// 判断是否是游戏内协议（需要进入游戏后）
bool IsGamePlayPacket(PacketType type) {
    return type >= CM_WALK && type <= CM_RIDE;
}

} // namespace Protocol
} // namespace MirServer 