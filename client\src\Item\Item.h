#pragma once

#include <string>
#include <memory>
#include <vector>

/**
 * @enum ItemType
 * @brief Types of items in the game
 */
enum class ItemType {
    NONE = 0,
    WEAPON = 1,
    ARMOR = 2,
    HELMET = 3,
    NECKLACE = 4,
    RING = 5,
    BRACELET = 6,
    SHOES = 7,
    BELT = 8,
    STONE = 9,
    TORCH = 10,
    SCROLL = 11,
    POTION = 12,
    QUEST = 13,
    GOLD = 14,
    BOOK = 15,
    MISC = 16
};

/**
 * @class Item
 * @brief Represents an item in the game
 */
class Item {
private:
    int m_id;                   ///< Unique ID of the item
    int m_makeIndex;            ///< Unique make index of the item
    ItemType m_type;            ///< Type of the item
    int m_look;                 ///< Appearance of the item
    std::string m_name;         ///< Name of the item
    bool m_identified;          ///< Whether the item is identified
    int m_durability;           ///< Current durability of the item
    int m_maxDurability;        ///< Maximum durability of the item
    int m_weight;               ///< Weight of the item
    int m_price;                ///< Base price of the item
    int m_requiredLevel;        ///< Required level to use the item
    int m_requiredClass;        ///< Required class to use the item
    int m_requiredGender;       ///< Required gender to use the item
    int m_attackPower;          ///< Attack power bonus
    int m_magicPower;           ///< Magic power bonus
    int m_defense;              ///< Defense bonus
    int m_magicDefense;         ///< Magic defense bonus
    std::vector<uint8_t> m_values; ///< Additional values/properties

public:
    /**
     * @brief Constructor
     * @param id Item ID
     * @param type Item type
     * @param look Item appearance
     * @param name Item name
     * @param identified Whether the item is identified
     */
    Item(int id = 0, ItemType type = ItemType::NONE, int look = 0, const std::string& name = "", bool identified = false);

    /**
     * @brief Destructor
     */
    ~Item();

    /**
     * @brief Get the item ID
     * @return Item ID
     */
    int GetId() const { return m_id; }

    /**
     * @brief Set the item ID
     * @param id Item ID
     */
    void SetId(int id) { m_id = id; }

    /**
     * @brief Get the make index
     * @return Make index
     */
    int GetMakeIndex() const { return m_makeIndex; }

    /**
     * @brief Set the make index
     * @param makeIndex Make index
     */
    void SetMakeIndex(int makeIndex) { m_makeIndex = makeIndex; }

    /**
     * @brief Get the item type
     * @return Item type
     */
    ItemType GetType() const { return m_type; }

    /**
     * @brief Set the item type
     * @param type Item type
     */
    void SetType(ItemType type) { m_type = type; }

    /**
     * @brief Get the item appearance
     * @return Item appearance
     */
    int GetLook() const { return m_look; }

    /**
     * @brief Set the item appearance
     * @param look Item appearance
     */
    void SetLook(int look) { m_look = look; }

    /**
     * @brief Get the item name
     * @return Item name
     */
    const std::string& GetName() const { return m_name; }

    /**
     * @brief Set the item name
     * @param name Item name
     */
    void SetName(const std::string& name) { m_name = name; }

    /**
     * @brief Check if the item is identified
     * @return true if identified, false otherwise
     */
    bool IsIdentified() const { return m_identified; }

    /**
     * @brief Set whether the item is identified
     * @param identified Whether the item is identified
     */
    void SetIdentified(bool identified) { m_identified = identified; }

    /**
     * @brief Get the current durability
     * @return Current durability
     */
    int GetDurability() const { return m_durability; }

    /**
     * @brief Set the current durability
     * @param durability Current durability
     */
    void SetDurability(int durability) { m_durability = durability; }

    /**
     * @brief Get the maximum durability
     * @return Maximum durability
     */
    int GetMaxDurability() const { return m_maxDurability; }

    /**
     * @brief Set the maximum durability
     * @param maxDurability Maximum durability
     */
    void SetMaxDurability(int maxDurability) { m_maxDurability = maxDurability; }

    /**
     * @brief Get the item weight
     * @return Item weight
     */
    int GetWeight() const { return m_weight; }

    /**
     * @brief Set the item weight
     * @param weight Item weight
     */
    void SetWeight(int weight) { m_weight = weight; }

    /**
     * @brief Get the item price
     * @return Item price
     */
    int GetPrice() const { return m_price; }

    /**
     * @brief Set the item price
     * @param price Item price
     */
    void SetPrice(int price) { m_price = price; }

    /**
     * @brief Get the required level
     * @return Required level
     */
    int GetRequiredLevel() const { return m_requiredLevel; }

    /**
     * @brief Set the required level
     * @param level Required level
     */
    void SetRequiredLevel(int level) { m_requiredLevel = level; }

    /**
     * @brief Get the required class
     * @return Required class
     */
    int GetRequiredClass() const { return m_requiredClass; }

    /**
     * @brief Set the required class
     * @param requiredClass Required class
     */
    void SetRequiredClass(int requiredClass) { m_requiredClass = requiredClass; }

    /**
     * @brief Get the required gender
     * @return Required gender
     */
    int GetRequiredGender() const { return m_requiredGender; }

    /**
     * @brief Set the required gender
     * @param requiredGender Required gender
     */
    void SetRequiredGender(int requiredGender) { m_requiredGender = requiredGender; }

    /**
     * @brief Get the attack power bonus
     * @return Attack power bonus
     */
    int GetAttackPower() const { return m_attackPower; }

    /**
     * @brief Set the attack power bonus
     * @param attackPower Attack power bonus
     */
    void SetAttackPower(int attackPower) { m_attackPower = attackPower; }

    /**
     * @brief Get the magic power bonus
     * @return Magic power bonus
     */
    int GetMagicPower() const { return m_magicPower; }

    /**
     * @brief Set the magic power bonus
     * @param magicPower Magic power bonus
     */
    void SetMagicPower(int magicPower) { m_magicPower = magicPower; }

    /**
     * @brief Get the defense bonus
     * @return Defense bonus
     */
    int GetDefense() const { return m_defense; }

    /**
     * @brief Set the defense bonus
     * @param defense Defense bonus
     */
    void SetDefense(int defense) { m_defense = defense; }

    /**
     * @brief Get the magic defense bonus
     * @return Magic defense bonus
     */
    int GetMagicDefense() const { return m_magicDefense; }

    /**
     * @brief Set the magic defense bonus
     * @param magicDefense Magic defense bonus
     */
    void SetMagicDefense(int magicDefense) { m_magicDefense = magicDefense; }

    /**
     * @brief Get a specific value from the values array
     * @param index Index of the value
     * @return Value at the specified index, or 0 if index is out of range
     */
    uint8_t GetValue(size_t index) const;

    /**
     * @brief Set a specific value in the values array
     * @param index Index of the value
     * @param value Value to set
     */
    void SetValue(size_t index, uint8_t value);

    /**
     * @brief Get the values array
     * @return Values array
     */
    const std::vector<uint8_t>& GetValues() const { return m_values; }

    /**
     * @brief Set the values array
     * @param values Values array
     */
    void SetValues(const std::vector<uint8_t>& values) { m_values = values; }
};
