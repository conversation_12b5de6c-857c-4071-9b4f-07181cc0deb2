#pragma once

#include "../Common/Types.h"
#include "PacketTypes.h"
#include <vector>
#include <string>
#include <cstring>

namespace MirServer {
namespace Protocol {

// 消息转换器类 - 负责编码和解码网络消息
class MessageConverter {
public:
    // ==================== 编码函数 ====================
    
    // 基础编码
    static void EncodeHeader(std::vector<uint8_t>& buffer, const PacketHeader& header);
    static void EncodeByte(std::vector<uint8_t>& buffer, uint8_t value);
    static void EncodeWord(std::vector<uint8_t>& buffer, uint16_t value);
    static void EncodeDWord(std::vector<uint8_t>& buffer, uint32_t value);
    static void EncodeString(std::vector<uint8_t>& buffer, const std::string& str, size_t fixedSize = 0);
    static void EncodePoint(std::vector<uint8_t>& buffer, const Point& point);
    
    // 游戏对象编码
    static void EncodeAbility(std::vector<uint8_t>& buffer, const Ability& ability);
    static void EncodeUserItem(std::vector<uint8_t>& buffer, const UserItem& item);
    static void EncodeHumDataInfo(std::vector<uint8_t>& buffer, const HumDataInfo& info);
    
    // 登录相关消息编码
    static std::vector<uint8_t> EncodeLoginSuccess(const std::string& message);
    static std::vector<uint8_t> EncodeLoginFail(int errorCode, const std::string& message);
    static std::vector<uint8_t> EncodeCharacterList(const std::vector<HumDataInfo>& characters);
    static std::vector<uint8_t> EncodeStartPlay(const HumDataInfo& charInfo);
    
    // 游戏内消息编码
    static std::vector<uint8_t> EncodeMapChanged(const std::string& mapName, const Point& pos);
    static std::vector<uint8_t> EncodeAbilityUpdate(const Ability& ability);
    static std::vector<uint8_t> EncodeBagItems(const std::vector<UserItem>& items);
    static std::vector<uint8_t> EncodeAddItem(const UserItem& item);
    static std::vector<uint8_t> EncodeDeleteItem(uint16_t makeIndex);
    static std::vector<uint8_t> EncodeGoldChanged(uint32_t gold);
    static std::vector<uint8_t> EncodeHealthChanged(uint16_t hp, uint16_t mp);
    
    // 移动和动作消息编码
    static std::vector<uint8_t> EncodeObjectWalk(uint32_t objectId, const Point& pos, DirectionType dir);
    static std::vector<uint8_t> EncodeObjectRun(uint32_t objectId, const Point& pos, DirectionType dir);
    static std::vector<uint8_t> EncodeObjectTurn(uint32_t objectId, DirectionType dir);
    static std::vector<uint8_t> EncodeObjectAttack(uint32_t objectId, uint32_t targetId, DirectionType dir);
    
    // NPC和商店消息编码
    static std::vector<uint8_t> EncodeNPCDialog(uint32_t npcId, const std::string& text, const std::vector<std::string>& options);
    static std::vector<uint8_t> EncodeShopItemList(const std::vector<std::pair<uint16_t, uint32_t>>& items); // itemId, price
    
    // ==================== 解码函数 ====================
    
    // 基础解码
    static bool DecodeHeader(const uint8_t* data, size_t dataSize, PacketHeader& header);
    static bool DecodeByte(const uint8_t*& data, size_t& remaining, uint8_t& value);
    static bool DecodeWord(const uint8_t*& data, size_t& remaining, uint16_t& value);
    static bool DecodeDWord(const uint8_t*& data, size_t& remaining, uint32_t& value);
    static bool DecodeString(const uint8_t*& data, size_t& remaining, std::string& str, size_t fixedSize = 0);
    static bool DecodePoint(const uint8_t*& data, size_t& remaining, Point& point);
    
    // 登录相关消息解码
    struct LoginRequest {
        std::string account;
        std::string password;
        uint8_t clientVersion;
    };
    static bool DecodeLoginRequest(const uint8_t* data, size_t dataSize, LoginRequest& request);
    
    struct CharacterCreateRequest {
        std::string charName;
        uint8_t job;
        uint8_t gender;
        uint8_t hair;
    };
    static bool DecodeCharacterCreate(const uint8_t* data, size_t dataSize, CharacterCreateRequest& request);
    
    struct CharacterSelectRequest {
        std::string charName;
    };
    static bool DecodeCharacterSelect(const uint8_t* data, size_t dataSize, CharacterSelectRequest& request);
    
    // 游戏内消息解码
    struct MoveRequest {
        Point targetPos;
        DirectionType direction;
    };
    static bool DecodeMoveRequest(const uint8_t* data, size_t dataSize, MoveRequest& request);
    
    struct AttackRequest {
        uint32_t targetId;
        DirectionType direction;
    };
    static bool DecodeAttackRequest(const uint8_t* data, size_t dataSize, AttackRequest& request);
    
    struct ChatMessage {
        std::string message;
        uint8_t chatType; // 0=普通, 1=私聊, 2=组队, 3=行会
        std::string targetName; // 私聊时的目标
    };
    static bool DecodeChatMessage(const uint8_t* data, size_t dataSize, ChatMessage& message);
    
    struct ItemActionRequest {
        uint16_t makeIndex;
        uint8_t action; // 0=使用, 1=丢弃, 2=装备, 3=卸下
        uint16_t targetPos; // 装备位置或背包位置
    };
    static bool DecodeItemAction(const uint8_t* data, size_t dataSize, ItemActionRequest& request);
    
    // ==================== 工具函数 ====================
    
    // 计算字符串在传奇协议中的字节长度（处理中文）
    static size_t GetStringByteLength(const std::string& str);
    
    // 验证数据包完整性
    static bool ValidatePacket(const uint8_t* data, size_t dataSize);
    
    // 创建错误响应包
    static std::vector<uint8_t> CreateErrorResponse(uint16_t errorCode, const std::string& errorMsg);
    
    // DefaultMessage编解码（传奇特有的6字节编码）
    static std::string EncodeMessage(const DefaultMessage& msg);
    static bool DecodeMessage(const std::string& str, DefaultMessage& msg);
    static std::string EncodeString(const std::string& str);
    static std::string DecodeString(const std::string& str);
    static std::string EncodeBuffer(const void* data, size_t size);
    static bool DecodeBuffer(const std::string& str, void* data, size_t size);
    
private:
    // 内部辅助函数
    template<typename T>
    static void EncodeValue(std::vector<uint8_t>& buffer, T value) {
        size_t oldSize = buffer.size();
        buffer.resize(oldSize + sizeof(T));
        std::memcpy(buffer.data() + oldSize, &value, sizeof(T));
    }
    
    template<typename T>
    static bool DecodeValue(const uint8_t*& data, size_t& remaining, T& value) {
        if (remaining < sizeof(T)) {
            return false;
        }
        std::memcpy(&value, data, sizeof(T));
        data += sizeof(T);
        remaining -= sizeof(T);
        return true;
    }
};

} // namespace Protocol
} // namespace MirServer 