#include "NetworkManager.h"
#include <iostream>

NetworkManager::NetworkManager()
    : m_socket(nullptr)
    , m_socketSet(nullptr)
    , m_serverPort(0)
    , m_connected(false)
    , m_running(false)
{
}

NetworkManager::~NetworkManager()
{
    Shutdown();
}

bool NetworkManager::Initialize()
{
#if NETWORK_ENABLED
    // Initialize SDL_net
    if (SDLNet_Init() < 0) {
        std::cerr << "Failed to initialize SDL_net: " << SDLNet_GetError() << std::endl;
        return false;
    }

    // Create socket set
    m_socketSet = SDLNet_AllocSocketSet(1);
    if (!m_socketSet) {
        std::cerr << "Failed to allocate socket set: " << SDLNet_GetError() << std::endl;
        return false;
    }

    return true;
#else
    std::cerr << "Network functionality is disabled (SDL_net not available)" << std::endl;
    return false;
#endif
}

void NetworkManager::Shutdown()
{
    // Disconnect from server
    Disconnect();

#if NETWORK_ENABLED
    // Free socket set
    if (m_socketSet) {
        SDLNet_FreeSocketSet(m_socketSet);
        m_socketSet = nullptr;
    }

    // Quit SDL_net
    SDLNet_Quit();
#endif
}

bool NetworkManager::Connect(const std::string& address, uint16_t port)
{
#if NETWORK_ENABLED
    // Disconnect if already connected
    if (m_connected) {
        Disconnect();
    }

    // Resolve server address
    IPaddress ip;
    if (SDLNet_ResolveHost(&ip, address.c_str(), port) < 0) {
        std::cerr << "Failed to resolve host: " << SDLNet_GetError() << std::endl;
        return false;
    }

    // Open TCP socket
    m_socket = SDLNet_TCP_Open(&ip);
    if (!m_socket) {
        std::cerr << "Failed to open TCP socket: " << SDLNet_GetError() << std::endl;
        return false;
    }

    // Add socket to socket set
    if (SDLNet_TCP_AddSocket(m_socketSet, m_socket) < 0) {
        std::cerr << "Failed to add socket to socket set: " << SDLNet_GetError() << std::endl;
        SDLNet_TCP_Close(m_socket);
        m_socket = nullptr;
        return false;
    }

    // Store server address and port
    m_serverAddress = address;
    m_serverPort = port;

    // Set connection status
    m_connected = true;

    // Start receive thread
    m_running = true;
    m_receiveThread = std::thread(&NetworkManager::ReceiveThread, this);

    // Send connect packet
    Packet packet(PacketType::CONNECT);
    QueuePacket(packet);

    return true;
#else
    std::cerr << "Cannot connect: Network functionality is disabled (SDL_net not available)" << std::endl;
    return false;
#endif
}

void NetworkManager::Disconnect()
{
    // Stop receive thread
    if (m_running) {
        m_running = false;
        if (m_receiveThread.joinable()) {
            m_receiveThread.join();
        }
    }

#if NETWORK_ENABLED
    // Close socket
    if (m_socket) {
        SDLNet_TCP_DelSocket(m_socketSet, m_socket);
        SDLNet_TCP_Close(m_socket);
        m_socket = nullptr;
    }
#endif

    // Clear queues
    {
        std::lock_guard<std::mutex> lock(m_sendMutex);
        std::queue<Packet> empty;
        std::swap(m_sendQueue, empty);
    }
    {
        std::lock_guard<std::mutex> lock(m_receiveMutex);
        std::queue<Packet> empty;
        std::swap(m_receiveQueue, empty);
    }

    // Reset connection status
    m_connected = false;
}

void NetworkManager::Update()
{
    if (!m_connected) {
        return;
    }

    // Process send queue
    {
        std::lock_guard<std::mutex> lock(m_sendMutex);
        while (!m_sendQueue.empty()) {
            Packet packet = m_sendQueue.front();
            m_sendQueue.pop();

            if (!SendPacket(packet)) {
                std::cerr << "Failed to send packet" << std::endl;
                Disconnect();
                return;
            }
        }
    }

    // Process receive queue
    {
        std::lock_guard<std::mutex> lock(m_receiveMutex);
        while (!m_receiveQueue.empty()) {
            Packet packet = m_receiveQueue.front();
            m_receiveQueue.pop();

            // Call packet handler
            auto it = m_packetHandlers.find(packet.GetType());
            if (it != m_packetHandlers.end()) {
                it->second(packet);
            }
        }
    }
}

void NetworkManager::QueuePacket(const Packet& packet)
{
    std::lock_guard<std::mutex> lock(m_sendMutex);
    m_sendQueue.push(packet);
}

void NetworkManager::QueueMessage(const TDefaultMessage& msg)
{
    // Convert TDefaultMessage to Packet
    Packet packet = MessageConverter::ToPacket(msg);

    // Queue the packet
    QueuePacket(packet);
}

void NetworkManager::RegisterPacketHandler(PacketType type, PacketHandler handler)
{
    m_packetHandlers[type] = handler;
}

void NetworkManager::UnregisterPacketHandler(PacketType type)
{
    m_packetHandlers.erase(type);
}

void NetworkManager::ReceiveThread()
{
#if NETWORK_ENABLED
    while (m_running) {
        // Check for socket activity
        int numReady = SDLNet_CheckSockets(m_socketSet, 100);  // 100ms timeout

        if (numReady <= 0) {
            // No activity or error
            continue;
        }

        // Check if socket is ready
        if (SDLNet_SocketReady(m_socket)) {
            // Receive packet
            Packet packet;
            if (ReceivePacket(packet)) {
                // Add to receive queue
                std::lock_guard<std::mutex> lock(m_receiveMutex);
                m_receiveQueue.push(packet);
            } else {
                // Connection lost
                std::cerr << "Connection lost" << std::endl;
                m_connected = false;
                break;
            }
        }
    }
#endif
}

bool NetworkManager::SendPacket(const Packet& packet)
{
#if NETWORK_ENABLED
    if (!m_socket) {
        return false;
    }

    // Get packet data
    const std::vector<uint8_t>& data = packet.GetData();

    // Send packet type
    uint16_t type = static_cast<uint16_t>(packet.GetType());
    if (SDLNet_TCP_Send(m_socket, &type, sizeof(type)) < sizeof(type)) {
        return false;
    }

    // Send packet size
    uint16_t size = static_cast<uint16_t>(data.size());
    if (SDLNet_TCP_Send(m_socket, &size, sizeof(size)) < sizeof(size)) {
        return false;
    }

    // Send packet data
    if (size > 0) {
        if (SDLNet_TCP_Send(m_socket, data.data(), size) < size) {
            return false;
        }
    }

    return true;
#else
    return false;
#endif
}

bool NetworkManager::ReceivePacket(Packet& packet)
{
#if NETWORK_ENABLED
    if (!m_socket) {
        return false;
    }

    // Receive packet type
    uint16_t type;
    if (SDLNet_TCP_Recv(m_socket, &type, sizeof(type)) <= 0) {
        return false;
    }

    // Receive packet size
    uint16_t size;
    if (SDLNet_TCP_Recv(m_socket, &size, sizeof(size)) <= 0) {
        return false;
    }

    // Set packet type
    packet.SetType(static_cast<PacketType>(type));

    // Clear packet data
    packet.Clear();

    // Receive packet data
    if (size > 0) {
        std::vector<uint8_t> data(size);
        if (SDLNet_TCP_Recv(m_socket, data.data(), size) <= 0) {
            return false;
        }

        // Add data to packet
        for (uint8_t byte : data) {
            packet << byte;
        }
    }

    return true;
#else
    return false;
#endif
}

// Utility methods for creating packets

Packet NetworkManager::CreateLoginRequest(const std::string& username, const std::string& password)
{
    // Create login request packet
    Packet packet(PacketType::LOGIN_REQUEST);
    packet << username << password;
    return packet;
}

Packet NetworkManager::CreateCharacterListRequest()
{
    // Create character list request packet
    Packet packet(PacketType::CHARACTER_LIST_REQUEST);
    return packet;
}

Packet NetworkManager::CreateCharacterSelectRequest(const std::string& characterName)
{
    // Create character select request packet
    Packet packet(PacketType::CHARACTER_SELECT_REQUEST);
    packet << characterName;
    return packet;
}

Packet NetworkManager::CreateCharacterCreateRequest(const std::string& characterName, int characterClass, int gender, int hair)
{
    // Create character create request packet
    Packet packet(PacketType::CHARACTER_CREATE_REQUEST);
    packet << characterName << characterClass << gender << hair;
    return packet;
}

Packet NetworkManager::CreateCharacterDeleteRequest(const std::string& characterName)
{
    // Create character delete request packet
    Packet packet(PacketType::CHARACTER_DELETE_REQUEST);
    packet << characterName;
    return packet;
}

Packet NetworkManager::CreatePlayerMovePacket(int direction, bool run)
{
    // Create player move packet
    // If run is true, use PLAYER_MOVE with run flag, otherwise use PLAYER_MOVE with walk flag
    Packet packet(PacketType::PLAYER_MOVE);
    packet << direction << (run ? 1 : 0);
    return packet;
}

Packet NetworkManager::CreatePlayerAttackPacket(int attackType)
{
    // Create player attack packet
    Packet packet(PacketType::PLAYER_ATTACK);
    packet << attackType;
    return packet;
}

Packet NetworkManager::CreatePlayerCastPacket(int skillId, int targetX, int targetY)
{
    // Create player cast packet
    Packet packet(PacketType::PLAYER_CAST);
    packet << skillId << targetX << targetY;
    return packet;
}

Packet NetworkManager::CreateChatMessagePacket(const std::string& message)
{
    // Create chat message packet
    Packet packet(PacketType::CHAT_MESSAGE);
    packet << message;
    return packet;
}

Packet NetworkManager::CreateWhisperMessagePacket(const std::string& targetName, const std::string& message)
{
    // Create whisper message packet
    Packet packet(PacketType::WHISPER_MESSAGE);
    packet << targetName << message;
    return packet;
}

Packet NetworkManager::CreateGuildMessagePacket(const std::string& message)
{
    // Create guild message packet
    Packet packet(PacketType::GUILD_MESSAGE);
    packet << message;
    return packet;
}

Packet NetworkManager::CreateGroupMessagePacket(const std::string& message)
{
    // Create group message packet
    Packet packet(PacketType::GROUP_MESSAGE);
    packet << message;
    return packet;
}

void NetworkManager::SendChatMessage(const std::string& message)
{
    // Create and queue chat message packet
    Packet packet = CreateChatMessagePacket(message);
    QueuePacket(packet);
}

void NetworkManager::SendWhisperMessage(const std::string& targetName, const std::string& message)
{
    // Create and queue whisper message packet
    Packet packet = CreateWhisperMessagePacket(targetName, message);
    QueuePacket(packet);
}

void NetworkManager::SendGuildMessage(const std::string& message)
{
    // Create and queue guild message packet
    Packet packet = CreateGuildMessagePacket(message);
    QueuePacket(packet);
}

void NetworkManager::SendGroupMessage(const std::string& message)
{
    // Create and queue group message packet
    Packet packet = CreateGroupMessagePacket(message);
    QueuePacket(packet);
}