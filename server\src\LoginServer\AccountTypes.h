#ifndef ACCOUNT_TYPES_H
#define ACCOUNT_TYPES_H

#include <cstdint>
#include <ctime>
#include <string>
#include "../Protocol/PacketTypes.h"
#include "../Protocol/MessageConverter.h"

namespace MirServer {

// Database constants
constexpr int DEFBLOCKSIZE = 16;

// ID record header
struct TIDRecordHeader {
    bool boDeleted;
    uint8_t bt1;
    uint8_t bt2;
    uint8_t bt3;
    double CreateDate;
    double LastLoginDate;
    int32_t n14;
    int32_t nNextDeletedIdx;
};

// Database header
struct TDBHeader {
    bool boDeleted;
    uint8_t bt1;
    uint8_t bt2;
    uint8_t bt3;
    double UpdateDateTime;
    double CreateDate;
    uint16_t UpdateDate;
    uint16_t reserved;
};

// User entry - corresponds to Delphi TUserEntry
struct TUserEntry {
    char sAccount[11];      // string[10] in Delphi
    char sPassword[11];     // string[10] in Delphi
    char sUserName[21];     // string[20] in Delphi
    char sSSNo[15];         // string[14] in Delphi
    char sPhone[15];        // string[14] in Delphi
    char sQuiz[21];         // string[20] in Delphi
    char sAnswer[13];       // string[12] in Delphi
    char sEMail[41];        // string[40] in Delphi
};

// User entry additional info - corresponds to Delphi TUserEntryAdd
struct TUserEntryAdd {
    char sQuiz2[21];        // string[20] in Delphi
    char sAnswer2[13];      // string[12] in Delphi
    char sBirthDay[11];     // string[10] in Delphi
    char sMobilePhone[14];  // string[13] in Delphi
    char sMemo[21];         // string[20] in Delphi
    char sMemo2[21];        // string[20] in Delphi
};

// Default message structure - corresponds to Delphi TDefaultMessage
#pragma pack(push, 1)
struct TDefaultMessage {
    uint16_t Recog;
    uint16_t Ident;
    uint16_t Param;
    uint8_t Tag;
    uint8_t Series;
    uint32_t Nonce;
    uint32_t Reserved;  // Add 4 bytes padding to make it 16 bytes total
};
#pragma pack(pop)

// Ensure the struct size matches Delphi (should be 16 bytes)
static_assert(sizeof(TDefaultMessage) == DEFBLOCKSIZE, "TDefaultMessage size mismatch");

// Protocol constants for LoginServer
// Client messages
constexpr uint16_t CM_PROTOCOL          = 2001;
constexpr uint16_t CM_IDPASSWORD        = 2002;
constexpr uint16_t CM_ADDNEWUSER        = 2003;
constexpr uint16_t CM_CHANGEPASSWORD    = 2004;
constexpr uint16_t CM_UPDATEUSER        = 2005;
constexpr uint16_t CM_SELECTSERVER      = 104;
constexpr uint16_t CM_GETBACKPASSWORD   = 2010;

// Server messages  
constexpr uint16_t SM_CERTIFICATION_FAIL    = 501;
constexpr uint16_t SM_CERTIFICATION_SUCCESS = 500;
constexpr uint16_t SM_PASSWD_FAIL           = 502;
constexpr uint16_t SM_PASSOK_SELECTSERVER   = 503;
constexpr uint16_t SM_SELECTSERVER_OK       = 504;
constexpr uint16_t SM_SELECTSERVER_FAIL     = 505;
constexpr uint16_t SM_NEEDUPDATE_ACCOUNT    = 506;
constexpr uint16_t SM_UPDATEID_SUCCESS      = 507;
constexpr uint16_t SM_UPDATEID_FAIL         = 508;
constexpr uint16_t SM_NEWID_SUCCESS         = 600;
constexpr uint16_t SM_NEWID_FAIL            = 601;
constexpr uint16_t SM_CHGPASSWD_SUCCESS     = 602;
constexpr uint16_t SM_CHGPASSWD_FAIL        = 603;
constexpr uint16_t SM_GETBACKPASSWD_SUCCESS = 604;
constexpr uint16_t SM_GETBACKPASSWD_FAIL    = 605;
constexpr uint16_t SM_STARTFAIL             = 530;

// Helper function to create default message
inline TDefaultMessage MakeDefaultMsg(uint16_t ident, uint16_t recog, uint16_t param, uint8_t tag, uint8_t series) {
    TDefaultMessage msg;
    msg.Ident = ident;
    msg.Recog = recog;
    msg.Param = param;
    msg.Tag = tag;
    msg.Series = series;
    msg.Nonce = 0;
    msg.Reserved = 0;
    return msg;
}

// Helper function to encode default message
inline std::string EncodeDefaultMessage(const TDefaultMessage& msg) {
    // Convert to Protocol format for encoding
    Protocol::DefaultMessage protoMsg;
    protoMsg.nRecog = msg.Recog;
    protoMsg.wIdent = msg.Ident;
    protoMsg.wParam = msg.Param;
    protoMsg.wTag = msg.Tag;
    protoMsg.wSeries = msg.Series;
    return Protocol::MessageConverter::EncodeMessage(protoMsg);
}

// Helper function to get encoded message size
inline size_t GetEncodedSize(size_t rawSize) {
    return (rawSize * 4 + 2) / 3;  // Base64-like encoding
}

} // namespace MirServer

#endif // ACCOUNT_TYPES_H 