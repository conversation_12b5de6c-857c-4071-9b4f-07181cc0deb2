;This help file was created with HelpScribble 7.1.0
;Registered to: KpGb Software

:BASE VCLZip.hlp
:TITLE VCLZip Help File
:LINK D7VCL.HLP
1 Overview=Scribble10
1 TVCLUnZip component
2 TVCLUnZip component Reference=Scribble2300
2 Properties
3 ArchiveStream=Scribble2310
3 BlockMode=Scribble2315
3 BufferedStreamSize=Scribble2320
3 BufferLength=Scribble2325
3 Busy=Scribble2330
3 CheckDiskLabels=Scribble2335
3 CompressedSize=Scribble2340
3 CompressMethod=Scribble2345
3 CompressMethodStr=Scribble2350
3 Count=Scribble2355
3 Crc=Scribble2360
3 DateTime=Scribble2365
3 DecryptHeader=Scribble2370
3 DestDir=Scribble2375
3 DiskNo=Scribble2380
3 DoAll=Scribble2385
3 DoProcessMessages=Scribble2390
3 EncryptBeforeCompress=Scribble2395
3 ExternalFileAttributes=Scribble2400
3 FileComment=Scribble2405
3 FileHasComment=Scribble2410
3 FileIsOK=Scribble2415
3 Filename=Scribble2420
3 FilesList=Scribble2425
3 FlushFilesOnClose=Scribble2430
3 FullName=Scribble2435
3 ImproperZip=Scribble2440
3 IncompleteZipMode=Scribble2445
3 IsEncrypted=Scribble2450
3 isZip64=Scribble2455
3 KeepZipOpen=Scribble2460
3 MultiMode=Scribble2465
3 NumDisks=Scribble2470
3 NumSelected=Scribble2475
3 OEMConvert=Scribble2480
3 OnBadCRC=Scribble2485
3 OnBadPassword=Scribble2490
3 OnFilePercentDone=Scribble2495
3 OnGetNextBuffer=Scribble2500
3 OnGetNextDisk=Scribble2505
3 OnInCompleteZip=Scribble2510
3 OnPromptForOverwrite=Scribble2515
3 OnSkippingFile=Scribble2520
3 OnStartUnZipInfo=Scribble2525
3 OnTotalPercentDone=Scribble2530
3 OnUnZipComplete=Scribble2535
3 OperationMode=Scribble2540
3 OverwriteMode=Scribble2545
3 Password=Scribble2550
3 Pathname=Scribble2555
3 RecreateDirs=Scribble2560
3 RelativePathList=Scribble2565
3 ReplaceReadOnly=Scribble2570
3 RetainAttributes=Scribble2575
3 RootDir=Scribble2580
3 Selected=Scribble2585
3 SortMode=Scribble2590
3 ThisVersion=Scribble2595
3 UnCompressedSize=Scribble2600
3 ZipComment=Scribble2605
3 ZipHasComment=Scribble2610
3 ZipName=Scribble2615
3 ZipSize=Scribble2620
2 Methods
3 CheckArchive=Scribble2625
3 DecryptHeaderByte=Scribble2630
3 DecryptHeaderByteByPtr=Scribble2635
3 UnZip=Scribble2640
3 UnZipSelected=Scribble2645
3 UnZipToBuffer=Scribble2650
3 UnZipToBufferByIndex=Scribble2655
3 UnZipToStream=Scribble2660
3 UnZipToStreamByIndex=Scribble2665
3 AskForNewDisk=Scribble2670
3 CancelTheOperation=Scribble2675
3 ClearSelected=Scribble2680
3 ClearZip=Scribble2685
3 decrypt_buff=Scribble2690
3 DefaultFileNameForSplitPart=Scribble2695
3 DefaultGetNextDisk=Scribble2700
3 FillList=Scribble2705
3 GetDecryptHeaderPtr=Scribble2710
3 PauseTheOperation=Scribble2715
3 ReadZip=Scribble2720
3 ResetFileIsOK=Scribble2725
3 RestartTheOperation=Scribble2730
3 Sort=Scribble2735
2 Events
3 OnDecrypt=Scribble2740
3 OnEndUnZip=Scribble2745
3 OnFileNameForSplitPart=Scribble2750
3 OnHandleMessage=Scribble2755
3 OnStartUnZip=Scribble2760
1 TVCLZip component
2 TVCLZip component Reference=Scribble3200
2 Properties
3 AddDirEntriesOnRecurse=Scribble3210
3 CheckDiskLabels=Scribble3215
3 DateTime=Scribble3220
3 Dispose=Scribble3225
3 ExcludeList=Scribble3230
3 FileComment=Scribble3235
3 Filename=Scribble3240
3 FileOpenMode=Scribble3245
3 IncludeArchiveFiles=Scribble3250
3 IncludeHiddenFiles=Scribble3255
3 IncludeReadOnlyFiles=Scribble3260
3 IncludeSysFiles=Scribble3265
3 IsModified=Scribble3270
3 MultiMode=Scribble3275
3 MultiZipInfo=Scribble3280
3 NoCompressList=Scribble3285
3 OnPrepareNextDisk=Scribble3290
3 OnRecursingFile=Scribble3295
3 OnStartSpanCopy=Scribble3300
3 OnStartZipInfo=Scribble3305
3 OnZipComplete=Scribble3310
3 OtherVCLZip=Scribble3315
3 PackLevel=Scribble3320
3 Pathname=Scribble3325
3 PreserveStubs=Scribble3330
3 Recurse=Scribble3335
3 RelativePaths=Scribble3340
3 ResetArchiveBitOnZip=Scribble3345
3 SkipIfArchiveBitNotSet=Scribble3350
3 Store83Names=Scribble3355
3 StorePaths=Scribble3360
3 StoreVolumes=Scribble3365
3 TempPath=Scribble3370
3 ZipAction=Scribble3375
3 ZipComment=Scribble3380
2 Methods
3 DeleteEntries=Scribble3385
3 FixZip=Scribble3390
3 MakeNewSFX=Scribble3395
3 Split=Scribble3400
3 Zip=Scribble3405
3 ZipFromBuffer=Scribble3410
3 ZipFromStream=Scribble3415
3 encrypt_buff=Scribble3420
3 ExpandFilesList=Scribble3425
3 GetRawCompressedFile=Scribble3430
3 InsertRawCompressedFile=Scribble3435
3 MakeSFX=Scribble3440
3 SaveModifiedZipFile=Scribble3445
3 SFXToZip=Scribble3450
2 Events
3 OnDeleteEntry=Scribble3455
3 OnDisposeFile=Scribble3460
3 OnEncrypt=Scribble3465
3 OnEndZip=Scribble3470
3 OnGetNextStream=Scribble3475
3 OnNoSuchFile=Scribble3480
3 OnStartZip=Scribble3485
3 OnUpdate=Scribble3490
1 TMultiZipInfo class
2 TMultiZipInfo class Reference=Scribble3100
2 Properties
3 BlockSize=Scribble3110
3 CheckDiskLabels=Scribble3115
3 FirstBlockSize=Scribble3120
3 MultiMode=Scribble3125
3 SaveOnFirstDisk=Scribble3130
3 SaveZipInfoOnFirstDisk=Scribble3135
3 WriteDiskLabels=Scribble3140
2 Methods
3 Create=Scribble3145
1 TSFXConfig component
2 TSFXConfig component Reference=Scribble4010
2 Properties
3 AutoExtract=Scribble4020
3 Caption=Scribble4025
3 CmdLine=Scribble4030
3 DefaultPath=Scribble4035
3 HeaderLen=Scribble4040
3 InfoText=Scribble4045
3 InfoTitle=Scribble4050
3 OverwriteMode=Scribble4055
3 theHeader=Scribble4060
3 UserCanChangeOverwrite=Scribble4065
3 UserCanChooseFiles=Scribble4070
3 UserCanDisableCmdLine=Scribble4075
2 Methods
3 CreateHeader=Scribble4080
