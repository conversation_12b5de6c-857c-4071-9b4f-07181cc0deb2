#pragma once

#include "../Common/Types.h"
#include "PacketTypes.h"
#include <functional>
#include <thread>
#include <atomic>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <unordered_map>
#include <unordered_set>
#include <chrono>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <unistd.h>
    #include <fcntl.h>
    #include <errno.h>
#endif

namespace MirServer {
namespace Network {

// 游戏数据包结构
struct GamePacket {
    std::vector<uint8_t> data;
    
    GamePacket() = default;
    GamePacket(const uint8_t* d, size_t size) : data(d, d + size) {}
};

// 前向声明
class ClientConnection;
class PacketHandler;

// 网络事件类型
enum class NetworkEventType {
    CLIENT_CONNECTED,
    CLIENT_DISCONNECTED,
    PACKET_RECEIVED,
    ERROR_OCCURRED,
    CONNECTION_TIMEOUT,
    SEND_BUFFER_FULL,
    RECEIVE_BUFFER_OVERFLOW
};

// 网络事件结构
struct NetworkEvent {
    NetworkEventType type;
    std::shared_ptr<ClientConnection> client;
    std::vector<uint8_t> data;
    std::string errorMsg;
    int errorCode = 0;
};

// 会话信息结构（对应原delphi的SessionInfo）
struct SessionInfo {
    std::string sSocData;             // 接收数据缓冲区
    std::string sSendData;            // 发送数据缓冲区
    int nUserListIndex = 0;           // 用户列表索引
    int nPacketIdx = -1;              // 数据包索引
    int nPacketErrCount = 0;          // 错误包计数
    
    bool boStartLogon = true;         // 是否刚开始登录
    bool boSendLock = false;          // 发送锁定
    bool boOverNomSize = false;       // 超过正常大小
    int nOverNomSizeCount = 0;        // 超大小计数
    
    std::chrono::steady_clock::time_point dwSendLatestTime;    // 最后发送时间
    int nCheckSendLength = 0;         // 检查发送长度
    bool boSendAvailable = true;      // 可发送标志
    bool boSendCheck = false;         // 发送检查
    std::chrono::steady_clock::time_point dwTimeOutTime;      // 超时时间
    
    int nReceiveLength = 0;           // 接收长度
    std::chrono::steady_clock::time_point dwReceiveLengthTick; // 接收长度时间戳
    std::chrono::steady_clock::time_point dwReceiveTick;       // 接收时间戳
    std::string sRemoteAddr;          // 远程地址
    std::chrono::steady_clock::time_point dwSayMsgTick;       // 聊天消息时间戳
    
    SessionInfo() {
        auto now = std::chrono::steady_clock::now();
        dwSendLatestTime = dwTimeOutTime = dwReceiveLengthTick = 
        dwReceiveTick = dwSayMsgTick = now;
    }
};

// IP地址过滤信息
struct IPFilterInfo {
    std::string ipAddress;
    std::chrono::steady_clock::time_point blockTime;
    int connectionCount = 0;
    int attackCount = 0;
    bool isPermanent = false;
    
    IPFilterInfo() = default;
    IPFilterInfo(const std::string& ip) : ipAddress(ip) {
        blockTime = std::chrono::steady_clock::now();
    }
};

// 网络管理器类
class NetworkManager {
public:
    NetworkManager();
    ~NetworkManager();

    // 初始化和清理
    bool Initialize();
    void Shutdown();

    // 服务器操作
    bool StartServer(const std::string& address, uint16_t port, int maxConnections = 1000);
    bool StartServer(uint16_t port, int maxConnections = 1000);
    void StopServer();
    bool IsRunning() const { return m_isRunning; }
    
    // 配置设置
    void SetMaxConnectionsPerIP(int maxConn) { m_maxConnectionsPerIP = maxConn; }
    void SetClientTimeout(uint32_t timeoutMs) { m_clientTimeoutMs = timeoutMs; }
    void SetSendBufferSize(size_t size) { m_sendBufferSize = size; }
    void SetReceiveBufferSize(size_t size) { m_receiveBufferSize = size; }
    void SetEnableNagle(bool enable) { m_enableNagle = enable; }
    void SetKeepAlive(bool enable, uint32_t intervalMs = 30000) { 
        m_enableKeepAlive = enable; 
        m_keepAliveIntervalMs = intervalMs;
    }

    // 客户端管理
    void BroadcastPacket(const void* data, size_t size);
    void SendToClient(uint32_t clientId, const void* data, size_t size);
    bool SendToClient(std::shared_ptr<ClientConnection> client, const void* data, size_t size);
    void DisconnectClient(uint32_t clientId);
    void DisconnectClient(std::shared_ptr<ClientConnection> client);
    size_t GetClientCount() const;
    std::shared_ptr<ClientConnection> GetClient(uint32_t clientId) const;
    
    // IP过滤管理
    bool AddBlockedIP(const std::string& ipAddress, bool permanent = false);
    bool RemoveBlockedIP(const std::string& ipAddress);
    bool IsIPBlocked(const std::string& ipAddress) const;
    void ClearTempBlockedIPs();
    size_t GetConnectionCountForIP(const std::string& ipAddress) const;
    bool IsConnectionLimited(const std::string& ipAddress) const;
    void CloseConnectionsByIP(const std::string& ipAddress);
    
    // 攻击检测
    bool AddAttackIP(const std::string& ipAddress);
    int GetAttackCount(const std::string& ipAddress) const;
    void ClearAttackList();
    
    // 事件处理回调
    using ClientConnectedCallback = std::function<void(std::shared_ptr<ClientConnection>)>;
    using ClientDisconnectedCallback = std::function<void(std::shared_ptr<ClientConnection>)>;
    using ClientMessageCallback = std::function<void(std::shared_ptr<ClientConnection>, const std::vector<uint8_t>&)>;
    using ErrorCallback = std::function<void(const std::string&, int)>;
    
    void SetOnClientConnect(ClientConnectedCallback callback) { m_onClientConnect = callback; }
    void SetOnClientDisconnect(ClientDisconnectedCallback callback) { m_onClientDisconnect = callback; }
    void SetOnClientMessage(ClientMessageCallback callback) { m_onClientMessage = callback; }
    void SetOnError(ErrorCallback callback) { m_onError = callback; }
    
    // 事件处理（旧版兼容）
    using EventCallback = std::function<void(const NetworkEvent&)>;
    void SetEventCallback(EventCallback callback) { m_eventCallback = callback; }
    
    // 包处理器
    void SetPacketHandler(std::shared_ptr<PacketHandler> handler) { m_packetHandler = handler; }
    
    // 统计信息
    struct Statistics {
        uint64_t totalBytesReceived = 0;
        uint64_t totalBytesSent = 0;
        uint64_t totalPacketsReceived = 0;
        uint64_t totalPacketsSent = 0;
        uint32_t currentConnections = 0;
        uint32_t totalConnections = 0;
        uint32_t rejectedConnections = 0;
        uint32_t timedOutConnections = 0;
        uint32_t errorCount = 0;
    };
    Statistics GetStatistics() const;
    void ResetStatistics();

    // 客户端连接功能（用于连接到其他服务器）
    std::shared_ptr<ClientConnection> ConnectToServer(const std::string& address, uint16_t port);
    bool IsConnectedToServer() const;
    std::shared_ptr<ClientConnection> GetServerConnection() const { return m_serverConnection; }
    void DisconnectFromServer();

private:
    // 线程函数
    void AcceptThread();
    void IOThread();
    void ProcessEvents();
    void MaintenanceThread();
    
    // 辅助函数
    bool SetSocketNonBlocking(SOCKET_TYPE socket);
    bool SetSocketOptions(SOCKET_TYPE socket);
    void CloseSocket(SOCKET_TYPE socket);
    uint32_t GenerateClientId();
    std::string GetIPFromSocket(SOCKET_TYPE socket);
    
    // 事件队列操作
    void PushEvent(const NetworkEvent& event);
    bool PopEvent(NetworkEvent& event);
    
    // 内部处理
    void HandleClientDisconnect(std::shared_ptr<ClientConnection> client, const std::string& reason);
    void ProcessClientData(std::shared_ptr<ClientConnection> client);
    void CheckClientTimeouts();
    void UpdateStatistics();
    
private:
    // 服务器套接字
    SOCKET_TYPE m_serverSocket = INVALID_SOCKET;
    std::string m_serverAddress = "0.0.0.0";
    uint16_t m_serverPort = 0;
    int m_maxConnections = 1000;
    int m_maxConnectionsPerIP = 50;
    
    // 缓冲区配置
    size_t m_sendBufferSize = 65536;
    size_t m_receiveBufferSize = 65536;
    
    // 超时配置
    uint32_t m_clientTimeoutMs = 60000;
    uint32_t m_keepAliveIntervalMs = 30000;
    bool m_enableKeepAlive = true;
    bool m_enableNagle = false;
    
    // 运行状态
    std::atomic<bool> m_isRunning{false};
    std::atomic<bool> m_shouldStop{false};
    
    // 线程
    std::thread m_acceptThread;
    std::thread m_ioThread;
    std::thread m_eventThread;
    std::thread m_maintenanceThread;
    
    // 客户端管理
    std::unordered_map<uint32_t, std::shared_ptr<ClientConnection>> m_clients;
    mutable std::mutex m_clientsMutex;
    std::atomic<uint32_t> m_nextClientId{1};
    
    // IP过滤
    std::unordered_map<std::string, IPFilterInfo> m_blockedIPs;
    std::unordered_map<std::string, IPFilterInfo> m_tempBlockedIPs;
    std::unordered_map<std::string, std::vector<uint32_t>> m_ipConnections;  // IP -> 客户端ID列表
    std::unordered_map<std::string, IPFilterInfo> m_attackIPs;
    mutable std::mutex m_ipFilterMutex;
    
    // 事件队列
    std::queue<NetworkEvent> m_eventQueue;
    std::mutex m_eventMutex;
    std::condition_variable m_eventCV;
    
    // 回调函数
    ClientConnectedCallback m_onClientConnect;
    ClientDisconnectedCallback m_onClientDisconnect;
    ClientMessageCallback m_onClientMessage;
    ErrorCallback m_onError;
    EventCallback m_eventCallback;
    std::shared_ptr<PacketHandler> m_packetHandler;
    
    // 统计信息
    mutable std::mutex m_statsMutex;
    Statistics m_stats;
    
    // 服务器连接
    std::shared_ptr<ClientConnection> m_serverConnection;
    
#ifdef _WIN32
    WSADATA m_wsaData;
#endif
};

// 客户端连接类
class ClientConnection {
public:
    ClientConnection(uint32_t id, SOCKET_TYPE socket, const std::string& remoteIP, uint16_t remotePort);
    ~ClientConnection();

    // 基本信息
    uint32_t GetId() const { return m_id; }
    SOCKET_TYPE GetSocket() const { return m_socket; }
    std::string GetRemoteIP() const { return m_remoteIP; }
    uint16_t GetRemotePort() const { return m_remotePort; }
    bool IsConnected() const { return m_connected; }
    
    // 数据操作
    bool Send(const void* data, size_t size);
    bool SendText(const std::string& text);
    bool Receive();
    bool HasCompletePacket() const;
    bool GetPacket(std::vector<uint8_t>& packet);
    void ClearReceiveBuffer();
    void ClearSendBuffer();
    
    // 连接管理
    void Disconnect();
    void SetUserData(void* data) { m_userData = data; }
    void* GetUserData() const { return m_userData; }
    
    // 会话信息
    SessionInfo& GetSessionInfo() { return m_sessionInfo; }
    const SessionInfo& GetSessionInfo() const { return m_sessionInfo; }
    
    // 统计和状态
    uint64_t GetBytesReceived() const { return m_bytesReceived; }
    uint64_t GetBytesSent() const { return m_bytesSent; }
    uint32_t GetLastActiveTime() const { return m_lastActiveTime; }
    void UpdateLastActiveTime();
    bool IsTimedOut(uint32_t timeoutMs) const;
    
    // 发送缓冲区管理
    bool IsSendBufferFull() const;
    size_t GetSendBufferSize() const;
    bool FlushSendBuffer();
    void SetSendBufferLimit(size_t limit) { m_sendBufferLimit = limit; }
    
    // 接收缓冲区管理
    size_t GetReceiveBufferSize() const { return m_recvSize; }
    void SetReceiveBufferLimit(size_t limit) { m_receiveBufferLimit = limit; }

private:
    // 数据处理
    void ProcessReceivedData();
    bool ParsePacket();
    
private:
    uint32_t m_id;
    SOCKET_TYPE m_socket;
    std::string m_remoteIP;
    uint16_t m_remotePort;
    std::atomic<bool> m_connected{true};
    
    // 接收缓冲区
    static constexpr size_t DEFAULT_RECV_BUFFER_SIZE = 65536;
    std::vector<uint8_t> m_recvBuffer;
    size_t m_recvSize = 0;
    size_t m_receiveBufferLimit = DEFAULT_RECV_BUFFER_SIZE;
    
    // 发送缓冲区
    std::vector<uint8_t> m_sendBuffer;
    size_t m_sendBufferLimit = 65536;
    bool m_sendPending = false;
    
    // 数据包队列
    std::queue<std::vector<uint8_t>> m_packetQueue;
    mutable std::mutex m_packetMutex;
    mutable std::mutex m_sendMutex;
    
    // 用户数据和会话信息
    void* m_userData = nullptr;
    SessionInfo m_sessionInfo;
    
    // 统计信息
    std::atomic<uint64_t> m_bytesReceived{0};
    std::atomic<uint64_t> m_bytesSent{0};
    std::atomic<uint32_t> m_lastActiveTime{0};
};

// 包处理器接口
class PacketHandler {
public:
    virtual ~PacketHandler() = default;
    virtual void OnPacketReceived(std::shared_ptr<ClientConnection> client, const std::vector<uint8_t>& packet) = 0;
    virtual void OnClientConnected(std::shared_ptr<ClientConnection> client) {}
    virtual void OnClientDisconnected(std::shared_ptr<ClientConnection> client) {}
    virtual void OnError(const std::string& error) {}
};

} // namespace Network
} // namespace MirServer 