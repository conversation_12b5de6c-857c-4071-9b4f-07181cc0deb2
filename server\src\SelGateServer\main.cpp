#include "SelGateServer.h"
#include "../Common/Logger.h"
#include <iostream>
#include <csignal>
#include <string>
#include <thread>
#include <chrono>

using namespace MirServer;
using MirServer::LogLevel;

static SelGateServer* g_selGateServer = nullptr;

void SignalHandler(int signal) {
    std::cout << "Received signal " << signal << ", shutting down..." << std::endl;
    if (g_selGateServer) {
        g_selGateServer->Stop();
    }
}

void ShowUsage(const char* programName) {
    std::cout << "Usage: " << programName << " [options]" << std::endl;
    std::cout << "Options:" << std::endl;
    std::cout << "  -c <config>   Specify config file (default: SelGate.ini)" << std::endl;
    std::cout << "  -h            Show this help message" << std::endl;
    std::cout << "  -v            Show version information" << std::endl;
}

void ShowVersion() {
    std::cout << "SelGateServer v1.0.0" << std::endl;
    std::cout << "Legend of Mir Server Selection Gateway" << std::endl;
    std::cout << "Based on original Delphi ESelGate implementation" << std::endl;
}

int main(int argc, char* argv[]) {
    std::string configFile = "SelGate.ini";
    
    // 解析命令行参数
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            ShowUsage(argv[0]);
            return 0;
        } else if (arg == "-v" || arg == "--version") {
            ShowVersion();
            return 0;
        } else if (arg == "-c" || arg == "--config") {
            if (i + 1 < argc) {
                configFile = argv[++i];
            } else {
                std::cerr << "Error: -c option requires a config file argument" << std::endl;
                return 1;
            }
        } else {
            std::cerr << "Unknown option: " << arg << std::endl;
            ShowUsage(argv[0]);
            return 1;
        }
    }
    
    // 初始化日志系统
    Logger::SetLogFile("SelGateServer.log");
    Logger::SetLogLevel(LogLevel::LOG_DEBUG);
    Logger::EnableConsoleOutput(true);
    Logger::EnableFileOutput(true);
    
    std::cout << "========================================" << std::endl;
    std::cout << "    Legend of Mir - SelGateServer      " << std::endl;
    std::cout << "      Selection Gateway Server         " << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << "Config file: " << configFile << std::endl;
    std::cout << std::endl;
    
    // 注册信号处理
    std::signal(SIGINT, SignalHandler);
    std::signal(SIGTERM, SignalHandler);
#ifdef _WIN32
    std::signal(SIGBREAK, SignalHandler);
#endif
    
    try {
        // 创建SelGateServer实例
        g_selGateServer = new SelGateServer();
        
        // 初始化服务器
        if (!g_selGateServer->Initialize(configFile)) {
            std::cerr << "Failed to initialize SelGateServer" << std::endl;
            delete g_selGateServer;
            return 1;
        }
        
        // 启动服务器
        if (!g_selGateServer->Start()) {
            std::cerr << "Failed to start SelGateServer" << std::endl;
            delete g_selGateServer;
            return 1;
        }
        
        std::cout << std::endl;
        std::cout << "SelGateServer is running..." << std::endl;
        std::cout << "Press Ctrl+C to stop the server" << std::endl;
        std::cout << std::endl;
        
        // 主循环
        while (g_selGateServer->IsRunning()) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            
            // 可以在这里添加一些周期性的状态输出或检查
            static int statusCounter = 0;
            if (++statusCounter >= 300) { // 每5分钟输出一次状态
                statusCounter = 0;
                
                std::cout << "=== Server Status ===" << std::endl;
                std::cout << "Sessions: " << g_selGateServer->GetSessionCount() << std::endl;
                std::cout << "Active RunGates: " << g_selGateServer->GetActiveRunGateCount() << std::endl;
                std::cout << "Total Connections: " << g_selGateServer->GetTotalConnections() << std::endl;
                std::cout << "===================" << std::endl;
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Exception in main: " << e.what() << std::endl;
        Logger::Error("Exception in main: " + std::string(e.what()));
        
        if (g_selGateServer) {
            delete g_selGateServer;
        }
        return 1;
    }
    
    // 清理
    if (g_selGateServer) {
        delete g_selGateServer;
        g_selGateServer = nullptr;
    }
    
    std::cout << "SelGateServer stopped successfully." << std::endl;
    Logger::Info("SelGateServer stopped successfully");
    
    return 0;
} 