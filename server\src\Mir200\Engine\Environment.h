#pragma once

// Mir200 Environment - Game environment management
// Based on delphi/EM2Engine/Envir.pas - Following original project structure
// Phase 1 Implementation - Basic placeholder for M2Server integration

#include "Common/M2Share.h"
#include <string>

// Environment class - Game environment management
class Environment {
private:
    std::string m_map_name;
    bool m_active;
    bool m_initialized;
    
public:
    Environment(const std::string& map_name);
    ~Environment();
    
    // Core lifecycle
    bool Initialize();
    void Finalize();
    
    // Processing
    void ProcessEnvironment();
    
    // State management
    bool IsActive() const { return m_active; }
    bool IsInitialized() const { return m_initialized; }
    
    // Map management
    const std::string& GetMapName() const { return m_map_name; }
    
    // Data management
    void SaveEnvironmentData();
};
