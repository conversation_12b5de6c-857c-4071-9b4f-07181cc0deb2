#include "DialogManager.h"
#include <algorithm>
#include "ResourcePaths.h"

DialogManager::DialogManager(SDL_Renderer* renderer)
    : m_renderer(renderer)
    , m_wilManager(nullptr)
    , m_resourceFile(ResourcePaths::MAIN)
{
}

DialogManager::~DialogManager()
{
    CloseAllDialogs();
}

void DialogManager::Update(int deltaTime)
{
    // Update all dialogs
    for (auto& dialog : m_dialogs) {
        if (dialog->IsVisible()) {
            dialog->Update(deltaTime);
        }
    }

    // Remove closed dialogs
    m_dialogs.erase(
        std::remove_if(m_dialogs.begin(), m_dialogs.end(),
            [](const std::shared_ptr<Dialog>& dialog) {
                return !dialog->IsVisible();
            }),
        m_dialogs.end()
    );
}

void DialogManager::Render()
{
    // Render all dialogs
    for (auto& dialog : m_dialogs) {
        if (dialog->IsVisible()) {
            dialog->Render(m_renderer);
        }
    }
}

bool DialogManager::HandleEvent(const SDL_Event& event)
{
    // Handle events for all dialogs in reverse order (top to bottom)
    for (auto it = m_dialogs.rbegin(); it != m_dialogs.rend(); ++it) {
        if ((*it)->IsVisible()) {
            // Handle mouse motion
            if (event.type == SDL_MOUSEMOTION) {
                if ((*it)->HandleMouseMotion(event.motion.x, event.motion.y)) {
                    return true;
                }
            }
            // Handle mouse button
            else if (event.type == SDL_MOUSEBUTTONDOWN || event.type == SDL_MOUSEBUTTONUP) {
                if ((*it)->HandleMouseButton(event.button.button, event.type == SDL_MOUSEBUTTONDOWN, event.button.x, event.button.y)) {
                    return true;
                }
            }
            // Handle key
            else if (event.type == SDL_KEYDOWN || event.type == SDL_KEYUP) {
                if ((*it)->HandleKey(event.key.keysym.sym, event.type == SDL_KEYDOWN)) {
                    return true;
                }
            }
        }
    }

    return false;
}

std::shared_ptr<Dialog> DialogManager::ShowMessageDialog(const std::string& title, const std::string& message, DialogButtons buttons, std::function<void(DialogResult)> callback)
{
    // Create dialog - use 0,0 as initial position, it will be centered later
    std::shared_ptr<Dialog> dialog = std::make_shared<Dialog>(0, 0, 0, 0, title, message, buttons);

    // Set WIL manager and resource file
    // TODO: Get the actual WIL manager and resource file from the game
    if (m_wilManager) {
        dialog->SetWILManager(m_wilManager);
        dialog->SetResourceFile(m_resourceFile);
    }

    // Set dialog size to normal (matches original Delphi implementation)
    dialog->SetDialogSize(DialogSize::NORMAL);

    // Initialize dialog
    dialog->Initialize(m_renderer);

    // Set result callback
    if (callback) {
        dialog->SetOnResult(callback);
    }

    // Add to dialogs list
    m_dialogs.push_back(dialog);

    // Show dialog
    dialog->Show();

    return dialog;
}

std::shared_ptr<MessageDialog> DialogManager::ShowMessageDialog(const std::string& title, const std::string& message, MessageType messageType, DialogButtons buttons, std::function<void(DialogResult)> callback)
{
    // Create dialog - use 0,0 as initial position, it will be centered later
    std::shared_ptr<MessageDialog> dialog = std::make_shared<MessageDialog>(0, 0, 0, 0, title, message, messageType, buttons, "MessageDialog");

    // Set WIL manager and resource file
    if (m_wilManager) {
        dialog->SetWILManager(m_wilManager);
        dialog->SetResourceFile(m_resourceFile);
    }

    // Set dialog size to normal (matches original Delphi implementation)
    dialog->SetDialogSize(DialogSize::NORMAL);

    // Initialize dialog
    dialog->Initialize(m_renderer);

    // Set result callback
    if (callback) {
        dialog->SetOnResult(callback);
    }

    // Add to dialogs list
    m_dialogs.push_back(dialog);

    // Show dialog
    dialog->Show();

    return dialog;
}

std::shared_ptr<Dialog> DialogManager::ShowInputDialog(const std::string& title, const std::string& message, const std::string& defaultText, std::function<void(const std::string&)> callback)
{
    // Create dialog - use 0,0 as initial position, it will be centered later
    std::shared_ptr<Dialog> dialog = std::make_shared<Dialog>(0, 0, 0, 0, title, message, DialogButtons::OK_CANCEL);

    // Set WIL manager and resource file
    if (m_wilManager) {
        dialog->SetWILManager(m_wilManager);
        dialog->SetResourceFile(m_resourceFile);
    }

    // Set dialog size to normal (matches original Delphi implementation)
    dialog->SetDialogSize(DialogSize::NORMAL);

    // Initialize dialog
    dialog->Initialize(m_renderer);

    // TODO: Add text input control

    // Set result callback
    if (callback) {
        dialog->SetOnResult([callback, defaultText](DialogResult result) {
            if (result == DialogResult::OK) {
                // TODO: Get text from input control
                callback(defaultText);
            }
        });
    }

    // Add to dialogs list
    m_dialogs.push_back(dialog);

    // Show dialog
    dialog->Show();

    return dialog;
}

void DialogManager::CloseAllDialogs()
{
    // Close all dialogs
    for (auto& dialog : m_dialogs) {
        dialog->Hide();
    }

    // Clear dialogs list
    m_dialogs.clear();
}
