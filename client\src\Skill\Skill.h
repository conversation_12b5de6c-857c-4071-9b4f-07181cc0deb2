#pragma once

#include <string>
#include <vector>
#include <memory>

// 技能效果类型
enum class EffectType {
    NONE = 0,
    FIRE = 1,        // 火系效果
    ICE = 2,         // 冰系效果
    LIGHTNING = 3,   // 雷系效果
    WIND = 4,        // 风系效果
    HOLY = 5,        // 神圣效果
    DARK = 6,        // 黑暗效果
    PHYSICAL = 7,    // 物理效果
    HEAL = 8,        // 治疗效果
    BUFF = 9,        // 增益效果
    DEBUFF = 10,     // 减益效果
    SUMMON = 11,     // 召唤效果
    TELEPORT = 12,   // 传送效果
    SPECIAL = 13     // 特殊效果
};

// 技能攻击范围类型
enum class AttackRange {
    SINGLE = 0,      // 单体目标
    AREA = 1,        // 范围目标
    SCREEN = 2       // 全屏目标
};

// 技能施放条件
enum class CastCondition {
    MANA = 0,        // 需要魔法
    SCROLL = 1,      // 需要符咒
    POTION = 2       // 需要药水
};

// 职业类型
enum class JobClass {
    ALL = 0,         // 所有职业
    WARRIOR = 1,     // 战士
    WIZARD = 2,      // 法师
    TAOIST = 3,      // 道士
    ASSASSIN = 4     // 刺客
};

/**
 * @class Skill
 * @brief 表示游戏中的一个技能
 */
class Skill {
private:
    int m_id;                    // 技能ID
    std::string m_name;          // 技能名称
    EffectType m_effectType;     // 效果类型
    int m_effectValue;           // 效果值
    int m_manaCost;              // 魔法消耗
    int m_power;                 // 威力
    int m_maxPower;              // 最大威力
    int m_defaultManaCost;       // 默认魔法消耗
    int m_defaultPower;          // 默认威力
    int m_defaultMaxPower;       // 默认最大威力
    JobClass m_jobClass;         // 职业要求
    int m_level1Requirement;     // 1级要求
    int m_level1TrainingPoints;  // 1级训练点数
    int m_level2Requirement;     // 2级要求
    int m_level2TrainingPoints;  // 2级训练点数
    int m_level3Requirement;     // 3级要求
    int m_level3TrainingPoints;  // 3级训练点数
    int m_cooldown;              // 冷却时间(毫秒)
    std::string m_description;   // 技能描述
    AttackRange m_attackRange;   // 攻击范围
    CastCondition m_castCondition; // 施放条件
    int m_level;                 // 当前等级
    int m_experience;            // 当前经验值
    int m_trainingPoints;        // 当前训练点数
    int m_lastCastTime;          // 上次施放时间
    int m_key;                   // 快捷键绑定

public:
    /**
     * @brief 构造函数
     * @param id 技能ID
     * @param name 技能名称
     * @param effectType 效果类型
     * @param effectValue 效果值
     * @param manaCost 魔法消耗
     * @param power 威力
     * @param maxPower 最大威力
     * @param jobClass 职业要求
     * @param cooldown 冷却时间(毫秒)
     */
    Skill(int id, const std::string& name, EffectType effectType, int effectValue,
          int manaCost, int power, int maxPower, JobClass jobClass, int cooldown);

    /**
     * @brief 获取技能ID
     * @return 技能ID
     */
    int GetId() const { return m_id; }

    /**
     * @brief 获取技能名称
     * @return 技能名称
     */
    const std::string& GetName() const { return m_name; }

    /**
     * @brief 获取效果类型
     * @return 效果类型
     */
    EffectType GetEffectType() const { return m_effectType; }

    /**
     * @brief 获取效果类型的整数值
     * @return 效果类型的整数值
     */
    int GetEffectTypeValue() const { return static_cast<int>(m_effectType); }

    /**
     * @brief 设置效果类型
     * @param effectType 效果类型
     */
    void SetEffectType(EffectType effectType) { m_effectType = effectType; }

    /**
     * @brief 获取效果值
     * @return 效果值
     */
    int GetEffectValue() const { return m_effectValue; }

    /**
     * @brief 获取魔法消耗
     * @return 魔法消耗
     */
    int GetManaCost() const { return m_manaCost; }

    /**
     * @brief 获取威力
     * @return 威力
     */
    int GetPower() const { return m_power; }

    /**
     * @brief 获取最大威力
     * @return 最大威力
     */
    int GetMaxPower() const { return m_maxPower; }

    /**
     * @brief 获取职业要求
     * @return 职业要求
     */
    JobClass GetJobClass() const { return m_jobClass; }

    /**
     * @brief 获取冷却时间
     * @return 冷却时间(毫秒)
     */
    int GetCooldown() const { return m_cooldown; }

    /**
     * @brief 获取技能描述
     * @return 技能描述
     */
    const std::string& GetDescription() const { return m_description; }

    /**
     * @brief 设置技能描述
     * @param description 技能描述
     */
    void SetDescription(const std::string& description) { m_description = description; }

    /**
     * @brief 获取当前等级
     * @return 当前等级
     */
    int GetLevel() const { return m_level; }

    /**
     * @brief 设置当前等级
     * @param level 等级
     */
    void SetLevel(int level) { m_level = level; }

    /**
     * @brief 获取当前经验值
     * @return 当前经验值
     */
    int GetExperience() const { return m_experience; }

    /**
     * @brief 设置当前经验值
     * @param experience 经验值
     */
    void SetExperience(int experience) { m_experience = experience; }

    /**
     * @brief 获取攻击范围
     * @return 攻击范围
     */
    AttackRange GetAttackRange() const { return m_attackRange; }

    /**
     * @brief 设置攻击范围
     * @param range 攻击范围
     */
    void SetAttackRange(AttackRange range) { m_attackRange = range; }

    /**
     * @brief 获取施放条件
     * @return 施放条件
     */
    CastCondition GetCastCondition() const { return m_castCondition; }

    /**
     * @brief 设置施放条件
     * @param condition 施放条件
     */
    void SetCastCondition(CastCondition condition) { m_castCondition = condition; }

    /**
     * @brief 获取快捷键
     * @return 快捷键
     */
    int GetKey() const { return m_key; }

    /**
     * @brief 设置快捷键
     * @param key 快捷键
     */
    void SetKey(int key) { m_key = key; }

    /**
     * @brief 获取1级要求
     * @return 1级要求
     */
    int GetLevel1Requirement() const { return m_level1Requirement; }

    /**
     * @brief 设置1级要求
     * @param requirement 1级要求
     */
    void SetLevel1Requirement(int requirement) { m_level1Requirement = requirement; }

    /**
     * @brief 获取2级要求
     * @return 2级要求
     */
    int GetLevel2Requirement() const { return m_level2Requirement; }

    /**
     * @brief 设置2级要求
     * @param requirement 2级要求
     */
    void SetLevel2Requirement(int requirement) { m_level2Requirement = requirement; }

    /**
     * @brief 获取3级要求
     * @return 3级要求
     */
    int GetLevel3Requirement() const { return m_level3Requirement; }

    /**
     * @brief 设置3级要求
     * @param requirement 3级要求
     */
    void SetLevel3Requirement(int requirement) { m_level3Requirement = requirement; }

    /**
     * @brief 获取1级训练点数
     * @return 1级训练点数
     */
    int GetLevel1TrainingPoints() const { return m_level1TrainingPoints; }

    /**
     * @brief 设置1级训练点数
     * @param points 1级训练点数
     */
    void SetLevel1TrainingPoints(int points) { m_level1TrainingPoints = points; }

    /**
     * @brief 获取2级训练点数
     * @return 2级训练点数
     */
    int GetLevel2TrainingPoints() const { return m_level2TrainingPoints; }

    /**
     * @brief 设置2级训练点数
     * @param points 2级训练点数
     */
    void SetLevel2TrainingPoints(int points) { m_level2TrainingPoints = points; }

    /**
     * @brief 获取3级训练点数
     * @return 3级训练点数
     */
    int GetLevel3TrainingPoints() const { return m_level3TrainingPoints; }

    /**
     * @brief 设置3级训练点数
     * @param points 3级训练点数
     */
    void SetLevel3TrainingPoints(int points) { m_level3TrainingPoints = points; }

    /**
     * @brief 获取当前训练点数
     * @return 当前训练点数
     */
    int GetTrainingPoints() const { return m_trainingPoints; }

    /**
     * @brief 增加训练点数
     * @param points 增加的点数
     * @return 是否升级
     */
    bool AddTrainingPoints(int points);

    /**
     * @brief 增加经验值
     * @param exp 增加的经验值
     * @return 是否升级
     */
    bool AddExperience(int exp);

    /**
     * @brief 检查是否可以升级
     * @return 是否可以升级
     */
    bool CheckLevelUp();

    /**
     * @brief 获取上次施放时间
     * @return 上次施放时间
     */
    int GetLastCastTime() const { return m_lastCastTime; }

    /**
     * @brief 检查是否可以施放
     * @param currentMana 当前魔法值
     * @param currentTime 当前时间
     * @return 是否可以施放
     */
    bool CanCast(int currentMana, int currentTime) const;

    /**
     * @brief 施放技能
     * @param currentTime 当前时间
     */
    void Cast(int currentTime);

    /**
     * @brief 计算技能伤害
     * @param casterLevel 施放者等级
     * @param casterMagicPower 施放者魔法攻击力
     * @return 技能伤害
     */
    int CalculateDamage(int casterLevel, int casterMagicPower) const;

    /**
     * @brief 计算技能效果
     * @param userLevel 使用者等级
     * @param userMagicLevel 使用者魔法等级
     * @return 技能效果值
     */
    int CalculateEffect(int userLevel, int userMagicLevel) const;
};
