#pragma once

#include "../Common/Types.h"
#include <string>
#include <vector>

namespace MirServer {
namespace Protocol {

// 网络协议类型定义（完全对应delphi原版grobal2.pas）
enum PacketType : WORD {
    // =============== 客户端到服务器的协议（CM_开头）===============

    // 基础角色管理协议
    CM_QUERYCHR = 100,            // 查询角色
    CM_NEWCHR = 101,              // 创建角色
    CM_DELCHR = 102,              // 删除角色
    CM_SELCHR = 103,              // 选择角色
    CM_SELECTSERVER = 104,        // 选择服务器

    // 数据库操作协议（DB_开头）- 严格按照原版定义
    DB_LOADHUMANRCD = 1000,       // 加载人物记录
    DB_SAVEHUMANRCD = 1010,       // 保存人物记录
    DB_SAVEHUMANRCDEX = 1020,     // 扩展保存人物记录
    // 注意：移除了原版不存在的协议 DB_QUERYCHR, DB_NEWCHR, DB_DELCHR

    // 数据库服务器响应协议（DBR_开头）- 严格按照原版定义
    DBR_LOADHUMANRCD = 1100,      // 加载人物记录响应
    DBR_SAVEHUMANRCD = 1101,      // 保存人物记录响应
    DBR_FAIL = 1102,              // 操作失败
    // 注意：移除了原版不存在的协议 DBR_QUERYCHR, DBR_NEWCHR, DBR_DELCHR

    // 游戏内基础操作协议
    CM_DROPITEM = 1000,           // 扔物品
    CM_PICKUP = 1001,             // 拾取物品
    CM_OPENDOOR = 1002,           // 开门操作
    CM_TAKEONITEM = 1003,         // 佩戴物品
    CM_TAKEOFFITEM = 1004,        // 卸下物品
    CM_1005 = 1005,               // 原版协议
    CM_EAT = 1006,                // 吃东西
    CM_BUTCH = 1007,              // 挖肉
    CM_MAGICKEYCHANGE = 1008,     // 改变魔法快捷键
    CM_SOFTCLOSE = 1009,          // 软关闭

    // NPC交互协议
    CM_CLICKNPC = 1010,           // 点击NPC
    CM_MERCHANTDLGSELECT = 1011,  // 商人对话选择
    CM_MERCHANTQUERYSELLPRICE = 1012, // 商人查询卖价
    CM_USERSELLITEM = 1013,       // 用户卖物品
    CM_USERBUYITEM = 1014,        // 用户买物品
    CM_USERGETDETAILITEM = 1015,  // 用户获取物品详情
    CM_DROPGOLD = 1016,           // 扔金币
    CM_1017 = 1017,               // 原版协议
    CM_LOGINNOTICEOK = 1018,      // 登录游戏公告确认按钮

    // 组队系统协议
    CM_GROUPMODE = 1019,          // 组队模式
    CM_CREATEGROUP = 1020,        // 创建组队
    CM_ADDGROUPMEMBER = 1021,     // 添加组队成员
    CM_DELGROUPMEMBER = 1022,     // 删除组队成员
    CM_USERREPAIRITEM = 1023,     // 修理物品
    CM_MERCHANTQUERYREPAIRCOST = 1024, // 查询修理价格

    // 交易系统协议
    CM_DEALTRY = 1025,            // 交易开始
    CM_DEALADDITEM = 1026,        // 交易添加物品
    CM_DEALDELITEM = 1027,        // 交易删除物品
    CM_DEALCANCEL = 1028,         // 交易取消
    CM_DEALCHGGOLD = 1029,        // 交易改变金币
    CM_DEALEND = 1030,            // 交易结束

    // 仓库系统协议
    CM_USERSTORAGEITEM = 1031,    // 用户存储物品
    CM_USERTAKEBACKSTORAGEITEM = 1032, // 从仓库取回物品
    CM_WANTMINIMAP = 1033,        // 请求小地图
    CM_USERMAKEDRUGITEM = 1034,   // 制造药品物品

    // 行会系统协议 - 恢复原版编号
    CM_OPENGUILDDLG = 1035,       // 打开行会对话框
    CM_GUILDHOME = 1036,          // 行会主页
    CM_GUILDMEMBERLIST = 1037,    // 行会成员列表
    CM_GUILDADDMEMBER = 1038,     // 添加行会成员
    CM_GUILDDELMEMBER = 1039,     // 删除行会成员
    CM_GUILDUPDATENOTICE = 1040,  // 更新行会公告
    CM_GUILDUPDATERANKINFO = 1041, // 更新行会职位信息
    CM_1042 = 1042,               // 原版协议
    CM_ADJUST_BONUS = 1043,       // 调整属性点
    CM_GUILDALLY = 1044,          // 行会结盟
    CM_GUILDBREAKALLY = 1045,     // 行会解除联盟

    // 补充遗漏的行会协议（基于原版存在的协议）
    CM_GUILDMAKEALLY = 1044,      // 行会结盟（别名）
    // 注意：移除了不存在的协议 CM_GUILDWAR, CM_GUILDPEACE, CM_GUILDMSG

    // 基础查询协议
    CM_QUERYUSERNAME = 80,        // 查询用户名称
    CM_QUERYBAGITEMS = 81,        // 查询背包物品
    CM_QUERYUSERSTATE = 82,       // 查询用户状态
    CM_QUERYUSERSET = 49999,      // 查询用户设置（原版协议）

    // 密码相关协议
    CM_PASSWORD = 1105,           // 密码
    CM_CHGPASSWORD = 1221,        // 修改密码
    CM_SETPASSWORD = 1222,        // 设置密码

    // 战斗动作协议 - 严格按照原版编号
    CM_THROW = 3005,              // 投掷物品
    CM_HORSERUN = 3009,           // 骑马跑
    CM_TURN = 3010,               // 转向
    CM_WALK = 3011,               // 行走
    CM_SITDOWN = 3012,            // 坐下
    CM_RUN = 3013,                // 跑步
    CM_HIT = 3014,                // 攻击
    CM_HEAVYHIT = 3015,           // 重击
    CM_BIGHIT = 3016,             // 大击
    CM_SPELL = 3017,              // 魔法
    CM_POWERHIT = 3018,           // 烈火剑法
    CM_LONGHIT = 3019,            // 刺杀剑术
    CM_WIDEHIT = 3024,            // 半月弯刀
    CM_FIREHIT = 3025,            // 烈火剑法
    CM_40HIT = 3026,              // 原版协议
    CM_41HIT = 3027,              // 原版协议
    CM_43HIT = 3028,              // 原版协议
    CM_42HIT = 3029,              // 原版协议
    CM_SAY = 3030,                // 说话
    CM_CRSHIT = 3036,             // 连击
    CM_3037 = 3037,               // 原版协议
    CM_TWINHIT = 3038,            // 双击

    // 登录认证协议
    CM_PROTOCOL = 2000,           // 协议版本检查
    CM_IDPASSWORD = 2001,         // 登录密码
    CM_ADDNEWUSER = 2002,         // 添加新用户
    CM_CHANGEPASSWORD = 2003,     // 修改密码
    CM_UPDATEUSER = 2004,         // 更新用户信息
    CM_GETBACKPASSWORD = 2010,    // 找回密码

    // 缺失的客户端协议
    CM_USEBAGITEM = 1005,         // 使用背包物品（等同于CM_1005）
    CM_WHISPER = 3031,            // 悄悄话
    CM_GM = 3032,                 // GM命令
    CM_LOGINOUT = 3040,           // 登出
    CM_LOGINGAME = 3041,          // 登录游戏
    CM_ALIVE = 3050,              // 复活
    CM_RUSH = 3051,               // 冲锋
    CM_RUSHKUNG = 3052,           // 野蛮冲撞
    CM_BACKSTEP = 3053,           // 后退步法
    CM_DIGUP = 3054,              // 挖取
    CM_DIGDOWN = 3055,            // 挖掘
    CM_FLYAXE = 3056,             // 飞斧
    CM_LIGHTING = 3057,           // 雷电
    CM_SUBABILITY = 3058,         // 子属性
    CM_RIDE = 3059,               // 骑马

    // =============== 服务器到客户端的协议（SM_开头）===============

    // 基础响应协议
    SM_OUTOFCONNECTION = 1,       // 连接断开
    SM_PASSOK_SELECTSERVER = 3,   // 密码正确，选择服务器
    SM_SELECTSERVER_OK = 530,     // 选择服务器成功

    // 基础动作响应协议 - 严格按照原版编号
    SM_HORSERUN = 5,              // 骑马跑
    SM_RUSH = 6,                  // 冲锋
    SM_RUSHKUNG = 7,              // 野蛮冲撞
    SM_FIREHIT = 8,               // 烈火剑法
    SM_BACKSTEP = 9,              // 后退步法
    SM_TURN = 10,                 // 转向
    SM_WALK = 11,                 // 行走
    SM_SITDOWN = 12,              // 坐下
    SM_RUN = 13,                  // 跑步
    SM_HIT = 14,                  // 攻击
    SM_HEAVYHIT = 15,             // 重击
    SM_BIGHIT = 16,               // 大击
    SM_SPELL = 17,                // 魔法
    SM_POWERHIT = 18,             // 烈火剑法
    SM_LONGHIT = 19,              // 刺杀剑术
    SM_DIGUP = 20,                // 挖取
    SM_DIGDOWN = 21,              // 挖掘
    SM_FLYAXE = 22,               // 飞斧
    SM_LIGHTING = 23,             // 雷电
    SM_WIDEHIT = 24,              // 半月弯刀
    SM_CRSHIT = 25,               // 连击
    SM_TWINHIT = 26,              // 双击

    // 基础状态响应
    SM_ALIVE = 27,                // 复活
    SM_MOVEFAIL = 28,             // 移动失败
    SM_HIDE = 29,                 // 隐身
    SM_DISAPPEAR = 30,            // 消失
    SM_STRUCK = 31,               // 受到攻击
    SM_DEATH = 32,                // 死亡
    SM_SKELETON = 33,             // 骨架
    SM_NOWDEATH = 34,             // 当前死亡

    // 状态信息响应
    SM_HEAR = 40,                 // 听到消息
    SM_FEATURECHANGED = 41,       // 特征改变
    SM_USERNAME = 42,             // 用户名
    SM_WINEXP = 44,               // 获得经验
    SM_LEVELUP = 45,              // 升级
    SM_DAYCHANGING = 46,          // 昼夜变化

    // 游戏状态响应
    SM_LOGON = 50,                // 登录
    SM_NEWMAP = 51,               // 新地图
    SM_ABILITY = 52,              // 能力值
    SM_HEALTHSPELLCHANGED = 53,   // 生命魔法值改变
    SM_MAPDESCRIPTION = 54,       // 地图描述
    SM_GAMEGOLDNAME = 55,         // 游戏金币名称

    // 聊天消息响应
    SM_SYSMESSAGE = 100,          // 系统消息
    SM_GROUPMESSAGE = 101,        // 组队消息
    SM_CRY = 102,                 // 大喊
    SM_WHISPER = 103,             // 悄悄话
    SM_GUILDMESSAGE = 104,        // 行会消息

    // 物品和背包管理响应
    SM_ADDITEM = 200,             // 添加物品
    SM_BAGITEMS = 201,            // 背包物品
    SM_DELITEM = 202,             // 删除物品
    SM_UPDATEITEM = 203,          // 更新物品
    SM_ADDMAGIC = 210,            // 添加魔法
    SM_SENDMYMAGIC = 211,         // 发送我的魔法
    SM_DELMAGIC = 212,            // 删除魔法

    // 登录相关响应
    SM_CERTIFICATION_FAIL = 501,  // 认证失败
    SM_ID_NOTFOUND = 502,         // ID未找到
    SM_CERTIFICATION_SUCCESS = 502,  // 认证成功（注意：与ID_NOTFOUND相同）
    SM_PASSWD_FAIL = 503,         // 密码错误
    SM_NEWID_SUCCESS = 504,       // 新ID成功
    SM_NEWID_FAIL = 505,          // 新ID失败
    SM_CHGPASSWD_SUCCESS = 506,   // 修改密码成功
    SM_CHGPASSWD_FAIL = 507,      // 修改密码失败
    SM_GETBACKPASSWD_SUCCESS = 508, // 找回密码成功
    SM_GETBACKPASSWD_FAIL = 509,  // 找回密码失败

    // 角色管理响应
    SM_QUERYCHR = 520,            // 查询角色响应
    SM_NEWCHR_SUCCESS = 521,      // 创建角色成功
    SM_NEWCHR_FAIL = 522,         // 创建角色失败
    SM_DELCHR_SUCCESS = 523,      // 删除角色成功
    SM_DELCHR_FAIL = 524,         // 删除角色失败
    SM_STARTPLAY = 525,           // 开始游戏
    SM_STARTFAIL = 526,           // 开始游戏失败
    SM_QUERYCHR_FAIL = 527,       // 查询角色失败
    SM_OUTOFCONNECTION2 = 528,    // 连接断开2

    // 游戏内状态响应
    SM_MAPCHANGED = 601,          // 地图改变
    SM_LOGON2 = 602,              // 登录
    SM_MAPDESCRIPTION2 = 603,     // 地图描述
    SM_ABILITY2 = 604,            // 能力值
    SM_HEALTHSPELLCHANGED2 = 605, // 生命魔法值改变
    SM_DAYCHANGING2 = 614,        // 昼夜变化
    SM_LOGINSTATUS = 615,         // 登录状态
    SM_NEWMAP2 = 616,             // 新地图
    SM_RECONNECT = 617,           // 重连
    SM_GHOST = 618,               // 幽灵状态
    SM_SHOWEVENT = 619,           // 显示事件
    SM_HIDEEVENT = 620,           // 隐藏事件
    SM_SPACEMOVE_HIDE = 621,      // 隐身移动
    SM_SPACEMOVE_SHOW = 622,      // 显示移动
    SM_RECONNECT2 = 623,          // 重连2
    SM_MOVEMODE_CHANGED = 624,    // 移动模式改变

    // 其他重要协议（根据原版Grobal2.pas补充）
    SM_DROPITEM_SUCCESS = 600,    // 扔物品成功
    SM_DROPITEM_FAIL = 601,       // 扔物品失败
    SM_ITEMSHOW = 610,            // 物品显示
    SM_ITEMHIDE = 611,            // 物品隐藏
    SM_OPENDOOR_OK = 612,         // 开门成功
    SM_OPENDOOR_LOCK = 613,       // 门被锁
    SM_CLOSEDOOR = 614,           // 关门
    SM_TAKEON_OK = 615,           // 穿戴成功
    SM_TAKEON_FAIL = 616,         // 穿戴失败
    SM_TAKEOFF_OK = 619,          // 卸下成功
    SM_TAKEOFF_FAIL = 620,        // 卸下失败
    SM_SENDUSEITEMS = 621,        // 发送使用物品
    SM_WEIGHTCHANGED = 622,       // 重量改变
    SM_CLEAROBJECTS = 633,        // 清除对象
    SM_CHANGEMAP = 634,           // 改变地图
    SM_EAT_OK = 635,              // 使用物品成功
    SM_EAT_FAIL = 636,            // 使用物品失败
    SM_BUTCH = 637,               // 挖肉
    SM_MAGICFIRE = 638,           // 魔法火焰
    SM_MAGICFIRE_FAIL = 639,      // 魔法火焰失败
    SM_MAGIC_LVEXP = 640,         // 魔法等级经验
    SM_DURACHANGE = 642,          // 持久度改变
    SM_MERCHANTSAY = 643,         // 商人说话
    SM_MERCHANTDLGCLOSE = 644,    // 商人对话关闭
    SM_SENDGOODSLIST = 645,       // 发送商品列表
    SM_SENDUSERSELL = 646,        // 发送用户出售
    SM_SENDBUYPRICE = 647,        // 发送购买价格
    SM_USERSELLITEM_OK = 648,     // 用户卖物品成功
    SM_USERSELLITEM_FAIL = 649,   // 用户卖物品失败
    SM_BUYITEM_SUCCESS = 650,     // 购买物品成功
    SM_BUYITEM_FAIL = 651,        // 购买物品失败
    SM_SENDDETAILGOODSLIST = 652, // 发送详细商品列表
    SM_GOLDCHANGED = 653,         // 金币改变
    SM_CHANGELIGHT = 654,         // 改变光源
    SM_LAMPCHANGEDURA = 655,      // 灯具耐久改变
    SM_CHANGENAMECOLOR = 656,     // 改变名称颜色
    SM_CHARSTATUSCHANGED = 657,   // 角色状态改变
    SM_SENDNOTICE = 658,          // 发送公告
    SM_GROUPMODECHANGED = 659,    // 组队模式改变
    SM_CREATEGROUP_OK = 660,      // 创建组队成功
    SM_CREATEGROUP_FAIL = 661,    // 创建组队失败
    SM_GROUPADDMEM_OK = 662,      // 组队添加成员成功
    SM_GROUPDELMEM_OK = 663,      // 组队删除成员成功
    SM_GROUPADDMEM_FAIL = 664,    // 组队添加成员失败
    SM_GROUPDELMEM_FAIL = 665,    // 组队删除成员失败
    SM_GROUPCANCEL = 666,         // 组队取消
    SM_GROUPMEMBERS = 667,        // 组队成员
    SM_SENDUSERREPAIR = 668,      // 发送用户修理
    SM_USERREPAIRITEM_OK = 669,   // 修理物品成功
    SM_USERREPAIRITEM_FAIL = 670, // 修理物品失败
    SM_SENDREPAIRCOST = 671,      // 发送修理费用

    // 交易系统响应
    SM_DEALMENU = 673,            // 交易菜单
    SM_DEALTRY_FAIL = 674,        // 交易开始失败
    SM_DEALADDITEM_OK = 675,      // 添加物品成功
    SM_DEALADDITEM_FAIL = 676,    // 添加物品失败
    SM_DEALDELITEM_OK = 677,      // 删除物品成功
    SM_DEALDELITEM_FAIL = 678,    // 删除物品失败
    SM_DEALCANCEL = 681,          // 交易取消
    SM_DEALREMOTEADDITEM = 682,   // 远程交易添加物品
    SM_DEALREMOTEDELITEM = 683,   // 远程交易删除物品
    SM_DEALCHGGOLD_OK = 684,      // 改变金币成功
    SM_DEALCHGGOLD_FAIL = 685,    // 改变金币失败
    SM_DEALREMOTECHGGOLD = 686,   // 远程改变金币
    SM_DEALSUCCESS = 687,         // 交易成功

    // 仓库系统响应
    SM_SENDUSERSTORAGEITEM = 700, // 发送用户仓库物品
    SM_STORAGE_OK = 701,          // 仓库操作成功
    SM_STORAGE_FULL = 702,        // 仓库已满
    SM_STORAGE_FAIL = 703,        // 仓库操作失败
    SM_SAVEITEMLIST = 704,        // 保存物品列表
    SM_TAKEBACKSTORAGEITEM_OK = 705,     // 取回物品成功
    SM_TAKEBACKSTORAGEITEM_FAIL = 706,   // 取回物品失败
    SM_TAKEBACKSTORAGEITEM_FULLBAG = 707, // 取回物品背包满
    SM_DELITEMS = 709,            // 删除物品列表（对应原版）

    // 行会系统响应
    SM_CHANGEGUILDNAME = 750,     // 改变行会名称
    SM_SENDUSERSTATE = 751,       // 发送用户状态
    SM_SUBABILITY = 752,          // 子属性
    SM_OPENGUILDDLG2 = 753,       // 打开行会对话框
    SM_OPENGUILDDLG_FAIL = 754,   // 打开行会对话框失败
    SM_SENDGUILDMEMBERLIST = 756, // 发送行会成员列表
    SM_GUILDADDMEMBER_OK = 757,   // 添加行会成员成功
    SM_GUILDADDMEMBER_FAIL = 758, // 添加行会成员失败
    SM_GUILDDELMEMBER_OK = 759,   // 删除行会成员成功
    SM_GUILDDELMEMBER_FAIL = 760, // 删除行会成员失败
    SM_GUILDRANKUPDATE_FAIL = 761, // 行会职位更新失败
    SM_BUILDGUILD_OK = 762,       // 建立行会成功
    SM_BUILDGUILD_FAIL = 763,     // 建立行会失败
    SM_DONATE_OK = 764,           // 捐献成功
    SM_DONATE_FAIL = 765,         // 捐献失败
    SM_MYSTATUS = 766,            // 我的状态
    SM_MENU_OK = 767,             // 菜单确认
    SM_GUILDMAKEALLY_OK = 768,    // 行会结盟成功
    SM_GUILDMAKEALLY_FAIL = 769,  // 行会结盟失败
    SM_GUILDBREAKALLY_OK = 770,   // 行会解盟成功
    SM_GUILDBREAKALLY_FAIL = 771, // 行会解盟失败
    SM_DLGMSG = 772,              // 对话消息

    // 特殊效果和状态
    SM_SPACEMOVE_HIDE2 = 800,     // 隐身移动2
    SM_SPACEMOVE_SHOW2 = 801,     // 显示移动2
    SM_RECONNECT3 = 802,          // 重连3
    SM_GHOST2 = 803,              // 幽灵状态2
    SM_SHOWEVENT2 = 804,          // 显示事件2
    SM_HIDEEVENT2 = 805,          // 隐藏事件2
    SM_SPACEMOVE_HIDE3 = 806,     // 隐身移动3
    SM_SPACEMOVE_SHOW3 = 807,     // 显示移动3
    SM_TIMECHECK_MSG = 810,       // 时间检查消息
    SM_ADJUST_BONUS2 = 811,       // 调整属性2

    // 高级功能
    SM_OPENHEALTH = 1100,         // 显示血量
    SM_CLOSEHEALTH = 1101,        // 关闭血量显示
    SM_BREAKWEAPON = 1102,        // 武器破碎
    SM_INSTANCEHEALGUAGE = 1103,  // 实例治疗量表
    SM_CHANGEFACE = 1104,         // 改变脸型
    SM_VERSION_FAIL = 1106,       // 版本失败
    SM_ITEMUPDATE = 1500,         // 物品更新
    SM_MONSTERSAY = 1501,         // 怪物说话

    // 特殊协议
    SM_EXCHGTAKEON_OK = 65023,    // 交换穿戴成功
    SM_EXCHGTAKEON_FAIL = 65024,  // 交换穿戴失败
    SM_TEST = 65037,              // 测试
    SM_THROW = 65069,             // 投掷
    SM_MAKEDRUG_FAIL = 65036,     // 制药失败

    // 密码和安全
    SM_PASSWORD = 3030,           // 密码
    SM_PLAYDICE = 1200,           // 玩骰子
    SM_PASSWORDSTATUS = 20001,    // 密码状态
    SM_SERVERCONFIG = 20002,      // 服务器配置
    SM_GETREGINFO = 20003,        // 获取注册信息

    // =============== 运行时消息（RM_开头，源自Grobal2.pas）==============
    RM_TURN = 10001,              // 转向
    RM_WALK = 10002,              // 行走
    RM_RUN = 10003,               // 跑步
    RM_HIT = 10004,               // 攻击
    RM_HEAVYHIT = 10005,          // 重击
    RM_BIGHIT = 10006,            // 大击
    RM_SPELL = 10007,             // 魔法
    RM_SPELL2 = 10008,            // 魔法2
    RM_POWERHIT = 10009,          // 烈火剑法
    RM_MOVEFAIL = 10010,          // 移动失败
    RM_LONGHIT = 10011,           // 刺杀剑术
    RM_WIDEHIT = 10012,           // 半月弯刀
    RM_PUSH = 10013,              // 推
    RM_FIREHIT = 10014,           // 烈火攻击
    RM_RUSH = 10015,              // 冲锋
    RM_TWINHIT = 10016,           // 双击（基于原版）
    RM_STRUCK = 10020,            // 受到攻击
    RM_DEATH = 10021,             // 死亡
    RM_DISAPPEAR = 10022,         // 消失
    RM_SKELETON = 10024,          // 骨架
    RM_MAGSTRUCK = 10025,         // 魔法攻击
    RM_MAGHEALING = 10026,        // 魔法治疗
    RM_STRUCK_MAG = 10027,        // 魔法攻击命中
    RM_MAGSTRUCK_MINE = 10028,    // 我的魔法攻击
    RM_INSTANCEHEALGUAGE = 10029, // 实例治疗量表
    RM_HEAR = 10030,              // 听到
    RM_WHISPER = 10031,           // 悄悄话
    RM_CRY = 10032,               // 大喊
    RM_RIDE = 10033,              // 骑马
    RM_USERNAME = 10043,          // 用户名
    RM_WINEXP = 10044,            // 获得经验
    RM_LEVELUP = 10045,           // 升级
    RM_CHANGENAMECOLOR = 10046,   // 改变名称颜色
    RM_LOGON = 10050,             // 登录
    RM_ABILITY = 10051,           // 能力值
    RM_HEALTHSPELLCHANGED = 10052, // 生命魔法值改变
    RM_DAYCHANGING = 10053,       // 昼夜变化
    
    // 消息系统运行时消息 (RM_10100-RM_10155)
    RM_10101 = 10101,             // 原版协议
    RM_SYSMESSAGE = 10100,        // 系统消息
    RM_GROUPMESSAGE = 10102,      // 组队消息
    RM_SYSMESSAGE2 = 10103,       // 系统消息2
    RM_GUILDMESSAGE = 10104,      // 行会消息
    RM_SYSMESSAGE3 = 10105,       // 系统消息3
    RM_ITEMSHOW = 10110,          // 物品显示
    RM_ITEMHIDE = 10111,          // 物品隐藏
    RM_DOOROPEN = 10112,          // 开门
    RM_DOORCLOSE = 10113,         // 关门
    RM_SENDUSEITEMS = 10114,      // 发送使用物品
    RM_WEIGHTCHANGED = 10115,     // 重量改变
    RM_FEATURECHANGED = 10116,    // 特征改变
    RM_CLEAROBJECTS = 10117,      // 清除对象
    RM_CHANGEMAP = 10118,         // 改变地图
    RM_BUTCH = 10119,             // 挖肉
    RM_MAGICFIRE = 10120,         // 魔法火焰
    RM_MAGICFIREFAIL = 10121,     // 魔法火焰失败
    RM_SENDMYMAGIC = 10122,       // 发送我的魔法
    RM_MAGIC_LVEXP = 10123,       // 魔法等级经验
    RM_DURACHANGE = 10125,        // 持久度改变
    RM_MERCHANTSAY = 10126,       // 商人说话
    RM_MERCHANTDLGCLOSE = 10127,  // 商人对话关闭
    RM_SENDGOODSLIST = 10128,     // 发送商品列表
    RM_SENDUSERSELL = 10129,      // 发送用户出售
    RM_SENDBUYPRICE = 10130,      // 发送购买价格
    RM_USERSELLITEM_OK = 10131,   // 用户卖物品成功
    RM_USERSELLITEM_FAIL = 10132, // 用户卖物品失败
    RM_BUYITEM_SUCCESS = 10133,   // 购买物品成功
    RM_BUYITEM_FAIL = 10134,      // 购买物品失败
    RM_SENDDETAILGOODSLIST = 10135, // 发送详细商品列表
    RM_GOLDCHANGED = 10136,       // 金币改变
    RM_CHANGELIGHT = 10137,       // 改变光源
    RM_LAMPCHANGEDURA = 10138,    // 灯具耐久改变
    RM_CHARSTATUSCHANGED = 10139, // 角色状态改变
    RM_GROUPCANCEL = 10140,       // 组队取消
    RM_SENDUSERSREPAIR = 10141,   // 发送用户修理
    RM_SENDREPAIRCOST = 10142,    // 发送修理费用
    RM_USERREPAIRITEM_OK = 10143, // 修理物品成功
    RM_USERREPAIRITEM_FAIL = 10144, // 修理物品失败
    RM_USERSTORAGEITEM = 10146,   // 用户仓库物品
    RM_USERGETBACKITEM = 10147,   // 用户取回物品
    RM_SENDDELITEMLIST = 10148,   // 发送删除物品列表
    RM_USERMAKEDRUGITEMLIST = 10149, // 用户制药物品列表
    RM_MAKEDRUG_SUCCESS = 10150,  // 制药成功
    RM_MAKEDRUG_FAIL = 10151,     // 制药失败
    RM_ALIVE = 10153,             // 复活
    RM_DELAYMAGIC = 10154,        // 延迟魔法
    RM_10155 = 10155,             // 原版协议
    
    // 特殊动作运行时消息 (RM_10200+)
    RM_10205 = 10205,             // 原版协议
    RM_DIGUP = 10200,             // 挖取
    RM_DIGDOWN = 10201,           // 挖掘
    RM_FLYAXE = 10202,            // 飞斧
    RM_LIGHTING = 10204,          // 雷电
    
    // 行会和状态运行时消息 (RM_10300+)
    RM_POISON = 10300,            // 中毒
    RM_CHANGEGUILDNAME = 10301,   // 改变行会名称
    RM_SUBABILITY = 10302,        // 子属性
    RM_BUILDGUILD_OK = 10303,     // 建立行会成功
    RM_BUILDGUILD_FAIL = 10304,   // 建立行会失败
    RM_DONATE_OK = 10305,         // 捐献成功
    RM_DONATE_FAIL = 10306,       // 捐献失败
    RM_TRANSPARENT = 10308,       // 透明
    RM_MENU_OK = 10309,           // 菜单确认
    
    // 特殊移动和效果 (RM_10330+)
    RM_SPACEMOVE_SHOW = 10331,    // 显示移动
    RM_SPACEMOVE_SHOW2 = 10332,   // 显示移动2
    RM_HIDEEVENT = 10333,         // 隐藏事件
    RM_SHOWEVENT = 10334,         // 显示事件
    RM_ZEN_BEE = 10337,           // 禅蜂
    
    // 调整和状态 (RM_10400+)
    RM_ADJUST_BONUS = 10400,      // 调整属性点
    RM_10401 = 10401,             // 原版协议
    RM_OPENHEALTH = 10410,        // 显示血量
    RM_CLOSEHEALTH = 10411,       // 关闭血量显示
    RM_DOOPENHEALTH = 10412,      // 执行显示血量
    RM_BREAKWEAPON = 10413,       // 武器破碎
    RM_10414 = 10414,             // 原版协议
    RM_CHANGEFACE = 10415,        // 改变脸型
    RM_PASSWORD = 10416,          // 密码
    
    // 游戏功能 (RM_10500+)
    RM_PLAYDICE = 10500,          // 玩骰子
    RM_DELAYPUSHED = 10555,       // 延迟推动
    RM_PASSWORDSTATUS = 10601,    // 密码状态
    RM_GAMEGOLDCHANGED = 10666,   // 游戏金币改变
    RM_MYSTATUS = 10777,          // 我的状态
    
    // 高级功能 (RM_11000+)
    RM_HORSERUN = 11000,          // 骑马跑
    RM_ITEMUPDATE = 11000,        // 物品更新（重复？）
    RM_MONSTERSAY = 11001,        // 怪物说话
    RM_MAKESLAVE = 11002,         // 制造奴隶
    RM_CRSHIT = 11014,            // 连击
    RM_RUSHKUNG = 11015,          // 野蛮冲撞
    RM_SENDUSERREPAIR = 11139,    // 发送用户修理
    RM_SPACEMOVE_FIRE2 = 11330,   // 隐身移动火焰2
    RM_SPACEMOVE_FIRE = 11331,    // 隐身移动火焰
    RM_RECONNECTION = 11332,      // 重连
    
    // 删除物品系统 (RM_9000)
    RM_DELITEMS = 9000,           // 删除物品
    
    // 怪物移动 (RM_21000+)
    RM_MONMOVE = 21004,           // 怪物移动
    
    // 大仓库系统 (RM_20000+)
    RM_USERBIGSTORAGEITEM = 20146, // 用户大仓库物品
    RM_USERBIGGETBACKITEM = 20147, // 用户大仓库取回物品
    
    // =============== 商店系统协议 ===============
    CM_OPENSHOP = 9000,           // 打开商店
    SM_SENGSHOPITEMS = 9001,      // 发送商店物品
    CM_BUYSHOPITEM = 9002,        // 购买商店物品
    SM_BUYSHOPITEM_SUCCESS = 9003, // 购买商店物品成功
    SM_BUYSHOPITEM_FAIL = 9004,   // 购买商店物品失败
    
    // =============== 状态协议 (SS_开头) ===============
    SS_200 = 200,                 // 状态协议200
    SS_201 = 201,                 // 状态协议201
    SS_202 = 202,                 // 状态协议202
    SS_WHISPER = 203,             // 悄悄话状态
    SS_204 = 204,                 // 状态协议204
    SS_205 = 205,                 // 状态协议205
    SS_206 = 206,                 // 状态协议206
    SS_207 = 207,                 // 状态协议207
    SS_208 = 208,                 // 状态协议208
    SS_209 = 219,                 // 状态协议209（注意：Delphi中是219）
    SS_210 = 210,                 // 状态协议210
    SS_211 = 211,                 // 状态协议211
    SS_212 = 212,                 // 状态协议212
    SS_213 = 213,                 // 状态协议213
    SS_214 = 214,                 // 状态协议214
    
    // =============== 寄售系统协议 ===============
    // 用户寄售物品操作
    RM_SENDUSERSELLOFFITEM_OK = 2006,     // 寄售物品成功
    RM_SENDUSERSELLOFFITEM_FAIL = 2007,   // 寄售物品失败
    SM_SENDUSERSELLOFFITEM_OK = 20006,    // 寄售物品成功
    SM_SENDUSERSELLOFFITEM_FAIL = 20007,  // 寄售物品失败
    SM_SENDUSERSELLOFFITEM = 20005,       // 发送用户寄售物品
    SM_SENDSELLOFFGOODSLIST = 20008,      // 发送寄售商品列表
    CM_SENDSELLOFFITEMLIST = 20009,       // 查询寄售物品列表
    SM_SENDBUYSELLOFFITEM_OK = 20010,     // 购买寄售物品成功
    SM_SENDBUYSELLOFFITEM_FAIL = 20011,   // 购买寄售物品失败
    
    // 客户端寄售操作
    CM_SENDSELLOFFITEM = 4004,            // 寄售物品
    CM_SENDBUYSELLOFFITEM = 4005,         // 购买寄售物品
    CM_SENDQUERYSELLOFFITEM = 4006,       // 查询寄售物品
    
    // 服务端寄售响应
    RM_SENDUSERSELLOFFITEM = 21005,       // 发送用户寄售物品
    RM_SENDSELLOFFGOODSLIST = 21008,      // 发送寄售商品列表
    RM_SENDSELLOFFITEMLIST = 22009,       // 发送寄售物品列表
    RM_SENDBUYSELLOFFITEM_OK = 21010,     // 购买寄售物品成功
    RM_SENDBUYSELLOFFITEM_FAIL = 21011,   // 购买寄售物品失败
    
    // 高级寄售操作
    RM_SENDSELLOFFITEM = 41004,           // 寄售物品响应
    RM_SENDBUYSELLOFFITEM = 41005,        // 购买寄售物品响应
    RM_SENDQUERYSELLOFFITEM = 41006,      // 查询寄售物品响应
    
    // =============== 协议别名（为了与PacketTypes.cpp兼容）===============
    SM_SAY = 39,                  // 说话回应
    SM_TAKEONITEM = 615,          // 穿戴物品（别名为SM_TAKEON_OK）
    SM_TAKEOFFITEM = 620,         // 卸下物品（别名为SM_TAKEOFF_FAIL）
    SM_MAKEDRUG_SUCCESS = 672,    // 制药成功
    SM_MERCHANTDLG = 650,         // 商人对话
    SM_STORAGEPASSWORD_OK = 701,  // 仓库密码正确（别名为SM_STORAGE_OK）
    SM_STORAGEPASSWORD_FAIL = 702, // 仓库密码错误（别名为SM_STORAGE_FULL）
    SM_READMINIMAP_OK = 690,      // 读取小地图成功
    SM_READMINIMAP_FAIL = 691,    // 读取小地图失败
    SM_RIDEHORSE = 45,            // 骑马
    SM_41 = 41,                   // 原版协议41
};

#pragma pack(push, 1)

// 数据包头结构
struct PacketHeader {
    WORD length;                  // 数据包长度
    WORD packetType;              // 数据包类型
    WORD sequence;                // 序列号（可选）

    // 默认构造函数
    PacketHeader() : length(0), packetType(0), sequence(0) {}
    
    // 带参数的构造函数
    PacketHeader(WORD len, WORD type, WORD seq = 0)
        : length(len), packetType(type), sequence(seq) {}
};

// 登录请求数据包
struct LoginRequestPacket {
    PacketHeader header;
    char account[32];             // 账号
    char password[32];            // 密码
    BYTE clientVersion;           // 客户端版本
    BYTE reserved[3];             // 保留字段

    LoginRequestPacket() {
        header.packetType = CM_IDPASSWORD;
        header.length = sizeof(LoginRequestPacket);
        memset(account, 0, sizeof(account));
        memset(password, 0, sizeof(password));
        clientVersion = 0;
        memset(reserved, 0, sizeof(reserved));
    }
};

// 角色信息数据包
struct CharacterInfoPacket {
    PacketHeader header;
    char charName[32];            // 角色名
    BYTE job;                     // 职业
    BYTE gender;                  // 性别
    BYTE level;                   // 等级
    BYTE reserved;                // 保留

    CharacterInfoPacket() {
        header.packetType = SM_QUERYCHR;
        header.length = sizeof(CharacterInfoPacket);
        memset(charName, 0, sizeof(charName));
        job = 0;
        gender = 0;
        level = 1;
        reserved = 0;
    }
};

// 移动数据包
struct MovePacket {
    PacketHeader header;
    WORD x;                       // X坐标
    WORD y;                       // Y坐标
    BYTE direction;               // 方向
    BYTE reserved[3];             // 保留

    MovePacket() {
        header.length = sizeof(MovePacket);
        x = 0;
        y = 0;
        direction = 0;
        memset(reserved, 0, sizeof(reserved));
    }
};

// 说话数据包
struct SayPacket {
    PacketHeader header;
    BYTE sayType;                 // 说话类型（普通/组队/行会等）
    BYTE color;                   // 颜色
    WORD textLength;              // 文本长度
    // 紧跟着文本内容

    SayPacket() {
        header.packetType = CM_SAY;
        header.length = sizeof(SayPacket);
        sayType = 0;
        color = 0;
        textLength = 0;
    }
};

// 物品数据包
struct ItemPacket {
    PacketHeader header;
    WORD makeIndex;               // 制造索引
    WORD itemIndex;               // 物品索引
    WORD dura;                    // 持久度
    WORD duraMax;                 // 最大持久度
    BYTE nameLength;              // 名称长度
    // 紧跟着物品名称
    BYTE btValue[14];             // 附加属性

    ItemPacket() {
        header.length = sizeof(ItemPacket);
        makeIndex = 0;
        itemIndex = 0;
        dura = 0;
        duraMax = 0;
        nameLength = 0;
        memset(btValue, 0, sizeof(btValue));
    }
};

// 默认消息结构
struct DefaultMessage {
    int32_t nRecog;               // 识别码
    WORD wIdent;                  // 消息标识
    WORD wParam;                  // 参数
    WORD wTag;                    // 标记
    WORD wSeries;                 // 序列

    DefaultMessage() : nRecog(0), wIdent(0), wParam(0), wTag(0), wSeries(0) {}
    DefaultMessage(WORD ident, int32_t recog = 0, WORD param = 0, WORD tag = 0, WORD series = 0)
        : nRecog(recog), wIdent(ident), wParam(param), wTag(tag), wSeries(series) {}
};

// 查询角色信息结构
struct QueryChr {
    char sName[15];               // 角色名称
    BYTE btJob;                   // 职业
    BYTE btHair;                  // 发型
    BYTE btLevel;                 // 等级
    BYTE btSex;                   // 性别

    QueryChr() {
        memset(sName, 0, sizeof(sName));
        btJob = 0;
        btHair = 0;
        btLevel = 1;
        btSex = 0;
    }
};
#pragma pack(pop)

// 创建默认消息的辅助函数
inline DefaultMessage MakeDefaultMsg(uint16_t wIdent, int nRecog = 0, uint16_t wParam = 0,
                                   uint16_t wTag = 0, uint16_t wSeries = 0) {
    DefaultMessage msg;
    msg.nRecog = nRecog;
    msg.wIdent = wIdent;
    msg.wParam = wParam;
    msg.wTag = wTag;
    msg.wSeries = wSeries;
    return msg;
}

// 工具函数声明
std::string GetPacketTypeName(PacketType type);
bool IsClientPacket(PacketType type);
bool IsServerPacket(PacketType type);
bool RequiresLogin(PacketType type);
bool IsGamePlayPacket(PacketType type);

} // namespace Protocol
} // namespace MirServer