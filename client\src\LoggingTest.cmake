# Add executable for logging test
add_executable(LoggingTest 
    src/LoggingTest.cpp
    src/Utils/Logger.cpp
    src/Utils/ExceptionHandler.cpp
)

# Set include directories
target_include_directories(LoggingTest PRIVATE ${CMAKE_SOURCE_DIR})

# Set output directory
set_target_properties(LoggingTest PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Add compile options
if(MSVC)
    target_compile_options(LoggingTest PRIVATE /W4)
else()
    # For GCC/MinGW
    target_compile_options(LoggingTest PRIVATE -Wall -Wextra -pedantic -g)

    # Enable debugging symbols for Debug build
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        target_compile_options(LoggingTest PRIVATE -g3 -Og)
    endif()

    # Optimization for Release build
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        target_compile_options(LoggingTest PRIVATE -O2)
    endif()
endif()
