# MirClient C++ with SDL

This is a refactored version of the MirClient using C++ with SDL for graphics rendering. The original client was written in Delphi with DirectX.

## Features

- Cross-platform support through SDL2
- Modern C++ implementation
- WIL file format support
- Sprite and animation system
- Game state management
- UI system with controls and dialogs
- Network communication
- Sound and music playback

## Dependencies

- SDL2
- SDL2_image
- SDL2_ttf
- SDL2_mixer
- SDL2_net
- CMake (for building)

## Building

### Windows

1. Install dependencies using vcpkg:
   ```
   vcpkg install sdl2 sdl2-image sdl2-ttf sdl2-mixer sdl2-net
   ```

2. Configure and build with CMake:
   ```
   mkdir build
   cd build
   cmake .. -DCMAKE_TOOLCHAIN_FILE=[path to vcpkg]/scripts/buildsystems/vcpkg.cmake
   cmake --build .
   ```

### Linux

1. Install dependencies:
   ```
   sudo apt-get install libsdl2-dev libsdl2-image-dev libsdl2-ttf-dev libsdl2-mixer-dev libsdl2-net-dev
   ```

2. Configure and build with <PERSON><PERSON>ake:
   ```
   mkdir build
   cd build
   cmake ..
   make
   ```

### macOS

1. Install dependencies using Homebrew:
   ```
   brew install sdl2 sdl2_image sdl2_ttf sdl2_mixer sdl2_net
   ```

2. Configure and build with CMake:
   ```
   mkdir build
   cd build
   cmake ..
   make
   ```

## Project Structure

- `src/`: Source code
  - `Graphics/`: Graphics-related code
    - `WILLoader.h/cpp`: WIL file loader
    - `Texture.h/cpp`: Texture wrapper
    - `Sprite.h/cpp`: Sprite and animation system
  - `Map/`: Map-related code
    - `MapCell.h/cpp`: Map cell class
    - `MapManager.h/cpp`: Map manager
  - `Actor/`: Actor-related code
    - `Actor.h/cpp`: Actor base class
    - `Player.h/cpp`: Player class
    - `Monster.h/cpp`: Monster class
    - `ActorManager.h/cpp`: Actor manager
  - `Game/`: Game state-related code
    - `IntroState.h/cpp`: Intro state
    - `PlayState.h/cpp`: Play state
    - `MenuState.h/cpp`: Menu state
  - `UI/`: UI-related code
    - `UIControl.h/cpp`: UI control base class
    - `Button.h/cpp`: Button control
    - `Label.h/cpp`: Label control
    - `Dialog.h/cpp`: Dialog control
    - `UIManager.h/cpp`: UI manager
    - `DialogManager.h/cpp`: Dialog manager
  - `Network/`: Network-related code
    - `Packet.h/cpp`: Network packet
    - `NetworkManager.h/cpp`: Network manager
  - `Sound/`: Sound-related code
    - `SoundManager.h/cpp`: Sound manager
  - `Application.h/cpp`: Main application class
  - `GameState.h`: Game state interface
  - `main.cpp`: Entry point
- `assets/`: Game assets
  - `data/`: Game data files
  - `maps/`: Map files
  - `sounds/`: Sound files
- `CMakeLists.txt`: CMake build configuration
- `README.md`: This file

## Usage

1. Build the project as described above
2. Copy your WIL files and other assets to the `assets` directory
3. Run the executable

## Implementation Details

### Graphics System

The graphics system is responsible for loading and rendering game assets. It includes:

- **WILLoader**: Loads WIL files, which are the custom image format used by the original Mir client
- **Texture**: Wraps SDL_Texture with additional functionality
- **Sprite**: Represents a game sprite that can be rendered on the screen
- **AnimatedSprite**: Extends Sprite with animation capabilities

### Map System

The map system is responsible for loading, managing, and rendering game maps. It includes:

- **MapCell**: Represents a single cell in the game map
- **MapManager**: Manages the game map, handling loading, rendering, and interaction

### Actor System

The actor system is responsible for managing game entities. It includes:

- **Actor**: Base class for all game entities
- **Player**: Represents the player character
- **Monster**: Represents a monster
- **ActorManager**: Manages all actors in the game

### Game State System

The game state system is responsible for managing different screens in the game. It includes:

- **GameState**: Interface for game states
- **IntroState**: Represents the intro screen
- **PlayState**: Represents the main gameplay screen
- **MenuState**: Represents the menu screen

### UI System

The UI system is responsible for creating and managing user interface elements. It includes:

- **UIControl**: Base class for UI controls
- **Button**: Button control
- **Label**: Label control
- **Dialog**: Dialog control
- **UIManager**: Manages UI controls
- **DialogManager**: Manages dialogs

### Network System

The network system is responsible for client-server communication. It includes:

- **Packet**: Represents a network packet
- **NetworkManager**: Manages network communication

### Sound System

The sound system is responsible for playing sound effects and music. It includes:

- **Sound**: Represents a sound effect
- **Music**: Represents background music
- **SoundManager**: Manages sound effects and music

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- The original MirClient developers
- SDL2 development team
