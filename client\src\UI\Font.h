#pragma once

#include <SDL2/SDL_ttf.h>
#include <string>
#include <memory>

/**
 * @class Font
 * @brief Wrapper for SDL_ttf font
 *
 * This class provides a convenient wrapper for SDL_ttf font with additional
 * functionality for rendering text.
 */
class Font {
private:
    TTF_Font* m_font;       ///< SDL_ttf font handle
    int m_size;             ///< Font size
    std::string m_path;     ///< Font file path

public:
    /**
     * @brief Constructor
     * @param path Font file path
     * @param size Font size
     */
    Font(const std::string& path, int size);

    /**
     * @brief Destructor
     */
    ~Font();

    /**
     * @brief Get the SDL_ttf font handle
     * @return SDL_ttf font handle
     */
    TTF_Font* GetFont() const { return m_font; }

    /**
     * @brief Get the font size
     * @return Font size
     */
    int GetSize() const { return m_size; }

    /**
     * @brief Get the font file path
     * @return Font file path
     */
    const std::string& GetPath() const { return m_path; }

    /**
     * @brief Render text to a surface
     * @param text Text to render
     * @param color Text color
     * @return SDL surface containing the rendered text
     */
    SDL_Surface* RenderText(const std::string& text, SDL_Color color) const;

    /**
     * @brief Render text to a surface with wrapping
     * @param text Text to render
     * @param color Text color
     * @param wrapLength Maximum width of the text
     * @return SDL surface containing the rendered text
     */
    SDL_Surface* RenderTextWrapped(const std::string& text, SDL_Color color, int wrapLength) const;

    /**
     * @brief Get the size of the rendered text
     * @param text Text to measure
     * @param width Output width
     * @param height Output height
     * @return true if successful, false otherwise
     */
    bool GetTextSize(const std::string& text, int& width, int& height) const;

    /**
     * @brief Render text directly to a renderer
     * @param renderer SDL renderer
     * @param text Text to render
     * @param x X position
     * @param y Y position
     * @param color Text color (default: white)
     * @return true if successful, false otherwise
     */
    bool RenderText(SDL_Renderer* renderer, const std::string& text, int x, int y, SDL_Color color = {255, 255, 255, 255}) const;
};

