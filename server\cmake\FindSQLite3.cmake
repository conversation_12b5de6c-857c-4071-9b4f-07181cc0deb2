# FindSQLite3.cmake - Find SQLite3 library
# This module defines:
#  SQLite3_FOUND - System has SQLite3
#  SQLite3_INCLUDE_DIRS - The SQLite3 include directories
#  SQLite3_LIBRARIES - The libraries needed to use SQLite3
#  SQLite3_VERSION - The version of SQLite3

find_path(SQLite3_INCLUDE_DIR sqlite3.h
    HINTS
        ENV SQLite3_ROOT
        ${SQLite3_ROOT}
    PATHS
        /usr/local
        /usr
        /opt/local
        /opt
    PATH_SUFFIXES
        include
)

find_library(SQLite3_LIBRARY
    NAMES sqlite3
    HINTS
        ENV SQLite3_ROOT
        ${SQLite3_ROOT}
    PATHS
        /usr/local
        /usr
        /opt/local
        /opt
    PATH_SUFFIXES
        lib
        lib64
)

# Handle Windows specific cases
if(WIN32)
    find_path(SQLite3_INCLUDE_DIR sqlite3.h
        PATHS
            ${CMAKE_SOURCE_DIR}/third_party/sqlite3/include
            ${CMAKE_SOURCE_DIR}/external/sqlite3/include
            C:/sqlite3/include
            C:/Program Files/sqlite3/include
            C:/Program Files (x86)/sqlite3/include
    )
    
    find_library(SQLite3_LIBRARY
        NAMES sqlite3
        PATHS
            ${CMAKE_SOURCE_DIR}/third_party/sqlite3/lib
            ${CMAKE_SOURCE_DIR}/external/sqlite3/lib
            C:/sqlite3/lib
            C:/Program Files/sqlite3/lib
            C:/Program Files (x86)/sqlite3/lib
    )
endif()

# Get version
if(SQLite3_INCLUDE_DIR AND EXISTS "${SQLite3_INCLUDE_DIR}/sqlite3.h")
    file(STRINGS "${SQLite3_INCLUDE_DIR}/sqlite3.h" sqlite3_version_str
         REGEX "^#define[\t ]+SQLITE_VERSION[\t ]+\".*\"")
    string(REGEX REPLACE "^#define[\t ]+SQLITE_VERSION[\t ]+\"([^\"]*)\".*" "\\1"
           SQLite3_VERSION "${sqlite3_version_str}")
    unset(sqlite3_version_str)
endif()

include(FindPackageHandleStandardArgs)
find_package_handle_standard_args(SQLite3
    REQUIRED_VARS SQLite3_LIBRARY SQLite3_INCLUDE_DIR
    VERSION_VAR SQLite3_VERSION
)

if(SQLite3_FOUND)
    set(SQLite3_LIBRARIES ${SQLite3_LIBRARY})
    set(SQLite3_INCLUDE_DIRS ${SQLite3_INCLUDE_DIR})
    
    # Create imported target
    if(NOT TARGET SQLite::SQLite3)
        add_library(SQLite::SQLite3 UNKNOWN IMPORTED)
        set_target_properties(SQLite::SQLite3 PROPERTIES
            IMPORTED_LOCATION "${SQLite3_LIBRARY}"
            INTERFACE_INCLUDE_DIRECTORIES "${SQLite3_INCLUDE_DIR}"
        )
    endif()
endif()

mark_as_advanced(SQLite3_INCLUDE_DIR SQLite3_LIBRARY) 