#ifndef LOGGER_H
#define LOGGER_H

#include <string>
#include <fstream>
#include <mutex>
#include <memory>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <functional>

/**
 * @brief Log level enumeration
 *
 * Note: Avoid using names that might conflict with Windows macros (like ERROR)
 */
enum class LogLevel {
    LOG_LEVEL_DEBUG,
    LOG_LEVEL_INFO,
    LOG_LEVEL_WARNING,
    LOG_LEVEL_ERROR,
    LOG_LEVEL_FATAL
};

/**
 * @brief Log rotation mode
 */
enum class LogRotationMode {
    NONE,           // No rotation
    SIZE_BASED,     // Rotate based on file size
    DAILY           // Rotate daily
};

/**
 * @brief Logger class for writing logs to a file
 *
 * This class provides methods for logging messages at different levels.
 * It uses a singleton pattern for easy access throughout the codebase.
 */
class Logger {
public:
    /**
     * @brief Get the singleton instance of the logger
     *
     * @return Logger& The logger instance
     */
    static Logger& GetInstance();

    /**
     * @brief Initialize the logger
     *
     * @param logFilePath Path to the log file
     * @param level Minimum log level to record
     * @return true if initialization was successful
     * @return false if initialization failed
     */
    bool Initialize(const std::string& logFilePath, LogLevel level = LogLevel::LOG_LEVEL_INFO);

    /**
     * @brief Initialize the logger with rotation settings
     * 
     * @param logFilePath Path to the log file
     * @param level Minimum log level to record
     * @param rotationMode Log rotation mode
     * @param maxSizeKB Maximum size in KB for size-based rotation (default 10MB)
     * @param maxFilesToKeep Maximum number of rotated log files to keep (default 5)
     * @return true if initialization was successful
     * @return false if initialization failed
     */
    bool Initialize(const std::string& logFilePath, 
                   LogLevel level,
                   LogRotationMode rotationMode,
                   size_t maxSizeKB = 10 * 1024,
                   int maxFilesToKeep = 5);

    /**
     * @brief Safe Initialize with timeout option
     * 
     * Attempts to initialize the logger with a timeout. First tries direct initialization,
     * and if that fails, tries using a thread with timeout to prevent blocking.
     * 
     * @param logFilePath Path to the log file
     * @param level Minimum log level to record
     * @param timeoutMs Maximum time to wait for initialization in milliseconds (0 = no timeout)
     * @param progressCallback Optional callback function to report progress (receives elapsed ms)
     * @return true if initialization was successful
     * @return false if initialization failed or timed out
     */
    bool SafeInitialize(const std::string& logFilePath, 
                        LogLevel level = LogLevel::LOG_LEVEL_INFO,
                        int timeoutMs = 5000,
                        std::function<void(int)> progressCallback = nullptr);
    
    /**
     * @brief Safe Initialize with rotation and timeout options
     * 
     * @param logFilePath Path to the log file
     * @param level Minimum log level to record
     * @param rotationMode Log rotation mode
     * @param maxSizeKB Maximum size in KB for size-based rotation
     * @param maxFilesToKeep Maximum number of rotated log files to keep
     * @param timeoutMs Maximum time to wait for initialization in milliseconds
     * @param progressCallback Optional callback function to report progress
     * @return true if initialization was successful
     * @return false if initialization failed or timed out
     */
    bool SafeInitialize(const std::string& logFilePath, 
                       LogLevel level,
                       LogRotationMode rotationMode,
                       size_t maxSizeKB = 10 * 1024,
                       int maxFilesToKeep = 5,
                       int timeoutMs = 5000,
                       std::function<void(int)> progressCallback = nullptr);

    /**
     * @brief Check if the logger is initialized
     * 
     * @return true if the logger is initialized
     * @return false if the logger is not initialized
     */
    bool IsInitialized() const;

    /**
     * @brief Set the log level
     *
     * @param level The new log level
     */
    void SetLogLevel(LogLevel level);

    /**
     * @brief Log a debug message
     *
     * @param message The message to log
     */
    void Debug(const std::string& message);

    /**
     * @brief Log an info message
     *
     * @param message The message to log
     */
    void Info(const std::string& message);

    /**
     * @brief Log a warning message
     *
     * @param message The message to log
     */
    void Warning(const std::string& message);

    /**
     * @brief Log an error message
     *
     * @param message The message to log
     */
    void Error(const std::string& message);

    /**
     * @brief Log a fatal error message
     *
     * @param message The message to log
     */
    void Fatal(const std::string& message);

    /**
     * @brief Log an exception
     *
     * @param exception The exception message
     * @param file The file where the exception occurred
     * @param line The line where the exception occurred
     */
    void LogException(const std::string& exception, const std::string& file, int line);

    /**
     * @brief Close the log file
     */
    void Close();

private:
    /**
     * @brief Private constructor for singleton pattern
     */
    Logger();

    /**
     * @brief Private destructor for singleton pattern
     */
    ~Logger();

    /**
     * @brief Log a message with the specified level
     *
     * @param level The log level
     * @param message The message to log
     */
    void Log(LogLevel level, const std::string& message);

    /**
     * @brief Get the current timestamp as a string
     *
     * @return std::string The formatted timestamp
     */
    std::string GetTimestamp() const;
    
    /**
     * @brief Get current date as string for log rotation
     * 
     * @return std::string The date in YYYY-MM-DD format
     */
    std::string GetCurrentDate() const;

    /**
     * @brief Convert a log level to a string
     *
     * @param level The log level
     * @return std::string The string representation of the log level
     */
    std::string LogLevelToString(LogLevel level) const;

    /**
     * @brief Write to debug log file
     * 
     * @param message Message to write to debug log
     */
    void WriteDebugLog(const std::string& message);
    
    /**
     * @brief Check if log rotation is needed and perform rotation if necessary
     */
    void CheckRotation();
    
    /**
     * @brief Rotate log files
     */
    void RotateLogFiles();

private:
    std::ofstream m_logFile;
    LogLevel m_logLevel;
    std::mutex m_mutex;
    bool m_initialized;
    
    // Log rotation settings
    LogRotationMode m_rotationMode;
    size_t m_maxSizeKB;
    int m_maxFilesToKeep;
    std::string m_logFilePath;
    std::string m_lastRotationDate;
};

// Global function to write to debug log
void WriteDebugLog(const std::string& message);

// Safe logging functions that check if logger is initialized
void SafeLogDebug(const std::string& message);
void SafeLogInfo(const std::string& message);
void SafeLogWarning(const std::string& message);
void SafeLogError(const std::string& message);
void SafeLogFatal(const std::string& message);

// System information functions
std::string GetSystemInfo();

// Convenience macros for logging
#define LOG_DEBUG(message) Logger::GetInstance().Debug(message)
#define LOG_INFO(message) Logger::GetInstance().Info(message)
#define LOG_WARNING(message) Logger::GetInstance().Warning(message)
#define LOG_ERROR(message) Logger::GetInstance().Error(message)
#define LOG_FATAL(message) Logger::GetInstance().Fatal(message)
#define LOG_EXCEPTION(exception) Logger::GetInstance().LogException(exception, __FILE__, __LINE__)

#endif // LOGGER_H
