#pragma once

#include <memory>
#include <string>
#include <vector>
#include <SDL2/SDL.h>
#include <SDL2/SDL_ttf.h>
#include "../Graphics/Texture.h"

/**
 * @enum DamageType
 * @brief Type of damage to display
 */
enum class DamageType {
    NORMAL,     ///< Normal damage (white)
    CRITICAL,   ///< Critical damage (red)
    HEAL,       ///< Healing (green)
    MISS        ///< Miss (gray)
};

/**
 * @class DamageNumber
 * @brief Class for displaying floating damage numbers
 */
class DamageNumber {
private:
    int m_x;                            ///< X position
    int m_y;                            ///< Y position
    int m_value;                        ///< Damage value
    DamageType m_type;                  ///< Damage type
    int m_duration;                     ///< Total duration in milliseconds
    int m_timer;                        ///< Current timer in milliseconds
    float m_speed;                      ///< Floating speed in pixels per second
    std::shared_ptr<Texture> m_texture; ///< Texture for the damage number
    bool m_active;                      ///< Whether the damage number is active

public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param value Damage value
     * @param type Damage type
     * @param font Font to use for rendering
     * @param renderer SDL renderer
     */
    DamageNumber(int x, int y, int value, DamageType type, TTF_Font* font, SDL_Renderer* renderer);

    /**
     * @brief Destructor
     */
    ~DamageNumber();

    /**
     * @brief Update the damage number
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    void Update(int deltaTime);

    /**
     * @brief Render the damage number
     */
    void Render();

    /**
     * @brief Check if the damage number is active
     * @return True if active, false otherwise
     */
    bool IsActive() const { return m_active; }

    /**
     * @brief Get the X position
     * @return X position
     */
    int GetX() const { return m_x; }

    /**
     * @brief Get the Y position
     * @return Y position
     */
    int GetY() const { return m_y; }

private:
    /**
     * @brief Create the texture for the damage number
     * @param font Font to use for rendering
     * @param renderer SDL renderer
     */
    void CreateTexture(TTF_Font* font, SDL_Renderer* renderer);
};

/**
 * @class DamageNumberManager
 * @brief Manager for damage numbers
 */
class DamageNumberManager {
private:
    std::vector<std::shared_ptr<DamageNumber>> m_damageNumbers; ///< List of active damage numbers
    TTF_Font* m_font;                                          ///< Font for rendering damage numbers
    SDL_Renderer* m_renderer;                                  ///< SDL renderer

public:
    /**
     * @brief Constructor
     * @param renderer SDL renderer
     */
    DamageNumberManager(SDL_Renderer* renderer);

    /**
     * @brief Destructor
     */
    ~DamageNumberManager();

    /**
     * @brief Update all damage numbers
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    void Update(int deltaTime);

    /**
     * @brief Render all damage numbers
     */
    void Render();

    /**
     * @brief Add a damage number
     * @param x X position
     * @param y Y position
     * @param value Damage value
     * @param type Damage type
     * @return Pointer to the created damage number
     */
    std::shared_ptr<DamageNumber> AddDamageNumber(int x, int y, int value, DamageType type);

    /**
     * @brief Set the font
     * @param font Font to use for rendering damage numbers
     */
    void SetFont(TTF_Font* font) { m_font = font; }
};

