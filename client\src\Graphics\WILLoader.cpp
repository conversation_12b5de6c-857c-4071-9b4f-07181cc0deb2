#include "WILLoader.h"
#include <fstream>
#include <iostream>
#include <sstream>
#include <algorithm>
#include <zlib.h>

WILLoader::WILLoader()
    : m_loaded(false)
    , m_paletteLoaded(false)
    , m_fileType(FileType::WIL)
{
    // Initialize header
    memset(&m_header, 0, sizeof(WILHeader));
    m_header.offsets = nullptr;

    // Initialize palette with default grayscale colors
    // This matches the original Delphi implementation where default palette is grayscale
    for (int i = 0; i < 256; i++) {
        m_palette[i].r = i;
        m_palette[i].g = i;
        m_palette[i].b = i;
        m_palette[i].a = 255;
    }
}

WILLoader::~WILLoader()
{
    Unload();
}

std::string WILLoader::GetFileExtension(const std::string& filename) {
    size_t pos = filename.find_last_of('.');
    if (pos != std::string::npos) {
        return filename.substr(pos);
    }
    return "";
}

std::string WILLoader::GetFilePathWithoutExtension(const std::string& filename) {
    size_t pos = filename.find_last_of('.');
    if (pos != std::string::npos) {
        return filename.substr(0, pos);
    }
    return filename;
}

bool WILLoader::Load(const std::string& filename, const std::string& paletteFile)
{
    // Unload any previously loaded file
    Unload();

    m_filename = filename;

    // Load palette if specified
    // if (!paletteFile.empty()) {
        if (!LoadPalette(paletteFile)) {
            std::cerr << "Failed to load palette: " << paletteFile << std::endl;
            // Continue without palette - we'll use default colors
            std::cout << "Using default grayscale palette instead" << std::endl;
        }
    // }

    // Determine file type (WIL or WZL)
    std::string ext = GetFileExtension(filename);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
    bool isWzl = (ext == ".wzl");

    // Set the file type
    if (isWzl) {
        m_fileType = FileType::WZL;
    } else {
        m_fileType = FileType::WIL;
    }

    // Open the file
    std::ifstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Failed to open file: " << filename << std::endl;

        // Try alternative extension
        std::string altFilename;
        if (ext == ".wil") {
            altFilename = GetFilePathWithoutExtension(filename) + ".wzl";
        } else if (ext == ".wzl") {
            altFilename = GetFilePathWithoutExtension(filename) + ".wil";
        }

        if (!altFilename.empty()) {
            std::cout << "Trying alternative file: " << altFilename << std::endl;
            std::ifstream altFile(altFilename, std::ios::binary);
            if (altFile.is_open()) {
                std::cout << "Successfully opened alternative file: " << altFilename << std::endl;
                m_filename = altFilename;

                // Update file type based on alternative extension
                std::string altExt = GetFileExtension(altFilename);
                std::transform(altExt.begin(), altExt.end(), altExt.begin(), ::tolower);
                if (altExt == ".wzl") {
                    m_fileType = FileType::WZL;
                    return LoadWzlFile(altFile);
                } else {
                    m_fileType = FileType::WIL;
                    return LoadWilFile(altFile);
                }
            }
        }

        // Create a dummy 1x1 image as fallback
        // This is similar to the original Delphi implementation's fallback mechanism
        std::cout << "Creating dummy image as fallback for: " << filename << std::endl;

        // Set up a minimal header
        if (m_fileType == FileType::WIL) {
            m_header.header.imageCount = 1;
        } else {
            // For WZL files
            m_header.header.imageCount = 1;
        }

        // Create a single dummy image
        m_images.resize(1);
        m_surfaces.resize(1, nullptr);

        m_images[0].width = 1;
        m_images[0].height = 1;
        m_images[0].offsetX = 0;
        m_images[0].offsetY = 0;
        m_images[0].data = new uint8_t[1];
        m_images[0].data[0] = 1; // Non-transparent pixel

        m_loaded = true;
        return true;
    }

    // Load the appropriate file type
    if (m_fileType == FileType::WZL) {
        return LoadWzlFile(file);
    } else {
        return LoadWilFile(file);
    }
}

bool WILLoader::LoadWilFile(std::ifstream& file)
{
    // Read the WIL header (title, image count, color count, palette size)
    file.read(reinterpret_cast<char*>(&m_header.header), sizeof(WMImageHeader));
    if (file.fail()) {
        std::cerr << "Failed to read WIL header: " << m_filename << std::endl;
        return false;
    }

    // Set the image count from the header
    int32_t imageCount = m_header.header.imageCount;

    // Read the palette if it exists
    if (m_header.header.paletteSize > 0 && !m_paletteLoaded) {
        // Read the palette data (256 RGB colors)
        file.read(reinterpret_cast<char*>(m_palette), sizeof(SDL_Color) * 256);
        if (file.fail()) {
            std::cerr << "Failed to read palette data from WIL file: " << m_filename << std::endl;
            // Continue with default palette
        } else {
            m_paletteLoaded = true;
        }
    }

    // Allocate memory for offsets
    m_header.offsets = new int32_t[imageCount];

    // Read the offsets
    file.read(reinterpret_cast<char*>(m_header.offsets), sizeof(int32_t) * imageCount);
    if (file.fail()) {
        std::cerr << "Failed to read WIL offsets: " << m_filename << std::endl;
        delete[] m_header.offsets;
        m_header.offsets = nullptr;
        return false;
    }

    // Resize the images and surfaces vectors
    m_images.resize(imageCount);
    m_surfaces.resize(imageCount, nullptr);

    // Load each image
    for (int32_t i = 0; i < imageCount; i++) {
        // Skip if the offset is 0 (no image)
        if (m_header.offsets[i] == 0) {
            continue;
        }

        // Seek to the image data
        file.seekg(m_header.offsets[i], std::ios::beg);
        if (file.fail()) {
            std::cerr << "Failed to seek to image " << i << " in WIL file: " << m_filename << std::endl;
            continue;
        }

        // Read the image header (TWMImageInfo structure without the bits pointer)
        // In the original Delphi code, this is: Stream.Read(imgi, sizeof(TWMImageInfo) - 4);
        file.read(reinterpret_cast<char*>(&m_images[i].width), sizeof(uint16_t));
        file.read(reinterpret_cast<char*>(&m_images[i].height), sizeof(uint16_t));
        file.read(reinterpret_cast<char*>(&m_images[i].offsetX), sizeof(uint16_t));
        file.read(reinterpret_cast<char*>(&m_images[i].offsetY), sizeof(uint16_t));

        if (file.fail()) {
            std::cerr << "Failed to read image " << i << " header in WIL file: " << m_filename << std::endl;
            continue;
        }

        // Skip images with invalid dimensions
        if (m_images[i].width == 0 || m_images[i].height == 0) {
            continue;
        }

        // Allocate memory for the image data
        size_t dataSize = m_images[i].width * m_images[i].height;
        m_images[i].data = new uint8_t[dataSize];

        // Read the image data
        file.read(reinterpret_cast<char*>(m_images[i].data), dataSize);
        if (file.fail()) {
            std::cerr << "Failed to read image " << i << " data in WIL file: " << m_filename << std::endl;
            delete[] m_images[i].data;
            m_images[i].data = nullptr;
            continue;
        }
    }

    file.close();
    m_loaded = true;
    return true;
}

bool WILLoader::LoadWzlFile(std::ifstream& file)
{
    // Read the WZL header
    WZLHeader header;
    file.read(reinterpret_cast<char*>(&header), sizeof(WZLHeader));
    if (file.fail()) {
        std::cerr << "Failed to read WZL header: " << m_filename << std::endl;
        return false;
    }

    // Store the image count
    int32_t imageCount = header.imageCount;

    // Resize the images and surfaces vectors
    m_images.resize(imageCount);
    m_surfaces.resize(imageCount, nullptr);

    // Load index file if available
    std::string idxfile = GetFilePathWithoutExtension(m_filename) + ".wzx";
    std::vector<int32_t> indexList;
    if (LoadIndexFile(idxfile, indexList)) {
        // Use index file for offsets
        for (int32_t i = 0; i < imageCount && i < indexList.size(); i++) {
            if (indexList[i] > 0) {
                LoadWzlImage(file, indexList[i], i);
            }
        }
    } else {
        // Without index file, we can't properly load WZL files
        // This matches the original Delphi implementation
        std::cerr << "Index file not found for WZL file: " << m_filename << std::endl;
        std::cerr << "WZL files require an index file (.wzx) for proper loading" << std::endl;
        return false;
    }

    file.close();
    m_loaded = true;
    return true;
}

void WILLoader::LoadWzlImage(std::ifstream& file, int32_t offset, int32_t index)
{
    // Skip if the offset is 0 (no image)
    if (offset == 0) {
        return;
    }

    // Seek to the image data
    file.seekg(offset, std::ios::beg);
    if (file.fail()) {
        std::cerr << "Failed to seek to image " << index << " in WZL file: " << m_filename << std::endl;
        return;
    }

    // Read the image info
    WZLImageInfo imgInfo;
    file.read(reinterpret_cast<char*>(&imgInfo), sizeof(WZLImageInfo));
    if (file.fail()) {
        std::cerr << "Failed to read image " << index << " info in WZL file: " << m_filename << std::endl;
        return;
    }

    // Skip images with invalid dimensions
    if (imgInfo.nWidth == 0 || imgInfo.nHeight == 0) {
        return;
    }

    // Store image metadata
    m_images[index].width = imgInfo.nWidth;
    m_images[index].height = imgInfo.nHeight;
    m_images[index].offsetX = imgInfo.wPx;
    m_images[index].offsetY = imgInfo.wPy;

    // Allocate memory for the image data
    short bytesPerPixels = imgInfo.btEn1 == 3 ? 1 : 2;
    size_t dataSize = m_images[index].width * m_images[index].height* bytesPerPixels;
    m_images[index].data = new uint8_t[dataSize];

    // Check encoding type (btEn1)
    if (imgInfo.btEn1 == 3||imgInfo.btEn1 == 5) {
        // 8-bit encoding
        if (imgInfo.iLen > 0) {
            // Compressed data
            uint8_t* compressedData = new uint8_t[imgInfo.iLen];
            file.read(reinterpret_cast<char*>(compressedData), imgInfo.iLen);

            // Decompress the data using zlib (matches DecompressBufZ in original Delphi code)
            if (!DecompressZlibData(compressedData, imgInfo.iLen, m_images[index].data, dataSize)) {
                std::cerr << "Failed to decompress image " << index << " in WZL file: " << m_filename << std::endl;
                delete[] compressedData;
                delete[] m_images[index].data;
                m_images[index].data = nullptr;
                return;
            }

            delete[] compressedData;
        } else {
            // Uncompressed data
            file.read(reinterpret_cast<char*>(m_images[index].data), dataSize);
            if (file.fail()) {
                std::cerr << "Failed to read image " << index << " data in WZL file: " << m_filename << std::endl;
                delete[] m_images[index].data;
                m_images[index].data = nullptr;
                return;
            }
        }
    }
    // else if (imgInfo.btEn1 == 5) {
    //     // 16-bit encoding (not fully implemented in original Delphi code)
    //     std::cerr << "16-bit encoding not fully supported for image " << index << " in WZL file: " << m_filename << std::endl;

    //     // Create a blank image as fallback
    //     memset(m_images[index].data, 0, dataSize);
    // }
    else {
        // Unknown encoding
        std::cerr << "Unknown encoding type " << (int)imgInfo.btEn1 << " for image " << index << " in WZL file: " << m_filename << std::endl;

        // Create a blank image as fallback
        memset(m_images[index].data, 0, dataSize);
    }
}

bool WILLoader::DecompressZlibData(const uint8_t* compressedData, size_t compressedSize,
                                  uint8_t* decompressedData, size_t decompressedSize)
{
    z_stream zstream;
    memset(&zstream, 0, sizeof(zstream));

    zstream.next_in = const_cast<Bytef*>(compressedData);
    zstream.avail_in = compressedSize;
    zstream.next_out = decompressedData;
    zstream.avail_out = decompressedSize;

    // Initialize zlib for decompression
    if (inflateInit(&zstream) != Z_OK) {
        std::cerr << "Failed to initialize zlib for decompression" << std::endl;
        return false;
    }

    // Decompress the data
    int result = inflate(&zstream, Z_FINISH);
    inflateEnd(&zstream);

    if (result != Z_STREAM_END) {
        std::cerr << "Failed to decompress data: " << result << std::endl;
        return false;
    }

    return true;
}

bool WILLoader::LoadIndexFile(const std::string& idxfile, std::vector<int32_t>& indexList)
{
    std::ifstream file(idxfile, std::ios::binary);
    if (!file.is_open()) {
        return false;
    }

    // Read index header
    WZLIndexHeader header;
    file.read(reinterpret_cast<char*>(&header), sizeof(WZLIndexHeader));
    if (file.fail()) {
        file.close();
        return false;
    }

    // Read index entries
    // In the original Delphi code, this is:
    // FileRead(fhandle, pvalue^, 4 * wzlheader.ImageCount);
    int32_t indexCount = header.indexCount;
    indexList.resize(indexCount);
    file.read(reinterpret_cast<char*>(indexList.data()), indexCount * sizeof(int32_t));
            // if IndexList[0]<>1084 then
            // Begin
            //   FileSeek(fHandle,48,0);
            //   FileRead (fhandle, IndexList[0], 4*headerofIndex.IndexCount);
            // End;
    if(indexList[0]!=1084){
        if(file.eof()) {file.clear();}
        file.seekg(sizeof(WZLIndexHeader) - 4, std::ios::beg);
        file.read(reinterpret_cast<char*>(indexList.data()), indexCount * sizeof(int32_t));
    }
    file.close();
    return true;
}

void WILLoader::Unload()
{
    // Free all surfaces
    for (auto& surface : m_surfaces) {
        if (surface) {
            SDL_FreeSurface(surface);
            surface = nullptr;
        }
    }
    m_surfaces.clear();

    // Free all image data
    for (auto& image : m_images) {
        delete[] image.data;
        image.data = nullptr;
    }
    m_images.clear();

    // Free the offsets array
    delete[] m_header.offsets;
    m_header.offsets = nullptr;

    // Reset header values
    m_header.header.imageCount = 0;
    m_header.header.colorCount = 0;
    m_header.header.paletteSize = 0;
    m_loaded = false;
}

bool WILLoader::LoadPalette(const std::string& filename)
{
    // First try to load from the specified file
    if (!filename.empty()) {
        std::ifstream file(filename, std::ios::binary);
        if (file.is_open()) {
            // Read the palette data (256 RGB colors)
            for (int i = 0; i < 256; i++) {
                file.read(reinterpret_cast<char*>(&m_palette[i].r), 1);
                file.read(reinterpret_cast<char*>(&m_palette[i].g), 1);
                file.read(reinterpret_cast<char*>(&m_palette[i].b), 1);
                m_palette[i].a = 255; // Full opacity
            }

            if (!file.fail()) {
                file.close();
                m_paletteLoaded = true;
                return true;
            }
            file.close();
        }
    }
    const uint32_t paletteData[] = {
        // 基础色 (0-15)
        0xFF000000, 0xFF800000, 0xFF008000, 0xFF808000, 0xFF000080, 0xFF800080, 0xFF008080, 0xFFC0C0C0,
        0xFF558097, 0xFF9DB9C8, 0xFF7B7373, 0xFF2D2929, 0xFF5A5252, 0xFF635A5A, 0xFF423939, 0xFF1D1818,

        // 主色系 (16-239)
        0xFF181010, 0xFF291818, 0xFF100808, 0xFFF27971, 0xFFE1675F, 0xFFFF5A5A, 0xFFFF3131, 0xFFD65A52,
        0xFF941000, 0xFF942918, 0xFF390800, 0xFF731000, 0xFFB51800, 0xFFBD6352, 0xFF421810, 0xFFFFAA99,
        0xFF5A1000, 0xFF733929, 0xFFA54A31, 0xFF947B73, 0xFFBD5231, 0xFF522110, 0xFF7B3118, 0xFF2D1810,
        0xFF8C4A31, 0xFF942900, 0xFFBD3100, 0xFFC67352, 0xFF6B3118, 0xFFC66B42, 0xFFCE4A00, 0xFFA56339,
        0xFF5A3118, 0xFF2A1000, 0xFF150800, 0xFF3A1800, 0xFF080000, 0xFF290000, 0xFF4A0000, 0xFF9D0000,
        0xFFDC0000, 0xFFDE0000, 0xFFFB0000, 0xFF9C7352, 0xFF946B4A, 0xFF734A29, 0xFF523118, 0xFF8C4A18,
        0xFF884411, 0xFF4A2100, 0xFF211810, 0xFFD6945A, 0xFFC66B21, 0xFFEF6B00, 0xFFFF7700, 0xFFA59484,
        0xFF423121, 0xFF181008, 0xFF291808, 0xFF211000, 0xFF392918, 0xFF8C6339, 0xFF422910, 0xFF6B4218,
        0xFF7B4A18, 0xFF944A00, 0xFF8C847B, 0xFF6B635A, 0xFF4A4239, 0xFF292118, 0xFF463929, 0xFFB5A594,
        0xFF7B6B5A, 0xFFCEB194, 0xFFA58C73, 0xFF8C735A, 0xFFB59473, 0xFFD6A573, 0xFFEFA54A, 0xFFEFC68C,
        0xFF7B6342, 0xFF6B5639, 0xFFBD945A, 0xFF633900, 0xFFD6C6AD, 0xFF524229, 0xFF946318, 0xFFEFD6AD,
        0xFFA58C63, 0xFF635A4A, 0xFFBDA57B, 0xFF5A4218, 0xFFBD8C31, 0xFF353129, 0xFF948463, 0xFF7B6B4A,
        0xFFA58C5A, 0xFF5A4A29, 0xFF9C7B39, 0xFF423110, 0xFFEFAD21, 0xFF181000, 0xFF292100, 0xFF9C6B00,
        0xFF94845A, 0xFF524218, 0xFF6B5A29, 0xFF7B6321, 0xFF9C7B21, 0xFFDEA500, 0xFF5A5239, 0xFF312910,
        0xFFCEBD7B, 0xFF635A39, 0xFF94844A, 0xFFC6A529, 0xFF109C18, 0xFF428C4A, 0xFF318C42, 0xFF109429,
        0xFF081810, 0xFF081818, 0xFF082910, 0xFF184229, 0xFFA5B5AD, 0xFF6B7373, 0xFF182929, 0xFF18424A,
        0xFF31424A, 0xFF63C6DE, 0xFF44DDFF, 0xFF8CD6EF, 0xFF736B39, 0xFFF7DE39, 0xFFF7EF8C, 0xFFF7E700,
        0xFF6B6B5A, 0xFF5A8CA5, 0xFF39B5EF, 0xFF4A9CCE, 0xFF3184B5, 0xFF31526B, 0xFFDEDED6, 0xFFBDBDB5,
        0xFF8C8C84, 0xFFF7F7DE, 0xFF000818, 0xFF081839, 0xFF081029, 0xFF081800, 0xFF082900, 0xFF0052A5,
        0xFF007BDE, 0xFF10294A, 0xFF10396B, 0xFF10528C, 0xFF215AA5, 0xFF10315A, 0xFF104284, 0xFF315284,
        0xFF182131, 0xFF4A5A7B, 0xFF526BA5, 0xFF293963, 0xFF104ADE, 0xFF292921, 0xFF4A4A39, 0xFF292918,
        0xFF4A4A29, 0xFF7B7B42, 0xFF9C9C4A, 0xFF5A5A29, 0xFF424214, 0xFF393900, 0xFF595900, 0xFFCA352C,
        0xFF6B7321, 0xFF293100, 0xFF313910, 0xFF313918, 0xFF424A00, 0xFF526318, 0xFF5A7329, 0xFF314A18,
        0xFF182100, 0xFF183100, 0xFF183910, 0xFF63844A, 0xFF6BBD4A, 0xFF63B54A, 0xFF63BD4A, 0xFF5A9C4A,
        0xFF4A8C39, 0xFF63C64A, 0xFF63D64A, 0xFF52844A, 0xFF317329, 0xFF63C65A, 0xFF52BD4A, 0xFF10FF00,
        0xFF182918, 0xFF4A884A, 0xFF4AE74A, 0xFF005A00, 0xFF008800, 0xFF009400, 0xFF00DE00, 0xFF00EE00,
        0xFF00FB00, 0xFF4A5A94, 0xFF6373B5, 0xFF7B8CD6, 0xFF6B7BD6, 0xFF7788FF, 0xFFC6C6CE, 0xFF94949C,
        0xFF9C94C6, 0xFF313139, 0xFF291884, 0xFF180084, 0xFF4A4252, 0xFF52427B, 0xFF635A73, 0xFFCEB5F7,
        0xFF8C7B9C, 0xFF7722CC, 0xFFDDA6FF, 0xFFF0B42A, 0xFFDF009F, 0xFFE317B3, 0xFFFFFBF0, 0xFFA0A0A4,
        0xFF808080, 0xFFFF0000, 0xFF00FF00, 0xFFFFFF00, 0xFF0000FF, 0xFFFF00FF, 0xFF00FFFF, 0xFFFFFFFF,
    };

    // Correctly convert the packed ARGB values to SDL_Color format
    for (int i = 0; i < 256; i++) {
        // Extract individual color components from the packed ARGB value
        // Format: 0xAARRGGBB
        uint32_t color = paletteData[i];
        m_palette[i].a = (color >> 24) & 0xFF; // Alpha from bits 24-31
        m_palette[i].r = (color >> 16) & 0xFF; // Red from bits 16-23
        m_palette[i].g = (color >> 8) & 0xFF;  // Green from bits 8-15
        m_palette[i].b = color & 0xFF;         // Blue from bits 0-7
    }

    m_paletteLoaded = true;
    return true;

    // If the specified file couldn't be loaded, try to load from ChrSel.wil
    // This matches the original Delphi implementation in TWMImages.LoadPalette
    // std::string chrSelPath = "Data/ChrSel.wil";
    // if (std::ifstream(chrSelPath, std::ios::binary).good()) {
    //     std::ifstream chrSelFile(chrSelPath, std::ios::binary);
    //     if (chrSelFile.is_open()) {
    //         // Skip the header
    //         WMImageHeader header;
    //         chrSelFile.read(reinterpret_cast<char*>(&header), sizeof(WMImageHeader));

    //         // Read the palette data (256 RGB colors)
    //         for (int i = 0; i < 256; i++) {
    //             chrSelFile.read(reinterpret_cast<char*>(&m_palette[i].r), 1);
    //             chrSelFile.read(reinterpret_cast<char*>(&m_palette[i].g), 1);
    //             chrSelFile.read(reinterpret_cast<char*>(&m_palette[i].b), 1);
    //             m_palette[i].a = 255; // Full opacity
    //         }

    //         if (!chrSelFile.fail()) {
    //             chrSelFile.close();
    //             m_paletteLoaded = true;
    //             return true;
    //         }
    //         chrSelFile.close();
    //     }
    // }

    // If we still couldn't load a palette, use the default grayscale palette
//     std::cerr << "Failed to load palette, using default grayscale palette" << std::endl;
//     return false;
}

SDL_Surface* WILLoader::GetSurface(int index)
{
    // Check if the index is valid
    if (index < 0 || index >= static_cast<int>(m_images.size())) {
        return nullptr;
    }

    // Check if the surface is already created
    if (m_surfaces[index]) {
        return m_surfaces[index];
    }

    // Create the surface
    m_surfaces[index] = CreateSurfaceFromImage(index);
    return m_surfaces[index];
}

bool WILLoader::GetImageOffset(int index, int& offsetX, int& offsetY)
{
    // Check if the index is valid
    if (index < 0 || index >= static_cast<int>(m_images.size())) {
        return false;
    }

    // Check if the image data exists
    if (!m_images[index].data) {
        return false;
    }

    // Get the offset values
    // This matches the original Delphi implementation: px := ImgArr[index].nPx; py := ImgArr[index].nPy;
    offsetX = m_images[index].offsetX;
    offsetY = m_images[index].offsetY;

    return true;
}

SDL_Surface* WILLoader::CreateSurfaceFromImage(int index)
{
    // Check if the index is valid
    if (index < 0 || index >= static_cast<int>(m_images.size())) {
        return nullptr;
    }

    // Check if the image data exists
    if (!m_images[index].data) {
        return nullptr;
    }

    // Create an SDL surface
    SDL_Surface* surface = SDL_CreateRGBSurface(
        0,
        m_images[index].width,
        m_images[index].height,
        32,
        // 0xFF000000,
        // 0x00FF0000,
        // 0x0000FF00,
        // 0xFF0000FF
        0x00FF0000,
        0x0000FF00,
        0x000000FF,
        0xFF000000
    );

    if (!surface) {
        std::cerr << "Failed to create SDL surface: " << SDL_GetError() << std::endl;
        return nullptr;
    }

    // Lock the surface
    if (SDL_LockSurface(surface) != 0) {
        std::cerr << "Failed to lock SDL surface: " << SDL_GetError() << std::endl;
        SDL_FreeSurface(surface);
        return nullptr;
    }

    // Copy the image data to the surface, flipping vertically during the copy
    // This fixes the upside-down rendering issue at the source
    uint32_t* pixels = static_cast<uint32_t*>(surface->pixels);
    for (int y = 0; y < m_images[index].height; y++) {
        // Calculate the y-coordinate in the destination surface with vertical flip
        // (height - 1 - y) reverses the y-coordinate to flip the image vertically
        int destY = m_images[index].height - 1 - y;

        for (int x = 0; x < m_images[index].width; x++) {
            uint8_t colorIndex = m_images[index].data[y * m_images[index].width + x];

            // Skip transparent pixels (index 0)
            // This matches the original Delphi implementation where index 0 is transparent
            if (colorIndex == 0) {
                pixels[destY * surface->w + x] = 0; // Transparent
            } else {
                // Convert the color index to RGBA using the palette
                pixels[destY * surface->w + x] = SDL_MapRGBA(
                    surface->format,
                    m_palette[colorIndex].r,
                    m_palette[colorIndex].g,
                    m_palette[colorIndex].b,
                    m_palette[colorIndex].a
                );
            }
        }
    }

    // Unlock the surface
    SDL_UnlockSurface(surface);

    // Set color key for transparency
    // This matches the original Delphi implementation: pdximg.surface.TransparentColor := 0;
    SDL_SetColorKey(surface, SDL_TRUE, SDL_MapRGBA(surface->format, 0, 0, 0, 0));

    return surface;
}

