# MirClient C++ with SDL

This is a refactored version of the MirClient using C++ with SDL for graphics rendering. The original client was written in Delphi with DirectX.

## Features

- Cross-platform support through SDL2
- Modern C++ implementation
- WIL file format support
- Sprite and animation system
- Game state management

## Dependencies

- SDL2
- SDL2_image
- SDL2_ttf
- SDL2_mixer
- CMake (for building)

## Building

### Windows

1. Install dependencies using vcpkg:
   ```
   vcpkg install sdl2 sdl2-image sdl2-ttf sdl2-mixer
   ```

2. Configure and build with CMake:
   ```
   mkdir build
   cd build
   cmake .. -DCMAKE_TOOLCHAIN_FILE=[path to vcpkg]/scripts/buildsystems/vcpkg.cmake
   cmake --build .
   ```

### Linux

1. Install dependencies:
   ```
   sudo apt-get install libsdl2-dev libsdl2-image-dev libsdl2-ttf-dev libsdl2-mixer-dev
   ```

2. Configure and build with <PERSON>Make:
   ```
   mkdir build
   cd build
   cmake ..
   make
   ```

### macOS

1. Install dependencies using Homebrew:
   ```
   brew install sdl2 sdl2_image sdl2_ttf sdl2_mixer
   ```

2. Configure and build with CMake:
   ```
   mkdir build
   cd build
   cmake ..
   make
   ```

## Project Structure

- `src/`: Source code
  - `Graphics/`: Graphics-related code
    - `WILLoader.h/cpp`: WIL file loader
    - `Texture.h/cpp`: Texture wrapper
    - `Sprite.h/cpp`: Sprite and animation system
  - `Application.h/cpp`: Main application class
  - `GameState.h`: Game state interface
  - `main.cpp`: Entry point
- `assets/`: Game assets
- `CMakeLists.txt`: CMake build configuration
- `README.md`: This file

## Usage

1. Build the project as described above
2. Copy your WIL files and other assets to the `assets` directory
3. Run the executable

## Implementation Details

### WIL File Format

The WIL file format is a custom image format used by the original Mir client. It contains multiple images in a single file, with each image having its own dimensions and offset.

The WILLoader class provides functionality to load WIL files and convert the images to SDL_Surfaces, which can then be used to create SDL_Textures for rendering.

### Texture System

The Texture class provides a wrapper around SDL_Texture with additional functionality for rendering and manipulation. It supports loading from files, SDL_Surfaces, and text.

### Sprite System

The Sprite class represents a game sprite, which is a graphical object that can be rendered on the screen. It can be animated or static.

The AnimatedSprite class extends Sprite to provide animation functionality by cycling through a series of frames.

### Game State System

The GameState interface defines the interface for game states. Each state (like intro, play, menu) implements this interface.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- The original MirClient developers
- SDL2 development team
