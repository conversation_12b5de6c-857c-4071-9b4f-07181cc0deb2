{"version": "0.2.0", "configurations": [{"name": "Debug MirClient", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/bin/MirClient.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": true, "MIMode": "gdb", "miDebuggerPath": "C:/msys64/mingw64/bin/gdb.exe", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "Build with <PERSON><PERSON>"}]}