#pragma once

// Mir200 Network Protocol Definitions
// Based on delphi/Common/Grobal2.pas - EXACT protocol numbers from original project
// DO NOT MODIFY THESE VALUES - They must match the original Delphi implementation

#include "Types.h"

// Client to Server Messages (CM_*)
constexpr WORD CM_QUERYCHR = 100;
constexpr WORD CM_NEWCHR = 101;
constexpr WORD CM_DELCHR = 102;
constexpr WORD CM_SELCHR = 103;
constexpr WORD CM_SELECTSERVER = 104;

constexpr WORD CM_QUERYUSERNAME = 80;
constexpr WORD CM_GETBACKPASSWORD = 2010;

// Item related
constexpr WORD CM_DROPITEM = 1000;
constexpr WORD CM_PICKUP = 1001;
constexpr WORD CM_TAKEONITEM = 1003;
constexpr WORD CM_TAKEOFFITEM = 1004;
constexpr WORD CM_1005 = 1005;
constexpr WORD CM_EAT = 1006;
constexpr WORD CM_BUTCH = 1007;
constexpr WORD CM_MAGICKEYCHANGE = 1008;

// NPC interaction
constexpr WORD CM_CLICKNPC = 1010;
constexpr WORD CM_MERCHANTDLGSELECT = 1011;
constexpr WORD CM_MERCHANTQUERYSELLPRICE = 1012;
constexpr WORD CM_USERSELLITEM = 1013;
constexpr WORD CM_USERBUYITEM = 1014;
constexpr WORD CM_USERGETDETAILITEM = 1015;
constexpr WORD CM_DROPGOLD = 1016;
constexpr WORD CM_LOGINNOTICEOK = 1018;

// Group related
constexpr WORD CM_GROUPMODE = 1019;
constexpr WORD CM_CREATEGROUP = 1020;
constexpr WORD CM_ADDGROUPMEMBER = 1021;
constexpr WORD CM_DELGROUPMEMBER = 1022;

// Repair and deal
constexpr WORD CM_USERREPAIRITEM = 1023;
constexpr WORD CM_MERCHANTQUERYREPAIRCOST = 1024;
constexpr WORD CM_DEALTRY = 1025;
constexpr WORD CM_DEALADDITEM = 1026;
constexpr WORD CM_DEALDELITEM = 1027;
constexpr WORD CM_DEALCANCEL = 1028;
constexpr WORD CM_DEALCHGGOLD = 1029;
constexpr WORD CM_DEALEND = 1030;

// Storage
constexpr WORD CM_USERSTORAGEITEM = 1031;
constexpr WORD CM_USERTAKEBACKSTORAGEITEM = 1032;
constexpr WORD CM_WANTMINIMAP = 1033;
constexpr WORD CM_USERMAKEDRUGITEM = 1034;

// Guild related
constexpr WORD CM_OPENGUILDDLG = 1035;
constexpr WORD CM_GUILDHOME = 1036;
constexpr WORD CM_GUILDMEMBERLIST = 1037;
constexpr WORD CM_GUILDADDMEMBER = 1038;
constexpr WORD CM_GUILDDELMEMBER = 1039;
constexpr WORD CM_GUILDUPDATENOTICE = 1040;
constexpr WORD CM_GUILDUPDATERANKINFO = 1041;
constexpr WORD CM_ADJUST_BONUS = 1043;

// Password
constexpr WORD CM_PASSWORD = 1105;
constexpr WORD CM_CHGPASSWORD = 1221;
constexpr WORD CM_SETPASSWORD = 1222;

// Movement and actions
constexpr WORD CM_TURN = 3010;
constexpr WORD CM_WALK = 3011;
constexpr WORD CM_SITDOWN = 3012;
constexpr WORD CM_RUN = 3013;
constexpr WORD CM_HIT = 3014;
constexpr WORD CM_SPELL = 3017;
constexpr WORD CM_THROW = 3005;
constexpr WORD CM_HORSERUN = 3009;

constexpr WORD CM_SPEEDHACKUSER = 10430;

// Server to Client Messages (SM_*)
// Action messages
constexpr WORD SM_RUSH = 6;
constexpr WORD SM_RUSHKUNG = 7;
constexpr WORD SM_FIREHIT = 8;
constexpr WORD SM_BACKSTEP = 9;
constexpr WORD SM_TURN = 10;
constexpr WORD SM_WALK = 11;
constexpr WORD SM_SITDOWN = 12;
constexpr WORD SM_RUN = 13;
constexpr WORD SM_HIT = 14;
constexpr WORD SM_HEAVYHIT = 15;
constexpr WORD SM_BIGHIT = 16;
constexpr WORD SM_SPELL = 17;
constexpr WORD SM_POWERHIT = 18;
constexpr WORD SM_LONGHIT = 19;
constexpr WORD SM_DIGUP = 20;
constexpr WORD SM_DIGDOWN = 21;
constexpr WORD SM_FLYAXE = 22;
constexpr WORD SM_LIGHTING = 23;
constexpr WORD SM_WIDEHIT = 24;
constexpr WORD SM_CRSHIT = 25;
constexpr WORD SM_TWINHIT = 26;

// Status messages
constexpr WORD SM_ALIVE = 27;
constexpr WORD SM_MOVEFAIL = 28;
constexpr WORD SM_HIDE = 29;
constexpr WORD SM_DISAPPEAR = 30;
constexpr WORD SM_STRUCK = 31;
constexpr WORD SM_DEATH = 32;
constexpr WORD SM_SKELETON = 33;
constexpr WORD SM_NOWDEATH = 34;

// Action ranges
constexpr WORD SM_ACTION_MIN = SM_RUSH;
constexpr WORD SM_ACTION_MAX = SM_WIDEHIT;
constexpr WORD SM_ACTION2_MIN = 65072;
constexpr WORD SM_ACTION2_MAX = 65073;

// Communication messages
constexpr WORD SM_HEAR = 40;
constexpr WORD SM_FEATURECHANGED = 41;
constexpr WORD SM_USERNAME = 42;
constexpr WORD SM_WINEXP = 44;
constexpr WORD SM_LEVELUP = 45;
constexpr WORD SM_DAYCHANGING = 46;

// Login and map
constexpr WORD SM_LOGON = 50;
constexpr WORD SM_NEWMAP = 51;
constexpr WORD SM_ABILITY = 52;
constexpr WORD SM_HEALTHSPELLCHANGED = 53;
constexpr WORD SM_MAPDESCRIPTION = 54;
constexpr WORD SM_SPELL2 = 117;

// Chat messages
constexpr WORD SM_SYSMESSAGE = 100;
constexpr WORD SM_GROUPMESSAGE = 101;
constexpr WORD SM_CRY = 102;
constexpr WORD SM_WHISPER = 103;
constexpr WORD SM_GUILDMESSAGE = 104;

// Item messages
constexpr WORD SM_ADDITEM = 200;
constexpr WORD SM_BAGITEMS = 201;
constexpr WORD SM_DELITEM = 202;
constexpr WORD SM_UPDATEITEM = 203;

// Magic messages
constexpr WORD SM_ADDMAGIC = 210;
constexpr WORD SM_SENDMYMAGIC = 211;
constexpr WORD SM_DELMAGIC = 212;

// Authentication messages
constexpr WORD SM_CERTIFICATION_FAIL = 501;
constexpr WORD SM_ID_NOTFOUND = 502;
constexpr WORD SM_PASSWD_FAIL = 503;
constexpr WORD SM_NEWID_SUCCESS = 504;
constexpr WORD SM_NEWID_FAIL = 505;
constexpr WORD SM_CHGPASSWD_SUCCESS = 506;
constexpr WORD SM_CHGPASSWD_FAIL = 507;
constexpr WORD SM_GETBACKPASSWD_SUCCESS = 508;
constexpr WORD SM_GETBACKPASSWD_FAIL = 509;

// Character messages
constexpr WORD SM_QUERYCHR = 520;
constexpr WORD SM_NEWCHR_SUCCESS = 521;
constexpr WORD SM_NEWCHR_FAIL = 522;
constexpr WORD SM_DELCHR_SUCCESS = 523;
constexpr WORD SM_DELCHR_FAIL = 524;
constexpr WORD SM_STARTPLAY = 525;
constexpr WORD SM_STARTFAIL = 526;
constexpr WORD SM_QUERYCHR_FAIL = 527;
constexpr WORD SM_OUTOFCONNECTION = 528;
constexpr WORD SM_PASSOK_SELECTSERVER = 529;
constexpr WORD SM_SELECTSERVER_OK = 530;
constexpr WORD SM_NEEDUPDATE_ACCOUNT = 531;
constexpr WORD SM_UPDATEID_SUCCESS = 532;
constexpr WORD SM_UPDATEID_FAIL = 533;

// Item operation messages
constexpr WORD SM_DROPITEM_SUCCESS = 600;
constexpr WORD SM_DROPITEM_FAIL = 601;

constexpr WORD SM_ITEMSHOW = 610;
constexpr WORD SM_ITEMHIDE = 611;
constexpr WORD SM_OPENDOOR_OK = 612;
constexpr WORD SM_OPENDOOR_LOCK = 613;
constexpr WORD SM_CLOSEDOOR = 614;
constexpr WORD SM_TAKEON_OK = 615;
constexpr WORD SM_TAKEON_FAIL = 616;
constexpr WORD SM_TAKEOFF_OK = 619;
constexpr WORD SM_TAKEOFF_FAIL = 620;
constexpr WORD SM_SENDUSEITEMS = 621;
constexpr WORD SM_WEIGHTCHANGED = 622;
constexpr WORD SM_CLEAROBJECTS = 633;
constexpr WORD SM_CHANGEMAP = 634;
constexpr WORD SM_EAT_OK = 635;
constexpr WORD SM_EAT_FAIL = 636;
constexpr WORD SM_BUTCH = 637;
constexpr WORD SM_MAGICFIRE = 638;
constexpr WORD SM_MAGICFIRE_FAIL = 639;
constexpr WORD SM_MAGIC_LVEXP = 640;
constexpr WORD SM_DURACHANGE = 642;

// Merchant messages
constexpr WORD SM_MERCHANTSAY = 643;
constexpr WORD SM_MERCHANTDLGCLOSE = 644;
constexpr WORD SM_SENDGOODSLIST = 645;
constexpr WORD SM_SENDUSERSELL = 646;
constexpr WORD SM_SENDBUYPRICE = 647;
constexpr WORD SM_USERSELLITEM_OK = 648;
constexpr WORD SM_USERSELLITEM_FAIL = 649;
constexpr WORD SM_BUYITEM_SUCCESS = 650;
constexpr WORD SM_BUYITEM_FAIL = 651;
constexpr WORD SM_SENDDETAILGOODSLIST = 652;
constexpr WORD SM_GOLDCHANGED = 653;
constexpr WORD SM_CHANGELIGHT = 654;
constexpr WORD SM_LAMPCHANGEDURA = 655;
constexpr WORD SM_CHANGENAMECOLOR = 656;
constexpr WORD SM_CHARSTATUSCHANGED = 657;
constexpr WORD SM_SENDNOTICE = 658;

// Group messages
constexpr WORD SM_GROUPMODECHANGED = 659;
constexpr WORD SM_CREATEGROUP_OK = 660;
constexpr WORD SM_CREATEGROUP_FAIL = 661;
constexpr WORD SM_GROUPADDMEM_OK = 662;
constexpr WORD SM_GROUPDELMEM_OK = 663;
constexpr WORD SM_GROUPADDMEM_FAIL = 664;
constexpr WORD SM_GROUPDELMEM_FAIL = 665;
constexpr WORD SM_GROUPCANCEL = 666;
constexpr WORD SM_GROUPMEMBERS = 667;

// Repair messages
constexpr WORD SM_SENDUSERREPAIR = 668;
constexpr WORD SM_USERREPAIRITEM_OK = 669;
constexpr WORD SM_USERREPAIRITEM_FAIL = 670;
constexpr WORD SM_SENDREPAIRCOST = 671;

// Deal messages
constexpr WORD SM_DEALMENU = 673;
constexpr WORD SM_DEALTRY_FAIL = 674;
constexpr WORD SM_DEALADDITEM_OK = 675;
constexpr WORD SM_DEALADDITEM_FAIL = 676;
constexpr WORD SM_DEALDELITEM_OK = 677;
constexpr WORD SM_DEALDELITEM_FAIL = 678;
constexpr WORD SM_DEALCANCEL = 681;
constexpr WORD SM_DEALREMOTEADDITEM = 682;
constexpr WORD SM_DEALREMOTEDELITEM = 683;
constexpr WORD SM_DEALCHGGOLD_OK = 684;
constexpr WORD SM_DEALCHGGOLD_FAIL = 685;
constexpr WORD SM_DEALREMOTECHGGOLD = 686;
constexpr WORD SM_DEALSUCCESS = 687;

// Storage messages
constexpr WORD SM_SENDUSERSTORAGEITEM = 700;
constexpr WORD SM_STORAGE_OK = 701;
constexpr WORD SM_STORAGE_FULL = 702;
constexpr WORD SM_STORAGE_FAIL = 703;
constexpr WORD SM_SAVEITEMLIST = 704;
constexpr WORD SM_TAKEBACKSTORAGEITEM_OK = 705;
constexpr WORD SM_TAKEBACKSTORAGEITEM_FAIL = 706;
constexpr WORD SM_TAKEBACKSTORAGEITEM_FULLBAG = 707;

constexpr WORD SM_AREASTATE = 708;
constexpr WORD SM_DELITEMS = 709;
constexpr WORD SM_READMINIMAP_OK = 710;
constexpr WORD SM_READMINIMAP_FAIL = 711;
constexpr WORD SM_SENDUSERMAKEDRUGITEMLIST = 712;
constexpr WORD SM_MAKEDRUG_SUCCESS = 713;
constexpr WORD SM_MAKEDRUG_FAIL = 65036;

// Guild messages
constexpr WORD SM_CHANGEGUILDNAME = 750;
constexpr WORD SM_SENDUSERSTATE = 751;
constexpr WORD SM_SUBABILITY = 752;
constexpr WORD SM_OPENGUILDDLG = 753;
constexpr WORD SM_OPENGUILDDLG_FAIL = 754;
constexpr WORD SM_SENDGUILDMEMBERLIST = 756;
constexpr WORD SM_GUILDADDMEMBER_OK = 757;
constexpr WORD SM_GUILDADDMEMBER_FAIL = 758;
constexpr WORD SM_GUILDDELMEMBER_OK = 759;
constexpr WORD SM_GUILDDELMEMBER_FAIL = 760;
constexpr WORD SM_GUILDRANKUPDATE_FAIL = 761;
constexpr WORD SM_BUILDGUILD_OK = 762;
constexpr WORD SM_BUILDGUILD_FAIL = 763;
constexpr WORD SM_DONATE_OK = 764;
constexpr WORD SM_DONATE_FAIL = 765;
constexpr WORD SM_MYSTATUS = 766;
constexpr WORD SM_MENU_OK = 767;
constexpr WORD SM_GUILDMAKEALLY_OK = 768;
constexpr WORD SM_GUILDMAKEALLY_FAIL = 769;
constexpr WORD SM_GUILDBREAKALLY_OK = 770;
constexpr WORD SM_GUILDBREAKALLY_FAIL = 771;
constexpr WORD SM_DLGMSG = 772;

// Special messages
constexpr WORD SM_SPACEMOVE_HIDE = 800;
constexpr WORD SM_SPACEMOVE_SHOW = 801;
constexpr WORD SM_RECONNECT = 802;
constexpr WORD SM_GHOST = 803;
constexpr WORD SM_SHOWEVENT = 804;
constexpr WORD SM_HIDEEVENT = 805;
constexpr WORD SM_SPACEMOVE_HIDE2 = 806;
constexpr WORD SM_SPACEMOVE_SHOW2 = 807;
constexpr WORD SM_TIMECHECK_MSG = 810;
constexpr WORD SM_ADJUST_BONUS = 811;

// Health and status
constexpr WORD SM_OPENHEALTH = 1100;
constexpr WORD SM_CLOSEHEALTH = 1101;
constexpr WORD SM_BREAKWEAPON = 1102;
constexpr WORD SM_INSTANCEHEALGUAGE = 1103;
constexpr WORD SM_CHANGEFACE = 1104;
constexpr WORD SM_VERSION_FAIL = 1106;

// Item update
constexpr WORD SM_ITEMUPDATE = 1500;
constexpr WORD SM_MONSTERSAY = 1501;

// Exchange
constexpr WORD SM_EXCHGTAKEON_OK = 65023;
constexpr WORD SM_EXCHGTAKEON_FAIL = 65024;

// Test and throw
constexpr WORD SM_TEST = 65037;
constexpr WORD SM_THROW = 65069;

// Message type enumeration
enum class MessageType : BYTE {
    NORMAL = 0,
    SYSTEM = 1,
    NOTICE = 2,
    GUILD = 3,
    GROUP = 4,
    WHISPER = 5
};

// Message color enumeration
enum class MessageColor : BYTE {
    RED = 0,
    GREEN = 1,
    BLUE = 2,
    YELLOW = 3,
    WHITE = 4,
    PURPLE = 5
};

// Protocol utility functions
inline bool IsActionMessage(WORD ident) {
    return (ident >= SM_ACTION_MIN && ident <= SM_ACTION_MAX) ||
           (ident >= SM_ACTION2_MIN && ident <= SM_ACTION2_MAX);
}

inline bool IsMovementMessage(WORD ident) {
    return ident == SM_TURN || ident == SM_WALK || ident == SM_RUN;
}

inline bool IsCombatMessage(WORD ident) {
    return ident == SM_HIT || ident == SM_HEAVYHIT || ident == SM_BIGHIT ||
           ident == SM_POWERHIT || ident == SM_LONGHIT || ident == SM_WIDEHIT ||
           ident == SM_SPELL || ident == SM_FIREHIT;
}
