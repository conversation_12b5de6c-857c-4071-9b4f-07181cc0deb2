# Build directories
build/
Build/
debug/
Debug/
release/
Release/
data/
out/
cmake-build-*/

# Object files
*.o
*.obj
*.ko
*.elf

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app
MirClient
MirClient.exe

# Log files
*.log
logs/
debug_log.txt
*.txt.log

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~
.cache/

# IDE specific files
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/c_cpp_properties.json
*.code-workspace

# Visual Studio
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates
*.vcxproj.filters
*.vcxproj.user
*.VC.db
*.VC.VC.opendb
ipch/
*.aps
*.ncb
*.opensdf
*.sdf
*.cachefile
*.psess
*.vsp
*.vspx

# CLion
.idea/
cmake-build-*/

# Qt Creator
*.autosave
*.pro.user*

# CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps/
Makefile

# Package files
*.zip
*.tar.gz
*.rar
*.7z

# OS specific files
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.AppleDouble
.LSOverride
._*

# Linux
.directory
.Trash-*

# Python (for any tools/scripts)
__pycache__/
*.py[cod]
*$py.class
*.pyc

# Backup files
*.bak
*.backup
*.old
~*

# Core dumps
core
core.*
*.stackdump

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Profiling
gmon.out
*.gcda
*.gcno
*.gcov

# Documentation generation
docs/_build/
doxygen/

# Test coverage
*.coverage
coverage/
htmlcov/

# Dependencies
vendor/
third_party/
external/

# Generated files
generated/
gen/

# Local configuration
local.config
*.local

# Game specific
MirServerId.DB
MirServerId.DB.idx
*.DB
*.idx

# Null file (seems to be a special case in your project)
null

# Keep important configuration files
!CMakeLists.txt
!*.cmake 
