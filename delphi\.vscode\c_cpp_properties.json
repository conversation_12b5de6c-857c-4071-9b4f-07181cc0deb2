{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/src/**", "C:/msys64/mingw64/include/**", "C:/msys64/mingw64/include/SDL2/**"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "compilerPath": "C:/msys64/mingw64/bin/g++.exe", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "gcc-x64", "configurationProvider": "ms-vscode.cmake-tools"}], "version": 4}