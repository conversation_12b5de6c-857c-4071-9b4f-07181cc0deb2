#include "ChatInput.h"
#include <algorithm>
#include <iostream>
#include <SDL2/SDL.h>

ChatInput::ChatInput(int x, int y, int width, int height, TTF_Font* font, int maxHistorySize, const std::string& name)
    : UIControl(x, y, width, height, name)
    , m_cursorPosition(0)
    , m_selectionStart(0)
    , m_selectionLength(0)
    , m_focused(false)
    , m_passwordMode(false)
    , m_passwordChar('*')
    , m_font(font)
    , m_historyPosition(-1)
    , m_maxHistorySize(maxHistorySize)
{
}

ChatInput::~ChatInput()
{
    // Font is managed externally
}

const std::string& ChatInput::GetText() const
{
    return m_text;
}

void ChatInput::SetText(const std::string& text)
{
    m_text = text;
    m_cursorPosition = static_cast<int>(m_text.length());
    m_selectionStart = m_cursorPosition;
    m_selectionLength = 0;
}

void ChatInput::SetPlaceholder(const std::string& placeholder)
{
    m_placeholder = placeholder;
}

void ChatInput::SetPasswordMode(bool passwordMode, char passwordChar)
{
    m_passwordMode = passwordMode;
    m_passwordChar = passwordChar;
}

void ChatInput::SetBackgroundTexture(std::shared_ptr<Texture> texture)
{
    m_backgroundTexture = texture;
}

void ChatInput::SetOnSubmit(std::function<void(const std::string&)> callback)
{
    m_onSubmit = callback;
}

void ChatInput::Clear()
{
    m_text.clear();
    m_cursorPosition = 0;
    m_selectionStart = 0;
    m_selectionLength = 0;
}

void ChatInput::Focus()
{
    m_focused = true;
    SDL_StartTextInput();
}

void ChatInput::Unfocus()
{
    m_focused = false;
    SDL_StopTextInput();
}

bool ChatInput::IsFocused() const
{
    return m_focused;
}

void ChatInput::InsertText(const std::string& text)
{
    if (m_selectionLength > 0) {
        DeleteSelection();
    }

    m_text.insert(m_cursorPosition, text);
    m_cursorPosition += static_cast<int>(text.length());
    m_selectionStart = m_cursorPosition;
}

void ChatInput::DeleteSelection()
{
    if (m_selectionLength > 0) {
        int start = std::min(m_selectionStart, m_selectionStart + m_selectionLength);
        int length = std::abs(m_selectionLength);

        m_text.erase(start, length);
        m_cursorPosition = start;
        m_selectionStart = m_cursorPosition;
        m_selectionLength = 0;
    }
}

void ChatInput::MoveCursorLeft(bool select)
{
    if (m_cursorPosition > 0) {
        m_cursorPosition--;
    }

    if (!select) {
        m_selectionStart = m_cursorPosition;
        m_selectionLength = 0;
    } else {
        m_selectionLength = m_cursorPosition - m_selectionStart;
    }
}

void ChatInput::MoveCursorRight(bool select)
{
    if (m_cursorPosition < static_cast<int>(m_text.length())) {
        m_cursorPosition++;
    }

    if (!select) {
        m_selectionStart = m_cursorPosition;
        m_selectionLength = 0;
    } else {
        m_selectionLength = m_cursorPosition - m_selectionStart;
    }
}

void ChatInput::MoveCursorToStart(bool select)
{
    m_cursorPosition = 0;

    if (!select) {
        m_selectionStart = m_cursorPosition;
        m_selectionLength = 0;
    } else {
        m_selectionLength = m_cursorPosition - m_selectionStart;
    }
}

void ChatInput::MoveCursorToEnd(bool select)
{
    m_cursorPosition = static_cast<int>(m_text.length());

    if (!select) {
        m_selectionStart = m_cursorPosition;
        m_selectionLength = 0;
    } else {
        m_selectionLength = m_cursorPosition - m_selectionStart;
    }
}

void ChatInput::AddToHistory()
{
    if (m_text.empty()) {
        return;
    }

    // Remove duplicates
    auto it = std::find(m_history.begin(), m_history.end(), m_text);
    if (it != m_history.end()) {
        m_history.erase(it);
    }

    // Add to history
    m_history.insert(m_history.begin(), m_text);

    // Limit history size
    if (m_history.size() > m_maxHistorySize) {
        m_history.pop_back();
    }

    // Reset history position
    m_historyPosition = -1;
}

void ChatInput::HistoryUp()
{
    if (m_history.empty()) {
        return;
    }

    if (m_historyPosition < static_cast<int>(m_history.size()) - 1) {
        m_historyPosition++;
        SetText(m_history[m_historyPosition]);
    }
}

void ChatInput::HistoryDown()
{
    if (m_historyPosition > 0) {
        m_historyPosition--;
        SetText(m_history[m_historyPosition]);
    } else if (m_historyPosition == 0) {
        m_historyPosition = -1;
        Clear();
    }
}

std::shared_ptr<Texture> ChatInput::CreateTextTexture()
{
    if (!m_font) {
        return nullptr;
    }

    std::string displayText;

    if (m_text.empty() && !m_placeholder.empty() && !m_focused) {
        // Show placeholder
        displayText = m_placeholder;
    } else if (m_passwordMode) {
        // Show password
        displayText = std::string(m_text.length(), m_passwordChar);
    } else {
        // Show normal text
        displayText = m_text;
    }

    // Create texture
    SDL_Color textColor = {255, 255, 255, 255}; // White

    if (m_text.empty() && !m_placeholder.empty() && !m_focused) {
        textColor = {128, 128, 128, 255}; // Gray for placeholder
    }

    SDL_Surface* surface = TTF_RenderText_Blended(m_font, displayText.c_str(), textColor);
    if (!surface) {
        std::cerr << "Failed to render text: " << TTF_GetError() << std::endl;
        return nullptr;
    }

    // Create texture from surface
    std::shared_ptr<Texture> texture = std::make_shared<Texture>();
    texture->LoadFromSurface(surface);

    // Free surface
    SDL_FreeSurface(surface);

    return texture;
}

void ChatInput::Update(int deltaTime)
{
    // Nothing to update
}

void ChatInput::Render(SDL_Renderer* renderer)
{
    if (!IsVisible()) {
        return;
    }

    // Render background
    if (m_backgroundTexture) {
        m_backgroundTexture->Render(m_x, m_y);
    } else {
        // Render a default background
        SDL_Rect rect = {m_x, m_y, m_width, m_height};
        SDL_SetRenderDrawColor(renderer, 0, 0, 0, 200);
        SDL_RenderFillRect(renderer, &rect);

        SDL_SetRenderDrawColor(renderer, 200, 200, 200, 255);
        SDL_RenderDrawRect(renderer, &rect);
    }

    // Render text
    std::shared_ptr<Texture> textTexture = CreateTextTexture();
    if (textTexture) {
        textTexture->Render(m_x + 5, m_y + (m_height - textTexture->GetHeight()) / 2);
    }

    // Render cursor if focused
    if (m_focused) {
        // Calculate cursor position
        int cursorX = m_x + 5;

        if (!m_text.empty()) {
            std::string textBeforeCursor;

            if (m_passwordMode) {
                textBeforeCursor = std::string(m_cursorPosition, m_passwordChar);
            } else {
                textBeforeCursor = m_text.substr(0, m_cursorPosition);
            }

            int textWidth;
            TTF_SizeText(m_font, textBeforeCursor.c_str(), &textWidth, nullptr);
            cursorX += textWidth;
        }

        // Render cursor
        SDL_SetRenderDrawColor(renderer, 255, 255, 255, 255);
        SDL_RenderDrawLine(renderer, cursorX, m_y + 5, cursorX, m_y + m_height - 5);
    }

    // Render children
    UIControl::Render(renderer);
}

bool ChatInput::OnKeyDown(const SDL_KeyboardEvent& event)
{
    if (!m_focused) {
        return false;
    }

    switch (event.keysym.sym) {
        case SDLK_RETURN:
        case SDLK_KP_ENTER:
            if (m_onSubmit) {
                AddToHistory();
                m_onSubmit(m_text);
                Clear();
            }
            return true;

        case SDLK_ESCAPE:
            Unfocus();
            return true;

        case SDLK_BACKSPACE:
            if (m_selectionLength > 0) {
                DeleteSelection();
            } else if (m_cursorPosition > 0) {
                m_text.erase(m_cursorPosition - 1, 1);
                m_cursorPosition--;
                m_selectionStart = m_cursorPosition;
            }
            return true;

        case SDLK_DELETE:
            if (m_selectionLength > 0) {
                DeleteSelection();
            } else if (m_cursorPosition < static_cast<int>(m_text.length())) {
                m_text.erase(m_cursorPosition, 1);
            }
            return true;

        case SDLK_LEFT:
            MoveCursorLeft(SDL_GetModState() & KMOD_SHIFT);
            return true;

        case SDLK_RIGHT:
            MoveCursorRight(SDL_GetModState() & KMOD_SHIFT);
            return true;

        case SDLK_HOME:
            MoveCursorToStart(SDL_GetModState() & KMOD_SHIFT);
            return true;

        case SDLK_END:
            MoveCursorToEnd(SDL_GetModState() & KMOD_SHIFT);
            return true;

        case SDLK_UP:
            HistoryUp();
            return true;

        case SDLK_DOWN:
            HistoryDown();
            return true;

        case SDLK_a:
            if (SDL_GetModState() & KMOD_CTRL) {
                // Select all
                m_selectionStart = 0;
                m_cursorPosition = static_cast<int>(m_text.length());
                m_selectionLength = m_cursorPosition;
                return true;
            }
            break;

        case SDLK_c:
            if (SDL_GetModState() & KMOD_CTRL) {
                // Copy
                if (m_selectionLength > 0) {
                    int start = std::min(m_selectionStart, m_selectionStart + m_selectionLength);
                    int length = std::abs(m_selectionLength);

                    std::string selectedText = m_text.substr(start, length);
                    SDL_SetClipboardText(selectedText.c_str());
                }
                return true;
            }
            break;

        case SDLK_v:
            if (SDL_GetModState() & KMOD_CTRL) {
                // Paste
                char* clipboardText = SDL_GetClipboardText();
                if (clipboardText) {
                    InsertText(clipboardText);
                    SDL_free(clipboardText);
                }
                return true;
            }
            break;

        case SDLK_x:
            if (SDL_GetModState() & KMOD_CTRL) {
                // Cut
                if (m_selectionLength > 0) {
                    int start = std::min(m_selectionStart, m_selectionStart + m_selectionLength);
                    int length = std::abs(m_selectionLength);

                    std::string selectedText = m_text.substr(start, length);
                    SDL_SetClipboardText(selectedText.c_str());

                    DeleteSelection();
                }
                return true;
            }
            break;
    }

    return false;
}

bool ChatInput::OnTextInput(const SDL_TextInputEvent& event)
{
    if (!m_focused) {
        return false;
    }

    // Ignore text input events generated by key combinations
    if (SDL_GetModState() & (KMOD_CTRL | KMOD_ALT)) {
        return false;
    }

    InsertText(event.text);
    return true;
}

bool ChatInput::OnMouseDown(const SDL_MouseButtonEvent& event)
{
    if (event.button != SDL_BUTTON_LEFT) {
        return false;
    }

    // Check if mouse is over the input
    if (event.x >= m_x && event.x < m_x + m_width &&
        event.y >= m_y && event.y < m_y + m_height) {
        // Focus the input
        Focus();

        // Calculate cursor position
        if (!m_text.empty() && m_font) {
            int relativeX = event.x - m_x - 5;
            int bestPosition = 0;
            int bestDistance = INT_MAX;

            // Find the closest character position
            for (int i = 0; i <= static_cast<int>(m_text.length()); i++) {
                std::string textBeforeCursor;

                if (m_passwordMode) {
                    textBeforeCursor = std::string(i, m_passwordChar);
                } else {
                    textBeforeCursor = m_text.substr(0, i);
                }

                int textWidth;
                TTF_SizeText(m_font, textBeforeCursor.c_str(), &textWidth, nullptr);

                int distance = std::abs(relativeX - textWidth);
                if (distance < bestDistance) {
                    bestDistance = distance;
                    bestPosition = i;
                }
            }

            m_cursorPosition = bestPosition;
            m_selectionStart = m_cursorPosition;
            m_selectionLength = 0;
        } else {
            m_cursorPosition = 0;
            m_selectionStart = 0;
            m_selectionLength = 0;
        }

        return true;
    } else {
        // Unfocus if clicked outside
        Unfocus();
    }

    return false;
}

bool ChatInput::OnMouseUp(const SDL_MouseButtonEvent& event)
{
    return false;
}

bool ChatInput::OnMouseMove(const SDL_MouseMotionEvent& event)
{
    return false;
}

