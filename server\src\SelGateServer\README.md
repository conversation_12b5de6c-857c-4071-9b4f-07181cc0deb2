# SelGateServer - 选择网关服务器

## 概述

SelGateServer 是基于原Delphi ESelGate重构的选择网关服务器，采用现代C++实现。它的主要功能是作为负载均衡器，为客户端选择最合适的RunGate（游戏网关）服务器进行连接。

## 功能特性

### 核心功能
- **负载均衡**: 支持多种负载均衡策略（轮询、最少连接、负载均衡、随机）
- **服务器管理**: 动态管理多个RunGate服务器的状态和负载
- **健康检查**: 定期检查RunGate服务器的运行状态
- **连接分发**: 根据策略将客户端连接分配到最合适的RunGate

### 安全功能
- **IP过滤**: 支持IP黑名单和白名单功能
- **连接限制**: 限制每个IP的最大连接数
- **攻击防护**: 检测和阻止恶意连接攻击
- **多种阻挡方式**: 支持断开连接、临时阻挡、永久阻挡

### 监控功能
- **实时统计**: 显示当前连接数、活跃服务器数等统计信息
- **日志记录**: 详细的操作日志和错误日志
- **状态监控**: 实时监控各RunGate服务器的状态

## 与原Delphi版本的对应关系

| 原Delphi功能 | C++重构版本 | 说明 |
|-------------|------------|------|
| TUserSession | ClientSession | 客户端会话管理 |
| TSessionArray | std::array<ClientSession> | 会话数组 |
| RunGate服务器列表 | std::vector<RunGateInfo> | 服务器信息管理 |
| IP阻挡列表 | std::vector<IPAddressInfo> | IP过滤功能 |
| 负载均衡算法 | SelectBestRunGate() | 服务器选择逻辑 |
| 配置管理 | INI文件解析 | 配置文件处理 |
| 网络通信 | NetworkManager | 网络连接管理 |

## 架构设计

```
SelGateServer
├── 网络层 (NetworkManager)
├── 会话管理 (ClientSession)
├── 服务器管理 (RunGateInfo)
├── 负载均衡 (SelectStrategy)
├── IP过滤 (IPAddressInfo)
└── 配置管理 (Config Files)
```

## 配置文件

### SelGate.ini
主配置文件，包含服务器基本设置：
```ini
[SelGate]
ServerName=选择网关
ListenIP=0.0.0.0
ListenPort=7100
MaxConnOfIP=10
SessionTimeOut=300000
HeartbeatInterval=30
CheckTimeOut=60
BlockMethod=1
SelectStrategy=1
EnableLoadBalance=1
EnableIPFilter=1
```

### RunGateList.txt
RunGate服务器列表配置：
```
# 格式：ServerName|ServerIP|ServerPort|GateIP|GatePort|MaxUsers|Enabled
RunGate1|127.0.0.1|5000|127.0.0.1|7200|1000|1
RunGate2|127.0.0.1|5001|127.0.0.1|7201|1000|1
```

### BlockIPList.txt
IP阻挡列表：
```
*************
*********
```

## 负载均衡策略

### 1. 轮询 (Round Robin)
- 按顺序轮流分配客户端到各个RunGate服务器
- 适用于服务器性能相近的情况

### 2. 最少连接 (Least Connections)
- 将客户端分配到当前连接数最少的服务器
- 适用于连接持续时间差异较大的情况

### 3. 负载均衡 (Load Balance)
- 根据服务器的负载比例（当前用户数/最大用户数）进行分配
- 综合考虑服务器容量和当前负载

### 4. 随机 (Random)
- 随机选择可用的服务器
- 简单但有效的分配方式

## 使用方法

### 编译
```bash
cd server/build
cmake ..
make SelGateServer
```

### 运行
```bash
# 使用默认配置文件
./SelGateServer

# 指定配置文件
./SelGateServer -c custom_config.ini

# 显示帮助信息
./SelGateServer -h

# 显示版本信息
./SelGateServer -v
```

### 管理RunGate服务器

#### 添加服务器
编辑 `RunGateList.txt` 文件，添加新的服务器信息。

#### 启用/禁用服务器
修改服务器配置行的最后一个字段：
- `1` = 启用
- `0` = 禁用

#### 动态重新加载配置
发送SIGHUP信号到进程（Linux/Unix）或重启服务器（Windows）。

## 监控和日志

### 日志文件
- `SelGateServer.log`: 主日志文件
- 包含启动、停止、连接、错误等信息

### 实时监控
服务器运行时会定期输出状态信息：
```
=== Server Status ===
Sessions: 150
Active RunGates: 3/5
Total Connections: 2500
===================
```

## 性能调优

### 连接数优化
- 调整 `MaxConnOfIP` 限制单IP连接数
- 根据硬件性能调整 `MAX_SESSIONS` 常量

### 检查间隔优化
- `HeartbeatInterval`: 心跳检查间隔，影响故障检测速度
- `CheckTimeOut`: 检查超时时间，影响故障判断准确性

### 内存优化
- 会话数组使用固定大小，避免动态分配开销
- IP列表使用高效的数据结构

## 故障排除

### 常见问题

1. **无法启动服务器**
   - 检查端口是否被占用
   - 确认配置文件格式正确
   - 查看日志文件获取详细错误信息

2. **RunGate服务器显示离线**
   - 检查网络连接
   - 确认RunGate服务器正在运行
   - 调整心跳检查间隔

3. **客户端无法连接**
   - 检查防火墙设置
   - 确认监听端口配置正确
   - 查看IP过滤设置

### 调试模式
编译时使用Debug模式可获得更详细的日志信息：
```bash
cmake -DCMAKE_BUILD_TYPE=Debug ..
make SelGateServer
```

## 开发说明

### 代码结构
```
SelGateServer/
├── SelGateServer.h       # 主类定义
├── SelGateServer.cpp     # 主类实现
├── main.cpp             # 程序入口
├── CMakeLists.txt       # CMake配置
├── SelGate.ini.in      # 配置文件模板
└── README.md           # 文档
```

### 扩展功能
- 添加新的负载均衡策略
- 扩展监控和统计功能
- 添加Web管理界面
- 支持更多的配置选项

### 线程安全
- 所有共享数据都使用互斥锁保护
- 使用原子变量存储简单状态
- 网络操作在独立线程中进行

## 版本历史

### v1.0.0
- 基于原Delphi ESelGate重构
- 实现核心负载均衡功能
- 支持多种服务器选择策略
- 完整的IP过滤和安全功能
- 跨平台支持（Windows/Linux）

## 许可证

本项目基于传奇私服开源项目，遵循相应的开源许可证。 