#include "IniFile.h"
#include <iostream>
#include <cctype>

namespace MirServer {

IniFile::IniFile() 
    : m_commentChars(";#"), m_autoSave(false) {
}

IniFile::IniFile(const std::string& filename) 
    : m_filename(filename), m_commentChars(";#"), m_autoSave(false) {
    LoadFromFile(filename);
}

IniFile::~IniFile() {
    if (m_autoSave && !m_filename.empty()) {
        SaveToFile();
    }
}

bool IniFile::LoadFromFile(const std::string& filename) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!filename.empty()) {
        m_filename = filename;
    }
    
    std::ifstream file(m_filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open INI file: " << m_filename << std::endl;
        return false;
    }
    
    m_data.clear();
    std::string line;
    std::string currentSection;
    
    while (std::getline(file, line)) {
        // 去除BOM标记（如果存在）
        if (!line.empty() && line[0] == '\xEF') {
            if (line.length() >= 3 && line[1] == '\xBB' && line[2] == '\xBF') {
                line = line.substr(3);
            }
        }
        
        line = Trim(line);
        
        // 跳过空行和注释行
        if (line.empty() || IsCommentLine(line)) {
            continue;
        }
        
        // 检查是否是节标题 [section]
        if (line.front() == '[' && line.back() == ']') {
            currentSection = line.substr(1, line.length() - 2);
            currentSection = Trim(currentSection);
            // 确保节存在（即使为空）
            if (m_data.find(currentSection) == m_data.end()) {
                m_data[currentSection] = KeyValueMap();
            }
            continue;
        }
        
        // 解析键值对 key=value
        std::string section, key, value;
        if (ParseLine(line, section, key, value)) {
            if (!currentSection.empty()) {
                m_data[currentSection][key] = UnescapeValue(value);
            }
        }
    }
    
    file.close();
    return true;
}

bool IniFile::SaveToFile(const std::string& filename) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    std::string targetFile = filename.empty() ? m_filename : filename;
    if (targetFile.empty()) {
        std::cerr << "No filename specified for saving INI file" << std::endl;
        return false;
    }
    
    std::ofstream file(targetFile);
    if (!file.is_open()) {
        std::cerr << "Failed to open INI file for writing: " << targetFile << std::endl;
        return false;
    }
    
    // 写入所有节和键值对
    for (const auto& sectionPair : m_data) {
        const std::string& sectionName = sectionPair.first;
        const KeyValueMap& keys = sectionPair.second;
        
        // 写入节标题
        if (!sectionName.empty()) {
            file << "[" << sectionName << "]" << std::endl;
        }
        
        // 写入该节的所有键值对
        for (const auto& keyPair : keys) {
            const std::string& key = keyPair.first;
            const std::string& value = keyPair.second;
            file << key << "=" << EscapeValue(value) << std::endl;
        }
        
        // 节之间添加空行（除了最后一个节）
        file << std::endl;
    }
    
    file.close();
    
    if (!filename.empty()) {
        m_filename = filename;
    }
    
    return true;
}

bool IniFile::SaveToFile(const std::string& filename) const {
    // 使用const_cast来调用非const版本，因为文件操作本身不改变对象状态
    return const_cast<IniFile*>(this)->SaveToFile(filename);
}

void IniFile::Clear() {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_data.clear();
}

std::string IniFile::GetString(const std::string& section, const std::string& key, const std::string& defaultValue) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto sectionIt = m_data.find(section);
    if (sectionIt == m_data.end()) {
        return defaultValue;
    }
    
    auto keyIt = sectionIt->second.find(key);
    if (keyIt == sectionIt->second.end()) {
        return defaultValue;
    }
    
    return keyIt->second;
}

int IniFile::GetInt(const std::string& section, const std::string& key, int defaultValue) const {
    std::string value = GetString(section, key);
    if (value.empty()) {
        return defaultValue;
    }
    
    try {
        return std::stoi(value);
    } catch (const std::exception&) {
        return defaultValue;
    }
}

bool IniFile::GetBool(const std::string& section, const std::string& key, bool defaultValue) const {
    std::string value = GetString(section, key);
    if (value.empty()) {
        return defaultValue;
    }
    
    // 转换为小写进行比较
    std::string lowerValue = ToLower(value);
    
    if (lowerValue == "true" || lowerValue == "yes" || lowerValue == "1" || lowerValue == "on") {
        return true;
    } else if (lowerValue == "false" || lowerValue == "no" || lowerValue == "0" || lowerValue == "off") {
        return false;
    }
    
    return defaultValue;
}

double IniFile::GetDouble(const std::string& section, const std::string& key, double defaultValue) const {
    std::string value = GetString(section, key);
    if (value.empty()) {
        return defaultValue;
    }
    
    try {
        return std::stod(value);
    } catch (const std::exception&) {
        return defaultValue;
    }
}

void IniFile::SetString(const std::string& section, const std::string& key, const std::string& value) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_data[section][key] = value;
    
    if (m_autoSave && !m_filename.empty()) {
        // 解锁后保存（避免递归锁定）
        lock.~lock_guard();
        SaveToFile();
    }
}

void IniFile::SetInt(const std::string& section, const std::string& key, int value) {
    SetString(section, key, std::to_string(value));
}

void IniFile::SetBool(const std::string& section, const std::string& key, bool value) {
    SetString(section, key, value ? "true" : "false");
}

void IniFile::SetDouble(const std::string& section, const std::string& key, double value) {
    SetString(section, key, std::to_string(value));
}

bool IniFile::HasSection(const std::string& section) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_data.find(section) != m_data.end();
}

bool IniFile::HasKey(const std::string& section, const std::string& key) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto sectionIt = m_data.find(section);
    if (sectionIt == m_data.end()) {
        return false;
    }
    
    return sectionIt->second.find(key) != sectionIt->second.end();
}

bool IniFile::RemoveSection(const std::string& section) {
    std::lock_guard<std::mutex> lock(m_mutex);
    auto it = m_data.find(section);
    if (it != m_data.end()) {
        m_data.erase(it);
        return true;
    }
    return false;
}

bool IniFile::RemoveKey(const std::string& section, const std::string& key) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto sectionIt = m_data.find(section);
    if (sectionIt == m_data.end()) {
        return false;
    }
    
    auto keyIt = sectionIt->second.find(key);
    if (keyIt != sectionIt->second.end()) {
        sectionIt->second.erase(keyIt);
        return true;
    }
    
    return false;
}

std::vector<std::string> IniFile::GetSectionNames() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    std::vector<std::string> sections;
    
    for (const auto& pair : m_data) {
        sections.push_back(pair.first);
    }
    
    return sections;
}

std::vector<std::string> IniFile::GetKeyNames(const std::string& section) const {
    std::lock_guard<std::mutex> lock(m_mutex);
    std::vector<std::string> keys;
    
    auto sectionIt = m_data.find(section);
    if (sectionIt != m_data.end()) {
        for (const auto& pair : sectionIt->second) {
            keys.push_back(pair.first);
        }
    }
    
    return keys;
}

// 私有辅助方法实现
std::string IniFile::Trim(const std::string& str) const {
    auto start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) {
        return "";
    }
    
    auto end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

std::string IniFile::ToLower(const std::string& str) const {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), 
                   [](unsigned char c) { return std::tolower(c); });
    return result;
}

bool IniFile::IsCommentLine(const std::string& line) const {
    if (line.empty()) {
        return false;
    }
    
    return m_commentChars.find(line[0]) != std::string::npos;
}

bool IniFile::ParseLine(const std::string& line, std::string& section, 
                       std::string& key, std::string& value) const {
    size_t equalPos = line.find('=');
    if (equalPos == std::string::npos) {
        return false;
    }
    
    key = Trim(line.substr(0, equalPos));
    value = Trim(line.substr(equalPos + 1));
    
    return !key.empty();
}

std::string IniFile::EscapeValue(const std::string& value) const {
    // 简单的转义处理，可以根据需要扩展
    std::string result = value;
    
    // 如果值包含特殊字符，用引号包围
    if (result.find_first_of("\r\n\t") != std::string::npos) {
        result = "\"" + result + "\"";
    }
    
    return result;
}

std::string IniFile::UnescapeValue(const std::string& value) const {
    std::string result = value;
    
    // 移除首尾的引号
    if (result.length() >= 2 && result.front() == '"' && result.back() == '"') {
        result = result.substr(1, result.length() - 2);
    }
    
    return result;
}

} // namespace MirServer 