#include "Label.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>

Label::Label(int x, int y, int width, int height, const std::string& text, const std::string& name)
    : UIControl(x, y, width, height, name)
    , m_text(text)
    , m_font(nullptr)
    , m_alignment(TextAlignment::LEFT)
{
    // Set default text color
    m_textColor = {255, 255, 255, 255};  // White
}

Label::Label(int x, int y, const std::string& text, const std::string& name)
    : UIControl(x, y, name)  // Use the UIControl constructor that sets width and height to 0
    , m_text(text)
    , m_font(nullptr)
    , m_alignment(TextAlignment::LEFT)
{
    // Set default text color
    m_textColor = {255, 255, 255, 255};  // White
}

Label::~Label()
{
}

void Label::Render(SDL_Renderer* renderer)
{
    // Skip if not visible
    if (!m_visible) {
        return;
    }

    // 如有需要，重建纹理
    if (m_dirty) {
        CreateTexture(renderer);
        m_dirty = false;
    }

    // First try to use the resource indices from UIControl for background
    if (!m_resourceFile.empty() && m_wilManager && m_normalImageIndex >= 0 && m_normalImageIndex != NO_IMAGE) {
        // Let the base class handle rendering using resource indices
        UIControl::Render(renderer);
    } else {
        // Render children (only if we didn't call UIControl::Render already)
        for (auto& child : m_children) {
            if (child->IsVisible()) {
                child->Render(renderer);
            }
        }
    }

    // Always render the text on top
    if (m_texture) {
        int x = m_x;

        // Adjust x position based on alignment
        if (m_alignment == TextAlignment::CENTER) {
            x = m_x + (m_width - m_texture->GetWidth()) / 2;
        } else if (m_alignment == TextAlignment::RIGHT) {
            x = m_x + m_width - m_texture->GetWidth();
        }

        m_texture->Render(x, m_y);
    }
}

void Label::SetText(const std::string& text)
{
    m_text = text;
    m_dirty = true;
}

void Label::SetTextColor(const SDL_Color& color)
{
    m_textColor = color;
    m_dirty = true;
}

void Label::SetFont(TTF_Font* font)
{
    m_font = font;
    m_dirty = true;
}

void Label::CreateTexture(SDL_Renderer* renderer)
{
    // Skip if no text or font
    if (m_text.empty() || !m_font) {
        m_texture.reset();
        m_width = 0;
        m_height = 0;
        return;
    }
    m_texture = std::make_shared<Texture>(renderer);
    if (!m_texture->LoadFromText(m_text, m_font, m_textColor)) {
        std::cerr << "Failed to create text texture: " << TTF_GetError() << std::endl;
        m_texture.reset();
        m_width = 0;
        m_height = 0;
    } else {
        if (m_width == 0) {
            m_width = m_texture->GetWidth();
        }
        if (m_height == 0) {
            m_height = m_texture->GetHeight();
        }
    }
}

