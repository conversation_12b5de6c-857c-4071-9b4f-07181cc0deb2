#pragma once

#include "UIControl.h"
#include "../Item/Inventory.h"
#include <memory>
#include <functional>
#include <SDL2/SDL.h>
#include <SDL2/SDL_ttf.h>

/**
 * @class InventoryWindow
 * @brief UI window for displaying and interacting with the player's inventory
 */
class InventoryWindow : public UIControl {
private:
    std::shared_ptr<Inventory> m_inventory;  ///< Reference to the player's inventory
    TTF_Font* m_font;                        ///< Font for rendering text
    SDL_Color m_textColor;                   ///< Text color
    SDL_Color m_backgroundColor;             ///< Background color
    SDL_Color m_borderColor;                 ///< Border color
    SDL_Color m_slotColor;                   ///< Slot background color
    SDL_Color m_selectedSlotColor;           ///< Selected slot background color
    int m_slotSize;                          ///< Size of each inventory slot
    int m_slotSpacing;                       ///< Spacing between slots
    int m_slotsPerRow;                       ///< Number of slots per row
    int m_selectedSlot;                      ///< Currently selected slot
    int m_draggedSlot;                       ///< Slot being dragged
    bool m_isDragging;                       ///< Whether a slot is being dragged
    int m_dragX;                             ///< X position of the dragged item
    int m_dragY;                             ///< Y position of the dragged item
    std::string m_resourceFile;              ///< Resource file for item images

    // Callbacks
    std::function<void(int)> m_onItemSelected;  ///< Called when an item is selected
    std::function<void(int)> m_onItemUsed;      ///< Called when an item is used
    std::function<void(int)> m_onItemDropped;   ///< Called when an item is dropped
    std::function<void(int, int)> m_onItemMoved;///< Called when an item is moved

public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param name Name
     */
    InventoryWindow(int x, int y, int width, int height, const std::string& name = "InventoryWindow");

    /**
     * @brief Destructor
     */
    virtual ~InventoryWindow();

    /**
     * @brief Set the inventory
     * @param inventory Inventory
     */
    void SetInventory(std::shared_ptr<Inventory> inventory) { m_inventory = inventory; }

    /**
     * @brief Get the inventory
     * @return Inventory
     */
    std::shared_ptr<Inventory> GetInventory() const { return m_inventory; }

    /**
     * @brief Set the font
     * @param font Font
     */
    void SetFont(TTF_Font* font) { m_font = font; }

    /**
     * @brief Set the text color
     * @param color Text color
     */
    void SetTextColor(const SDL_Color& color) { m_textColor = color; }

    /**
     * @brief Set the background color
     * @param color Background color
     */
    void SetBackgroundColor(const SDL_Color& color) { m_backgroundColor = color; }

    /**
     * @brief Set the border color
     * @param color Border color
     */
    void SetBorderColor(const SDL_Color& color) { m_borderColor = color; }

    /**
     * @brief Set the slot color
     * @param color Slot color
     */
    void SetSlotColor(const SDL_Color& color) { m_slotColor = color; }

    /**
     * @brief Set the selected slot color
     * @param color Selected slot color
     */
    void SetSelectedSlotColor(const SDL_Color& color) { m_selectedSlotColor = color; }

    /**
     * @brief Set the slot size
     * @param size Slot size
     */
    void SetSlotSize(int size) { m_slotSize = size; }

    /**
     * @brief Set the slot spacing
     * @param spacing Slot spacing
     */
    void SetSlotSpacing(int spacing) { m_slotSpacing = spacing; }

    /**
     * @brief Set the number of slots per row
     * @param slotsPerRow Number of slots per row
     */
    void SetSlotsPerRow(int slotsPerRow) { m_slotsPerRow = slotsPerRow; }

    /**
     * @brief Set the resource file for item images
     * @param resourceFile Resource file
     */
    void SetResourceFile(const std::string& resourceFile) { m_resourceFile = resourceFile; }

    /**
     * @brief Set the callback for when an item is selected
     * @param callback Callback function
     */
    void SetOnItemSelected(std::function<void(int)> callback) { m_onItemSelected = callback; }

    /**
     * @brief Set the callback for when an item is used
     * @param callback Callback function
     */
    void SetOnItemUsed(std::function<void(int)> callback) { m_onItemUsed = callback; }

    /**
     * @brief Set the callback for when an item is dropped
     * @param callback Callback function
     */
    void SetOnItemDropped(std::function<void(int)> callback) { m_onItemDropped = callback; }

    /**
     * @brief Set the callback for when an item is moved
     * @param callback Callback function
     */
    void SetOnItemMoved(std::function<void(int, int)> callback) { m_onItemMoved = callback; }

    /**
     * @brief Update the inventory window
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    virtual void Update(int deltaTime) override;

    /**
     * @brief Render the inventory window
     * @param renderer SDL renderer
     */
    virtual void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Handle mouse button event
     * @param button Mouse button
     * @param pressed Whether the button was pressed or released
     * @param x Mouse X coordinate
     * @param y Mouse Y coordinate
     * @return true if handled, false otherwise
     */
    virtual bool HandleMouseButton(Uint8 button, bool pressed, int x, int y) override;

    /**
     * @brief Handle mouse motion event
     * @param x Mouse X coordinate
     * @param y Mouse Y coordinate
     * @return true if handled, false otherwise
     */
    virtual bool HandleMouseMotion(int x, int y) override;

    /**
     * @brief Handle key down event
     * @param key Key code
     * @param pressed Whether the key was pressed or released
     * @return true if the event was handled, false otherwise
     */
    virtual bool HandleKey(SDL_Keycode key, bool pressed) override;

private:
    /**
     * @brief Get the slot at the specified position
     * @param x X position
     * @param y Y position
     * @return Slot index, or -1 if no slot at the position
     */
    int GetSlotAt(int x, int y) const;

    /**
     * @brief Get the position of the specified slot
     * @param slot Slot index
     * @param x Output X position
     * @param y Output Y position
     */
    void GetSlotPosition(int slot, int& x, int& y) const;

    /**
     * @brief Render an item
     * @param renderer SDL renderer
     * @param item Item to render
     * @param x X position
     * @param y Y position
     * @param size Size of the item
     */
    void RenderItem(SDL_Renderer* renderer, const Item& item, int x, int y, int size);
};

