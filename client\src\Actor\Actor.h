#pragma once

#include "../Graphics/Sprite.h"
#include <SDL2/SDL_ttf.h>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>

/**
 * @enum Direction
 * @brief Direction of movement
 */
enum class Direction {
    UP,         ///< Up
    UP_RIGHT,   ///< Up-right
    RIGHT,      ///< Right
    DOWN_RIGHT, ///< Down-right
    DOWN,       ///< Down
    DOWN_LEFT,  ///< Down-left
    LEFT,       ///< Left
    UP_LEFT     ///< Up-left
};

/**
 * @enum ActorState
 * @brief State of the actor
 */
enum class ActorState {
    IDLE,       ///< Standing still
    WALKING,    ///< Walking
    RUNNING,    ///< Running
    ATTACKING,  ///< Attacking
    CASTING,    ///< Casting a spell
    SPELL,      ///< Performing a spell
    HIT,        ///< Being hit
    DYING,      ///< Dying
    DEAD        ///< Dead
};

/**
 * @class Actor
 * @brief Base class for all game actors
 *
 * This class represents a game actor, which is an entity that can move
 * and interact with the game world. It can be a player, monster, NPC, etc.
 */
class Actor {
protected:
    int m_id;                                                   ///< Actor ID
    std::string m_name;                                         ///< Actor name

    int m_x;                                                    ///< X position in map coordinates
    int m_y;                                                    ///< Y position in map coordinates
    Direction m_direction;                                      ///< Facing direction
    ActorState m_state;                                         ///< Current state

    std::unordered_map<ActorState, std::shared_ptr<AnimatedSprite>> m_sprites;  ///< Sprites for each state

    int m_moveSpeed;                                            ///< Movement speed in pixels per second
    int m_attackSpeed;                                          ///< Attack speed (attacks per minute)

    bool m_visible;                                             ///< Visibility flag

    // Movement
    int m_targetX;                                              ///< Target X position
    int m_targetY;                                              ///< Target Y position
    bool m_moving;                                              ///< Whether the actor is moving
    float m_moveProgress;                                       ///< Progress of movement (0.0 to 1.0)

    // Animation
    int m_animationTimer;                                       ///< Timer for animation updates

    // Light
    int m_light;                                                ///< Light level (0-5)
    int m_shiftX;                                               ///< X shift for light
    int m_shiftY;                                               ///< Y shift for light

    // Stats
    int m_health;                                               ///< Current health
    int m_maxHealth;                                            ///< Maximum health

    // Chat bubble
    std::string m_chatMessage;                                  ///< Current chat message
    int m_chatDuration;                                         ///< Duration of chat message in milliseconds
    int m_chatTimer;                                            ///< Timer for chat message
    std::shared_ptr<Texture> m_chatTexture;                     ///< Texture for chat message
    TTF_Font* m_chatFont;                                       ///< Font for chat message

    /**
     * @brief Update movement
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    virtual void UpdateMovement(int deltaTime);

    /**
     * @brief Update animation
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    virtual void UpdateAnimation(int deltaTime);

    /**
     * @brief Get the current sprite
     * @return Current sprite or nullptr if not found
     */
    std::shared_ptr<AnimatedSprite> GetCurrentSprite();

public:
    /**
     * @brief Constructor
     * @param id Actor ID
     * @param name Actor name
     */
    Actor(int id, const std::string& name);

    /**
     * @brief Virtual destructor
     */
    virtual ~Actor();

    /**
     * @brief Update the actor
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    virtual void Update(int deltaTime);

    /**
     * @brief Render the actor
     */
    virtual void Render();

    /**
     * @brief Set the position
     * @param x X position
     * @param y Y position
     */
    virtual void SetPosition(int x, int y);

    /**
     * @brief Set the direction
     * @param direction Direction
     */
    virtual void SetDirection(Direction direction);

    /**
     * @brief Set the state
     * @param state Actor state
     */
    virtual void SetState(ActorState state);

    /**
     * @brief Set visibility
     * @param visible Visibility flag
     */
    void SetVisible(bool visible);

    /**
     * @brief Move to a target position
     * @param x Target X position
     * @param y Target Y position
     * @return true if movement started, false otherwise
     */
    virtual bool MoveTo(int x, int y);

    /**
     * @brief Stop movement
     */
    virtual void StopMovement();

    /**
     * @brief Perform an attack
     * @return true if attack started, false otherwise
     */
    virtual bool Attack();

    /**
     * @brief Cast a spell
     * @param spellId Spell ID
     * @return true if casting started, false otherwise
     */
    virtual bool CastSpell(int spellId);

    /**
     * @brief Take damage
     * @param damage Damage amount
     * @return true if the actor is still alive, false otherwise
     */
    virtual bool TakeDamage(int damage);

    /**
     * @brief Add a sprite for a state
     * @param state Actor state
     * @param sprite Animated sprite
     */
    void AddSprite(ActorState state, std::shared_ptr<AnimatedSprite> sprite);

    /**
     * @brief Get the actor ID
     * @return Actor ID
     */
    int GetId() const { return m_id; }

    /**
     * @brief Get the actor name
     * @return Actor name
     */
    const std::string& GetName() const { return m_name; }

    /**
     * @brief Get the X position
     * @return X position
     */
    int GetX() const { return m_x; }

    /**
     * @brief Get the Y position
     * @return Y position
     */
    int GetY() const { return m_y; }

    /**
     * @brief Get the direction
     * @return Direction
     */
    Direction GetDirection() const { return m_direction; }

    /**
     * @brief Get the state
     * @return Actor state
     */
    ActorState GetState() const { return m_state; }

    /**
     * @brief Check if the actor is visible
     * @return true if visible, false otherwise
     */
    bool IsVisible() const { return m_visible; }

    /**
     * @brief Check if the actor is moving
     * @return true if moving, false otherwise
     */
    bool IsMoving() const { return m_moving; }

    /**
     * @brief Set the light level
     * @param light Light level (0-5)
     */
    void SetLight(int light) { m_light = light; }

    /**
     * @brief Get the light level
     * @return Light level (0-5)
     */
    int GetLight() const { return m_light; }

    /**
     * @brief Set the light shift
     * @param shiftX X shift
     * @param shiftY Y shift
     */
    void SetLightShift(int shiftX, int shiftY) { m_shiftX = shiftX; m_shiftY = shiftY; }

    /**
     * @brief Get the X shift for light
     * @return X shift
     */
    int GetShiftX() const { return m_shiftX; }

    /**
     * @brief Get the Y shift for light
     * @return Y shift
     */
    int GetShiftY() const { return m_shiftY; }

    /**
     * @brief Set the health
     * @param health Current health
     */
    virtual void SetHealth(int health) { m_health = health; }

    /**
     * @brief Get the health
     * @return Current health
     */
    virtual int GetHealth() const { return m_health; }

    /**
     * @brief Set the maximum health
     * @param maxHealth Maximum health
     */
    virtual void SetMaxHealth(int maxHealth) { m_maxHealth = maxHealth; }

    /**
     * @brief Say a message (display chat bubble)
     * @param message The message to say
     * @param duration Duration in milliseconds (default: 5000)
     */
    virtual void Say(const std::string& message, int duration = 5000);

    /**
     * @brief Set the chat font
     * @param font Font to use for chat bubbles
     */
    void SetChatFont(TTF_Font* font) { m_chatFont = font; }

    /**
     * @brief Get the chat font
     * @return Font used for chat bubbles
     */
    TTF_Font* GetChatFont() const { return m_chatFont; }

    /**
     * @brief Update chat bubble
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    virtual void UpdateChatBubble(int deltaTime);

    /**
     * @brief Render chat bubble
     * @param renderer SDL renderer
     */
    virtual void RenderChatBubble(SDL_Renderer* renderer);

    /**
     * @brief Set the actor's appearance
     * @param appearance Appearance ID
     */
    virtual void SetAppearance(int appearance) { /* Implementation in derived classes */ }

    /**
     * @brief Set the actor's status
     * @param status Status flags
     */
    virtual void SetStatus(int status) { /* Implementation in derived classes */ }

    /**
     * @brief Set the actor's name color
     * @param color Color value
     */
    virtual void SetNameColor(int color) { /* Implementation in derived classes */ }

    /**
     * @brief Get the actor's level
     * @return Actor level
     */
    virtual int GetLevel() const { return 1; } // Default implementation returns 1

    /**
     * @brief Set whether to show the health bar
     * @param show Whether to show the health bar
     */
    virtual void SetShowHealthBar(bool show) { m_showHealthBar = show; }

protected:
    bool m_showHealthBar = false; // Whether to show the health bar
};

