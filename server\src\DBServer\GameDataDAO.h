// GameDataDAO.h - 游戏数据访问对象
#pragma once

#include "../Database/IDatabase.h"
#include "../Common/GameData.h"
#include <vector>
#include <memory>
#include <optional>

namespace DBServer {

// 物品数据访问对象
class ItemDAO {
public:
    ItemDAO(std::shared_ptr<MirServer::IDatabase> db);
    ~ItemDAO() = default;
    
    // 初始化数据表
    bool InitializeTable();
    
    // CRUD操作
    bool Create(const MirServer::StdItem& item);
    std::optional<MirServer::StdItem> Read(int32_t idx);
    bool Update(const MirServer::StdItem& item);
    bool Delete(int32_t idx);
    
    // 批量操作
    std::vector<MirServer::StdItem> ReadAll();
    std::vector<MirServer::StdItem> ReadByType(uint8_t stdMode);
    std::vector<MirServer::StdItem> ReadByName(const std::string& namePart);
    bool CreateBatch(const std::vector<MirServer::StdItem>& items);
    
    // 查询操作
    size_t Count();
    bool Exists(int32_t idx);
    bool ExistsByName(const std::string& name);
    
    // 导入导出
    bool ImportFromCSV(const std::string& filename);
    bool ExportToCSV(const std::string& filename);
    
    // 获取错误信息
    std::string GetLastError() const { return m_lastError; }
    
private:
    std::shared_ptr<MirServer::IDatabase> m_db;
    mutable std::string m_lastError;
    
    // 从结果集构建物品对象
    MirServer::StdItem BuildItemFromResult(const MirServer::ResultRow& row);
};

// 魔法数据访问对象
class MagicDAO {
public:
    MagicDAO(std::shared_ptr<MirServer::IDatabase> db);
    ~MagicDAO() = default;
    
    // 初始化数据表
    bool InitializeTable();
    
    // CRUD操作
    bool Create(const MirServer::Magic& magic);
    std::optional<MirServer::Magic> Read(uint16_t magicId);
    bool Update(const MirServer::Magic& magic);
    bool Delete(uint16_t magicId);
    
    // 批量操作
    std::vector<MirServer::Magic> ReadAll();
    std::vector<MirServer::Magic> ReadByJob(uint8_t job);
    std::vector<MirServer::Magic> ReadByName(const std::string& namePart);
    bool CreateBatch(const std::vector<MirServer::Magic>& magics);
    
    // 查询操作
    size_t Count();
    bool Exists(uint16_t magicId);
    bool ExistsByName(const std::string& name);
    
    // 导入导出
    bool ImportFromCSV(const std::string& filename);
    bool ExportToCSV(const std::string& filename);
    
    // 获取错误信息
    std::string GetLastError() const { return m_lastError; }
    
private:
    std::shared_ptr<MirServer::IDatabase> m_db;
    mutable std::string m_lastError;
    
    // 从结果集构建魔法对象
    MirServer::Magic BuildMagicFromResult(const MirServer::ResultRow& row);
};

// 游戏数据管理器
class GameDataManager {
public:
    GameDataManager();
    ~GameDataManager();
    
    // 初始化
    bool Initialize(const std::string& dbPath);
    void Shutdown();
    
    // 获取DAO对象
    ItemDAO* GetItemDAO() { return m_itemDAO.get(); }
    MagicDAO* GetMagicDAO() { return m_magicDAO.get(); }
    
    // 加载所有数据到内存
    bool LoadAllData();
    
    // 获取内存中的数据
    const std::vector<MirServer::StdItem>& GetItems() const { return m_items; }
    const std::vector<MirServer::Magic>& GetMagics() const { return m_magics; }
    
    // 查找物品
    const MirServer::StdItem* FindItem(int32_t idx) const;
    const MirServer::StdItem* FindItemByName(const std::string& name) const;
    
    // 查找魔法
    const MirServer::Magic* FindMagic(uint16_t magicId) const;
    const MirServer::Magic* FindMagicByName(const std::string& name) const;
    
    // 获取数据库对象
    std::shared_ptr<MirServer::IDatabase> GetDatabase() { return m_database; }
    
private:
    std::shared_ptr<MirServer::IDatabase> m_database;
    std::unique_ptr<ItemDAO> m_itemDAO;
    std::unique_ptr<MagicDAO> m_magicDAO;
    
    // 内存缓存
    std::vector<MirServer::StdItem> m_items;
    std::vector<MirServer::Magic> m_magics;
    std::map<int32_t, size_t> m_itemIndexMap;  // idx -> vector index
    std::map<uint16_t, size_t> m_magicIndexMap; // magicId -> vector index
};

} // namespace DBServer 