# 传奇服务器 C++ 重构项目

## 项目概述

此项目旨在将传奇游戏的Delphi服务器端代码重构为现代C++实现，确保：
- 协议完全兼容原版
- 跨平台支持（Windows/Linux）
- 高性能和稳定性
- 模块化设计

## 架构设计

### 核心组件架构（基于delphi原版）

```
MirServer/
├── GameEngine/      # 游戏引擎核心（对应EM2Engine）
├── LoginServer/     # 登录服务器（对应ELoginSrv）
├── LoginGate/       # 登录网关（对应ELoginGate）
├── SelectGate/      # 选择网关（对应ESelGate）
├── RunGate/         # 运行网关（对应ERunGate）
├── DBServer/        # 数据库服务器（对应EDBServer）
├── LogServer/       # 日志服务器（对应ELogDataServer）
└── GameCenter/      # 游戏中心（对应EGameCenter）
```

### 关键系统模块

#### 1. 基础对象系统（BaseObject）
- `TBaseObject` -> `BaseObject`
- `TPlayObject` -> `PlayObject`
- `TNormNpc` -> `NormNpc`
- `TMerchant` -> `Merchant`
- `TMonster` -> `Monster`

#### 2. 游戏引擎（GameEngine）
- 用户管理 (`UsrEngn.pas` -> `UserEngine`)
- 魔法系统 (`Magic.pas` -> `MagicManager`)
- 地图管理 (`Envir.pas` -> `Environment`)
- 物品系统 (`ItmUnit.pas` -> `ItemManager`)

#### 3. 网络通信
- 协议定义（完全兼容delphi版本）
- 数据包处理
- 客户端连接管理

#### 4. 数据存储
- 角色数据
- 物品数据
- 行会数据
- 日志数据

## 协议兼容性

### 关键协议（必须完全一致）
```cpp
// 基础协议
CM_IDPASSWORD       = 2001,    // 登录密码
CM_SELECTSERVER     = 104,     // 选择服务器
CM_QUERYCHR         = 100,     // 查询角色
CM_NEWCHR           = 101,     // 创建角色
CM_DELCHR           = 102,     // 删除角色
CM_SELCHR           = 103,     // 选择角色

// 游戏内协议
CM_WALK             = 3000,    // 行走
CM_RUN              = 3001,    // 跑步
CM_HIT              = 3002,    // 攻击
CM_SPELL            = 3003,    // 魔法
CM_SAY              = 3004,    // 说话
CM_PICKUP           = 3005,    // 拾取
// ... 更多协议
```

### 缺失协议实现优先级
1. **最高优先级**：仓库系统、交易系统、修理系统
2. **高优先级**：魔法技能、骑马系统、小地图
3. **中优先级**：行会系统、特殊效果
4. **低优先级**：辅助功能、系统配置

## 实现计划

### 阶段一：核心框架
- [ ] 基础对象系统
- [ ] 网络通信框架
- [ ] 协议定义和解析
- [ ] 基础数据结构

### 阶段二：基本功能
- [ ] 用户登录系统
- [ ] 角色管理
- [ ] 基础游戏逻辑（移动、攻击）
- [ ] 地图系统

### 阶段三：扩展功能
- [ ] 完整魔法系统
- [ ] NPC和商店系统
- [ ] 仓库和交易系统
- [ ] 行会系统

### 阶段四：优化完善
- [ ] 性能优化
- [ ] 错误处理
- [ ] 日志系统
- [ ] 配置管理

## 跨平台考虑

### 网络库选择
- Windows: WSA/Winsock2
- Linux: epoll/select
- 统一接口封装

### 文件系统
- 路径分隔符处理
- 文件编码统一（UTF-8）
- 大小写敏感性处理

### 数据库
- 支持SQLite（默认）
- 支持MySQL/PostgreSQL（可选）
- 统一数据库接口

## 开发环境

### 构建系统
- CMake 3.16+
- 支持多种编译器（GCC、Clang、MSVC）

### 依赖库
- 网络：Asio或自实现
- 数据库：SQLite3、MySQL连接器（可选）
- 日志：spdlog
- 配置：nlohmann/json

### 编码规范
- C++17标准
- 使用智能指针管理内存
- 异常安全的代码设计
- 详细的错误处理和日志记录

## 测试策略

### 单元测试
- Google Test框架
- 模块独立测试
- 协议兼容性测试

### 集成测试
- 与原版客户端的兼容性测试
- 性能压力测试
- 多平台兼容性测试

### 部署测试
- Docker容器化部署
- 云平台兼容性测试

## 项目当前状态

### 已完成模块

✅ **Phase 1: 基础框架搭建 (100% 完成)**
- 跨平台构建系统 (CMake)
- 基础类型系统 (Types.h/cpp) - 完全兼容Delphi类型
- 协议定义 (PacketTypes.h) - 100%协议兼容
- 基础对象系统：
  - BaseObject (222行) - 对象基类
  - PlayObject (579行) - 玩家对象
  - Monster (625行) - 怪物系统(6种AI状态)
  - NPC (449行) - NPC系统(对话/商店/守卫)

✅ **Phase 2: 网络通信模块 (100% 完成)**
- NetworkManager - 多线程网络架构
  - Accept线程：接受新连接
  - IO线程：数据收发处理
  - Event线程：协议包处理
- ClientConnection - 客户端连接管理
- MessageConverter - 协议编解码
- PacketHandler - 数据包处理框架
- 测试服务器演示功能正常

✅ **Phase 3: 游戏引擎核心组件 (100% 完成)**
- UserEngine (667行) - 用户引擎
  - 玩家管理(增删查改)
  - GM命令系统
  - 排行榜功能
  - 定时任务系统
- Environment (约700行) - 环境系统
  - 地图单元格管理
  - 对象位置管理
  - A*寻路算法
  - 地图事件系统
  - 刷新点管理
- MapManager (653行) - 地图管理器
  - 地图数据加载
  - 地图连接管理
  - 安全区/传送门
  - 地图属性管理
- ItemManager (约900行) - 物品管理器
  - 物品数据库
  - 掉落系统
  - 商店系统
  - 物品生成/验证

### 正在开发

🔄 **Phase 4: 具体服务器实现**
- GameEngine主逻辑整合
- LoginServer实现
- DBServer实现
- GateServer实现

### 待开发功能

📋 **Phase 5: 游戏系统实现**
- 战斗系统详细实现
- 技能/魔法系统
- 交易系统
- 组队系统
- 行会系统
- 仓库系统

📋 **Phase 6: 优化和完善**
- 性能优化
- 内存管理优化
- 日志系统完善
- 配置系统
- GM管理工具

## 编译状态

✅ **当前编译状态：成功**
- 所有模块编译通过
- 无编译错误或警告
- 支持Debug/Release配置

## 下一步计划

1. 整合GameEngine主循环
2. 实现数据持久化
3. 完善战斗系统
4. 添加更多游戏功能 