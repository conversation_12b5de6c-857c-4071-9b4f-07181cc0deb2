#pragma once

#include <SDL2/SDL_mixer.h>
#include <string>
#include <unordered_map>
#include <memory>

/**
 * @class Sound
 * @brief Represents a sound effect
 * 
 * This class represents a sound effect that can be played.
 */
class Sound {
private:
    Mix_Chunk* m_chunk;  ///< SDL_mixer chunk
    int m_channel;       ///< Channel the sound is playing on
    
public:
    /**
     * @brief Constructor
     * @param chunk SDL_mixer chunk
     */
    Sound(Mix_Chunk* chunk);
    
    /**
     * @brief Destructor
     */
    ~Sound();
    
    /**
     * @brief Play the sound
     * @param loops Number of times to loop the sound (-1 for infinite)
     * @param volume Volume (0-128)
     * @return Channel the sound is playing on
     */
    int Play(int loops = 0, int volume = 128);
    
    /**
     * @brief Stop the sound
     */
    void Stop();
    
    /**
     * @brief Pause the sound
     */
    void Pause();
    
    /**
     * @brief Resume the sound
     */
    void Resume();
    
    /**
     * @brief Set the volume
     * @param volume Volume (0-128)
     */
    void SetVolume(int volume);
    
    /**
     * @brief Check if the sound is playing
     * @return true if playing, false otherwise
     */
    bool IsPlaying() const;
    
    /**
     * @brief Get the SDL_mixer chunk
     * @return SDL_mixer chunk
     */
    Mix_Chunk* GetChunk() const { return m_chunk; }
    
    /**
     * @brief Get the channel
     * @return Channel the sound is playing on
     */
    int GetChannel() const { return m_channel; }
};

/**
 * @class Music
 * @brief Represents background music
 * 
 * This class represents background music that can be played.
 */
class Music {
private:
    Mix_Music* m_music;  ///< SDL_mixer music
    
public:
    /**
     * @brief Constructor
     * @param music SDL_mixer music
     */
    Music(Mix_Music* music);
    
    /**
     * @brief Destructor
     */
    ~Music();
    
    /**
     * @brief Play the music
     * @param loops Number of times to loop the music (-1 for infinite)
     * @param fadeInMs Fade-in time in milliseconds
     * @return true if successful, false otherwise
     */
    bool Play(int loops = -1, int fadeInMs = 0);
    
    /**
     * @brief Stop the music
     * @param fadeOutMs Fade-out time in milliseconds
     */
    void Stop(int fadeOutMs = 0);
    
    /**
     * @brief Pause the music
     */
    void Pause();
    
    /**
     * @brief Resume the music
     */
    void Resume();
    
    /**
     * @brief Set the volume
     * @param volume Volume (0-128)
     */
    void SetVolume(int volume);
    
    /**
     * @brief Check if the music is playing
     * @return true if playing, false otherwise
     */
    bool IsPlaying() const;
    
    /**
     * @brief Get the SDL_mixer music
     * @return SDL_mixer music
     */
    Mix_Music* GetMusic() const { return m_music; }
};

/**
 * @class SoundManager
 * @brief Manages sound effects and music
 * 
 * This class is responsible for loading, playing, and managing sound effects and music.
 */
class SoundManager {
private:
    std::unordered_map<std::string, std::shared_ptr<Sound>> m_sounds;  ///< Map of sound effects
    std::unordered_map<std::string, std::shared_ptr<Music>> m_music;   ///< Map of music
    
    int m_soundVolume;  ///< Sound effect volume (0-128)
    int m_musicVolume;  ///< Music volume (0-128)
    bool m_soundEnabled;  ///< Whether sound effects are enabled
    bool m_musicEnabled;  ///< Whether music is enabled
    
    std::string m_currentMusic;  ///< Name of the currently playing music
    
public:
    /**
     * @brief Constructor
     */
    SoundManager();
    
    /**
     * @brief Destructor
     */
    ~SoundManager();
    
    /**
     * @brief Initialize the sound manager
     * @param frequency Audio frequency in Hz
     * @param channels Number of channels (1 for mono, 2 for stereo)
     * @param chunkSize Audio buffer size in bytes
     * @return true if successful, false otherwise
     */
    bool Initialize(int frequency = 44100, int channels = 2, int chunkSize = 2048);
    
    /**
     * @brief Shutdown the sound manager
     */
    void Shutdown();
    
    /**
     * @brief Load a sound effect
     * @param name Sound name
     * @param filename Sound file path
     * @return true if successful, false otherwise
     */
    bool LoadSound(const std::string& name, const std::string& filename);
    
    /**
     * @brief Load music
     * @param name Music name
     * @param filename Music file path
     * @return true if successful, false otherwise
     */
    bool LoadMusic(const std::string& name, const std::string& filename);
    
    /**
     * @brief Play a sound effect
     * @param name Sound name
     * @param loops Number of times to loop the sound (-1 for infinite)
     * @param volume Volume (0-128, -1 for default)
     * @return true if successful, false otherwise
     */
    bool PlaySound(const std::string& name, int loops = 0, int volume = -1);
    
    /**
     * @brief Play music
     * @param name Music name
     * @param loops Number of times to loop the music (-1 for infinite)
     * @param fadeInMs Fade-in time in milliseconds
     * @return true if successful, false otherwise
     */
    bool PlayMusic(const std::string& name, int loops = -1, int fadeInMs = 0);
    
    /**
     * @brief Stop a sound effect
     * @param name Sound name
     * @return true if successful, false otherwise
     */
    bool StopSound(const std::string& name);
    
    /**
     * @brief Stop all sound effects
     */
    void StopAllSounds();
    
    /**
     * @brief Stop music
     * @param fadeOutMs Fade-out time in milliseconds
     */
    void StopMusic(int fadeOutMs = 0);
    
    /**
     * @brief Pause a sound effect
     * @param name Sound name
     * @return true if successful, false otherwise
     */
    bool PauseSound(const std::string& name);
    
    /**
     * @brief Pause all sound effects
     */
    void PauseAllSounds();
    
    /**
     * @brief Pause music
     */
    void PauseMusic();
    
    /**
     * @brief Resume a sound effect
     * @param name Sound name
     * @return true if successful, false otherwise
     */
    bool ResumeSound(const std::string& name);
    
    /**
     * @brief Resume all sound effects
     */
    void ResumeAllSounds();
    
    /**
     * @brief Resume music
     */
    void ResumeMusic();
    
    /**
     * @brief Set sound effect volume
     * @param volume Volume (0-128)
     */
    void SetSoundVolume(int volume);
    
    /**
     * @brief Set music volume
     * @param volume Volume (0-128)
     */
    void SetMusicVolume(int volume);
    
    /**
     * @brief Enable or disable sound effects
     * @param enabled Whether sound effects are enabled
     */
    void EnableSound(bool enabled);
    
    /**
     * @brief Enable or disable music
     * @param enabled Whether music is enabled
     */
    void EnableMusic(bool enabled);
    
    /**
     * @brief Get a sound effect
     * @param name Sound name
     * @return Sound or nullptr if not found
     */
    std::shared_ptr<Sound> GetSound(const std::string& name);
    
    /**
     * @brief Get music
     * @param name Music name
     * @return Music or nullptr if not found
     */
    std::shared_ptr<Music> GetMusic(const std::string& name);
    
    /**
     * @brief Get the sound effect volume
     * @return Sound effect volume (0-128)
     */
    int GetSoundVolume() const { return m_soundVolume; }
    
    /**
     * @brief Get the music volume
     * @return Music volume (0-128)
     */
    int GetMusicVolume() const { return m_musicVolume; }
    
    /**
     * @brief Check if sound effects are enabled
     * @return true if enabled, false otherwise
     */
    bool IsSoundEnabled() const { return m_soundEnabled; }
    
    /**
     * @brief Check if music is enabled
     * @return true if enabled, false otherwise
     */
    bool IsMusicEnabled() const { return m_musicEnabled; }
    
    /**
     * @brief Get the name of the currently playing music
     * @return Music name
     */
    const std::string& GetCurrentMusic() const { return m_currentMusic; }
};

