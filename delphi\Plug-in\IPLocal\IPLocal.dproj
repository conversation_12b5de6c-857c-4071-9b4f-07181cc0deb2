﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectGuid>{8fa4c1c7-c8e3-48a5-b474-ddb90efd379a}</ProjectGuid>
    <MainSource>IPLocal.dpr</MainSource>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
    <DCC_DependencyCheckOutputName>..\..\MirServer\Mir200\IPLocal.dll</DCC_DependencyCheckOutputName>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_DebugInformation>False</DCC_DebugInformation>
    <DCC_LocalDebugSymbols>False</DCC_LocalDebugSymbols>
    <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
    <DCC_ExeOutput>..\..\MirServer</DCC_ExeOutput>
    <DCC_DcuOutput>.\Temp</DCC_DcuOutput>
    <DCC_ObjOutput>.\Temp</DCC_ObjOutput>
    <DCC_HppOutput>.\Temp</DCC_HppOutput>
    <DCC_UnitSearchPath>D:\EGameOfMir\Component\加密控件;D:\EGameOfMir\Component\加密控件\Apps;D:\EGameOfMir\Component\加密控件\Ciphers;D:\EGameOfMir\Component\加密控件\Dcr32;D:\EGameOfMir\Component\加密控件\Hashes</DCC_UnitSearchPath>
    <DCC_ResourcePath>D:\EGameOfMir\Component\加密控件;D:\EGameOfMir\Component\加密控件\Apps;D:\EGameOfMir\Component\加密控件\Ciphers;D:\EGameOfMir\Component\加密控件\Dcr32;D:\EGameOfMir\Component\加密控件\Hashes</DCC_ResourcePath>
    <DCC_ObjPath>D:\EGameOfMir\Component\加密控件;D:\EGameOfMir\Component\加密控件\Apps;D:\EGameOfMir\Component\加密控件\Ciphers;D:\EGameOfMir\Component\加密控件\Dcr32;D:\EGameOfMir\Component\加密控件\Hashes</DCC_ObjPath>
    <DCC_IncludePath>D:\EGameOfMir\Component\加密控件;D:\EGameOfMir\Component\加密控件\Apps;D:\EGameOfMir\Component\加密控件\Ciphers;D:\EGameOfMir\Component\加密控件\Dcr32;D:\EGameOfMir\Component\加密控件\Hashes</DCC_IncludePath>
    <DCC_Define>RELEASE</DCC_Define>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_ExeOutput>..\..\MirServer\Mir200</DCC_ExeOutput>
    <DCC_DcuOutput>..\..\Build\IPLocal</DCC_DcuOutput>
    <DCC_ObjOutput>..\..\Build\IPLocal</DCC_ObjOutput>
    <DCC_HppOutput>..\..\Build\IPLocal</DCC_HppOutput>
    <DCC_UnitSearchPath>..\..\Component\加密控件;..\..\Component\加密控件\Apps;..\..\Component\加密控件\Ciphers;..\..\Component\加密控件\Dcr32;..\..\Component\加密控件\Hashes</DCC_UnitSearchPath>
    <DCC_ResourcePath>..\..\Component\加密控件;..\..\Component\加密控件\Apps;..\..\Component\加密控件\Ciphers;..\..\Component\加密控件\Dcr32;..\..\Component\加密控件\Hashes</DCC_ResourcePath>
    <DCC_ObjPath>..\..\Component\加密控件;..\..\Component\加密控件\Apps;..\..\Component\加密控件\Ciphers;..\..\Component\加密控件\Dcr32;..\..\Component\加密控件\Hashes</DCC_ObjPath>
    <DCC_IncludePath>..\..\Component\加密控件;..\..\Component\加密控件\Apps;..\..\Component\加密控件\Ciphers;..\..\Component\加密控件\Dcr32;..\..\Component\加密控件\Hashes</DCC_IncludePath>
    <DCC_Define>DEBUG</DCC_Define>
  </PropertyGroup>
  <ProjectExtensions>
    <Borland.Personality>Delphi.Personality</Borland.Personality>
    <Borland.ProjectType>VCLApplication</Borland.ProjectType>
    <BorlandProject>
<BorlandProject xmlns=""> <Delphi.Personality>   <Parameters>
      <Parameters Name="UseLauncher">False</Parameters>
      <Parameters Name="LoadAllSymbols">True</Parameters>
      <Parameters Name="LoadUnspecifiedSymbols">False</Parameters>
    </Parameters>
    <VersionInfo>
      <VersionInfo Name="IncludeVerInfo">False</VersionInfo>
      <VersionInfo Name="AutoIncBuild">False</VersionInfo>
      <VersionInfo Name="MajorVer">1</VersionInfo>
      <VersionInfo Name="MinorVer">0</VersionInfo>
      <VersionInfo Name="Release">0</VersionInfo>
      <VersionInfo Name="Build">0</VersionInfo>
      <VersionInfo Name="Debug">False</VersionInfo>
      <VersionInfo Name="PreRelease">False</VersionInfo>
      <VersionInfo Name="Special">False</VersionInfo>
      <VersionInfo Name="Private">False</VersionInfo>
      <VersionInfo Name="DLL">False</VersionInfo>
      <VersionInfo Name="Locale">2052</VersionInfo>
      <VersionInfo Name="CodePage">936</VersionInfo>
    </VersionInfo>
    <VersionInfoKeys>
      <VersionInfoKeys Name="CompanyName"></VersionInfoKeys>
      <VersionInfoKeys Name="FileDescription"></VersionInfoKeys>
      <VersionInfoKeys Name="FileVersion">*******</VersionInfoKeys>
      <VersionInfoKeys Name="InternalName"></VersionInfoKeys>
      <VersionInfoKeys Name="LegalCopyright"></VersionInfoKeys>
      <VersionInfoKeys Name="LegalTrademarks"></VersionInfoKeys>
      <VersionInfoKeys Name="OriginalFilename"></VersionInfoKeys>
      <VersionInfoKeys Name="ProductName"></VersionInfoKeys>
      <VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys>
      <VersionInfoKeys Name="Comments"></VersionInfoKeys>
    </VersionInfoKeys>
    <Source>
      <Source Name="MainSource">IPLocal.dpr</Source>
    </Source>
  </Delphi.Personality> </BorlandProject></BorlandProject>
  </ProjectExtensions>
  <ItemGroup />
  <ItemGroup>
    <DelphiCompile Include="IPLocal.dpr">
      <MainSource>MainSource</MainSource>
    </DelphiCompile>
    <DCCReference Include="Module.pas" />
    <DCCReference Include="PlugMain.pas" />
    <DCCReference Include="SDK.pas" />
    <DCCReference Include="Share.pas" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Borland.Delphi.Targets" />
</Project>