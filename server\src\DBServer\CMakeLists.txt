# DBServer CMakeLists.txt

# Find SQLite3
find_package(SQLite3 REQUIRED)

# Add Database library
add_library(Database STATIC
    ../Database/SQLiteDatabase.cpp
)

target_include_directories(Database PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/..
    ${SQLite3_INCLUDE_DIRS}
)

target_link_libraries(Database PUBLIC
    Common
    ${SQLite3_LIBRARIES}
)

# Add DBServer executable
add_executable(DBServer
    DBServer.cpp
    GameDataDAO.cpp
    main.cpp
)

# Include directories
target_include_directories(DBServer PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/src
)

# Link libraries
target_link_libraries(DBServer PRIVATE
    Common
    Protocol
    Database
    ${CMAKE_THREAD_LIBS_INIT}
)

# Platform-specific settings
if(WIN32)
    target_link_libraries(DBServer PRIVATE ws2_32)
    # If SQLite3 is not found by find_package on Windows, use manual settings
    if(NOT SQLite3_FOUND)
        set(SQLite3_INCLUDE_DIRS "${CMAKE_SOURCE_DIR}/third_party/sqlite3/include")
        set(SQLite3_LIBRARIES "${CMAKE_SOURCE_DIR}/third_party/sqlite3/lib/sqlite3.lib")
        target_include_directories(Database PUBLIC ${SQLite3_INCLUDE_DIRS})
        target_link_libraries(Database PUBLIC ${SQLite3_LIBRARIES})
    endif()
endif()

# Set C++ standard
set_target_properties(DBServer PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

set_target_properties(Database PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
)

# Install target
install(TARGETS DBServer
    RUNTIME DESTINATION bin
) 