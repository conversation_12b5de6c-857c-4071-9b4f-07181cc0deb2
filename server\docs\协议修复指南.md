# 传奇服务器协议修复指南

## 🎯 修复目标
将重构版本的协议编号完全修正为与原版delphi项目一致，确保客户端与服务器的正常通信。

## 📝 修复清单

### 🚨 Critical（必须立即修复）

#### 1. 数据库协议编号修正
**文件**: `server/src/Protocol/PacketTypes.h`
```cpp
// 修正前（错误）
DB_LOADHUMANRCD = 200,
DB_SAVEHUMANRCD = 201,
DB_SAVEHUMANRCDEX = 202,
DBR_LOADHUMANRCD = 250,
DBR_SAVEHUMANRCD = 251,
DBR_FAIL = 255,

// 修正后（正确）
DB_LOADHUMANRCD = 1000,
DB_SAVEHUMANRCD = 1010,
DB_SAVEHUMANRCDEX = 1020,
DBR_LOADHUMANRCD = 1100,
DBR_SAVEHUMANRCD = 1101,
DBR_FAIL = 1102,
```

#### 2. 基础战斗动作协议修正
```cpp
// 修正前（错误）
CM_WALK = 3000,
CM_RUN = 3001,
CM_HIT = 3002,
CM_HEAVYHIT = 3003,
CM_BIGHIT = 3004,
CM_SPELL = 3005,
CM_POWERHIT = 3006,
CM_LONGHIT = 3007,
CM_WIDEHIT = 3008,
CM_FIREHIT = 3009,

// 修正后（正确）
CM_WALK = 3011,
CM_RUN = 3013,
CM_HIT = 3014,
CM_HEAVYHIT = 3015,
CM_BIGHIT = 3016,
CM_SPELL = 3017,
CM_POWERHIT = 3018,
CM_LONGHIT = 3019,
CM_WIDEHIT = 3024,
CM_FIREHIT = 3025,
```

#### 3. 服务器动作响应协议修正
```cpp
// 修正前（错误）
SM_WALK = 800,
SM_RUN = 801,
SM_HIT = 802,
SM_HEAVYHIT = 803,
SM_BIGHIT = 804,
SM_SPELL = 805,
SM_POWERHIT = 806,
SM_LONGHIT = 807,
SM_WIDEHIT = 808,
SM_TURN = 833,

// 修正后（正确）
SM_WALK = 11,
SM_RUN = 13,
SM_HIT = 14,
SM_HEAVYHIT = 15,
SM_BIGHIT = 16,
SM_SPELL = 17,
SM_POWERHIT = 18,
SM_LONGHIT = 19,
SM_WIDEHIT = 24,
SM_TURN = 10,
```

#### 4. 行会系统协议修正
```cpp
// 修正前（错误）
CM_OPENGUILDDLG = 2400,
CM_GUILDHOME = 2401,
CM_GUILDMEMBERLIST = 2402,
CM_GUILDADDMEMBER = 2403,
CM_GUILDDELMEMBER = 2404,
CM_GUILDUPDATENOTICE = 2405,
CM_GUILDUPDATERANKINFO = 2406,
CM_GUILDMAKEALLY = 2407,
CM_GUILDBREAKALLY = 2408,

// 修正后（正确）
CM_OPENGUILDDLG = 1035,
CM_GUILDHOME = 1036,
CM_GUILDMEMBERLIST = 1037,
CM_GUILDADDMEMBER = 1038,
CM_GUILDDELMEMBER = 1039,
CM_GUILDUPDATENOTICE = 1040,
CM_GUILDUPDATERANKINFO = 1041,
CM_GUILDMAKEALLY = 1044,
CM_GUILDBREAKALLY = 1045,
```

### ⚠️ High Priority（高优先级修复）

#### 5. 补充缺失的客户端协议
```cpp
// 需要添加的协议
CM_OPENDOOR = 1002,           // 开门操作
CM_1005 = 1005,               // 原版协议
CM_SOFTCLOSE = 1009,          // 软关闭
CM_1017 = 1017,               // 原版协议
CM_1042 = 1042,               // 原版协议
CM_ADJUST_BONUS = 1043,       // 调整属性点
CM_THROW = 3005,              // 投掷物品
CM_HORSERUN = 3009,           // 骑马跑
CM_SITDOWN = 3012,            // 坐下动作
CM_40HIT = 3026,              // 原版协议
CM_41HIT = 3027,              // 原版协议
CM_43HIT = 3028,              // 原版协议
CM_42HIT = 3029,              // 原版协议
CM_CRSHIT = 3036,             // 连击
CM_3037 = 3037,               // 原版协议
CM_TWINHIT = 3038,            // 双击
```

#### 6. 补充缺失的服务器响应协议
```cpp
// 需要添加的协议
SM_RUSH = 6,                  // 冲锋
SM_RUSHKUNG = 7,              // 野蛮冲撞
SM_FIREHIT = 8,               // 烈火剑法
SM_BACKSTEP = 9,              // 后退步法
SM_SITDOWN = 12,              // 坐下
SM_DIGUP = 20,                // 挖取
SM_DIGDOWN = 21,              // 挖掘
SM_FLYAXE = 22,               // 飞斧
SM_LIGHTING = 23,             // 雷电
SM_CRSHIT = 25,               // 连击
SM_TWINHIT = 26,              // 双击
```

#### 7. 修正其他重要协议
```cpp
// 物品操作协议修正
CM_TAKEONITEM = 1003,         // 原版是1003，不是1004
CM_TAKEOFFITEM = 1004,        // 原版是1004，不是1005

// 密码找回协议修正
CM_GETBACKPASSWORD = 2010,    // 原版是2010，不是2005
```

### 📋 Medium Priority（中等优先级修复）

#### 8. RM_协议添加
原版中有大量的RM_协议用于内部消息传递，需要补充：
```cpp
// 内部消息协议（RM_开头）
RM_TURN = 10001,
RM_WALK = 10002,
RM_RUN = 10003,
RM_HIT = 10004,
RM_SPELL = 10007,
RM_SPELL2 = 10008,
RM_POWERHIT = 10009,
RM_LONGHIT = 10011,
RM_WIDEHIT = 10012,
RM_PUSH = 10013,
RM_FIREHIT = 10014,
RM_RUSH = 10015,
RM_STRUCK = 10020,
RM_DEATH = 10021,
RM_DISAPPEAR = 10022,
// ... 更多RM_协议
```

## 🔧 修复步骤

### 第一步：备份当前文件
```bash
cp server/src/Protocol/PacketTypes.h server/src/Protocol/PacketTypes.h.backup
```

### 第二步：应用Critical修复
1. 修正数据库协议编号
2. 修正战斗动作协议编号
3. 修正服务器响应协议编号
4. 修正行会系统协议编号

### 第三步：应用High Priority修复
1. 补充缺失的客户端协议
2. 补充缺失的服务器响应协议
3. 修正其他重要协议

### 第四步：更新相关代码
1. 更新协议处理器代码以使用新的协议编号
2. 更新消息转换器代码
3. 更新网络管理器代码

### 第五步：测试验证
1. 编译服务器确保没有编译错误
2. 使用原版客户端连接测试基本功能
3. 测试移动、战斗、交易、仓库、行会等核心功能

## 🎯 验证清单

### 基础功能测试
- [ ] 角色登录
- [ ] 角色移动（走路、跑步）
- [ ] 基础战斗（普通攻击、技能）
- [ ] 物品拾取和丢弃
- [ ] NPC对话
- [ ] 商店买卖

### 高级功能测试
- [ ] 仓库存取
- [ ] 交易系统
- [ ] 组队功能
- [ ] 行会功能
- [ ] 技能使用
- [ ] 装备穿戴

### 数据库功能测试
- [ ] 角色数据保存
- [ ] 角色数据加载
- [ ] 物品持久化

## 📊 修复进度跟踪

| 类别 | 总数 | 已修复 | 待修复 | 进度 |
|------|------|--------|--------|------|
| Critical协议 | 25 | 0 | 25 | 0% |
| High Priority协议 | 30 | 0 | 30 | 0% |
| Medium Priority协议 | 50 | 0 | 50 | 0% |
| **总计** | **105** | **0** | **105** | **0%** |

## 🚨 注意事项

1. **严格按照原版编号**: 不能随意更改协议编号，必须与原版完全一致
2. **保持向后兼容**: 修改后的协议必须与原版客户端兼容
3. **测试充分**: 每个修改都必须经过充分测试
4. **文档更新**: 修改后及时更新相关文档

## 📖 参考资料

- 原版协议定义：`delphi/Common/Grobal2.pas`
- 原版数据库协议：`delphi/Common/Common.pas`
- 协议对比报告：`server/协议编号对比报告.md` 