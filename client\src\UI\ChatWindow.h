#pragma once

#include "UIControl.h"
#include <vector>
#include <string>
#include <SDL2/SDL.h>
#include <SDL2/SDL_ttf.h>
#include <memory>
#include "../Graphics/Texture.h"

/**
 * @struct ChatMessage
 * @brief Structure to hold a chat message with its properties
 */
struct ChatMessage {
    std::string text;           ///< The message text
    SDL_Color textColor;        ///< The color of the text
    SDL_Color backgroundColor;  ///< The background color (if any)
    int timestamp;              ///< When the message was received
    int senderId;               ///< ID of the sender (0 for system messages)
    std::string senderName;     ///< Name of the sender (empty for system messages)

    /**
     * @brief Constructor
     * @param text The message text
     * @param textColor The color of the text
     * @param backgroundColor The background color
     * @param senderId ID of the sender (0 for system messages)
     * @param senderName Name of the sender (empty for system messages)
     */
    ChatMessage(const std::string& text,
                const SDL_Color& textColor,
                const SDL_Color& backgroundColor,
                int senderId = 0,
                const std::string& senderName = "");
};

/**
 * @enum ChatMessageType
 * @brief Types of chat messages
 */
enum class ChatMessageType {
    NORMAL,     ///< Normal chat message
    WHISPER,    ///< Whisper message
    GUILD,      ///< Guild message
    GROUP,      ///< Group message
    SYSTEM,     ///< System message
    CRY,        ///< Cry message
    MERCHANT    ///< Merchant message
};

/**
 * @class ChatWindow
 * @brief UI control for displaying chat messages
 */
class ChatWindow : public UIControl {
private:
    std::vector<ChatMessage> m_messages;       ///< List of messages
    int m_maxMessages;                         ///< Maximum number of messages to store
    int m_scrollPosition;                      ///< Current scroll position
    int m_visibleLines;                        ///< Number of visible lines
    TTF_Font* m_font;                          ///< Font for rendering text
    std::shared_ptr<Texture> m_backgroundTexture;  ///< Background texture
    bool m_showTimestamps;                     ///< Whether to show timestamps

    /**
     * @brief Create a texture for a message
     * @param message The message to create a texture for
     * @return The created texture
     */
    std::shared_ptr<Texture> CreateMessageTexture(const ChatMessage& message);

public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param font Font for rendering text
     * @param maxMessages Maximum number of messages to store
     * @param name Name of the control
     */
    ChatWindow(int x, int y, int width, int height, TTF_Font* font, int maxMessages = 100, const std::string& name = "ChatWindow");

    /**
     * @brief Destructor
     */
    virtual ~ChatWindow();

    /**
     * @brief Add a message to the chat window
     * @param message The message to add
     */
    void AddMessage(const ChatMessage& message);

    /**
     * @brief Add a message to the chat window
     * @param text The message text
     * @param textColor The color of the text
     * @param backgroundColor The background color
     * @param senderId ID of the sender (0 for system messages)
     * @param senderName Name of the sender (empty for system messages)
     */
    void AddMessage(const std::string& text,
                    const SDL_Color& textColor,
                    const SDL_Color& backgroundColor,
                    int senderId = 0,
                    const std::string& senderName = "");

    /**
     * @brief Add a message to the chat window
     * @param text The message text
     * @param type The type of message
     * @param senderId ID of the sender (0 for system messages)
     * @param senderName Name of the sender (empty for system messages)
     */
    void AddMessage(const std::string& text,
                    ChatMessageType type,
                    int senderId = 0,
                    const std::string& senderName = "");

    /**
     * @brief Scroll up in the chat history
     * @param lines Number of lines to scroll
     */
    void ScrollUp(int lines = 1);

    /**
     * @brief Scroll down in the chat history
     * @param lines Number of lines to scroll
     */
    void ScrollDown(int lines = 1);

    /**
     * @brief Scroll to the top of the chat history
     */
    void ScrollToTop();

    /**
     * @brief Scroll to the bottom of the chat history
     */
    void ScrollToBottom();

    /**
     * @brief Set whether to show timestamps
     * @param show Whether to show timestamps
     */
    void ShowTimestamps(bool show);

    /**
     * @brief Set the background texture
     * @param texture The background texture
     */
    void SetBackgroundTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Update the control
     * @param deltaTime Time elapsed since last frame
     */
    virtual void Update(int deltaTime) override;

    /**
     * @brief Render the control
     * @param renderer The renderer to use
     */
    virtual void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Handle mouse wheel events
     * @param event The mouse wheel event
     * @return true if the event was handled, false otherwise
     */
    virtual bool OnMouseWheel(const SDL_MouseWheelEvent& event);
};

