#include "CharacterCreationState.h"
#include "CharacterSelectionState.h"
#include "../Application.h"
#include "../UI/DialogManager.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>

CharacterCreationState::CharacterCreationState(Application* app, std::shared_ptr<NetworkManager> networkManager)
    : GameState(app)
    , m_networkManager(networkManager)
    , m_selectedClass(PlayerClass::WARRIOR)
    , m_creating(false)
{
    // Register packet handlers
    m_networkManager->RegisterPacketHandler(
        PacketType::CHARACTER_CREATE_RESPONSE,
        [this](const Packet& packet) { OnCharacterCreateResponse(packet); }
    );
}

CharacterCreationState::~CharacterCreationState()
{
    // Unregister packet handlers
    if (m_networkManager) {
        m_networkManager->UnregisterPacketHandler(PacketType::CHARACTER_CREATE_RESPONSE);
    }
}

void CharacterCreationState::Enter()
{
    // Load background texture
    m_backgroundTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (!m_backgroundTexture->LoadFromFile("assets/data/character_creation_background.png")) {
        std::cerr << "Failed to load character creation background texture" << std::endl;
    }
    
    // Create UI controls
    CreateControls();
    
    // Load initial character preview
    std::string previewFile = "assets/data/character_preview_" + std::to_string(static_cast<int>(m_selectedClass)) + ".png";
    m_characterPreviewTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (!m_characterPreviewTexture->LoadFromFile(previewFile)) {
        std::cerr << "Failed to load character preview texture: " << previewFile << std::endl;
    }
}

void CharacterCreationState::Exit()
{
    // Clear textures
    m_backgroundTexture.reset();
    m_characterPreviewTexture.reset();
    
    // Clear UI controls
    m_titleLabel.reset();
    m_nameLabel.reset();
    m_nameInput.reset();
    m_classLabel.reset();
    m_classRadioButtons.clear();
    m_createButton.reset();
    m_backButton.reset();
    m_statusLabel.reset();
}

void CharacterCreationState::Update(float deltaTime)
{
    // Update network manager
    if (m_networkManager) {
        m_networkManager->Update();
    }
    
    // Update UI controls
    if (m_titleLabel) m_titleLabel->Update(static_cast<int>(deltaTime * 1000));
    if (m_nameLabel) m_nameLabel->Update(static_cast<int>(deltaTime * 1000));
    if (m_nameInput) m_nameInput->Update(static_cast<int>(deltaTime * 1000));
    if (m_classLabel) m_classLabel->Update(static_cast<int>(deltaTime * 1000));
    
    for (auto& radioButton : m_classRadioButtons) {
        if (radioButton) radioButton->Update(static_cast<int>(deltaTime * 1000));
    }
    
    if (m_createButton) m_createButton->Update(static_cast<int>(deltaTime * 1000));
    if (m_backButton) m_backButton->Update(static_cast<int>(deltaTime * 1000));
    if (m_statusLabel) m_statusLabel->Update(static_cast<int>(deltaTime * 1000));
}

void CharacterCreationState::Render()
{
    // Render background
    if (m_backgroundTexture) {
        m_backgroundTexture->Render(0, 0);
    }
    
    // Render character preview
    if (m_characterPreviewTexture) {
        int previewX = 500;
        int previewY = 150;
        m_characterPreviewTexture->Render(previewX, previewY);
    }
    
    // Render UI controls
    if (m_titleLabel) m_titleLabel->Render(m_app->GetRenderer());
    if (m_nameLabel) m_nameLabel->Render(m_app->GetRenderer());
    if (m_nameInput) m_nameInput->Render(m_app->GetRenderer());
    if (m_classLabel) m_classLabel->Render(m_app->GetRenderer());
    
    for (auto& radioButton : m_classRadioButtons) {
        if (radioButton) radioButton->Render(m_app->GetRenderer());
    }
    
    if (m_createButton) m_createButton->Render(m_app->GetRenderer());
    if (m_backButton) m_backButton->Render(m_app->GetRenderer());
    if (m_statusLabel) m_statusLabel->Render(m_app->GetRenderer());
}

void CharacterCreationState::HandleEvents(SDL_Event& event)
{
    // Handle UI control events
    if (m_nameInput) m_nameInput->HandleEvent(event);
    
    for (auto& radioButton : m_classRadioButtons) {
        if (radioButton) radioButton->HandleEvent(event);
    }
    
    if (m_createButton) m_createButton->HandleEvent(event);
    if (m_backButton) m_backButton->HandleEvent(event);
    
    // Handle keyboard events
    if (event.type == SDL_KEYDOWN) {
        if (event.key.keysym.sym == SDLK_RETURN) {
            // Enter key pressed, create character
            OnCreateButtonClick();
        } else if (event.key.keysym.sym == SDLK_ESCAPE) {
            // Escape key pressed, go back
            OnBackButtonClick();
        }
    }
}

void CharacterCreationState::CreateControls()
{
    // Load font
    TTF_Font* font = TTF_OpenFont("assets/data/font.ttf", 24);
    if (!font) {
        std::cerr << "Failed to load font: " << TTF_GetError() << std::endl;
        return;
    }
    
    // Create title label
    m_titleLabel = std::make_shared<Label>(0, 50, 800, 40, "Create Character");
    m_titleLabel->SetFont(font);
    m_titleLabel->SetTextColor({255, 255, 255, 255});
    m_titleLabel->SetAlignment(TextAlignment::CENTER);
    
    // Create name label
    m_nameLabel = std::make_shared<Label>(50, 120, 100, 30, "Name:");
    m_nameLabel->SetFont(font);
    m_nameLabel->SetTextColor({255, 255, 255, 255});
    
    // Create name input
    m_nameInput = std::make_shared<TextInput>(150, 120, 200, 30);
    m_nameInput->SetFont(font);
    m_nameInput->SetTextColor({0, 0, 0, 255});
    m_nameInput->SetBackgroundColor({255, 255, 255, 255});
    m_nameInput->SetMaxLength(12);
    
    // Create class label
    m_classLabel = std::make_shared<Label>(50, 170, 100, 30, "Class:");
    m_classLabel->SetFont(font);
    m_classLabel->SetTextColor({255, 255, 255, 255});
    
    // Create class radio buttons
    int radioY = 170;
    
    // Warrior
    auto warriorRadio = std::make_shared<RadioButton>(150, radioY, 100, 30, "Warrior");
    warriorRadio->SetFont(font);
    warriorRadio->SetTextColor({255, 255, 255, 255});
    warriorRadio->SetChecked(true);  // Default selection
    warriorRadio->SetOnClick([this]() { OnClassSelected(PlayerClass::WARRIOR); });
    m_classRadioButtons.push_back(warriorRadio);
    
    radioY += 40;
    
    // Wizard
    auto wizardRadio = std::make_shared<RadioButton>(150, radioY, 100, 30, "Wizard");
    wizardRadio->SetFont(font);
    wizardRadio->SetTextColor({255, 255, 255, 255});
    wizardRadio->SetOnClick([this]() { OnClassSelected(PlayerClass::WIZARD); });
    m_classRadioButtons.push_back(wizardRadio);
    
    radioY += 40;
    
    // Taoist
    auto taoistRadio = std::make_shared<RadioButton>(150, radioY, 100, 30, "Taoist");
    taoistRadio->SetFont(font);
    taoistRadio->SetTextColor({255, 255, 255, 255});
    taoistRadio->SetOnClick([this]() { OnClassSelected(PlayerClass::TAOIST); });
    m_classRadioButtons.push_back(taoistRadio);
    
    radioY += 40;
    
    // Assassin
    auto assassinRadio = std::make_shared<RadioButton>(150, radioY, 100, 30, "Assassin");
    assassinRadio->SetFont(font);
    assassinRadio->SetTextColor({255, 255, 255, 255});
    assassinRadio->SetOnClick([this]() { OnClassSelected(PlayerClass::ASSASSIN); });
    m_classRadioButtons.push_back(assassinRadio);
    
    // Set up radio button group
    for (size_t i = 0; i < m_classRadioButtons.size(); i++) {
        for (size_t j = 0; j < m_classRadioButtons.size(); j++) {
            if (i != j) {
                m_classRadioButtons[i]->AddToGroup(m_classRadioButtons[j].get());
            }
        }
    }
    
    // Create create button
    m_createButton = std::make_shared<Button>(200, 350, 100, 40, "Create");
    m_createButton->SetFont(font);
    m_createButton->SetTextColor({255, 255, 255, 255});
    m_createButton->SetOnClick([this]() { OnCreateButtonClick(); });
    
    // Create back button
    m_backButton = std::make_shared<Button>(350, 350, 100, 40, "Back");
    m_backButton->SetFont(font);
    m_backButton->SetTextColor({255, 255, 255, 255});
    m_backButton->SetOnClick([this]() { OnBackButtonClick(); });
    
    // Create status label
    m_statusLabel = std::make_shared<Label>(0, 400, 800, 30, "");
    m_statusLabel->SetFont(font);
    m_statusLabel->SetTextColor({255, 0, 0, 255});
    m_statusLabel->SetAlignment(TextAlignment::CENTER);
    
    // Close font
    TTF_CloseFont(font);
}

void CharacterCreationState::OnCreateButtonClick()
{
    // Check if we're already creating
    if (m_creating) {
        return;
    }
    
    // Get character name
    std::string name = m_nameInput->GetText();
    
    // Validate name
    if (name.empty()) {
        m_statusLabel->SetText("Please enter a name");
        return;
    }
    
    // Set creating state
    m_creating = true;
    m_statusLabel->SetText("Creating character...");
    
    // Send character create request
    Packet packet(PacketType::CHARACTER_CREATE_REQUEST);
    packet << name << static_cast<uint8_t>(m_selectedClass);
    m_networkManager->QueuePacket(packet);
}

void CharacterCreationState::OnBackButtonClick()
{
    // Go back to character selection state
    m_app->ChangeState(std::make_unique<CharacterSelectionState>(m_app, m_networkManager));
}

void CharacterCreationState::OnClassSelected(PlayerClass playerClass)
{
    // Set selected class
    m_selectedClass = playerClass;
    
    // Update character preview
    std::string previewFile = "assets/data/character_preview_" + std::to_string(static_cast<int>(m_selectedClass)) + ".png";
    m_characterPreviewTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (!m_characterPreviewTexture->LoadFromFile(previewFile)) {
        std::cerr << "Failed to load character preview texture: " << previewFile << std::endl;
    }
}

void CharacterCreationState::OnCharacterCreateResponse(const Packet& packet)
{
    // Reset creating state
    m_creating = false;
    
    // Read response
    uint8_t success;
    std::string message;
    packet >> success >> message;
    
    if (success) {
        // Create successful
        m_statusLabel->SetText("Character created");
        
        // Go back to character selection state
        m_app->ChangeState(std::make_unique<CharacterSelectionState>(m_app, m_networkManager));
    } else {
        // Create failed
        m_statusLabel->SetText(message);
    }
}

