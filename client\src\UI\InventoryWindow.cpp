#include "InventoryWindow.h"
#include <algorithm>
#include <iostream>

InventoryWindow::InventoryWindow(int x, int y, int width, int height, const std::string& name)
    : UIControl(x, y, width, height, name)
    , m_inventory(nullptr)
    , m_font(nullptr)
    , m_textColor({255, 255, 255, 255})
    , m_backgroundColor({0, 0, 0, 200})
    , m_borderColor({255, 255, 255, 255})
    , m_slotColor({50, 50, 50, 200})
    , m_selectedSlotColor({100, 100, 255, 200})
    , m_slotSize(40)
    , m_slotSpacing(5)
    , m_slotsPerRow(5)
    , m_selectedSlot(-1)
    , m_draggedSlot(-1)
    , m_isDragging(false)
    , m_dragX(0)
    , m_dragY(0)
    , m_resourceFile("items")
{
}

InventoryWindow::~InventoryWindow()
{
}

void InventoryWindow::Update(int deltaTime)
{
    // Update base class
    UIControl::Update(deltaTime);
}

void InventoryWindow::Render(SDL_Renderer* renderer)
{
    // Skip if not visible
    if (!m_visible) {
        return;
    }

    // Render background
    SDL_Rect backgroundRect = {m_x, m_y, m_width, m_height};
    SDL_SetRenderDrawColor(renderer, m_backgroundColor.r, m_backgroundColor.g, m_backgroundColor.b, m_backgroundColor.a);
    SDL_RenderFillRect(renderer, &backgroundRect);

    // Render border
    SDL_SetRenderDrawColor(renderer, m_borderColor.r, m_borderColor.g, m_borderColor.b, m_borderColor.a);
    SDL_RenderDrawRect(renderer, &backgroundRect);

    // Render title
    if (m_font) {
        SDL_Surface* surface = TTF_RenderText_Blended(m_font, "Inventory", m_textColor);
        if (surface) {
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                SDL_Rect textRect = {m_x + 10, m_y + 10, surface->w, surface->h};
                SDL_RenderCopy(renderer, texture, nullptr, &textRect);
                SDL_DestroyTexture(texture);
            }
            SDL_FreeSurface(surface);
        }
    }

    // Render inventory slots
    if (m_inventory) {
        const auto& items = m_inventory->GetItems();
        for (int i = 0; i < Inventory::MAX_INVENTORY_SIZE; i++) {
            int slotX, slotY;
            GetSlotPosition(i, slotX, slotY);

            // Render slot background
            SDL_Rect slotRect = {slotX, slotY, m_slotSize, m_slotSize};
            if (i == m_selectedSlot) {
                SDL_SetRenderDrawColor(renderer, m_selectedSlotColor.r, m_selectedSlotColor.g, m_selectedSlotColor.b, m_selectedSlotColor.a);
            } else {
                SDL_SetRenderDrawColor(renderer, m_slotColor.r, m_slotColor.g, m_slotColor.b, m_slotColor.a);
            }
            SDL_RenderFillRect(renderer, &slotRect);

            // Render slot border
            SDL_SetRenderDrawColor(renderer, m_borderColor.r, m_borderColor.g, m_borderColor.b, m_borderColor.a);
            SDL_RenderDrawRect(renderer, &slotRect);

            // Render item if there is one in this slot
            if (i < items.size() && items[i]) {
                if (i != m_draggedSlot || !m_isDragging) {
                    RenderItem(renderer, *items[i], slotX, slotY, m_slotSize);
                }
            }
        }
    }

    // Render dragged item
    if (m_isDragging && m_inventory) {
        const auto& items = m_inventory->GetItems();
        if (m_draggedSlot >= 0 && m_draggedSlot < items.size() && items[m_draggedSlot]) {
            RenderItem(renderer, *items[m_draggedSlot], m_dragX - m_slotSize / 2, m_dragY - m_slotSize / 2, m_slotSize);
        }
    }

    // Render children
    for (auto& child : m_children) {
        if (child->IsVisible()) {
            child->Render(renderer);
        }
    }
}

bool InventoryWindow::HandleMouseButton(Uint8 button, bool pressed, int x, int y)
{
    // Check if the click is within the inventory window
    if (x >= m_x && x < m_x + m_width && y >= m_y && y < m_y + m_height) {
        if (pressed) {
            // Mouse down event
            // Get the slot at the click position
            int slot = GetSlotAt(x, y);
            if (slot != -1) {
                // Select the slot
                m_selectedSlot = slot;
                if (m_onItemSelected) {
                    m_onItemSelected(slot);
                }

                // Start dragging if left button is pressed
                if (button == SDL_BUTTON_LEFT) {
                    m_draggedSlot = slot;
                    m_isDragging = true;
                    m_dragX = x;
                    m_dragY = y;
                }
                // Use item if right button is pressed
                else if (button == SDL_BUTTON_RIGHT) {
                    if (m_onItemUsed) {
                        m_onItemUsed(slot);
                    }
                }
            }
            return true;
        } else {
            // Mouse up event
            // Check if we were dragging an item
            if (m_isDragging) {
                // Get the slot at the drop position
                int slot = GetSlotAt(x, y);
                if (slot != -1 && slot != m_draggedSlot) {
                    // Move the item to the new slot
                    if (m_onItemMoved) {
                        m_onItemMoved(m_draggedSlot, slot);
                    }
                }
                // Check if the item was dropped outside the inventory
                else if (x < m_x || x >= m_x + m_width || y < m_y || y >= m_y + m_height) {
                    // Drop the item
                    if (m_onItemDropped) {
                        m_onItemDropped(m_draggedSlot);
                    }
                }

                // Stop dragging
                m_isDragging = false;
                m_draggedSlot = -1;
                return true;
            }
        }
    }
    return false;
}

bool InventoryWindow::HandleMouseMotion(int x, int y)
{
    // Update drag position if dragging
    if (m_isDragging) {
        m_dragX = x;
        m_dragY = y;
        return true;
    }
    return false;
}

bool InventoryWindow::HandleKey(SDL_Keycode key, bool pressed)
{
    // Handle key events
    if (pressed) {
        switch (key) {
            case SDLK_ESCAPE:
                // Close the inventory window
                SetVisible(false);
                return true;
            case SDLK_UP:
                // Move selection up
                if (m_selectedSlot >= m_slotsPerRow) {
                    m_selectedSlot -= m_slotsPerRow;
                    if (m_onItemSelected) {
                        m_onItemSelected(m_selectedSlot);
                    }
                }
                return true;
            case SDLK_DOWN:
                // Move selection down
                if (m_selectedSlot + m_slotsPerRow < Inventory::MAX_INVENTORY_SIZE) {
                    m_selectedSlot += m_slotsPerRow;
                    if (m_onItemSelected) {
                        m_onItemSelected(m_selectedSlot);
                    }
                }
                return true;
            case SDLK_LEFT:
                // Move selection left
                if (m_selectedSlot % m_slotsPerRow > 0) {
                    m_selectedSlot--;
                    if (m_onItemSelected) {
                        m_onItemSelected(m_selectedSlot);
                    }
                }
                return true;
            case SDLK_RIGHT:
                // Move selection right
                if (m_selectedSlot % m_slotsPerRow < m_slotsPerRow - 1 && m_selectedSlot + 1 < Inventory::MAX_INVENTORY_SIZE) {
                    m_selectedSlot++;
                    if (m_onItemSelected) {
                        m_onItemSelected(m_selectedSlot);
                    }
                }
                return true;
            case SDLK_RETURN:
                // Use selected item
                if (m_selectedSlot != -1 && m_onItemUsed) {
                    m_onItemUsed(m_selectedSlot);
                }
                return true;
        }
    }
    return false;
}

int InventoryWindow::GetSlotAt(int x, int y) const
{
    // Calculate the position of the first slot
    int startX = m_x + (m_width - (m_slotSize * m_slotsPerRow + m_slotSpacing * (m_slotsPerRow - 1))) / 2;
    int startY = m_y + 40; // Leave space for the title

    // Check if the position is within the inventory grid
    if (x < startX || y < startY) {
        return -1;
    }

    // Calculate the slot coordinates
    int slotX = (x - startX) / (m_slotSize + m_slotSpacing);
    int slotY = (y - startY) / (m_slotSize + m_slotSpacing);

    // Check if the slot coordinates are valid
    if (slotX >= m_slotsPerRow || slotY >= (Inventory::MAX_INVENTORY_SIZE + m_slotsPerRow - 1) / m_slotsPerRow) {
        return -1;
    }

    // Calculate the slot index
    int slot = slotY * m_slotsPerRow + slotX;
    if (slot >= Inventory::MAX_INVENTORY_SIZE) {
        return -1;
    }

    return slot;
}

void InventoryWindow::GetSlotPosition(int slot, int& x, int& y) const
{
    // Calculate the position of the first slot
    int startX = m_x + (m_width - (m_slotSize * m_slotsPerRow + m_slotSpacing * (m_slotsPerRow - 1))) / 2;
    int startY = m_y + 40; // Leave space for the title

    // Calculate the slot coordinates
    int slotX = slot % m_slotsPerRow;
    int slotY = slot / m_slotsPerRow;

    // Calculate the slot position
    x = startX + slotX * (m_slotSize + m_slotSpacing);
    y = startY + slotY * (m_slotSize + m_slotSpacing);
}

void InventoryWindow::RenderItem(SDL_Renderer* renderer, const Item& item, int x, int y, int size)
{
    // Get the item image from the WIL manager
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, item.GetLook());
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {x, y, size, size};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }

    // Render item count if stackable
    if (item.GetValue(0) > 1 && m_font) {
        std::string countText = std::to_string(item.GetValue(0));
        SDL_Surface* surface = TTF_RenderText_Blended(m_font, countText.c_str(), m_textColor);
        if (surface) {
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                SDL_Rect textRect = {x + size - surface->w - 2, y + size - surface->h - 2, surface->w, surface->h};
                SDL_RenderCopy(renderer, texture, nullptr, &textRect);
                SDL_DestroyTexture(texture);
            }
            SDL_FreeSurface(surface);
        }
    }
}
