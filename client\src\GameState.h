#pragma once

#include <SDL2/SDL.h>

// Forward declaration
class Application;

/**
 * @class GameState
 * @brief Interface for game states
 * 
 * This abstract class defines the interface for game states.
 * Each state (like intro, play, menu) will implement this interface.
 */
class GameState {
protected:
    Application* m_app;  ///< Pointer to the application
    
public:
    /**
     * @brief Constructor
     * @param app Pointer to the application
     */
    GameState(Application* app) : m_app(app) {}
    
    /**
     * @brief Virtual destructor
     */
    virtual ~GameState() {}
    
    /**
     * @brief Called when entering the state
     */
    virtual void Enter() = 0;
    
    /**
     * @brief Called when exiting the state
     */
    virtual void Exit() = 0;
    
    /**
     * @brief Update the state
     * @param deltaTime Time elapsed since last frame in seconds
     */
    virtual void Update(float deltaTime) = 0;
    
    /**
     * @brief Render the state
     */
    virtual void Render() = 0;
    
    /**
     * @brief Handle SDL events
     * @param event SDL event to handle
     */
    virtual void HandleEvents(SDL_Event& event) = 0;
};
