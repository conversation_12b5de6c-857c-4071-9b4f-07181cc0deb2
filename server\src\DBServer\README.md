# 传奇数据库服务器 (DBServer)

## 概述

DBServer是传奇服务器架构中的核心组件，负责管理所有玩家角色数据的存储和检索。它使用文件系统存储数据，提供高效的索引和查询功能。

## 主要功能

- **角色数据管理**：存储和管理玩家角色的完整数据
- **账号管理**：管理账号下的角色列表
- **数据持久化**：自动保存角色数据到文件系统
- **快速索引**：提供角色名和账号的快速查找
- **数据备份**：支持自动和手动数据备份
- **网络通信**：与GameServer和LoginServer通信

## 架构设计

### 核心组件

1. **HumanDB类**
   - 管理人物数据库文件（.DB和.idx）
   - 提供CRUD操作接口
   - 维护内存索引以提高查询效率

2. **DBServerMain类**
   - 主服务器类，管理网络连接
   - 处理来自GameServer的请求
   - 与LoginServer通信进行会话验证

### 数据存储格式

- **Hum.DB**：主数据文件，存储所有角色数据
- **Hum.DB.idx**：索引文件，加速查询操作

### 网络协议

DBServer实现了以下协议：

- `DB_LOADHUMANRCD (100)`: 加载角色数据
- `DB_SAVEHUMANRCD (101)`: 保存角色数据
- `DB_QUERYCHR (102)`: 查询账号下的角色列表
- `DB_NEWCHR (103)`: 创建新角色
- `DB_DELCHR (104)`: 删除角色

## 配置说明

配置文件位于：`config/DBServer.ini`

主要配置项：

```ini
[DB]
Dir=./FDB/              # 数据库文件目录
Backup=./Backup/        # 备份目录

[Setup]
ServerPort=6000         # 服务器端口
ServerAddr=0.0.0.0      # 服务器地址

[Server]
IDSAddr=127.0.0.1       # LoginServer地址
IDSPort=5600            # LoginServer端口
```

## 编译和运行

### 编译

```bash
cd server/build
cmake ..
make DBServer
```

### 运行

```bash
./DBServer
```

### 目录结构

运行前请确保以下目录存在：
- `./FDB/` - 数据库文件目录
- `./Backup/` - 备份目录
- `./Log/` - 日志目录
- `./config/` - 配置文件目录

## 安全配置

### IP白名单

编辑 `config/!AddrTable.txt` 文件，添加允许连接的GameServer IP地址：

```
127.0.0.1
*************
```

## 维护操作

### 数据备份

DBServer会在以下情况自动备份：
- 服务器正常关闭时
- 每天凌晨（可配置）

手动备份：
```bash
cp ./FDB/Hum.DB ./Backup/Hum.DB.$(date +%Y%m%d_%H%M%S)
```

### 数据恢复

1. 停止DBServer
2. 备份当前数据文件
3. 复制备份文件到FDB目录
4. 重启DBServer

## 性能优化

- **内存索引**：所有角色名和账号索引都缓存在内存中
- **延迟写入**：批量更新减少磁盘IO
- **文件锁**：使用读写锁提高并发性能

## 故障排查

### 常见问题

1. **无法启动**
   - 检查端口是否被占用
   - 检查配置文件是否正确
   - 确保数据目录有写权限

2. **连接失败**
   - 检查防火墙设置
   - 验证IP白名单配置
   - 查看日志文件

3. **数据损坏**
   - 使用备份恢复
   - 检查磁盘空间
   - 运行数据库修复工具

## 开发说明

### 扩展功能

如需添加新的数据库操作：

1. 在 `PacketTypes.h` 中定义新协议
2. 在 `DBServerMain::ProcessServerPacket` 中添加处理分支
3. 实现具体的处理函数
4. 更新客户端相应的协议处理

### 数据结构

角色数据使用 `HumDataInfo` 结构体，定义在 `Common/Types.h` 中。

## 版本历史

- v1.0.0 - 初始版本，基础功能实现
  - 角色CRUD操作
  - 网络通信框架
  - 文件存储系统

## 待实现功能

- [ ] 与LoginServer的完整集成
- [ ] 数据加密存储
- [ ] 自动清理过期数据
- [ ] 数据库修复工具
- [ ] Web管理界面
- [ ] 集群支持 