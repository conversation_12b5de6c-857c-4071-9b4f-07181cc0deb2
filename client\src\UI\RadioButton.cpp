#include "RadioButton.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>

RadioButton::RadioButton(int x, int y, int width, int height, const std::string& text, const std::string& name)
    : UIControl(x, y, width, height, name)
    , m_text(text)
    , m_font(nullptr)
    , m_textColor({255, 255, 255, 255})
    , m_backgroundColor({0, 0, 0, 0})
    , m_borderColor({255, 255, 255, 255})
    , m_checkColor({255, 255, 255, 255})
    , m_checked(false)
{
}

RadioButton::~RadioButton()
{
}

void RadioButton::Update(int deltaTime)
{
    // Nothing to update
}

void RadioButton::Render(SDL_Renderer* renderer)
{
    // Skip if not visible
    if (!m_visible) {
        return;
    }

    // First try to use the resource indices from UIControl for background
    if (!m_resourceFile.empty() && m_wilManager && m_normalImageIndex >= 0 && m_normalImageIndex != NO_IMAGE) {
        // Let the base class handle rendering using resource indices
        UIControl::Render(renderer);
    } else {
        // Calculate radio button circle position and size
        int circleSize = m_height - 10;
        int circleX = m_x + 5;
        int circleY = m_y + 5;

        // Render radio button circle
        SDL_SetRenderDrawColor(renderer, m_borderColor.r, m_borderColor.g, m_borderColor.b, m_borderColor.a);

        // Draw circle outline
        for (int w = 0; w < circleSize; w++) {
            for (int h = 0; h < circleSize; h++) {
                int dx = circleSize / 2 - w;
                int dy = circleSize / 2 - h;
                if ((dx * dx + dy * dy) <= (circleSize * circleSize) / 4) {
                    SDL_RenderDrawPoint(renderer, circleX + w, circleY + h);
                }
            }
        }

        // Render check if checked
        if (m_checked) {
            SDL_SetRenderDrawColor(renderer, m_checkColor.r, m_checkColor.g, m_checkColor.b, m_checkColor.a);

            // Draw inner circle
            int innerSize = circleSize - 6;
            int innerX = circleX + 3;
            int innerY = circleY + 3;

            for (int w = 0; w < innerSize; w++) {
                for (int h = 0; h < innerSize; h++) {
                    int dx = innerSize / 2 - w;
                    int dy = innerSize / 2 - h;
                    if ((dx * dx + dy * dy) <= (innerSize * innerSize) / 4) {
                        SDL_RenderDrawPoint(renderer, innerX + w, innerY + h);
                    }
                }
            }
        }
    }

    // Render text
    if (m_font && !m_text.empty()) {
        // Calculate circle size for text positioning
        int circleSize = m_height - 10;

        SDL_Surface* surface = TTF_RenderText_Blended(m_font, m_text.c_str(), m_textColor);
        if (surface) {
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                int textX = m_x + circleSize + 10;
                int textY = m_y + (m_height - surface->h) / 2;
                SDL_Rect textRect = {textX, textY, surface->w, surface->h};
                SDL_RenderCopy(renderer, texture, nullptr, &textRect);
                SDL_DestroyTexture(texture);
            }
            SDL_FreeSurface(surface);
        }
    }

    // Render children (only if we didn't call UIControl::Render already)
    if (m_resourceFile.empty() || !m_wilManager || m_normalImageIndex < 0 || m_normalImageIndex == NO_IMAGE) {
        for (auto& child : m_children) {
            if (child->IsVisible()) {
                child->Render(renderer);
            }
        }
    }
}

bool RadioButton::HandleEvent(const SDL_Event& event)
{
    // Skip if not visible or enabled
    if (!m_visible || !m_enabled) {
        return false;
    }

    // Handle mouse events
    if (event.type == SDL_MOUSEBUTTONDOWN) {
        if (event.button.button == SDL_BUTTON_LEFT) {
            // Check if the mouse is inside the control
            int mouseX = event.button.x;
            int mouseY = event.button.y;

            if (mouseX >= m_x && mouseX < m_x + m_width && mouseY >= m_y && mouseY < m_y + m_height) {
                // Set checked
                SetChecked(true);

                // Call click callback
                if (m_onClick) {
                    m_onClick();
                }

                return true;
            }
        }
    }

    return false;
}

void RadioButton::SetChecked(bool checked)
{
    if (checked == m_checked) {
        return;
    }

    m_checked = checked;

    // Uncheck other radio buttons in the group
    if (m_checked) {
        for (RadioButton* radioButton : m_group) {
            if (radioButton && radioButton != this) {
                radioButton->SetChecked(false);
            }
        }
    }
}

void RadioButton::AddToGroup(RadioButton* radioButton)
{
    if (radioButton && radioButton != this) {
        // Add to this group
        m_group.push_back(radioButton);

        // Add this to other radio button's group
        radioButton->m_group.push_back(this);
    }
}

