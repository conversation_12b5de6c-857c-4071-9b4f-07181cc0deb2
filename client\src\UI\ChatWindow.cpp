#include "ChatWindow.h"
#include <algorithm>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <ctime>

ChatMessage::ChatMessage(const std::string& text,
                         const SDL_Color& textColor,
                         const SDL_Color& backgroundColor,
                         int senderId,
                         const std::string& senderName)
    : text(text)
    , textColor(textColor)
    , backgroundColor(backgroundColor)
    , timestamp(static_cast<int>(time(nullptr)))
    , senderId(senderId)
    , senderName(senderName)
{
}

ChatWindow::ChatWindow(int x, int y, int width, int height, TTF_Font* font, int maxMessages, const std::string& name)
    : UIControl(x, y, width, height, name)
    , m_maxMessages(maxMessages)
    , m_scrollPosition(0)
    , m_visibleLines(0)
    , m_font(font)
    , m_showTimestamps(false)
{
    // Calculate visible lines based on font height
    if (m_font) {
        int fontHeight;
        TTF_SizeText(m_font, "Tg", nullptr, &fontHeight);
        m_visibleLines = height / (fontHeight + 2); // +2 for padding
    }
}

ChatWindow::~ChatWindow()
{
    // Font is managed externally
}

void ChatWindow::AddMessage(const ChatMessage& message)
{
    // Add message to the list
    m_messages.push_back(message);

    // Remove oldest messages if we exceed the maximum
    while (m_messages.size() > m_maxMessages) {
        m_messages.erase(m_messages.begin());
    }

    // Scroll to bottom if we're already at the bottom
    if (m_scrollPosition == 0) {
        ScrollToBottom();
    }
}

void ChatWindow::AddMessage(const std::string& text,
                           const SDL_Color& textColor,
                           const SDL_Color& backgroundColor,
                           int senderId,
                           const std::string& senderName)
{
    AddMessage(ChatMessage(text, textColor, backgroundColor, senderId, senderName));
}

void ChatWindow::AddMessage(const std::string& text,
                           ChatMessageType type,
                           int senderId,
                           const std::string& senderName)
{
    SDL_Color textColor = {255, 255, 255, 255}; // Default: white
    SDL_Color bgColor = {0, 0, 0, 0}; // Default: transparent

    // Set colors based on message type
    switch (type) {
        case ChatMessageType::NORMAL:
            textColor = {255, 255, 255, 255}; // White
            break;
        case ChatMessageType::WHISPER:
            textColor = {255, 128, 255, 255}; // Pink
            break;
        case ChatMessageType::GUILD:
            textColor = {128, 255, 128, 255}; // Light green
            break;
        case ChatMessageType::GROUP:
            textColor = {128, 128, 255, 255}; // Light blue
            break;
        case ChatMessageType::SYSTEM:
            textColor = {255, 255, 0, 255}; // Yellow
            break;
        case ChatMessageType::CRY:
            textColor = {255, 0, 0, 255}; // Red
            break;
        case ChatMessageType::MERCHANT:
            textColor = {255, 165, 0, 255}; // Orange
            break;
    }

    AddMessage(text, textColor, bgColor, senderId, senderName);
}

void ChatWindow::ScrollUp(int lines)
{
    m_scrollPosition = std::min(m_scrollPosition + lines, static_cast<int>(m_messages.size()) - m_visibleLines);
    if (m_scrollPosition < 0) {
        m_scrollPosition = 0;
    }
}

void ChatWindow::ScrollDown(int lines)
{
    m_scrollPosition = std::max(m_scrollPosition - lines, 0);
}

void ChatWindow::ScrollToTop()
{
    m_scrollPosition = static_cast<int>(m_messages.size()) - m_visibleLines;
    if (m_scrollPosition < 0) {
        m_scrollPosition = 0;
    }
}

void ChatWindow::ScrollToBottom()
{
    m_scrollPosition = 0;
}

void ChatWindow::ShowTimestamps(bool show)
{
    m_showTimestamps = show;
}

void ChatWindow::SetBackgroundTexture(std::shared_ptr<Texture> texture)
{
    m_backgroundTexture = texture;
}

std::shared_ptr<Texture> ChatWindow::CreateMessageTexture(const ChatMessage& message)
{
    if (!m_font) {
        return nullptr;
    }

    std::string displayText;

    // Add timestamp if enabled
    if (m_showTimestamps) {
        std::time_t time = static_cast<std::time_t>(message.timestamp);
        std::tm* tm = std::localtime(&time);
        std::stringstream ss;
        ss << "[" << std::setw(2) << std::setfill('0') << tm->tm_hour << ":"
           << std::setw(2) << std::setfill('0') << tm->tm_min << "] ";
        displayText += ss.str();
    }

    // Add sender name if available
    if (!message.senderName.empty()) {
        displayText += message.senderName + ": ";
    }

    // Add message text
    displayText += message.text;

    // Create texture
    SDL_Surface* surface = TTF_RenderText_Blended(m_font, displayText.c_str(), message.textColor);
    if (!surface) {
        std::cerr << "Failed to render text: " << TTF_GetError() << std::endl;
        return nullptr;
    }

    // Create texture from surface
    std::shared_ptr<Texture> texture = std::make_shared<Texture>();
    texture->LoadFromSurface(surface);

    // Free surface
    SDL_FreeSurface(surface);

    return texture;
}

void ChatWindow::Update(int deltaTime)
{
    // Nothing to update
}

void ChatWindow::Render(SDL_Renderer* renderer)
{
    if (!IsVisible()) {
        return;
    }

    // Render background
    if (m_backgroundTexture) {
        m_backgroundTexture->Render(m_x, m_y);
    } else {
        // Render a default semi-transparent background
        SDL_Rect rect = {m_x, m_y, m_width, m_height};
        SDL_SetRenderDrawBlendMode(renderer, SDL_BLENDMODE_BLEND);
        SDL_SetRenderDrawColor(renderer, 0, 0, 0, 128);
        SDL_RenderFillRect(renderer, &rect);
        SDL_SetRenderDrawBlendMode(renderer, SDL_BLENDMODE_NONE);
    }

    // Render messages
    int y = m_y + m_height - 5; // Start from bottom

    // Calculate range of messages to display
    int startIdx = m_scrollPosition;
    int endIdx = std::min(startIdx + m_visibleLines, static_cast<int>(m_messages.size()));

    // Render messages in reverse order (newest at bottom)
    for (int i = endIdx - 1; i >= startIdx; i--) {
        if (i < 0 || i >= static_cast<int>(m_messages.size())) {
            continue;
        }

        const ChatMessage& message = m_messages[i];
        std::shared_ptr<Texture> texture = CreateMessageTexture(message);

        if (texture) {
            int textHeight = texture->GetHeight();
            y -= textHeight + 2; // +2 for padding

            texture->Render(m_x + 5, y);
        }
    }

    // Render children
    UIControl::Render(renderer);
}

bool ChatWindow::OnMouseWheel(const SDL_MouseWheelEvent& event)
{
    // Check if mouse is over the chat window
    int mouseX, mouseY;
    SDL_GetMouseState(&mouseX, &mouseY);

    if (mouseX >= m_x && mouseX < m_x + m_width &&
        mouseY >= m_y && mouseY < m_y + m_height) {
        // Scroll up or down based on wheel direction
        if (event.y > 0) {
            ScrollUp(event.y);
        } else if (event.y < 0) {
            ScrollDown(-event.y);
        }

        return true;
    }

    return false;
}
