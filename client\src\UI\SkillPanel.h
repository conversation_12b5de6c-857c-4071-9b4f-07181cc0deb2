#pragma once

#include "UIControl.h"
#include "Button.h"
#include "Label.h"
#include "../Actor/Player.h"
#include "../Skill/SkillManager.h"
#include <memory>
#include <vector>
#include <string>
#include <SDL2/SDL.h>

/**
 * @class SkillPanel
 * @brief UI for displaying and managing character skills
 */
class SkillPanel : public UIControl {
public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param player Player reference
     * @param skillManager Skill manager reference
     */
    SkillPanel(int x, int y, int width, int height, std::shared_ptr<Player> player, std::shared_ptr<SkillManager> skillManager);

    /**
     * @brief Destructor
     */
    virtual ~SkillPanel();

    /**
     * @brief Update the UI
     * @param deltaTime Time since last update in milliseconds
     */
    virtual void Update(int deltaTime) override;

    /**
     * @brief Render the UI
     * @param renderer SDL renderer
     */
    virtual void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Handle mouse button events
     * @param button Mouse button
     * @param pressed True if pressed, false if released
     * @param x Mouse x position
     * @param y Mouse y position
     * @return True if handled, false otherwise
     */
    virtual bool HandleMouseButton(Uint8 button, bool pressed, int x, int y) override;

    /**
     * @brief Handle mouse motion events
     * @param x Mouse x position
     * @param y Mouse y position
     * @return True if handled, false otherwise
     */
    virtual bool HandleMouseMotion(int x, int y) override;

    /**
     * @brief Handle key events
     * @param key Key code
     * @param pressed True if pressed, false if released
     * @return True if handled, false otherwise
     */
    virtual bool HandleKey(SDL_Keycode key, bool pressed) override;

    /**
     * @brief Show the UI
     */
    void Show();

    /**
     * @brief Hide the UI
     */
    void Hide();

    /**
     * @brief Refresh the skill list
     */
    void RefreshSkillList();

private:
    /**
     * @brief Create UI controls
     */
    void CreateControls();

    /**
     * @brief Update page navigation
     */
    void UpdatePageNavigation();

    /**
     * @brief Handle skill button click
     * @param control Control that was clicked
     */
    void OnSkillButtonClick(UIControl* control);

    /**
     * @brief Handle close button click
     * @param control Control that was clicked (can be nullptr)
     */
    void OnCloseButtonClick(UIControl* control);

    /**
     * @brief Handle configure hotkeys button click
     * @param control Control that was clicked (can be nullptr)
     */
    void OnConfigHotkeysButtonClick(UIControl* control);

    /**
     * @brief Handle previous page button click
     * @param control Control that was clicked (can be nullptr)
     */
    void OnPrevPageButtonClick(UIControl* control);

    /**
     * @brief Handle next page button click
     * @param control Control that was clicked (can be nullptr)
     */
    void OnNextPageButtonClick(UIControl* control);

    std::shared_ptr<Player> m_player;                          ///< Player reference
    std::shared_ptr<SkillManager> m_skillManager;              ///< Skill manager reference
    std::shared_ptr<Button> m_closeButton;                     ///< Close button
    std::shared_ptr<Button> m_configHotkeysButton;             ///< Configure hotkeys button
    std::shared_ptr<Button> m_prevPageButton;                  ///< Previous page button
    std::shared_ptr<Button> m_nextPageButton;                  ///< Next page button
    std::shared_ptr<Label> m_pageLabel;                        ///< Page label
    std::vector<std::shared_ptr<Button>> m_skillButtons;       ///< Skill buttons
    std::vector<std::shared_ptr<Label>> m_skillLabels;         ///< Skill labels

    int m_currentPage;                                         ///< Current page
    int m_totalPages;                                          ///< Total pages
    int m_skillsPerPage;                                       ///< Skills per page
    bool m_isDragging;                                         ///< Whether a skill is being dragged
    int m_draggedSkillId;                                      ///< ID of the dragged skill
    int m_dragX;                                               ///< X position of dragged skill
    int m_dragY;                                               ///< Y position of dragged skill
    std::string m_resourceFile;                                ///< Resource file for skill images
};

