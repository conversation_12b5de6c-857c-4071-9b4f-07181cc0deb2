#include "RunGateServer.h"
#include "../Common/Logger.h"
#include <iostream>
#include <iomanip>
#include <string>
#include <signal.h>
#include <thread>
#include <chrono>

#ifdef _WIN32
#include <windows.h>
#include <conio.h>
#else
#include <termios.h>
#include <unistd.h>
#endif

using namespace MirServer;

// 全局RunGateServer实例
std::unique_ptr<RunGateServer> g_runGateServer;

// 信号处理函数
void SignalHandler(int signal) {
    std::cout << "\nReceived signal " << signal << ", shutting down RunGateServer..." << std::endl;
    
    if (g_runGateServer) {
        g_runGateServer->Stop();
        g_runGateServer.reset();
    }
    
    exit(0);
}

// 显示菜单
void ShowMenu() {
    std::cout << "\n=== RunGateServer Control Menu ===" << std::endl;
    std::cout << "1. Start Server" << std::endl;
    std::cout << "2. Stop Server" << std::endl;
    std::cout << "3. Show Status" << std::endl;
    std::cout << "4. Reload Configuration" << std::endl;
    std::cout << "5. Show Version Info" << std::endl;
    std::cout << "6. Close All Connections" << std::endl;
    std::cout << "0. Exit" << std::endl;
    std::cout << "=================================" << std::endl;
    std::cout << "Please select an option: ";
}

// 显示服务器状态
void ShowStatus() {
    if (!g_runGateServer) {
        std::cout << "RunGateServer is not initialized." << std::endl;
        return;
    }
    
    std::cout << "\n=== RunGateServer Status ===" << std::endl;
    std::cout << "Running: " << (g_runGateServer->IsRunning() ? "Yes" : "No") << std::endl;
    std::cout << "Active Connections: " << g_runGateServer->GetActiveConnections() << std::endl;
    std::cout << "Total Sessions: " << g_runGateServer->GetSessionCount() << std::endl;
    std::cout << "Version: " << g_runGateServer->GetVersionInfo() << std::endl;
    std::cout << "=============================" << std::endl;
}

// 非阻塞键盘输入检查
bool HasKeyPressed() {
#ifdef _WIN32
    return _kbhit();
#else
    struct termios oldt, newt;
    int ch;
    int oldf;
    
    tcgetattr(STDIN_FILENO, &oldt);
    newt = oldt;
    newt.c_lflag &= ~(ICANON | ECHO);
    tcsetattr(STDIN_FILENO, TCSANOW, &newt);
    oldf = fcntl(STDIN_FILENO, F_GETFL, 0);
    fcntl(STDIN_FILENO, F_SETFL, oldf | O_NONBLOCK);
    
    ch = getchar();
    
    tcsetattr(STDIN_FILENO, TCSANOW, &oldt);
    fcntl(STDIN_FILENO, F_SETFL, oldf);
    
    if (ch != EOF) {
        ungetc(ch, stdin);
        return true;
    }
    
    return false;
#endif
}

int main() {
    std::cout << "====================================" << std::endl;
    std::cout << "=== MirServer RunGateServer     ===" << std::endl;
    std::cout << "=== Version: 1.0.0              ===" << std::endl;
    std::cout << "=== Compatible with HAPPYM2.NET ===" << std::endl;
    std::cout << "=== Build Date: " << __DATE__ << " " << __TIME__ << " ===" << std::endl;
    std::cout << "====================================" << std::endl;
    
    // 设置信号处理
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    #ifdef _WIN32
    signal(SIGBREAK, SignalHandler);
    #endif
    
    try {
        // 创建RunGateServer实例
        g_runGateServer = std::make_unique<RunGateServer>();
        
        // 加载配置
        g_runGateServer->LoadConfig();
        
        std::cout << "RunGateServer initialized successfully." << std::endl;
        std::cout << "Type 'menu' for control options or 'auto' for automatic mode." << std::endl;
        
        // 检查是否自动启动
        std::string startMode;
        std::cout << "Start mode (auto/menu): ";
        std::getline(std::cin, startMode);
        
        if (startMode == "auto") {
            // 自动模式：直接启动服务器
            if (!g_runGateServer->Start()) {
                std::cerr << "Failed to start RunGateServer" << std::endl;
                return -1;
            }
            
            std::cout << "RunGateServer is running in automatic mode." << std::endl;
            std::cout << "Press Ctrl+C to stop or any key to enter menu mode." << std::endl;
            
            // 主循环 - 自动模式
            while (g_runGateServer->IsRunning()) {
                // 检查是否有按键输入
                if (HasKeyPressed()) {
                    std::cin.ignore(); // 清除输入缓冲
                    break; // 进入菜单模式
                }
                
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                
                // 每30秒显示一次状态
                static int statusCounter = 0;
                if (++statusCounter >= 300) { // 100ms * 300 = 30秒
                    statusCounter = 0;
                    auto now = std::chrono::system_clock::now();
                    auto time_t = std::chrono::system_clock::to_time_t(now);
                    
                    std::cout << "[" << std::put_time(std::localtime(&time_t), "%H:%M:%S") << "] "
                             << "Active Connections: " << g_runGateServer->GetActiveConnections() 
                             << ", Sessions: " << g_runGateServer->GetSessionCount() 
                             << std::endl;
                }
            }
        }
        
        // 交互式菜单模式
        int choice;
        bool running = true;
        
        while (running) {
            ShowMenu();
            std::cin >> choice;
            std::cin.ignore(); // 清除输入缓冲区
            
            switch (choice) {
                case 1: { // 启动服务器
                    if (g_runGateServer->IsRunning()) {
                        std::cout << "RunGateServer is already running." << std::endl;
                    } else {
                        std::cout << "Starting RunGateServer..." << std::endl;
                        if (g_runGateServer->Start()) {
                            std::cout << "RunGateServer started successfully." << std::endl;
                        } else {
                            std::cout << "Failed to start RunGateServer." << std::endl;
                        }
                    }
                    break;
                }
                
                case 2: { // 停止服务器
                    if (!g_runGateServer->IsRunning()) {
                        std::cout << "RunGateServer is not running." << std::endl;
                    } else {
                        std::cout << "Stopping RunGateServer..." << std::endl;
                        g_runGateServer->Stop();
                        std::cout << "RunGateServer stopped." << std::endl;
                    }
                    break;
                }
                
                case 3: { // 显示状态
                    ShowStatus();
                    break;
                }
                
                case 4: { // 重新加载配置
                    std::cout << "Reloading configuration..." << std::endl;
                    g_runGateServer->ReloadConfig();
                    std::cout << "Configuration reloaded." << std::endl;
                    break;
                }
                
                case 5: { // 显示版本信息
                    std::cout << "Version: " << g_runGateServer->GetVersionInfo() << std::endl;
                    break;
                }
                
                case 6: { // 关闭所有连接
                    std::cout << "Closing all connections..." << std::endl;
                    g_runGateServer->CloseAllUser();
                    std::cout << "All connections closed." << std::endl;
                    break;
                }
                
                case 0: { // 退出
                    running = false;
                    break;
                }
                
                default: {
                    std::cout << "Invalid option. Please try again." << std::endl;
                    break;
                }
            }
            
            if (choice != 0) {
                std::cout << "\nPress Enter to continue...";
                std::cin.get();
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "RunGateServer exception: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return -1;
    }
    
    // 清理资源
    if (g_runGateServer) {
        g_runGateServer->Stop();
        g_runGateServer.reset();
    }
    
    std::cout << "RunGateServer shutdown completed." << std::endl;
    return 0;
} 