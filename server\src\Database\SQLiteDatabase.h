// SQLiteDatabase.h - SQLite数据库实现
#pragma once

#include "IDatabase.h"
#include <sqlite3.h>
#include <mutex>
#include <thread>
#include <queue>
#include <condition_variable>

namespace MirServer {

// SQLite事务实现
class SQLiteTransaction : public ITransaction {
public:
    SQLiteTransaction(sqlite3* db);
    ~SQLiteTransaction();
    
    bool Commit() override;
    bool Rollback() override;
    bool Execute(const std::string& sql) override;
    bool Execute(const std::string& sql, const std::vector<std::any>& params) override;
    
private:
    sqlite3* m_db;
    bool m_committed;
    bool m_rolledBack;
};

// SQLite数据库实现
class SQLiteDatabase : public IDatabase {
public:
    SQLiteDatabase();
    ~SQLiteDatabase();
    
    // 连接管理
    bool Connect(const std::string& connectionString) override;
    bool Disconnect() override;
    bool IsConnected() const override;
    
    // 基本操作
    bool Execute(const std::string& sql) override;
    bool Execute(const std::string& sql, const std::vector<std::any>& params) override;
    
    // 查询操作
    ResultSet Query(const std::string& sql) override;
    ResultSet Query(const std::string& sql, const std::vector<std::any>& params) override;
    void QueryAsync(const std::string& sql, QueryCallback callback) override;
    
    // 事务管理
    std::unique_ptr<ITransaction> BeginTransaction() override;
    
    // 批量操作
    bool ExecuteBatch(const std::vector<std::string>& sqls) override;
    
    // 工具方法
    int64_t GetLastInsertId() override;
    int GetAffectedRows() override;
    std::string GetLastError() const override;
    DatabaseType GetType() const override { return DatabaseType::SQLite; }
    
    // 预处理语句
    bool Prepare(const std::string& name, const std::string& sql) override;
    bool ExecutePrepared(const std::string& name, const std::vector<std::any>& params) override;
    ResultSet QueryPrepared(const std::string& name, const std::vector<std::any>& params) override;
    
    // SQLite特有方法
    void SetBusyTimeout(int ms);
    void EnableWALMode();
    void OptimizeDatabase();
    
private:
    // 辅助方法
    bool BindParameters(sqlite3_stmt* stmt, const std::vector<std::any>& params);
    ResultSet ExecuteQuery(sqlite3_stmt* stmt);
    bool ExecuteStatement(sqlite3_stmt* stmt);
    std::string GetSQLiteErrorMessage() const;
    
    // 异步查询处理
    void AsyncQueryWorker();
    
private:
    sqlite3* m_db;
    std::string m_connectionString;
    mutable std::string m_lastError;
    int m_affectedRows;
    mutable std::mutex m_mutex;
    
    // 预处理语句缓存
    std::map<std::string, sqlite3_stmt*> m_preparedStatements;
    
    // 异步查询队列
    struct AsyncQuery {
        std::string sql;
        QueryCallback callback;
    };
    std::queue<AsyncQuery> m_asyncQueries;
    std::mutex m_asyncMutex;
    std::condition_variable m_asyncCV;
    std::thread m_asyncThread;
    bool m_asyncRunning;
};

} // namespace MirServer