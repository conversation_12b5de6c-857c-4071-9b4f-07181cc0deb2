#pragma once

// Mir200 ObjectCombat - Object combat and attack module
// Based on combat-related functionality from original ObjBase.pas
// Handles all combat mechanics, damage calculation, and attack processing

#include "../Common/M2Share.h"

// Forward declarations
class BaseObject;

// Combat mode enumeration (matching original attack modes)
enum class CombatMode : BYTE {
    PEACE = HAM_PEACE,          // 和平模式
    ALL = HAM_ALL,              // 全体模式
    DEAR = HAM_DEAR,            // 夫妻模式
    MASTER = HAM_MASTER,        // 师傅模式
    GROUP = HAM_GROUP,          // 组队模式
    GUILD = HAM_GUILD,          // 行会模式
    PK_ATTACK = HAM_PKATTACK    // PK攻击模式
};

// Attack type enumeration
enum class AttackType : BYTE {
    NORMAL = 0,                 // 普通攻击
    HEAVY = 1,                  // 重击
    BIG = 2,                    // 大攻击
    POWER = 3,                  // 力量攻击
    LONG = 4,                   // 长攻击(刺杀)
    WIDE = 5,                   // 广域攻击(半月)
    FIRE = 6,                   // 火焰攻击(烈火)
    TWIN = 7,                   // 双重攻击
    RUSH = 8,                   // 冲撞攻击
    MAGIC = 9                   // 魔法攻击
};

// Hit mode enumeration (matching original hit modes)
enum class HitMode : WORD {
    NORMAL = 0,
    CRITICAL = 1,
    DOUBLE = 2,
    MISS = 3,
    BLOCK = 4,
    DODGE = 5
};

// Damage type enumeration
enum class DamageType : BYTE {
    PHYSICAL = 0,               // 物理伤害
    MAGICAL = 1,                // 魔法伤害
    POISON = 2,                 // 毒素伤害
    FIRE = 3,                   // 火焰伤害
    ICE = 4,                    // 冰冻伤害
    LIGHTNING = 5,              // 雷电伤害
    HOLY = 6,                   // 神圣伤害
    DARK = 7                    // 黑暗伤害
};

// Combat result structure
struct CombatResult {
    bool hit;                   // 是否命中
    int damage;                 // 伤害值
    HitMode hit_mode;           // 命中模式
    DamageType damage_type;     // 伤害类型
    bool critical;              // 是否暴击
    bool blocked;               // 是否格挡
    bool dodged;                // 是否闪避
    int actual_damage;          // 实际伤害
    
    CombatResult() : hit(false), damage(0), hit_mode(HitMode::MISS), 
                     damage_type(DamageType::PHYSICAL), critical(false), 
                     blocked(false), dodged(false), actual_damage(0) {}
};

// Attack data structure
struct AttackData {
    BaseObject* attacker;       // 攻击者
    BaseObject* target;         // 目标
    AttackType attack_type;     // 攻击类型
    BYTE direction;             // 攻击方向
    Point attack_pos;           // 攻击位置
    DWORD timestamp;            // 时间戳
    int base_damage;            // 基础伤害
    
    AttackData() : attacker(nullptr), target(nullptr), attack_type(AttackType::NORMAL),
                   direction(DR_UP), attack_pos(0, 0), timestamp(0), base_damage(0) {}
};

// ObjectCombat class - Manages all combat functionality
class ObjectCombat {
private:
    BaseObject* m_owner;
    
    // Combat state (matching original ObjBase.pas combat fields)
    BYTE m_attack_mode;                     // 0x2A1 - 攻击模式
    BYTE m_name_color;                      // 0x2A2 - 名字颜色
    bool m_pk_flag;                         // 0x39C - PK标志
    DWORD m_pk_tick;                        // 0x3A0 - PK计时
    DWORD m_struck_tick;                    // 0x310 - 受击计时
    
    // Attack timing (matching original timing fields)
    DWORD m_hit_tick;                       // 0x3F8 - 攻击计时
    int m_next_hit_time;                    // 0x514 - 下次攻击时间
    DWORD m_latest_fire_hit_tick;           // 0x530 - 最近火焰攻击计时
    DWORD m_do_motaebo_tick;                // 0x534 - 魔法攻击计时
    
    // Combat abilities and modifiers
    BYTE m_hit_point;                       // 0x23C - 攻击准确度
    ShortInt m_hit_plus;                    // 0x23D - 攻击加成
    ShortInt m_hit_double;                  // 0x23E - 双倍攻击
    ShortInt m_hit_speed;                   // 0x26E - 攻击速度
    
    // Skill-related combat flags (matching original skill flags)
    bool m_power_hit;                       // 0x52C - 力量攻击
    bool m_use_thrusting;                   // 0x52D - 使用刺杀
    bool m_use_half_moon;                   // 0x52E - 使用半月
    bool m_fire_hit_skill;                  // 0x52F - 火焰攻击技能
    bool m_crs_hit_skill;                   // 0x52F - 十字攻击技能
    bool m_skill_41;                        // 0x52F - 技能41
    bool m_skill_42;                        // 0x52F - 技能42
    bool m_skill_43;                        // 0x52F - 技能43
    
    // Skill pointers (matching original magic skill pointers)
    UserMagic* m_magic_one_sword_skill;     // 0x518 - 基本剑术
    UserMagic* m_magic_power_hit_skill;     // 0x51C - 力量攻击技能
    UserMagic* m_magic_ergum_skill;         // 0x520 - 刺杀剑术
    UserMagic* m_magic_banwol_skill;        // 0x524 - 半月弯刀
    UserMagic* m_magic_fire_sword_skill;    // 0x528 - 烈火剑法
    UserMagic* m_magic_crs_skill;           // 0x528 - 十字剑法
    UserMagic* m_magic_41_skill;            // 0x528 - 技能41
    UserMagic* m_magic_42_skill;            // 0x528 - 技能42
    UserMagic* m_magic_43_skill;            // 0x528 - 技能43
    
    // Combat statistics
    int m_total_attacks;
    int m_successful_hits;
    int m_critical_hits;
    int m_total_damage_dealt;
    int m_total_damage_received;
    
    // Target management
    BaseObject* m_target_object;            // 0x378 - 目标对象
    DWORD m_target_focus_tick;              // 0x37C - 目标焦点计时
    BaseObject* m_last_hitter;              // 0x380 - 最后攻击者
    DWORD m_last_hitter_tick;               // 0x384 - 最后攻击者计时
    BaseObject* m_exp_hitter;               // 0x388 - 经验攻击者
    DWORD m_exp_hitter_tick;                // 0x38C - 经验攻击者计时

public:
    explicit ObjectCombat(BaseObject* owner);
    ~ObjectCombat();

    // Core combat interface
    void Initialize();
    void Update();
    void Reset();

    // Primary attack methods (matching original ObjBase.pas methods)
    bool Attack(BaseObject* target, AttackType attack_type = AttackType::NORMAL);
    bool _Attack(HitMode& hit_mode, BaseObject* target);
    void AttackDir(BaseObject* target, WORD hit_mode, int direction);
    CombatResult ProcessAttack(const AttackData& attack_data);
    
    // Damage calculation (matching original damage methods)
    int GetAttackPower(int base_power, int power) const;
    int GetHitStruckDamage(BaseObject* target, int damage) const;
    int GetMagStruckDamage(BaseObject* target, int damage) const;
    void DamageHealth(int damage);
    void StruckDamage(int damage);
    
    // Combat mode and PK system
    CombatMode GetAttackMode() const { return static_cast<CombatMode>(m_attack_mode); }
    void SetAttackMode(CombatMode mode) { m_attack_mode = static_cast<BYTE>(mode); }
    BYTE GetNameColor() const { return m_name_color; }
    void SetNameColor(BYTE color) { m_name_color = color; }
    bool GetPKFlag() const { return m_pk_flag; }
    void SetPKFlag(bool flag) { m_pk_flag = flag; }
    
    // PK system methods (matching original PK methods)
    void SetPKFlag(BaseObject* target);
    void ChangePKStatus(bool war_flag);
    void CheckPKStatus();
    void IncPkPoint(int point);
    void DecPKPoint(int point);
    int PKLevel() const;
    
    // Target management
    BaseObject* GetTargetObject() const { return m_target_object; }
    void SetTargetCreat(BaseObject* target);
    void DelTargetCreat();
    BaseObject* GetLastHitter() const { return m_last_hitter; }
    void SetLastHiter(BaseObject* hitter);
    BaseObject* GetExpHitter() const { return m_exp_hitter; }
    void SetExpHitter(BaseObject* hitter);
    
    // Combat abilities and stats
    BYTE GetHitPoint() const { return m_hit_point; }
    ShortInt GetHitPlus() const { return m_hit_plus; }
    ShortInt GetHitDouble() const { return m_hit_double; }
    ShortInt GetHitSpeed() const { return m_hit_speed; }
    void SetHitPoint(BYTE point) { m_hit_point = point; }
    void SetHitPlus(ShortInt plus) { m_hit_plus = plus; }
    void SetHitDouble(ShortInt double_hit) { m_hit_double = double_hit; }
    void SetHitSpeed(ShortInt speed) { m_hit_speed = speed; }
    
    // Skill flags (matching original skill boolean fields)
    bool IsPowerHit() const { return m_power_hit; }
    bool IsUseThrusting() const { return m_use_thrusting; }
    bool IsUseHalfMoon() const { return m_use_half_moon; }
    bool IsFireHitSkill() const { return m_fire_hit_skill; }
    bool IsCrsHitSkill() const { return m_crs_hit_skill; }
    bool IsSkill41() const { return m_skill_41; }
    bool IsSkill42() const { return m_skill_42; }
    bool IsSkill43() const { return m_skill_43; }
    
    // Skill flag setters
    void SetPowerHit(bool value) { m_power_hit = value; }
    void SetUseThrusting(bool value) { m_use_thrusting = value; }
    void SetUseHalfMoon(bool value) { m_use_half_moon = value; }
    void SetFireHitSkill(bool value) { m_fire_hit_skill = value; }
    void SetCrsHitSkill(bool value) { m_crs_hit_skill = value; }
    void SetSkill41(bool value) { m_skill_41 = value; }
    void SetSkill42(bool value) { m_skill_42 = value; }
    void SetSkill43(bool value) { m_skill_43 = value; }
    
    // Skill management
    UserMagic* GetMagicOneSwordSkill() const { return m_magic_one_sword_skill; }
    UserMagic* GetMagicPowerHitSkill() const { return m_magic_power_hit_skill; }
    UserMagic* GetMagicErgumSkill() const { return m_magic_ergum_skill; }
    UserMagic* GetMagicBanwolSkill() const { return m_magic_banwol_skill; }
    UserMagic* GetMagicFireSwordSkill() const { return m_magic_fire_sword_skill; }
    UserMagic* GetMagicCrsSkill() const { return m_magic_crs_skill; }
    UserMagic* GetMagic41Skill() const { return m_magic_41_skill; }
    UserMagic* GetMagic42Skill() const { return m_magic_42_skill; }
    UserMagic* GetMagic43Skill() const { return m_magic_43_skill; }
    
    void SetMagicOneSwordSkill(UserMagic* skill) { m_magic_one_sword_skill = skill; }
    void SetMagicPowerHitSkill(UserMagic* skill) { m_magic_power_hit_skill = skill; }
    void SetMagicErgumSkill(UserMagic* skill) { m_magic_ergum_skill = skill; }
    void SetMagicBanwolSkill(UserMagic* skill) { m_magic_banwol_skill = skill; }
    void SetMagicFireSwordSkill(UserMagic* skill) { m_magic_fire_sword_skill = skill; }
    void SetMagicCrsSkill(UserMagic* skill) { m_magic_crs_skill = skill; }
    void SetMagic41Skill(UserMagic* skill) { m_magic_41_skill = skill; }
    void SetMagic42Skill(UserMagic* skill) { m_magic_42_skill = skill; }
    void SetMagic43Skill(UserMagic* skill) { m_magic_43_skill = skill; }
    
    // Combat timing
    DWORD GetHitTick() const { return m_hit_tick; }
    int GetNextHitTime() const { return m_next_hit_time; }
    DWORD GetLatestFireHitTick() const { return m_latest_fire_hit_tick; }
    DWORD GetDoMotaeboTick() const { return m_do_motaebo_tick; }
    DWORD GetStruckTick() const { return m_struck_tick; }
    
    void SetHitTick(DWORD tick) { m_hit_tick = tick; }
    void SetNextHitTime(int time) { m_next_hit_time = time; }
    void SetLatestFireHitTick(DWORD tick) { m_latest_fire_hit_tick = tick; }
    void SetDoMotaeboTick(DWORD tick) { m_do_motaebo_tick = tick; }
    void SetStruckTick(DWORD tick) { m_struck_tick = tick; }
    
    // Combat validation
    bool CanAttack() const;
    bool CanAttack(BaseObject* target) const;
    bool CanBeAttacked() const;
    bool CanBeAttacked(BaseObject* attacker) const;
    bool IsInAttackRange(BaseObject* target) const;
    bool IsValidAttackTarget(BaseObject* target) const;
    
    // Combat statistics
    int GetTotalAttacks() const { return m_total_attacks; }
    int GetSuccessfulHits() const { return m_successful_hits; }
    int GetCriticalHits() const { return m_critical_hits; }
    int GetTotalDamageDealt() const { return m_total_damage_dealt; }
    int GetTotalDamageReceived() const { return m_total_damage_received; }
    double GetHitRate() const;
    double GetCriticalRate() const;
    
    // Combat callbacks and notifications
    void OnAttackStarted(BaseObject* target, AttackType type);
    void OnAttackCompleted(BaseObject* target, const CombatResult& result);
    void OnDamageDealt(BaseObject* target, int damage, DamageType type);
    void OnDamageReceived(BaseObject* attacker, int damage, DamageType type);
    void OnTargetChanged(BaseObject* old_target, BaseObject* new_target);
    void OnCombatModeChanged(CombatMode old_mode, CombatMode new_mode);

private:
    // Internal combat processing
    bool ProcessNormalAttack(BaseObject* target);
    bool ProcessSkillAttack(BaseObject* target, AttackType type);
    bool ProcessMagicAttack(BaseObject* target, WORD magic_id);
    
    // Damage calculation internals
    int CalculateBaseDamage(BaseObject* target, AttackType type) const;
    int CalculatePhysicalDamage(BaseObject* target, int base_damage) const;
    int CalculateMagicalDamage(BaseObject* target, int base_damage) const;
    int ApplyDamageReduction(BaseObject* target, int damage, DamageType type) const;
    
    // Hit calculation
    bool CalculateHit(BaseObject* target, AttackType type) const;
    HitMode CalculateHitMode(BaseObject* target, AttackType type) const;
    bool CalculateCritical(BaseObject* target, AttackType type) const;
    bool CalculateBlock(BaseObject* target, AttackType type) const;
    bool CalculateDodge(BaseObject* target, AttackType type) const;
    
    // Combat timing and cooldowns
    bool IsAttackReady() const;
    void UpdateAttackTiming();
    void ApplyAttackCooldown(AttackType type);
    
    // Combat effects and notifications
    void ApplyCombatEffects(const CombatResult& result);
    void SendCombatMessages(BaseObject* target, const CombatResult& result);
    void ProcessCombatSounds(AttackType type, const CombatResult& result);
    void ProcessCombatVisuals(AttackType type, const CombatResult& result);
    
    // Internal utilities
    void UpdateCombatStatistics(const CombatResult& result);
    void ValidateCombatState();
    void CleanupCombatData();
};
