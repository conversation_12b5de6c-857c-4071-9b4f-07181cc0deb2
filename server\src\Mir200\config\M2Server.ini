# Mir200 Server Configuration
# Based on original M2Server configuration structure
# Phase 1 Implementation - Core settings only

[Server]
# Server basic information
ServerName=Mir200 Test Server
ServerVersion=1.00
TestServer=1
ServiceMode=0
ShowException=1
ShowPreFix=1

# Network configuration
GateAddr=127.0.0.1
GatePort=7000
MaxUser=1000

# Database configuration
DBAddr=127.0.0.1
DBPort=3306
DBName=mir200
DBUser=root
DBPassword=

# Game configuration
StartLevel=1
MaxLevel=65535
MaxChangeLevel=1000
MaxMagic=20
MaxUpLevel=65535
MaxHumPower=65535

# Experience and rates
ExpRate=1.0
DropRate=1.0
GoldRate=1.0

# PK system
PKDieDropUseItem=1
PKDieDropBag=1
PKLevel1=100
PKLevel2=300

# Guild system
GuildRecallTime=300
GuildDir=Guild\

# Castle system
CastleDir=Castle\
CastleWarTime=20:00

# Map and environment
MapDir=Map\
EnvirDir=Envir\
GuildBase=SABUK

# Item and equipment
ItemDir=StdItems.DB
MonsterDir=Monster.DB
MagicDir=Magic.DB

# Timing configuration
ProcessMonsterInterval=100
ProcessNpcInterval=100
ProcessMissionInterval=5000
ProcessGuildInterval=30000

# Security settings
SpeedHackCheck=1
MaxConnectOfIP=3
MaxClientPacketSize=8192

# Log settings
LogDir=Logs\
SaveLogLevel=3
LogPlayerAction=1
LogPlayerDeal=1
LogPlayerChat=1

# Backup settings
BackupDir=Backup\
AutoBackupTime=04:00
BackupHumData=1
BackupGuildData=1

[Network]
# Socket configuration
SocketTimeout=30000
SocketSendBufferSize=8192
SocketRecvBufferSize=8192
MaxSocketConnections=2000

# Gate server settings
GateLoad=100
GateClass=TRunSocket

[Database]
# Database connection settings
ConnectionTimeout=30
QueryTimeout=60
MaxConnections=10
MinConnections=2

# Data saving intervals
SaveHumDataTime=600000
SaveGuildDataTime=300000
SaveCastleDataTime=600000

[Game]
# Game mechanics
StartGold=1000
StartHP=100
StartMP=100

# Death penalties
DeathDropGoldRate=0.1
DeathLostExpRate=0.05

# Item durability
ItemDurabilityRate=1.0
RepairItemDecDura=1

# Magic and skills
MagicAttackRate=1.0
SkillPointRate=1.0

# Monster settings
MonsterPowerRate=1.0
MonsterExpRate=1.0

[Security]
# Anti-cheat settings
CheckUserItemPlace=1
CheckDupItem=1
CheckNewHuman=1

# Speed hack detection
SpeedHackCheckTime=3000
SpeedHackKickCount=3

# Packet validation
CheckPacketSize=1
MaxPacketSize=8192

[Performance]
# Server performance settings
ProcessInterval=10
TimerInterval=1
SaveInterval=300000

# Memory management
MaxMemoryUsage=512
GarbageCollectInterval=60000

# CPU optimization
ThreadPriority=2
ProcessPriority=2

[Debug]
# Debug and testing settings
DebugMode=0
TestMode=0
ShowDebugInfo=0
LogDebugInfo=0

# Exception handling
ShowExceptionDialog=1
LogExceptions=1
CrashDumpEnabled=1
