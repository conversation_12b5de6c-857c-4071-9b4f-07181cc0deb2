#pragma once

#include <string>
#include <unordered_map>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <mutex>
#include <vector>

namespace MirServer {

/**
 * INI文件处理类
 * 提供INI格式配置文件的读取和写入功能
 * 支持节(Section)和键值对(Key=Value)格式
 * 线程安全的读写操作
 */
class IniFile {
public:
    IniFile();
    explicit IniFile(const std::string& filename);
    ~IniFile();

    // 文件操作
    bool LoadFromFile(const std::string& filename);
    bool SaveToFile(const std::string& filename = "");
    bool SaveToFile(const std::string& filename) const;
    void Clear();
    
    // 读取配置
    std::string GetString(const std::string& section, const std::string& key, const std::string& defaultValue = "") const;
    int GetInt(const std::string& section, const std::string& key, int defaultValue = 0) const;
    bool GetBool(const std::string& section, const std::string& key, bool defaultValue = false) const;
    double GetDouble(const std::string& section, const std::string& key, double defaultValue = 0.0) const;
    
    // 写入配置
    void SetString(const std::string& section, const std::string& key, const std::string& value);
    void SetInt(const std::string& section, const std::string& key, int value);
    void SetBool(const std::string& section, const std::string& key, bool value);
    void SetDouble(const std::string& section, const std::string& key, double value);
    
    // 节和键管理
    bool HasSection(const std::string& section) const;
    bool HasKey(const std::string& section, const std::string& key) const;
    bool RemoveSection(const std::string& section);
    bool RemoveKey(const std::string& section, const std::string& key);
    
    // 获取所有节名和键名
    std::vector<std::string> GetSectionNames() const;
    std::vector<std::string> GetKeyNames(const std::string& section) const;
    
    // 设置注释字符（默认为 ; 和 #）
    void SetCommentChars(const std::string& chars) { m_commentChars = chars; }
    
    // 启用/禁用自动保存
    void SetAutoSave(bool enable) { m_autoSave = enable; }
    bool IsAutoSaveEnabled() const { return m_autoSave; }
    
    // 获取文件名
    std::string GetFileName() const { return m_filename; }

private:
    // 内部数据结构: map<section, map<key, value>>
    using KeyValueMap = std::unordered_map<std::string, std::string>;
    using SectionMap = std::unordered_map<std::string, KeyValueMap>;
    
    SectionMap m_data;
    std::string m_filename;
    std::string m_commentChars;
    bool m_autoSave;
    mutable std::mutex m_mutex;
    
    // 辅助方法
    std::string Trim(const std::string& str) const;
    std::string ToLower(const std::string& str) const;
    bool IsCommentLine(const std::string& line) const;
    bool ParseLine(const std::string& line, std::string& section, std::string& key, std::string& value) const;
    std::string EscapeValue(const std::string& value) const;
    std::string UnescapeValue(const std::string& value) const;
};

} // namespace MirServer 