#pragma once

// Mir200 ObjectState - Object state management module
// Based on state-related functionality from original ObjBase.pas
// Handles all object states, conditions, and status effects

#include "../Common/M2Share.h"
#include <windows.h>

// Forward declaration
class BaseObject;

// Object state enumeration (matching original)
enum class ObjectStateType : BYTE {
    NORMAL = 0,
    GHOST = 1,
    DEATH = 2,
    INVISIBLE = 3,
    PARALYSIS = 4,
    TRANSPARENT = 5,
    STONE_MODE = 6,
    HIDE_MODE = 7,
    ADMIN_MODE = 8,
    OBSERVE_MODE = 9,
    TELEPORT = 10,
    REVIVAL = 11,
    SUPER_MAN = 12,
    NO_ATTACK_MODE = 13,
    SKELETON = 14,
    HOLY_SEIZE = 15,
    CRAZY_MODE = 16,
    SHOW_HP = 17
};

// Status effect types (matching original poison and state constants)
enum class StatusEffectType : BYTE {
    NONE = 0,
    POISON_DECHEALTH = POISON_DECHEALTH,
    POISON_DAMAGEARMOR = POISON_DAMAGEARMOR,
    POISON_LOCKSPELL = POISON_LOCKSPELL,
    POISON_DONTMOVE = POISON_DONTMOVE,
    POISON_STONE = POISON_STONE,
    STATE_TRANSPARENT = STATE_TRANSPARENT,
    STATE_DEFENCEUP = STATE_DEFENCEUP,
    STATE_MAGDEFENCEUP = STATE_MAGDEFENCEUP,
    STATE_BUBBLEDEFENCEUP = STATE_BUBBLEDEFENCEUP
};

// Status effect structure
struct StatusEffect {
    StatusEffectType type;
    DWORD start_time;
    DWORD duration;
    int power;
    int level;
    bool active;
    
    StatusEffect() : type(StatusEffectType::NONE), start_time(0), 
                     duration(0), power(0), level(0), active(false) {}
    
    StatusEffect(StatusEffectType t, DWORD dur, int pow = 0, int lv = 0)
        : type(t), start_time(g_functions::GetCurrentTime()), 
          duration(dur), power(pow), level(lv), active(true) {}
    
    bool IsExpired() const {
        return active && (g_functions::GetCurrentTime() - start_time >= duration);
    }
    
    DWORD GetRemainingTime() const {
        if (!active) return 0;
        DWORD elapsed = g_functions::GetCurrentTime() - start_time;
        return (elapsed < duration) ? (duration - elapsed) : 0;
    }
};

// ObjectState class - Manages all object states and status effects
class ObjectState {
private:
    BaseObject* m_owner;
    
    // Core state flags (matching original ObjBase.pas boolean fields)
    bool m_ghost;                           // 0x2FC - 幽灵状态
    bool m_death;                           // 0x304 - 死亡状态
    bool m_hide_mode;                       // 0x344 - 隐身状态
    bool m_stone_mode;                      // 0x345 - 石化状态
    bool m_cool_eye;                        // 0x346 - 冷眼状态
    bool m_user_unlock_drug;                // 0x347 - 解毒状态
    bool m_transparent;                     // 0x348 - 透明状态
    bool m_admin_mode;                      // 0x349 - 管理员模式
    bool m_observe_mode;                    // 0x34A - 观察模式
    bool m_teleport;                        // 0x34B - 传送状态
    bool m_paralysis;                       // 0x34C - 麻痹状态
    bool m_un_paralysis;                    // 反麻痹状态
    bool m_revival;                         // 0x34D - 复活状态
    bool m_un_revival;                      // 反复活状态
    bool m_super_man;                       // 0x2B8 - 无敌模式
    bool m_animal;                          // 0x2BB - 动物状态
    bool m_no_item;                         // 0x2BC - 无物品状态
    bool m_fixed_hide_mode;                 // 0x2BD - 固定隐身模式
    bool m_stick_mode;                      // 0x2BE - 粘贴模式
    bool m_no_attack_mode;                  // 0x2C0 - 无攻击模式
    bool m_skeleton;                        // 0x2C2 - 骷髅状态
    bool m_holy_seize;                      // 0x2CC - 神圣战甲术
    bool m_crazy_mode;                      // 0x2D8 - 疯狂模式
    bool m_show_hp;                         // 0x2E4 - 显示血量
    
    // State timing (matching original timing fields)
    DWORD m_ghost_tick;                     // 0x300 - 幽灵计时
    DWORD m_death_tick;                     // 0x308 - 死亡计时
    DWORD m_revival_tick;                   // 0x350 - 复活计时
    DWORD m_holy_seize_tick;                // 0x2D0 - 神圣战甲术计时
    DWORD m_holy_seize_interval;            // 0x2D4 - 神圣战甲术间隔
    DWORD m_crazy_mode_tick;                // 0x2DC - 疯狂模式计时
    DWORD m_crazy_mode_interval;            // 0x2E0 - 疯狂模式间隔
    DWORD m_show_hp_tick;                   // 0x2E8 - 显示血量计时
    DWORD m_show_hp_interval;               // 0x2EC - 显示血量间隔
    DWORD m_dup_obj_tick;                   // 0x2F4 - 复制对象计时
    
    // Status effects collection
    std::vector<StatusEffect> m_status_effects;
    
    // Ring effects (matching original ring boolean fields)
    bool m_flame_ring;                      // 0x354 - 火焰戒指
    bool m_recovery_ring;                   // 0x355 - 恢复戒指
    bool m_angry_ring;                      // 0x356 - 愤怒戒指
    bool m_magic_shield;                    // 0x357 - 魔法盾
    bool m_un_magic_shield;                 // 反魔法盾
    bool m_muscle_ring;                     // 0x358 - 肌肉戒指
    bool m_fast_train;                      // 0x359 - 快速训练
    bool m_probe_necklace;                  // 0x35A - 探测项链
    bool m_guild_move;                      // 行会传送
    bool m_superman_item;                   // 超人物品
    bool m_spirit;                          // 精神状态
    bool m_no_drop_item;                    // 不掉物品
    bool m_no_drop_use_item;                // 不掉装备
    bool m_exp_item;                        // 经验物品
    bool m_power_item;                      // 力量物品
    
    // Special abilities (matching original ability boolean fields)
    bool m_abil_see_heal_gauge;             // 0x35B - 显示血量能力
    bool m_abil_mag_bubble_defence;         // 0x35C - 魔法气泡防御
    BYTE m_mag_bubble_defence_level;        // 0x35D - 魔法气泡防御等级
    
    // Item effect rates
    double m_exp_item_rate;                 // 经验物品倍率
    double m_power_item_rate;               // 力量物品倍率

public:
    explicit ObjectState(BaseObject* owner);
    ~ObjectState();

    // Core state management
    void Initialize();
    void Update();
    void Reset();

    // Primary state accessors (matching original boolean fields exactly)
    bool IsGhost() const { return m_ghost; }
    bool IsDeath() const { return m_death; }
    bool IsHideMode() const { return m_hide_mode; }
    bool IsStoneMode() const { return m_stone_mode; }
    bool IsCoolEye() const { return m_cool_eye; }
    bool IsUserUnlockDrug() const { return m_user_unlock_drug; }
    bool IsTransparent() const { return m_transparent; }
    bool IsAdminMode() const { return m_admin_mode; }
    bool IsObserveMode() const { return m_observe_mode; }
    bool IsTeleport() const { return m_teleport; }
    bool IsParalysis() const { return m_paralysis; }
    bool IsUnParalysis() const { return m_un_paralysis; }
    bool IsRevival() const { return m_revival; }
    bool IsUnRevival() const { return m_un_revival; }
    bool IsSuperMan() const { return m_super_man; }
    bool IsAnimal() const { return m_animal; }
    bool IsNoItem() const { return m_no_item; }
    bool IsFixedHideMode() const { return m_fixed_hide_mode; }
    bool IsStickMode() const { return m_stick_mode; }
    bool IsNoAttackMode() const { return m_no_attack_mode; }
    bool IsSkeleton() const { return m_skeleton; }
    bool IsHolySeize() const { return m_holy_seize; }
    bool IsCrazyMode() const { return m_crazy_mode; }
    bool IsShowHP() const { return m_show_hp; }

    // Primary state setters (matching original methods)
    void SetGhost(bool value, DWORD duration = 0);
    void SetDeath(bool value, DWORD duration = 0);
    void SetHideMode(bool value);
    void SetStoneMode(bool value);
    void SetCoolEye(bool value);
    void SetUserUnlockDrug(bool value);
    void SetTransparent(bool value);
    void SetAdminMode(bool value);
    void SetObserveMode(bool value);
    void SetTeleport(bool value);
    void SetParalysis(bool value);
    void SetUnParalysis(bool value);
    void SetRevival(bool value);
    void SetUnRevival(bool value);
    void SetSuperMan(bool value);
    void SetAnimal(bool value);
    void SetNoItem(bool value);
    void SetFixedHideMode(bool value);
    void SetStickMode(bool value);
    void SetNoAttackMode(bool value);
    void SetSkeleton(bool value);
    void SetShowHP(bool value);

    // Special state methods (matching original methods)
    void OpenHolySeizeMode(DWORD interval);
    void BreakHolySeizeMode();
    void OpenCrazyMode(int time);
    void BreakCrazyMode();
    void MakeOpenHealth();
    void BreakOpenHealth();

    // Ring effect accessors
    bool IsFlameRing() const { return m_flame_ring; }
    bool IsRecoveryRing() const { return m_recovery_ring; }
    bool IsAngryRing() const { return m_angry_ring; }
    bool IsMagicShield() const { return m_magic_shield; }
    bool IsUnMagicShield() const { return m_un_magic_shield; }
    bool IsMuscleRing() const { return m_muscle_ring; }
    bool IsFastTrain() const { return m_fast_train; }
    bool IsProbeNecklace() const { return m_probe_necklace; }
    bool IsGuildMove() const { return m_guild_move; }
    bool IsSupermanItem() const { return m_superman_item; }
    bool IsSpirit() const { return m_spirit; }
    bool IsNoDropItem() const { return m_no_drop_item; }
    bool IsNoDropUseItem() const { return m_no_drop_use_item; }
    bool IsExpItem() const { return m_exp_item; }
    bool IsPowerItem() const { return m_power_item; }

    // Ring effect setters
    void SetFlameRing(bool value) { m_flame_ring = value; }
    void SetRecoveryRing(bool value) { m_recovery_ring = value; }
    void SetAngryRing(bool value) { m_angry_ring = value; }
    void SetMagicShield(bool value) { m_magic_shield = value; }
    void SetUnMagicShield(bool value) { m_un_magic_shield = value; }
    void SetMuscleRing(bool value) { m_muscle_ring = value; }
    void SetFastTrain(bool value) { m_fast_train = value; }
    void SetProbeNecklace(bool value) { m_probe_necklace = value; }
    void SetGuildMove(bool value) { m_guild_move = value; }
    void SetSupermanItem(bool value) { m_superman_item = value; }
    void SetSpirit(bool value) { m_spirit = value; }
    void SetNoDropItem(bool value) { m_no_drop_item = value; }
    void SetNoDropUseItem(bool value) { m_no_drop_use_item = value; }
    void SetExpItem(bool value, double rate = 1.0) { m_exp_item = value; m_exp_item_rate = rate; }
    void SetPowerItem(bool value, double rate = 1.0) { m_power_item = value; m_power_item_rate = rate; }

    // Special ability accessors
    bool IsAbilSeeHealGauge() const { return m_abil_see_heal_gauge; }
    bool IsAbilMagBubbleDefence() const { return m_abil_mag_bubble_defence; }
    BYTE GetMagBubbleDefenceLevel() const { return m_mag_bubble_defence_level; }
    double GetExpItemRate() const { return m_exp_item_rate; }
    double GetPowerItemRate() const { return m_power_item_rate; }

    // Special ability setters
    void SetAbilSeeHealGauge(bool value) { m_abil_see_heal_gauge = value; }
    void SetAbilMagBubbleDefence(bool value, BYTE level = 0) { 
        m_abil_mag_bubble_defence = value; 
        m_mag_bubble_defence_level = level; 
    }

    // Status effect management
    void AddStatusEffect(StatusEffectType type, DWORD duration, int power = 0, int level = 0);
    void RemoveStatusEffect(StatusEffectType type);
    void RemoveAllStatusEffects();
    bool HasStatusEffect(StatusEffectType type) const;
    const StatusEffect* GetStatusEffect(StatusEffectType type) const;
    void UpdateStatusEffects();

    // Poison management (matching original poison methods)
    bool MakePosion(int type, int time, int point);
    void RemovePoison(int type);
    bool IsPoisoned() const;
    int GetPoisonDamage() const;

    // State validation and utility
    bool CanMove() const;
    bool CanAttack() const;
    bool CanCast() const;
    bool CanBeAttacked() const;
    bool CanBeTargeted() const;
    bool IsVisible() const;
    bool IsInvulnerable() const;

    // State change notifications
    void OnStateChanged(ObjectStateType old_state, ObjectStateType new_state);
    void OnStatusEffectAdded(const StatusEffect& effect);
    void OnStatusEffectRemoved(StatusEffectType type);

private:
    // Internal state management
    void CheckStateTimeouts();
    void ProcessStateEffects();
    void ValidateStateConsistency();
    void NotifyOwnerOfStateChange();
    
    // Internal utility methods
    bool IsStateTimedOut(DWORD start_tick, DWORD duration) const;
    void ClearTimedOutStates();
    void ApplyStatusEffectToOwner(const StatusEffect& effect);
    void RemoveStatusEffectFromOwner(StatusEffectType type);
};
