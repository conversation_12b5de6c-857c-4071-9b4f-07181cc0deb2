// SQLiteDatabase.cpp - SQLite数据库实现
#include "SQLiteDatabase.h"
#include "../Common/Logger.h"
#include <sstream>
#include <filesystem>

namespace MirServer {

// SQLiteTransaction 实现
SQLiteTransaction::SQLiteTransaction(sqlite3* db) 
    : m_db(db), m_committed(false), m_rolledBack(false) {
    sqlite3_exec(m_db, "BEGIN TRANSACTION", nullptr, nullptr, nullptr);
}

SQLiteTransaction::~SQLiteTransaction() {
    if (!m_committed && !m_rolledBack) {
        Rollback();
    }
}

bool SQLiteTransaction::Commit() {
    if (m_committed || m_rolledBack) return false;
    
    int result = sqlite3_exec(m_db, "COMMIT", nullptr, nullptr, nullptr);
    m_committed = (result == SQLITE_OK);
    return m_committed;
}

bool SQLiteTransaction::Rollback() {
    if (m_committed || m_rolledBack) return false;
    
    int result = sqlite3_exec(m_db, "R<PERSON><PERSON><PERSON><PERSON>K", nullptr, nullptr, nullptr);
    m_rolledBack = (result == SQLITE_OK);
    return m_rolledBack;
}

bool SQLiteTransaction::Execute(const std::string& sql) {
    if (m_committed || m_rolledBack) return false;
    
    char* errMsg = nullptr;
    int result = sqlite3_exec(m_db, sql.c_str(), nullptr, nullptr, &errMsg);
    
    if (result != SQLITE_OK) {
        if (errMsg) {
            MirServer::Logger::Error("SQLite transaction error: " + std::string(errMsg));
            sqlite3_free(errMsg);
        }
        return false;
    }
    
    return true;
}

bool SQLiteTransaction::Execute(const std::string& sql, const std::vector<std::any>& params) {
    // TODO: 实现参数化查询
    return Execute(sql);
}

// SQLiteDatabase 实现
SQLiteDatabase::SQLiteDatabase() 
    : m_db(nullptr), m_affectedRows(0), m_asyncRunning(false) {
}

SQLiteDatabase::~SQLiteDatabase() {
    Disconnect();
}

bool SQLiteDatabase::Connect(const std::string& connectionString) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_db) {
        Disconnect();
    }
    
    m_connectionString = connectionString;
    
    // 确保目录存在
    std::filesystem::path dbPath(connectionString);
    std::filesystem::create_directories(dbPath.parent_path());
    
    int result = sqlite3_open(connectionString.c_str(), &m_db);
    if (result != SQLITE_OK) {
        m_lastError = GetSQLiteErrorMessage();
        MirServer::Logger::Error("Failed to open SQLite database: " + m_lastError);
        if (m_db) {
            sqlite3_close(m_db);
            m_db = nullptr;
        }
        return false;
    }
    
    // 设置默认配置
    SetBusyTimeout(5000);
    EnableWALMode();
    
    // 启动异步查询线程
    m_asyncRunning = true;
    m_asyncThread = std::thread(&SQLiteDatabase::AsyncQueryWorker, this);
    
    MirServer::Logger::Info("Connected to SQLite database: " + connectionString);
    return true;
}

bool SQLiteDatabase::Disconnect() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_db) return true;
    
    // 停止异步查询线程
    m_asyncRunning = false;
    m_asyncCV.notify_all();
    if (m_asyncThread.joinable()) {
        m_asyncThread.join();
    }
    
    // 释放预处理语句
    for (auto& pair : m_preparedStatements) {
        sqlite3_finalize(pair.second);
    }
    m_preparedStatements.clear();
    
    // 关闭数据库
    int result = sqlite3_close(m_db);
    if (result != SQLITE_OK) {
        m_lastError = GetSQLiteErrorMessage();
        MirServer::Logger::Error("Failed to close SQLite database: " + m_lastError);
        return false;
    }
    
    m_db = nullptr;
    MirServer::Logger::Info("Disconnected from SQLite database");
    return true;
}

bool SQLiteDatabase::IsConnected() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_db != nullptr;
}

bool SQLiteDatabase::Execute(const std::string& sql) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_db) {
        m_lastError = "Database not connected";
        return false;
    }
    
    char* errMsg = nullptr;
    int result = sqlite3_exec(m_db, sql.c_str(), nullptr, nullptr, &errMsg);
    
    if (result != SQLITE_OK) {
        m_lastError = errMsg ? errMsg : GetSQLiteErrorMessage();
        if (errMsg) sqlite3_free(errMsg);
        return false;
    }
    
    m_affectedRows = sqlite3_changes(m_db);
    return true;
}

bool SQLiteDatabase::Execute(const std::string& sql, const std::vector<std::any>& params) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_db) {
        m_lastError = "Database not connected";
        return false;
    }
    
    sqlite3_stmt* stmt = nullptr;
    int result = sqlite3_prepare_v2(m_db, sql.c_str(), -1, &stmt, nullptr);
    
    if (result != SQLITE_OK) {
        m_lastError = GetSQLiteErrorMessage();
        return false;
    }
    
    if (!BindParameters(stmt, params)) {
        sqlite3_finalize(stmt);
        return false;
    }
    
    bool success = ExecuteStatement(stmt);
    sqlite3_finalize(stmt);
    
    return success;
}

ResultSet SQLiteDatabase::Query(const std::string& sql) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_db) {
        m_lastError = "Database not connected";
        return ResultSet();
    }
    
    sqlite3_stmt* stmt = nullptr;
    int result = sqlite3_prepare_v2(m_db, sql.c_str(), -1, &stmt, nullptr);
    
    if (result != SQLITE_OK) {
        m_lastError = GetSQLiteErrorMessage();
        return ResultSet();
    }
    
    ResultSet results = ExecuteQuery(stmt);
    sqlite3_finalize(stmt);
    
    return results;
}

ResultSet SQLiteDatabase::Query(const std::string& sql, const std::vector<std::any>& params) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_db) {
        m_lastError = "Database not connected";
        return ResultSet();
    }
    
    sqlite3_stmt* stmt = nullptr;
    int result = sqlite3_prepare_v2(m_db, sql.c_str(), -1, &stmt, nullptr);
    
    if (result != SQLITE_OK) {
        m_lastError = GetSQLiteErrorMessage();
        return ResultSet();
    }
    
    if (!BindParameters(stmt, params)) {
        sqlite3_finalize(stmt);
        return ResultSet();
    }
    
    ResultSet results = ExecuteQuery(stmt);
    sqlite3_finalize(stmt);
    
    return results;
}

void SQLiteDatabase::QueryAsync(const std::string& sql, QueryCallback callback) {
    std::lock_guard<std::mutex> lock(m_asyncMutex);
    m_asyncQueries.push({sql, callback});
    m_asyncCV.notify_one();
}

std::unique_ptr<ITransaction> SQLiteDatabase::BeginTransaction() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_db) {
        m_lastError = "Database not connected";
        return nullptr;
    }
    
    return std::make_unique<SQLiteTransaction>(m_db);
}

bool SQLiteDatabase::ExecuteBatch(const std::vector<std::string>& sqls) {
    auto transaction = BeginTransaction();
    if (!transaction) return false;
    
    for (const auto& sql : sqls) {
        if (!transaction->Execute(sql)) {
            transaction->Rollback();
            return false;
        }
    }
    
    return transaction->Commit();
}

int64_t SQLiteDatabase::GetLastInsertId() {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_db ? sqlite3_last_insert_rowid(m_db) : -1;
}

int SQLiteDatabase::GetAffectedRows() {
    return m_affectedRows;
}

std::string SQLiteDatabase::GetLastError() const {
    return m_lastError;
}

bool SQLiteDatabase::Prepare(const std::string& name, const std::string& sql) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_db) {
        m_lastError = "Database not connected";
        return false;
    }
    
    // 如果已存在，先释放
    auto it = m_preparedStatements.find(name);
    if (it != m_preparedStatements.end()) {
        sqlite3_finalize(it->second);
        m_preparedStatements.erase(it);
    }
    
    sqlite3_stmt* stmt = nullptr;
    int result = sqlite3_prepare_v2(m_db, sql.c_str(), -1, &stmt, nullptr);
    
    if (result != SQLITE_OK) {
        m_lastError = GetSQLiteErrorMessage();
        return false;
    }
    
    m_preparedStatements[name] = stmt;
    return true;
}

bool SQLiteDatabase::ExecutePrepared(const std::string& name, const std::vector<std::any>& params) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_preparedStatements.find(name);
    if (it == m_preparedStatements.end()) {
        m_lastError = "Prepared statement not found: " + name;
        return false;
    }
    
    sqlite3_stmt* stmt = it->second;
    sqlite3_reset(stmt);
    
    if (!BindParameters(stmt, params)) {
        return false;
    }
    
    return ExecuteStatement(stmt);
}

ResultSet SQLiteDatabase::QueryPrepared(const std::string& name, const std::vector<std::any>& params) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_preparedStatements.find(name);
    if (it == m_preparedStatements.end()) {
        m_lastError = "Prepared statement not found: " + name;
        return ResultSet();
    }
    
    sqlite3_stmt* stmt = it->second;
    sqlite3_reset(stmt);
    
    if (!BindParameters(stmt, params)) {
        return ResultSet();
    }
    
    return ExecuteQuery(stmt);
}

void SQLiteDatabase::SetBusyTimeout(int ms) {
    if (m_db) {
        sqlite3_busy_timeout(m_db, ms);
    }
}

void SQLiteDatabase::EnableWALMode() {
    if (m_db) {
        Execute("PRAGMA journal_mode=WAL");
        Execute("PRAGMA synchronous=NORMAL");
    }
}

void SQLiteDatabase::OptimizeDatabase() {
    if (m_db) {
        Execute("VACUUM");
        Execute("ANALYZE");
    }
}

bool SQLiteDatabase::BindParameters(sqlite3_stmt* stmt, const std::vector<std::any>& params) {
    for (size_t i = 0; i < params.size(); ++i) {
        int index = static_cast<int>(i + 1);
        const std::any& param = params[i];
        
        try {
            if (param.type() == typeid(int)) {
                sqlite3_bind_int(stmt, index, std::any_cast<int>(param));
            } else if (param.type() == typeid(int64_t)) {
                sqlite3_bind_int64(stmt, index, std::any_cast<int64_t>(param));
            } else if (param.type() == typeid(double)) {
                sqlite3_bind_double(stmt, index, std::any_cast<double>(param));
            } else if (param.type() == typeid(std::string)) {
                const std::string& str = std::any_cast<const std::string&>(param);
                sqlite3_bind_text(stmt, index, str.c_str(), -1, SQLITE_TRANSIENT);
            } else if (param.type() == typeid(std::nullptr_t)) {
                sqlite3_bind_null(stmt, index);
            } else {
                m_lastError = "Unsupported parameter type";
                return false;
            }
        } catch (const std::bad_any_cast& e) {
            m_lastError = "Parameter binding error: " + std::string(e.what());
            return false;
        }
    }
    
    return true;
}

ResultSet SQLiteDatabase::ExecuteQuery(sqlite3_stmt* stmt) {
    ResultSet results;
    
    int columnCount = sqlite3_column_count(stmt);
    
    while (sqlite3_step(stmt) == SQLITE_ROW) {
        ResultRow row;
        
        for (int i = 0; i < columnCount; ++i) {
            std::string columnName = sqlite3_column_name(stmt, i);
            int columnType = sqlite3_column_type(stmt, i);
            
            switch (columnType) {
                case SQLITE_INTEGER:
                    row[columnName] = static_cast<int64_t>(sqlite3_column_int64(stmt, i));
                    break;
                case SQLITE_FLOAT:
                    row[columnName] = sqlite3_column_double(stmt, i);
                    break;
                case SQLITE_TEXT:
                    row[columnName] = std::string(reinterpret_cast<const char*>(sqlite3_column_text(stmt, i)));
                    break;
                case SQLITE_BLOB:
                    // TODO: 处理BLOB数据
                    row[columnName] = std::nullptr_t();
                    break;
                case SQLITE_NULL:
                    row[columnName] = std::nullptr_t();
                    break;
            }
        }
        
        results.push_back(std::move(row));
    }
    
    return results;
}

bool SQLiteDatabase::ExecuteStatement(sqlite3_stmt* stmt) {
    int result = sqlite3_step(stmt);
    
    if (result != SQLITE_DONE && result != SQLITE_ROW) {
        m_lastError = GetSQLiteErrorMessage();
        return false;
    }
    
    m_affectedRows = sqlite3_changes(m_db);
    return true;
}

std::string SQLiteDatabase::GetSQLiteErrorMessage() const {
    if (!m_db) return "Database not connected";
    return sqlite3_errmsg(m_db);
}

void SQLiteDatabase::AsyncQueryWorker() {
    while (m_asyncRunning) {
        std::unique_lock<std::mutex> lock(m_asyncMutex);
        m_asyncCV.wait(lock, [this] { return !m_asyncQueries.empty() || !m_asyncRunning; });
        
        while (!m_asyncQueries.empty()) {
            AsyncQuery query = std::move(m_asyncQueries.front());
            m_asyncQueries.pop();
            lock.unlock();
            
            ResultSet results = Query(query.sql);
            if (query.callback) {
                query.callback(results);
            }
            
            lock.lock();
        }
    }
}

// DatabaseFactory 实现
std::unique_ptr<IDatabase> DatabaseFactory::Create(DatabaseType type) {
    switch (type) {
        case DatabaseType::SQLite:
            return std::make_unique<SQLiteDatabase>();
        case DatabaseType::MySQL:
            // TODO: 实现MySQL数据库
            return nullptr;
        case DatabaseType::PostgreSQL:
            // TODO: 实现PostgreSQL数据库
            return nullptr;
        case DatabaseType::MSSQL:
            // TODO: 实现MSSQL数据库
            return nullptr;
        default:
            return nullptr;
    }
}

std::unique_ptr<IDatabase> DatabaseFactory::CreateFromConfig(const std::string& configFile) {
    // TODO: 从配置文件读取数据库类型并创建相应实例
    return Create(DatabaseType::SQLite);
}

} // namespace MirServer