const
  p8x8: array[0..1,0..255] of byte= ((
    $a9, $67, $b3, $e8, $04, $fd, $a3, $76,
    $9a, $92, $80, $78, $e4, $dd, $d1, $38,
    $0d, $c6, $35, $98, $18, $f7, $ec, $6c,
    $43, $75, $37, $26, $fa, $13, $94, $48,
    $f2, $d0, $8b, $30, $84, $54, $df, $23,
    $19, $5b, $3d, $59, $f3, $ae, $a2, $82,
    $63, $01, $83, $2e, $d9, $51, $9b, $7c,
    $a6, $eb, $a5, $be, $16, $0c, $e3, $61,
    $c0, $8c, $3a, $f5, $73, $2c, $25, $0b,
    $bb, $4e, $89, $6b, $53, $6a, $b4, $f1,
    $e1, $e6, $bd, $45, $e2, $f4, $b6, $66,
    $cc, $95, $03, $56, $d4, $1c, $1e, $d7,
    $fb, $c3, $8e, $b5, $e9, $cf, $bf, $ba,
    $ea, $77, $39, $af, $33, $c9, $62, $71,
    $81, $79, $09, $ad, $24, $cd, $f9, $d8,
    $e5, $c5, $b9, $4d, $44, $08, $86, $e7,
    $a1, $1d, $aa, $ed, $06, $70, $b2, $d2,
    $41, $7b, $a0, $11, $31, $c2, $27, $90, 
    $20, $f6, $60, $ff, $96, $5c, $b1, $ab, 
    $9e, $9c, $52, $1b, $5f, $93, $0a, $ef,
    $91, $85, $49, $ee, $2d, $4f, $8f, $3b,
    $47, $87, $6d, $46, $d6, $3e, $69, $64,
    $2a, $ce, $cb, $2f, $fc, $97, $05, $7a,
    $ac, $7f, $d5, $1a, $4b, $0e, $a7, $5a,
    $28, $14, $3f, $29, $88, $3c, $4c, $02,
    $b8, $da, $b0, $17, $55, $1f, $8a, $7d,
    $57, $c7, $8d, $74, $b7, $c4, $9f, $72,
    $7e, $15, $22, $12, $58, $07, $99, $34,
    $6e, $50, $de, $68, $65, $bc, $db, $f8,
    $c8, $a8, $2b, $40, $dc, $fe, $32, $a4,
    $ca, $10, $21, $f0, $d3, $5d, $0f, $00,
    $6f, $9d, $36, $42, $4a, $5e, $c1, $e0),(
    $75, $f3, $c6, $f4, $db, $7b, $fb, $c8,
    $4a, $d3, $e6, $6b, $45, $7d, $e8, $4b,
    $d6, $32, $d8, $fd, $37, $71, $f1, $e1,
    $30, $0f, $f8, $1b, $87, $fa, $06, $3f,
    $5e, $ba, $ae, $5b, $8a, $00, $bc, $9d,
    $6d, $c1, $b1, $0e, $80, $5d, $d2, $d5,
    $a0, $84, $07, $14, $b5, $90, $2c, $a3,
    $b2, $73, $4c, $54, $92, $74, $36, $51,
    $38, $b0, $bd, $5a, $fc, $60, $62, $96,
    $6c, $42, $f7, $10, $7c, $28, $27, $8c,
    $13, $95, $9c, $c7, $24, $46, $3b, $70,
    $ca, $e3, $85, $cb, $11, $d0, $93, $b8,
    $a6, $83, $20, $ff, $9f, $77, $c3, $cc,
    $03, $6f, $08, $bf, $40, $e7, $2b, $e2,
    $79, $0c, $aa, $82, $41, $3a, $ea, $b9,
    $e4, $9a, $a4, $97, $7e, $da, $7a, $17,
    $66, $94, $a1, $1d, $3d, $f0, $de, $b3,
    $0b, $72, $a7, $1c, $ef, $d1, $53, $3e,
    $8f, $33, $26, $5f, $ec, $76, $2a, $49,
    $81, $88, $ee, $21, $c4, $1a, $eb, $d9,
    $c5, $39, $99, $cd, $ad, $31, $8b, $01,
    $18, $23, $dd, $1f, $4e, $2d, $f9, $48,
    $4f, $f2, $65, $8e, $78, $5c, $58, $19,
    $8d, $e5, $98, $57, $67, $7f, $05, $64,
    $af, $63, $b6, $fe, $f5, $b7, $3c, $a5,
    $ce, $e9, $68, $44, $e0, $4d, $43, $69,
    $29, $2e, $ac, $15, $59, $a8, $0a, $9e,
    $6e, $47, $df, $34, $35, $6a, $cf, $dc,
    $22, $c9, $c0, $9b, $89, $d4, $ed, $ab,
    $12, $a2, $0d, $52, $bb, $02, $2f, $a9,
    $d7, $61, $1e, $b4, $50, $04, $f6, $c2,
    $16, $25, $86, $56, $55, $09, $be, $91));
