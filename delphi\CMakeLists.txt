cmake_minimum_required(VERSION 3.10)
project(MirClient-CPP)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE "Debug" CACHE STRING "Choose the type of build (Debug or Release)" FORCE)
endif()

# Output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Find SDL2 packages
find_package(SDL2 REQUIRED)
find_package(SDL2_image REQUIRED)
find_package(SDL2_ttf REQUIRED)
find_package(SDL2_mixer REQUIRED)
find_package(SDL2_net QUIET)

# Define SDL2_NET_FOUND based on whether SDL2_net was found
if(SDL2_net_FOUND)
  set(SDL2_NET_FOUND TRUE)
  message(STATUS "SDL2_net found")
else()
  set(SDL2_NET_FOUND FALSE)
  message(WARNING "SDL2_net not found, network functionality will be disabled")
  add_definitions(-DSDL_NET_DISABLED)
endif()

# Platform specific configurations
if(WIN32)
  # Windows-specific definitions
  add_definitions(-D_WIN32)
  set(GUI_TYPE "")
  if(MINGW)
    # For MinGW, we need to set SDL paths manually
    # Unset any vcpkg-related variables
    unset(SDL2_DIR CACHE)
    unset(SDL2_image_DIR CACHE)
    unset(SDL2_ttf_DIR CACHE)
    unset(SDL2_mixer_DIR CACHE)
    unset(SDL2_net_DIR CACHE)

    # Set the path to the MinGW SDL2 installation
    set(SDL2_PATH "C:/msys64/mingw64" CACHE PATH "Path to SDL2 installation")

    # Set include directories
    set(SDL2_INCLUDE_DIRS "${SDL2_PATH}/include/SDL2")
    set(SDL2_IMAGE_INCLUDE_DIRS "${SDL2_PATH}/include/SDL2")
    set(SDL2_TTF_INCLUDE_DIRS "${SDL2_PATH}/include/SDL2")
    set(SDL2_MIXER_INCLUDE_DIRS "${SDL2_PATH}/include/SDL2")
    set(SDL2_NET_INCLUDE_DIRS "${SDL2_PATH}/include/SDL2")

    # Set library paths
    set(SDL2_MAIN_LIBRARIES "${SDL2_PATH}/lib/libSDL2main.a")
    set(SDL2_LIBRARIES "${SDL2_PATH}/lib/libSDL2.dll.a")
    set(SDL2_IMAGE_LIBRARIES "${SDL2_PATH}/lib/libSDL2_image.dll.a")
    set(SDL2_TTF_LIBRARIES "${SDL2_PATH}/lib/libSDL2_ttf.dll.a")
    set(SDL2_MIXER_LIBRARIES "${SDL2_PATH}/lib/libSDL2_mixer.dll.a")

    # Check if SDL2_net is available
    if(EXISTS "${SDL2_PATH}/lib/libSDL2_net.dll.a")
      set(SDL2_NET_LIBRARIES "${SDL2_PATH}/lib/libSDL2_net.dll.a")
      set(SDL2_NET_FOUND TRUE)
      message(STATUS "SDL2_net found at ${SDL2_PATH}/lib/libSDL2_net.dll.a")
    else()
      set(SDL2_NET_FOUND FALSE)
      message(WARNING "SDL2_net not found, network functionality will be disabled")
      add_definitions(-DSDL_NET_DISABLED)
    endif()

    # Set compiler flags
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -static-libgcc -static-libstdc++")
  endif()
elseif(APPLE)
  # macOS-specific definitions
  add_definitions(-D__APPLE__)
  set(GUI_TYPE "")
else()
  # Linux/Unix-specific definitions
  add_definitions(-D__linux__)
  set(GUI_TYPE "")
endif()

# Include directories
include_directories(
    ${SDL2_INCLUDE_DIRS}
    ${SDL2_IMAGE_INCLUDE_DIRS}
    ${SDL2_TTF_INCLUDE_DIRS}
    ${SDL2_MIXER_INCLUDE_DIRS}
    ${CMAKE_SOURCE_DIR}/src
)

# Add SDL2_NET include directories if found
if(SDL2_NET_FOUND)
    include_directories(${SDL2_NET_INCLUDE_DIRS})
endif()

# Source files
file(GLOB_RECURSE SOURCES
    "src/*.cpp"
    "src/*.h"
)

# Exclude test files (moved to tests directory)
list(FILTER SOURCES EXCLUDE REGEX ".*ExceptionLoggingExample\\.cpp$")
list(FILTER SOURCES EXCLUDE REGEX ".*ExceptionLoggingTest\\.cpp$")
list(FILTER SOURCES EXCLUDE REGEX ".*LoggingTest\\.cpp$")

# Create executable
if(WIN32)
  # For Windows, create a GUI application (no console window in release mode)
  add_executable(MirClient WIN32 ${SOURCES})
else()
  # For other platforms, create a standard application
  add_executable(MirClient ${SOURCES})
endif()

# Find ZLIB package
find_package(ZLIB REQUIRED)

# Link libraries
if(MINGW)
    # For MinGW, ensure correct link order for SDL2
    target_link_libraries(MirClient
        mingw32
        ${SDL2_MAIN_LIBRARIES}
        ${SDL2_LIBRARIES}
        ${SDL2_IMAGE_LIBRARIES}
        ${SDL2_TTF_LIBRARIES}
        ${SDL2_MIXER_LIBRARIES}
        ${ZLIB_LIBRARIES}
        -lz
    )
else()
    # For non-MinGW builds
    target_link_libraries(MirClient
        ${SDL2_LIBRARIES}
        ${SDL2_IMAGE_LIBRARIES}
        ${SDL2_TTF_LIBRARIES}
        ${SDL2_MIXER_LIBRARIES}
        ${ZLIB_LIBRARIES}
    )
endif()

# Link SDL2_NET libraries if found
if(SDL2_NET_FOUND)
    target_link_libraries(MirClient ${SDL2_NET_LIBRARIES})
endif()

# Platform-specific linking
if(WIN32 AND NOT MINGW)
    # MSVC-specific libraries
    target_link_libraries(MirClient
        ${WIN32_LIBRARIES}
    )
elseif(APPLE)
  # macOS-specific libraries
  find_library(COCOA_LIBRARY Cocoa)
  target_link_libraries(MirClient ${COCOA_LIBRARY})
else()
  # Linux-specific libraries (if any)
  target_link_libraries(MirClient
      ${CMAKE_DL_LIBS}
  )
endif()

# Copy assets to build directory
file(COPY assets DESTINATION ${CMAKE_BINARY_DIR})

# Set output directory
set_target_properties(MirClient PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Add compile options
if(MSVC)
    target_compile_options(MirClient PRIVATE /W4)
else()
    # For GCC/MinGW
    target_compile_options(MirClient PRIVATE -Wall -Wextra -pedantic -g)

    # Enable debugging symbols for Debug build
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        target_compile_options(MirClient PRIVATE -g3 -Og)
    endif()

    # Optimization for Release build
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        target_compile_options(MirClient PRIVATE -O2)
    endif()
endif()

# Install
install(TARGETS MirClient DESTINATION bin)
install(DIRECTORY assets DESTINATION bin)

# Include logging test
include(src/LoggingTest.cmake)

# Include tests directory
add_subdirectory(tests)
