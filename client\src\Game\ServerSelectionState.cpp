#include "ServerSelectionState.h"
#include "CharacterSelectionState.h"
#include "LoginState.h"
#include "../Application.h"
#include "../UI/DialogManager.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>

ServerSelectionState::ServerSelectionState(Application* app, std::shared_ptr<NetworkManager> networkManager)
    : GameState(app)
    , m_networkManager(networkManager)
    , m_selectedServerIndex(-1)
    , m_connecting(false)
    , m_dialogManager(std::make_shared<DialogManager>(app->GetRenderer()))
{
    // Register packet handlers
    m_networkManager->RegisterPacketHandler(
        PacketType::SERVER_LIST,
        [this](const Packet& packet) { OnServerListResponse(packet); }
    );

    m_networkManager->RegisterPacketHandler(
        PacketType::SERVER_CONNECT_RESPONSE,
        [this](const Packet& packet) { OnServerConnectResponse(packet); }
    );
}

ServerSelectionState::~ServerSelectionState()
{
    // Unregister packet handlers
    if (m_networkManager) {
        m_networkManager->UnregisterPacketHandler(PacketType::SERVER_LIST);
        m_networkManager->UnregisterPacketHandler(PacketType::SERVER_CONNECT_RESPONSE);
    }
}

void ServerSelectionState::Enter()
{
    // Load background texture
    m_backgroundTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (!m_backgroundTexture->LoadFromFile("assets/data/server_selection_background.png")) {
        std::cerr << "Failed to load server selection background texture" << std::endl;
    }

    // Create UI controls
    CreateControls();

    // Request server list
    m_statusLabel->SetText("Requesting server list...");

    Packet packet(PacketType::SERVER_LIST_REQUEST);
    m_networkManager->QueuePacket(packet);
}

void ServerSelectionState::Exit()
{
    // Clear textures
    m_backgroundTexture.reset();

    // Clear UI controls
    m_titleLabel.reset();
    m_serverListView.reset();
    m_connectButton.reset();
    m_backButton.reset();
    m_statusLabel.reset();
}

void ServerSelectionState::Update(float deltaTime)
{
    // Update network manager
    if (m_networkManager) {
        m_networkManager->Update();
    }

    // Update dialog manager
    if (m_dialogManager) {
        m_dialogManager->Update(static_cast<int>(deltaTime * 1000));
    }

    // Update UI controls
    if (m_titleLabel) m_titleLabel->Update(static_cast<int>(deltaTime * 1000));
    if (m_serverListView) m_serverListView->Update(static_cast<int>(deltaTime * 1000));
    if (m_connectButton) m_connectButton->Update(static_cast<int>(deltaTime * 1000));
    if (m_backButton) m_backButton->Update(static_cast<int>(deltaTime * 1000));
    if (m_statusLabel) m_statusLabel->Update(static_cast<int>(deltaTime * 1000));
}

void ServerSelectionState::Render()
{
    // Render background
    if (m_backgroundTexture) {
        m_backgroundTexture->Render(0, 0);
    }

    // Render UI controls
    if (m_titleLabel) m_titleLabel->Render(m_app->GetRenderer());
    if (m_serverListView) m_serverListView->Render(m_app->GetRenderer());
    if (m_connectButton) m_connectButton->Render(m_app->GetRenderer());
    if (m_backButton) m_backButton->Render(m_app->GetRenderer());
    if (m_statusLabel) m_statusLabel->Render(m_app->GetRenderer());

    // Render dialog manager
    if (m_dialogManager) {
        m_dialogManager->Render();
    }
}

void ServerSelectionState::HandleEvents(SDL_Event& event)
{
    // Check if dialog manager has any active dialogs and let it handle events first
    if (m_dialogManager && m_dialogManager->HasActiveDialogs()) {
        if (m_dialogManager->HandleEvent(event)) {
            return;
        }
    }

    // Handle UI control events
    if (m_serverListView) m_serverListView->HandleEvent(event);
    if (m_connectButton) m_connectButton->HandleEvent(event);
    if (m_backButton) m_backButton->HandleEvent(event);

    // Handle keyboard events
    if (event.type == SDL_KEYDOWN) {
        if (event.key.keysym.sym == SDLK_RETURN) {
            // Enter key pressed, connect to selected server
            OnConnectButtonClick();
        } else if (event.key.keysym.sym == SDLK_ESCAPE) {
            // Escape key pressed, go back
            OnBackButtonClick();
        }
    }
}

void ServerSelectionState::CreateControls()
{
    // Load font
    TTF_Font* font = TTF_OpenFont("assets/data/font.ttf", 24);
    if (!font) {
        std::cerr << "Failed to load font: " << TTF_GetError() << std::endl;
        return;
    }

    // Create title label
    m_titleLabel = std::make_shared<Label>(0, 50, 800, 40, "Select Server");
    m_titleLabel->SetFont(font);
    m_titleLabel->SetTextColor({255, 255, 255, 255});
    m_titleLabel->SetAlignment(TextAlignment::CENTER);

    // Create server list view
    m_serverListView = std::make_shared<ListView>(200, 100, 400, 300);
    m_serverListView->SetFont(font);
    m_serverListView->SetTextColor({0, 0, 0, 255});
    m_serverListView->SetBackgroundColor({255, 255, 255, 255});
    m_serverListView->SetBorderColor({0, 0, 0, 255});
    m_serverListView->SetHeaderBackgroundColor({200, 200, 200, 255});
    m_serverListView->SetHeaderTextColor({0, 0, 0, 255});
    m_serverListView->SetSelectionColor({0, 120, 215, 255});
    m_serverListView->SetItemHeight(30);
    m_serverListView->SetHeaderHeight(30);

    // Add columns
    m_serverListView->AddColumn("Server Name", 200);
    m_serverListView->AddColumn("Status", 100);
    m_serverListView->AddColumn("Players", 100);

    // Set selection changed callback
    m_serverListView->SetOnSelectionChanged([this](int index) {
        m_selectedServerIndex = index;
        m_connectButton->SetEnabled(index >= 0);
    });

    // Set item double clicked callback
    m_serverListView->SetOnItemDoubleClicked([this](int index) {
        m_selectedServerIndex = index;
        OnConnectButtonClick();
    });

    // Create connect button
    m_connectButton = std::make_shared<Button>(250, 420, 100, 40, "Connect");
    m_connectButton->SetFont(font);
    m_connectButton->SetTextColor({255, 255, 255, 255});
    m_connectButton->SetOnClick([this]() { OnConnectButtonClick(); });
    m_connectButton->SetEnabled(false);

    // Create back button
    m_backButton = std::make_shared<Button>(450, 420, 100, 40, "Back");
    m_backButton->SetFont(font);
    m_backButton->SetTextColor({255, 255, 255, 255});
    m_backButton->SetOnClick([this]() { OnBackButtonClick(); });

    // Create status label
    m_statusLabel = std::make_shared<Label>(0, 480, 800, 30, "");
    m_statusLabel->SetFont(font);
    m_statusLabel->SetTextColor({255, 0, 0, 255});
    m_statusLabel->SetAlignment(TextAlignment::CENTER);

    // Close font
    TTF_CloseFont(font);
}

void ServerSelectionState::OnConnectButtonClick()
{
    // Check if we're already connecting
    if (m_connecting) {
        return;
    }

    // Check if a server is selected
    if (m_selectedServerIndex < 0 || m_selectedServerIndex >= static_cast<int>(m_servers.size())) {
        m_statusLabel->SetText("Please select a server");
        m_dialogManager->ShowMessageDialog("Error", "Please select a server", MessageType::ERROR);
        return;
    }

    // Get selected server
    const ServerInfo& server = m_servers[m_selectedServerIndex];

    // Check if the server is online
    if (server.GetStatus() != ServerStatus::ONLINE && server.GetStatus() != ServerStatus::BUSY) {
        m_statusLabel->SetText("Server is not available");
        m_dialogManager->ShowMessageDialog("Error", "Server is not available. Please select an online server.", MessageType::ERROR);
        return;
    }

    // Set connecting state
    m_connecting = true;
    m_statusLabel->SetText("Connecting to server...");

    // Send server connect request
    Packet packet(PacketType::SERVER_CONNECT_REQUEST);
    packet << server.GetName();
    m_networkManager->QueuePacket(packet);
}

void ServerSelectionState::OnBackButtonClick()
{
    // Disconnect from server
    m_networkManager->Disconnect();

    // Go back to login state
    m_app->ChangeState(std::make_unique<LoginState>(m_app));
}

void ServerSelectionState::OnServerListResponse(const Packet& packet)
{
    // Clear server list
    m_servers.clear();
    m_serverListView->Clear();

    // Read server count
    uint16_t serverCount;
    packet >> serverCount;

    // Read servers
    for (uint16_t i = 0; i < serverCount; i++) {
        // Read server info
        std::string name, address;
        uint16_t port;
        uint8_t status;
        uint16_t playerCount, maxPlayers;

        packet >> name >> address >> port >> status >> playerCount >> maxPlayers;

        // Create server info
        ServerInfo server(name, address, port, static_cast<ServerStatus>(status), playerCount, maxPlayers);
        m_servers.push_back(server);

        // Add to list view
        ListViewItem item({name, server.GetStatusString(), server.GetPlayerCountString()});
        m_serverListView->AddItem(item);
    }

    // Update status
    if (serverCount > 0) {
        m_statusLabel->SetText("Select a server to connect");
    } else {
        m_statusLabel->SetText("No servers available");
        m_dialogManager->ShowMessageDialog("Server List", "No servers are currently available. Please try again later.", MessageType::INFORMATION);
    }
}

void ServerSelectionState::OnServerConnectResponse(const Packet& packet)
{
    // Reset connecting state
    m_connecting = false;

    // Read response
    uint8_t success;
    std::string message;
    packet >> success >> message;

    if (success) {
        // Connect successful
        m_statusLabel->SetText("Connected to server");

        // Show success message and transition to character selection state
        m_dialogManager->ShowMessageDialog("Connection Successful", "Successfully connected to server.", MessageType::INFORMATION, DialogButtons::OK, [this](DialogResult result) {
            // Transition to character selection state
            m_app->ChangeState(std::make_unique<CharacterSelectionState>(m_app, m_networkManager));
        });
    } else {
        // Connect failed
        m_statusLabel->SetText(message);

        // Show error message
        m_dialogManager->ShowMessageDialog("Connection Failed", message, MessageType::ERROR);
    }
}

