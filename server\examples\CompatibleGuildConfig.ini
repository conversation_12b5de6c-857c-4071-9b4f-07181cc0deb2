; Guild Configuration File for 龙城行会
; Compatible with original Delphi project format
; Generated at 1234567890

[Guild]
BuildPoint=1000
Aurae=500
Stability=800
Flourishing=600
ChiefItemCount=50

[Permissions]
AllowMemberInvite=0
AllowMemberKick=0
AllowMemberNotice=0
AllowMemberWarehouse=0
RequireApproval=1

[War]
DefaultDuration=10800000
AllowAllyHelp=1
AutoAcceptWar=0
WarCooldown=86400000

[Skills]
AutoUpgrade=0
MaxSkillLevel=10
UpgradeCostMultiplier=1.0

[Warehouse]
MaxItems=1000
LogOperations=1
AccessLevel=2

[Donation]
MinGoldAmount=1000
MaxGoldAmount=1000000
ExpRewardRate=0.01
ShowDonationRank=1

; 兼容性说明：
; 
; [Guild] 节是原Delphi项目的标准格式，包含5个基本属性：
; - BuildPoint: 建设度
; - Aurae: 灵气值  
; - Stability: 安定度
; - Flourishing: 繁荣度
; - ChiefItemCount: 会长物品数量
;
; 这些属性会直接映射到C++类的成员变量：
; - m_buildPoint
; - m_aurae
; - m_stability
; - m_flourishing
; - m_chiefItemCount
;
; 其他节是C++版本的扩展功能，不会影响原项目的兼容性。
; 如果只有[Guild]节，C++版本会完全兼容原Delphi项目的配置文件。
