// NetworkManager.cpp - 网络管理器实现
#include "NetworkManager.h"
#include "../Common/Logger.h"
#include <iostream>
#include <chrono>
#include <cstring>
#include <algorithm>

namespace MirServer {
namespace Network {

// ==================== NetworkManager 实现 ====================

NetworkManager::NetworkManager() {
#ifdef _WIN32
    // 初始化 Winsock
    if (WSAStartup(MAKEWORD(2, 2), &m_wsaData) != 0) {
        std::cerr << "WSAStartup failed" << std::endl;
    }
#endif
}

NetworkManager::~NetworkManager() {
    Shutdown();
}

bool NetworkManager::Initialize() {
#ifdef _WIN32
    // Windows下已经在构造函数中初始化
    return true;
#else
    return true;
#endif
}

void NetworkManager::Shutdown() {
    StopServer();
    
#ifdef _WIN32
    WSACleanup();
#endif
}

bool NetworkManager::StartServer(const std::string& address, uint16_t port, int maxConnections) {
    m_serverAddress = address;
    return StartServer(port, maxConnections);
}

bool NetworkManager::StartServer(uint16_t port, int maxConnections) {
    if (m_isRunning) {
        return false;
    }
    
    m_serverPort = port;
    m_maxConnections = maxConnections;
    
    // 创建服务器套接字
    m_serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (m_serverSocket == INVALID_SOCKET) {
        if (m_onError) {
            m_onError("Failed to create server socket", -1);
        }
        return false;
    }
    
    // 设置套接字选项
    if (!SetSocketOptions(m_serverSocket)) {
        CloseSocket(m_serverSocket);
        return false;
    }
    
    // 绑定地址
    sockaddr_in serverAddr{};
    serverAddr.sin_family = AF_INET;
    
    if (m_serverAddress == "0.0.0.0") {
        serverAddr.sin_addr.s_addr = INADDR_ANY;
    } else {
        inet_pton(AF_INET, m_serverAddress.c_str(), &serverAddr.sin_addr);
    }
    serverAddr.sin_port = htons(port);
    
    if (bind(m_serverSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        if (m_onError) {
            m_onError("Failed to bind server socket to " + m_serverAddress + ":" + std::to_string(port), -1);
        }
        CloseSocket(m_serverSocket);
        return false;
    }
    
    // 开始监听
    if (listen(m_serverSocket, SOMAXCONN) == SOCKET_ERROR) {
        if (m_onError) {
            m_onError("Failed to listen on server socket", -1);
        }
        CloseSocket(m_serverSocket);
        return false;
    }
    
    // 设置为非阻塞模式
    if (!SetSocketNonBlocking(m_serverSocket)) {
        CloseSocket(m_serverSocket);
        return false;
    }
    
    // 启动工作线程
    m_isRunning = true;
    m_shouldStop = false;
    
    m_acceptThread = std::thread(&NetworkManager::AcceptThread, this);
    m_ioThread = std::thread(&NetworkManager::IOThread, this);
    m_eventThread = std::thread(&NetworkManager::ProcessEvents, this);
    m_maintenanceThread = std::thread(&NetworkManager::MaintenanceThread, this);
    
    std::cout << "Server started on " << m_serverAddress << ":" << port << std::endl;
    return true;
}

void NetworkManager::StopServer() {
    if (!m_isRunning) {
        return;
    }
    
    m_shouldStop = true;
    m_eventCV.notify_all();
    
    // 关闭服务器套接字
    if (m_serverSocket != INVALID_SOCKET) {
        CloseSocket(m_serverSocket);
        m_serverSocket = INVALID_SOCKET;
    }
    
    // 断开所有客户端
    {
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        for (auto& pair : m_clients) {
            pair.second->Disconnect();
        }
        m_clients.clear();
    }
    
    // 等待线程结束
    if (m_acceptThread.joinable()) {
        m_acceptThread.join();
    }
    if (m_ioThread.joinable()) {
        m_ioThread.join();
    }
    if (m_eventThread.joinable()) {
        m_eventThread.join();
    }
    if (m_maintenanceThread.joinable()) {
        m_maintenanceThread.join();
    }
    
    m_isRunning = false;
    std::cout << "Server stopped" << std::endl;
}

void NetworkManager::AcceptThread() {
    while (!m_shouldStop) {
        sockaddr_in clientAddr{};
        int addrLen = sizeof(clientAddr);
        
        SOCKET_TYPE clientSocket = accept(m_serverSocket, (sockaddr*)&clientAddr, &addrLen);
        if (clientSocket == INVALID_SOCKET) {
            if (!m_shouldStop) {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            continue;
        }
        
        // 获取客户端信息
        char ipStr[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &clientAddr.sin_addr, ipStr, INET_ADDRSTRLEN);
        std::string remoteIP(ipStr);
        uint16_t remotePort = ntohs(clientAddr.sin_port);
        
        // 检查IP是否被阻止
        if (IsIPBlocked(remoteIP)) {
            CloseSocket(clientSocket);
            std::lock_guard<std::mutex> lock(m_statsMutex);
            m_stats.rejectedConnections++;
            continue;
        }
        
        // 检查连接数限制
        if (GetClientCount() >= static_cast<size_t>(m_maxConnections)) {
            CloseSocket(clientSocket);
            std::lock_guard<std::mutex> lock(m_statsMutex);
            m_stats.rejectedConnections++;
            continue;
        }
        
        // 检查每IP连接数限制
        if (IsConnectionLimited(remoteIP)) {
            CloseSocket(clientSocket);
            std::lock_guard<std::mutex> lock(m_statsMutex);
            m_stats.rejectedConnections++;
            continue;
        }
        
        // 设置客户端套接字选项
        SetSocketOptions(clientSocket);
        SetSocketNonBlocking(clientSocket);
        
        // 创建客户端连接
        uint32_t clientId = GenerateClientId();
        auto client = std::make_shared<ClientConnection>(clientId, clientSocket, remoteIP, remotePort);
        
        // 设置缓冲区限制
        client->SetSendBufferLimit(m_sendBufferSize);
        client->SetReceiveBufferLimit(m_receiveBufferSize);
        
        // 添加到客户端列表
        {
            std::lock_guard<std::mutex> lock(m_clientsMutex);
            m_clients[clientId] = client;
        }
        
        // 更新IP连接统计
        {
            std::lock_guard<std::mutex> lock(m_ipFilterMutex);
            m_ipConnections[remoteIP].push_back(clientId);
        }
        
        // 更新统计
        {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            m_stats.currentConnections++;
            m_stats.totalConnections++;
        }
        
        // 发送连接事件
        if (m_onClientConnect) {
            m_onClientConnect(client);
        }
        
        NetworkEvent event;
        event.type = NetworkEventType::CLIENT_CONNECTED;
        event.client = client;
        PushEvent(event);
        
        std::cout << "Client connected: " << remoteIP << ":" << remotePort << " (ID: " << clientId << ")" << std::endl;
    }
}

void NetworkManager::IOThread() {
    while (!m_shouldStop) {
        std::vector<std::shared_ptr<ClientConnection>> clientsCopy;
        
        // 复制客户端列表
        {
            std::lock_guard<std::mutex> lock(m_clientsMutex);
            for (const auto& pair : m_clients) {
                if (pair.second && pair.second->IsConnected()) {
                    clientsCopy.push_back(pair.second);
                }
            }
        }
        
        // 处理每个客户端的数据
        for (auto& client : clientsCopy) {
            try {
                ProcessClientData(client);
            } catch (const std::exception& e) {
                if (m_onError) {
                    m_onError("IO Thread error for client " + std::to_string(client->GetId()) + ": " + e.what(), -1);
                }
                HandleClientDisconnect(client, "IO error");
            }
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
}

void NetworkManager::ProcessEvents() {
    while (!m_shouldStop) {
        NetworkEvent event;
        {
            std::unique_lock<std::mutex> lock(m_eventMutex);
            m_eventCV.wait_for(lock, std::chrono::milliseconds(100), [this] { 
                return !m_eventQueue.empty() || m_shouldStop; 
            });
            
            if (m_eventQueue.empty()) {
                continue;
            }
            
            event = m_eventQueue.front();
            m_eventQueue.pop();
        }
        
        // 处理事件
        if (m_eventCallback) {
            try {
                m_eventCallback(event);
            } catch (const std::exception& e) {
                if (m_onError) {
                    m_onError("Event callback error: " + std::string(e.what()), -1);
                }
            }
        }
        
        // 处理包处理器
        if (m_packetHandler) {
            try {
                switch (event.type) {
                    case NetworkEventType::CLIENT_CONNECTED:
                        m_packetHandler->OnClientConnected(event.client);
                        break;
                    case NetworkEventType::CLIENT_DISCONNECTED:
                        m_packetHandler->OnClientDisconnected(event.client);
                        break;
                    case NetworkEventType::PACKET_RECEIVED:
                        m_packetHandler->OnPacketReceived(event.client, event.data);
                        break;
                    case NetworkEventType::ERROR_OCCURRED:
                        m_packetHandler->OnError(event.errorMsg);
                        break;
                    default:
                        break;
                }
            } catch (const std::exception& e) {
                if (m_onError) {
                    m_onError("Packet handler error: " + std::string(e.what()), -1);
                }
            }
        }
    }
}

void NetworkManager::MaintenanceThread() {
    auto lastCheck = std::chrono::steady_clock::now();
    
    while (!m_shouldStop) {
        auto now = std::chrono::steady_clock::now();
        
        // 每10秒执行一次维护任务
        if (std::chrono::duration_cast<std::chrono::seconds>(now - lastCheck).count() >= 10) {
            lastCheck = now;
            
            try {
                CheckClientTimeouts();
                UpdateStatistics();
                
                // 清理临时阻止的IP（1小时后自动解除）
                {
                    std::lock_guard<std::mutex> lock(m_ipFilterMutex);
                    auto it = m_tempBlockedIPs.begin();
                    while (it != m_tempBlockedIPs.end()) {
                        auto elapsed = std::chrono::duration_cast<std::chrono::hours>(now - it->second.blockTime);
                        if (elapsed.count() >= 1) {
                            it = m_tempBlockedIPs.erase(it);
                        } else {
                            ++it;
                        }
                    }
                }
                
                // 清理攻击IP列表（5分钟后重置计数）
                {
                    std::lock_guard<std::mutex> lock(m_ipFilterMutex);
                    auto it = m_attackIPs.begin();
                    while (it != m_attackIPs.end()) {
                        auto elapsed = std::chrono::duration_cast<std::chrono::minutes>(now - it->second.blockTime);
                        if (elapsed.count() >= 5) {
                            it = m_attackIPs.erase(it);
                        } else {
                            ++it;
                        }
                    }
                }
            } catch (const std::exception& e) {
                if (m_onError) {
                    m_onError("Maintenance thread error: " + std::string(e.what()), -1);
                }
            }
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

void NetworkManager::ProcessClientData(std::shared_ptr<ClientConnection> client) {
    if (!client || !client->IsConnected()) {
        return;
    }
    
    // 检查超时
    if (client->IsTimedOut(m_clientTimeoutMs)) {
        HandleClientDisconnect(client, "timeout");
        return;
    }
    
    // 接收数据
    if (client->Receive()) {
        client->UpdateLastActiveTime();
        
        // 检查是否有完整的数据包
        while (client->HasCompletePacket()) {
            std::vector<uint8_t> packet;
            if (client->GetPacket(packet)) {
                // 更新统计
                {
                    std::lock_guard<std::mutex> lock(m_statsMutex);
                    m_stats.totalPacketsReceived++;
                    m_stats.totalBytesReceived += packet.size();
                }
                
                // 发送数据包事件
                if (m_onClientMessage) {
                    m_onClientMessage(client, packet);
                }
                
                NetworkEvent event;
                event.type = NetworkEventType::PACKET_RECEIVED;
                event.client = client;
                event.data = std::move(packet);
                PushEvent(event);
            }
        }
    }
}

void NetworkManager::HandleClientDisconnect(std::shared_ptr<ClientConnection> client, const std::string& reason) {
    if (!client) {
        return;
    }
    
    uint32_t clientId = client->GetId();
    std::string remoteIP = client->GetRemoteIP();
    
    // 从客户端列表移除
    {
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        m_clients.erase(clientId);
    }
    
    // 从IP连接列表移除
    {
        std::lock_guard<std::mutex> lock(m_ipFilterMutex);
        auto it = m_ipConnections.find(remoteIP);
        if (it != m_ipConnections.end()) {
            auto& clientIds = it->second;
            clientIds.erase(std::remove(clientIds.begin(), clientIds.end(), clientId), clientIds.end());
            if (clientIds.empty()) {
                m_ipConnections.erase(it);
            }
        }
    }
    
    // 更新统计
    {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        if (m_stats.currentConnections > 0) {
            m_stats.currentConnections--;
        }
        if (reason == "timeout") {
            m_stats.timedOutConnections++;
        }
    }
    
    // 断开连接
    client->Disconnect();
    
    // 发送断开事件
    if (m_onClientDisconnect) {
        m_onClientDisconnect(client);
    }
    
    NetworkEvent event;
    event.type = NetworkEventType::CLIENT_DISCONNECTED;
    event.client = client;
    PushEvent(event);
    
    std::cout << "Client disconnected: " << remoteIP << " (ID: " << clientId << ", Reason: " << reason << ")" << std::endl;
}

void NetworkManager::CheckClientTimeouts() {
    std::vector<std::shared_ptr<ClientConnection>> timedOutClients;
    
    {
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        for (const auto& pair : m_clients) {
            if (pair.second && pair.second->IsTimedOut(m_clientTimeoutMs)) {
                timedOutClients.push_back(pair.second);
            }
        }
    }
    
    for (auto& client : timedOutClients) {
        HandleClientDisconnect(client, "timeout");
    }
}

void NetworkManager::UpdateStatistics() {
    // 这里可以添加其他统计信息的更新
    // 目前统计信息在其他地方实时更新
}

// IP过滤相关方法
bool NetworkManager::AddBlockedIP(const std::string& ipAddress, bool permanent) {
    std::lock_guard<std::mutex> lock(m_ipFilterMutex);
    
    if (permanent) {
        IPFilterInfo info(ipAddress);
        info.isPermanent = true;
        m_blockedIPs[ipAddress] = info;
    } else {
        IPFilterInfo info(ipAddress);
        m_tempBlockedIPs[ipAddress] = info;
    }
    
    // 关闭该IP的所有连接
    CloseConnectionsByIP(ipAddress);
    return true;
}

bool NetworkManager::RemoveBlockedIP(const std::string& ipAddress) {
    std::lock_guard<std::mutex> lock(m_ipFilterMutex);
    
    bool removed = false;
    if (m_blockedIPs.erase(ipAddress) > 0) {
        removed = true;
    }
    if (m_tempBlockedIPs.erase(ipAddress) > 0) {
        removed = true;
    }
    
    return removed;
}

bool NetworkManager::IsIPBlocked(const std::string& ipAddress) const {
    std::lock_guard<std::mutex> lock(m_ipFilterMutex);
    
    return m_blockedIPs.find(ipAddress) != m_blockedIPs.end() ||
           m_tempBlockedIPs.find(ipAddress) != m_tempBlockedIPs.end();
}

void NetworkManager::ClearTempBlockedIPs() {
    std::lock_guard<std::mutex> lock(m_ipFilterMutex);
    m_tempBlockedIPs.clear();
}

size_t NetworkManager::GetConnectionCountForIP(const std::string& ipAddress) const {
    std::lock_guard<std::mutex> lock(m_ipFilterMutex);
    
    auto it = m_ipConnections.find(ipAddress);
    return (it != m_ipConnections.end()) ? it->second.size() : 0;
}

bool NetworkManager::IsConnectionLimited(const std::string& ipAddress) const {
    return GetConnectionCountForIP(ipAddress) >= static_cast<size_t>(m_maxConnectionsPerIP);
}

void NetworkManager::CloseConnectionsByIP(const std::string& ipAddress) {
    std::vector<std::shared_ptr<ClientConnection>> clientsToClose;
    
    {
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        for (const auto& pair : m_clients) {
            if (pair.second && pair.second->GetRemoteIP() == ipAddress) {
                clientsToClose.push_back(pair.second);
            }
        }
    }
    
    for (auto& client : clientsToClose) {
        HandleClientDisconnect(client, "IP blocked");
    }
}

bool NetworkManager::AddAttackIP(const std::string& ipAddress) {
    std::lock_guard<std::mutex> lock(m_ipFilterMutex);
    
    auto it = m_attackIPs.find(ipAddress);
    if (it != m_attackIPs.end()) {
        it->second.attackCount++;
        it->second.blockTime = std::chrono::steady_clock::now();
        
        // 如果攻击次数过多，自动加入临时阻止列表
        if (it->second.attackCount >= 5) {
            IPFilterInfo info(ipAddress);
            m_tempBlockedIPs[ipAddress] = info;
            CloseConnectionsByIP(ipAddress);
            return true;
        }
    } else {
        IPFilterInfo info(ipAddress);
        info.attackCount = 1;
        m_attackIPs[ipAddress] = info;
    }
    
    return false;
}

int NetworkManager::GetAttackCount(const std::string& ipAddress) const {
    std::lock_guard<std::mutex> lock(m_ipFilterMutex);
    
    auto it = m_attackIPs.find(ipAddress);
    return (it != m_attackIPs.end()) ? it->second.attackCount : 0;
}

void NetworkManager::ClearAttackList() {
    std::lock_guard<std::mutex> lock(m_ipFilterMutex);
    m_attackIPs.clear();
}

// 客户端管理方法
void NetworkManager::BroadcastPacket(const void* data, size_t size) {
    std::lock_guard<std::mutex> lock(m_clientsMutex);
    for (const auto& pair : m_clients) {
        if (pair.second && pair.second->IsConnected()) {
            pair.second->Send(data, size);
        }
    }
    
    std::lock_guard<std::mutex> statLock(m_statsMutex);
    m_stats.totalPacketsSent += m_clients.size();
    m_stats.totalBytesSent += size * m_clients.size();
}

void NetworkManager::SendToClient(uint32_t clientId, const void* data, size_t size) {
    std::shared_ptr<ClientConnection> client;
    
    {
        std::lock_guard<std::mutex> lock(m_clientsMutex);
        auto it = m_clients.find(clientId);
        if (it != m_clients.end()) {
            client = it->second;
        }
    }
    
    if (client && client->IsConnected()) {
        if (client->Send(data, size)) {
            std::lock_guard<std::mutex> lock(m_statsMutex);
            m_stats.totalPacketsSent++;
            m_stats.totalBytesSent += size;
        }
    }
}

bool NetworkManager::SendToClient(std::shared_ptr<ClientConnection> client, const void* data, size_t size) {
    if (!client || !client->IsConnected()) {
        return false;
    }
    
    if (client->Send(data, size)) {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_stats.totalPacketsSent++;
        m_stats.totalBytesSent += size;
        return true;
    }
    
    return false;
}

void NetworkManager::DisconnectClient(uint32_t clientId) {
    std::shared_ptr<ClientConnection> client = GetClient(clientId);
    if (client) {
        HandleClientDisconnect(client, "manual disconnect");
    }
}

void NetworkManager::DisconnectClient(std::shared_ptr<ClientConnection> client) {
    if (client) {
        HandleClientDisconnect(client, "manual disconnect");
    }
}

size_t NetworkManager::GetClientCount() const {
    std::lock_guard<std::mutex> lock(m_clientsMutex);
    return m_clients.size();
}

std::shared_ptr<ClientConnection> NetworkManager::GetClient(uint32_t clientId) const {
    std::lock_guard<std::mutex> lock(m_clientsMutex);
    auto it = m_clients.find(clientId);
    return (it != m_clients.end()) ? it->second : nullptr;
}

NetworkManager::Statistics NetworkManager::GetStatistics() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    return m_stats;
}

void NetworkManager::ResetStatistics() {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    m_stats = Statistics{};
    m_stats.currentConnections = static_cast<uint32_t>(GetClientCount());
}

// 辅助方法
bool NetworkManager::SetSocketNonBlocking(SOCKET_TYPE socket) {
#ifdef _WIN32
    u_long mode = 1;
    return ioctlsocket(socket, FIONBIO, &mode) == 0;
#else
    int flags = fcntl(socket, F_GETFL, 0);
    return fcntl(socket, F_SETFL, flags | O_NONBLOCK) == 0;
#endif
}

bool NetworkManager::SetSocketOptions(SOCKET_TYPE socket) {
    // 允许地址重用
    int reuse = 1;
    if (setsockopt(socket, SOL_SOCKET, SO_REUSEADDR, (char*)&reuse, sizeof(reuse)) == SOCKET_ERROR) {
        return false;
    }
    
    // Nagle算法设置
    int noDelay = m_enableNagle ? 0 : 1;
    setsockopt(socket, IPPROTO_TCP, TCP_NODELAY, (char*)&noDelay, sizeof(noDelay));
    
    // 设置Keep-Alive
    if (m_enableKeepAlive) {
        int keepAlive = 1;
        setsockopt(socket, SOL_SOCKET, SO_KEEPALIVE, (char*)&keepAlive, sizeof(keepAlive));
    }
    
    // 设置发送和接收缓冲区大小
    int sendBufSize = static_cast<int>(m_sendBufferSize);
    int recvBufSize = static_cast<int>(m_receiveBufferSize);
    setsockopt(socket, SOL_SOCKET, SO_SNDBUF, (char*)&sendBufSize, sizeof(sendBufSize));
    setsockopt(socket, SOL_SOCKET, SO_RCVBUF, (char*)&recvBufSize, sizeof(recvBufSize));
    
    return true;
}

void NetworkManager::CloseSocket(SOCKET_TYPE socket) {
    if (socket != INVALID_SOCKET) {
#ifdef _WIN32
        closesocket(socket);
#else
        close(socket);
#endif
    }
}

uint32_t NetworkManager::GenerateClientId() {
    return m_nextClientId++;
}

std::string NetworkManager::GetIPFromSocket(SOCKET_TYPE socket) {
    sockaddr_in addr{};
    int addrLen = sizeof(addr);
    
    if (getpeername(socket, (sockaddr*)&addr, &addrLen) == 0) {
        char ipStr[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &addr.sin_addr, ipStr, INET_ADDRSTRLEN);
        return std::string(ipStr);
    }
    
    return "unknown";
}

void NetworkManager::PushEvent(const NetworkEvent& event) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    m_eventQueue.push(event);
    m_eventCV.notify_one();
}

bool NetworkManager::PopEvent(NetworkEvent& event) {
    std::lock_guard<std::mutex> lock(m_eventMutex);
    if (m_eventQueue.empty()) {
        return false;
    }
    
    event = m_eventQueue.front();
    m_eventQueue.pop();
    return true;
}

std::shared_ptr<ClientConnection> NetworkManager::ConnectToServer(const std::string& address, uint16_t port) {
    // 如果已经连接，先断开
    if (m_serverConnection && m_serverConnection->IsConnected()) {
        DisconnectFromServer();
    }
    
    try {
        // 创建socket
        SOCKET_TYPE socket = ::socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (socket == INVALID_SOCKET) {
            if (m_onError) {
                m_onError("Failed to create socket for server connection", -1);
            }
            return nullptr;
        }
        
        // 设置socket选项
        SetSocketOptions(socket);
        SetSocketNonBlocking(socket);
        
        // 连接到服务器
        sockaddr_in serverAddr{};
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_port = htons(port);
        
        if (inet_pton(AF_INET, address.c_str(), &serverAddr.sin_addr) <= 0) {
            if (m_onError) {
                m_onError("Invalid server address: " + address, -1);
            }
            CloseSocket(socket);
            return nullptr;
        }
        
        int result = connect(socket, (sockaddr*)&serverAddr, sizeof(serverAddr));
        if (result == SOCKET_ERROR) {
#ifdef _WIN32
            int error = WSAGetLastError();
            if (error != WSAEWOULDBLOCK) {
#else
            int error = errno;
            if (error != EWOULDBLOCK && error != EINPROGRESS) {
#endif
                if (m_onError) {
                    m_onError("Failed to connect to server " + address + ":" + std::to_string(port), error);
                }
                CloseSocket(socket);
                return nullptr;
            }
        }
        
        // 创建连接对象
        uint32_t clientId = GenerateClientId();
        m_serverConnection = std::make_shared<ClientConnection>(clientId, socket, address, port);
        
        // 等待连接完成（简单的超时检查）
        fd_set writeSet;
        FD_ZERO(&writeSet);
        FD_SET(socket, &writeSet);
        
        timeval timeout;
        timeout.tv_sec = 5;  // 5秒超时
        timeout.tv_usec = 0;
        
        int selectResult = select(static_cast<int>(socket + 1), nullptr, &writeSet, nullptr, &timeout);
        if (selectResult <= 0) {
            if (m_onError) {
                m_onError("Connection timeout to server " + address + ":" + std::to_string(port), -1);
            }
            m_serverConnection.reset();
            CloseSocket(socket);
            return nullptr;
        }
        
        // 检查连接是否成功
        int error = 0;
        socklen_t errorLen = sizeof(error);
        if (getsockopt(socket, SOL_SOCKET, SO_ERROR, (char*)&error, &errorLen) == 0 && error == 0) {
            std::cout << "Connected to server: " << address << ":" << port << std::endl;
            return m_serverConnection;
        } else {
            if (m_onError) {
                m_onError("Failed to connect to server " + address + ":" + std::to_string(port), error);
            }
            m_serverConnection.reset();
            CloseSocket(socket);
            return nullptr;
        }
        
    } catch (const std::exception& e) {
        if (m_onError) {
            m_onError("Exception in ConnectToServer: " + std::string(e.what()), -1);
        }
        m_serverConnection.reset();
        return nullptr;
    }
}

bool NetworkManager::IsConnectedToServer() const {
    return m_serverConnection && m_serverConnection->IsConnected();
}

void NetworkManager::DisconnectFromServer() {
    if (m_serverConnection) {
        m_serverConnection->Disconnect();
        m_serverConnection.reset();
    }
}

// ==================== ClientConnection 实现 ====================

ClientConnection::ClientConnection(uint32_t id, SOCKET_TYPE socket, const std::string& remoteIP, uint16_t remotePort)
    : m_id(id), m_socket(socket), m_remoteIP(remoteIP), m_remotePort(remotePort) {
    
    m_recvBuffer.resize(DEFAULT_RECV_BUFFER_SIZE);
    m_lastActiveTime = static_cast<uint32_t>(std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
    
    // 初始化会话信息
    m_sessionInfo.sRemoteAddr = remoteIP;
}

ClientConnection::~ClientConnection() {
    Disconnect();
}

bool ClientConnection::Send(const void* data, size_t size) {
    if (!m_connected || m_socket == INVALID_SOCKET || !data || size == 0) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_sendMutex);
    
    // 检查发送缓冲区是否已满
    if (m_sendBuffer.size() + size > m_sendBufferLimit) {
        return false;
    }
    
    // 添加到发送缓冲区
    const uint8_t* bytes = static_cast<const uint8_t*>(data);
    m_sendBuffer.insert(m_sendBuffer.end(), bytes, bytes + size);
    
    // 尝试立即发送
    return FlushSendBuffer();
}

bool ClientConnection::SendText(const std::string& text) {
    return Send(text.c_str(), text.length());
}

bool ClientConnection::Receive() {
    if (!m_connected || m_socket == INVALID_SOCKET) {
        return false;
    }
    
    // 确保有足够的缓冲区空间
    if (m_recvSize >= m_receiveBufferLimit) {
        return false;
    }
    
    int bytesReceived = recv(m_socket, 
                            reinterpret_cast<char*>(m_recvBuffer.data() + m_recvSize), 
                            static_cast<int>(m_recvBuffer.size() - m_recvSize), 
                            0);
    
    if (bytesReceived > 0) {
        m_recvSize += bytesReceived;
        m_bytesReceived += bytesReceived;
        ProcessReceivedData();
        return true;
    } else if (bytesReceived == 0) {
        // 连接被对方关闭
        m_connected = false;
        return false;
    } else {
        // 检查错误
#ifdef _WIN32
        int error = WSAGetLastError();
        if (error != WSAEWOULDBLOCK) {
            m_connected = false;
            return false;
        }
#else
        if (errno != EAGAIN && errno != EWOULDBLOCK) {
            m_connected = false;
            return false;
        }
#endif
        return true; // 非阻塞模式下的正常情况
    }
}

void ClientConnection::ProcessReceivedData() {
    // 这里可以实现数据包的解析逻辑
    // 简单实现：将接收到的数据作为数据包处理
    if (m_recvSize > 0) {
        std::vector<uint8_t> packet(m_recvBuffer.begin(), m_recvBuffer.begin() + m_recvSize);
        
        std::lock_guard<std::mutex> lock(m_packetMutex);
        m_packetQueue.push(packet);
        
        // 清空接收缓冲区
        m_recvSize = 0;
    }
}

bool ClientConnection::ParsePacket() {
    // 这里应该实现具体的数据包解析逻辑
    // 目前简单地将所有数据作为一个包处理
    return m_recvSize > 0;
}

bool ClientConnection::HasCompletePacket() const {
    std::lock_guard<std::mutex> lock(m_packetMutex);
    return !m_packetQueue.empty();
}

bool ClientConnection::GetPacket(std::vector<uint8_t>& packet) {
    std::lock_guard<std::mutex> lock(m_packetMutex);
    if (m_packetQueue.empty()) {
        return false;
    }
    
    packet = std::move(m_packetQueue.front());
    m_packetQueue.pop();
    return true;
}

void ClientConnection::ClearReceiveBuffer() {
    m_recvSize = 0;
    std::lock_guard<std::mutex> lock(m_packetMutex);
    while (!m_packetQueue.empty()) {
        m_packetQueue.pop();
    }
}

void ClientConnection::ClearSendBuffer() {
    std::lock_guard<std::mutex> lock(m_sendMutex);
    m_sendBuffer.clear();
    m_sendPending = false;
}

void ClientConnection::Disconnect() {
    if (!m_connected) {
        return;
    }
    
    m_connected = false;
    
    if (m_socket != INVALID_SOCKET) {
#ifdef _WIN32
        closesocket(m_socket);
#else
        close(m_socket);
#endif
        m_socket = INVALID_SOCKET;
    }
}

void ClientConnection::UpdateLastActiveTime() {
    m_lastActiveTime = static_cast<uint32_t>(std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
}

bool ClientConnection::IsTimedOut(uint32_t timeoutMs) const {
    auto now = static_cast<uint32_t>(std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
    return (now - m_lastActiveTime) * 1000 > timeoutMs;
}

bool ClientConnection::IsSendBufferFull() const {
    std::lock_guard<std::mutex> lock(m_sendMutex);
    return m_sendBuffer.size() >= m_sendBufferLimit;
}

size_t ClientConnection::GetSendBufferSize() const {
    std::lock_guard<std::mutex> lock(m_sendMutex);
    return m_sendBuffer.size();
}

bool ClientConnection::FlushSendBuffer() {
    if (!m_connected || m_socket == INVALID_SOCKET || m_sendBuffer.empty()) {
        return true;
    }
    
    int bytesSent = send(m_socket, 
                        reinterpret_cast<const char*>(m_sendBuffer.data()), 
                        static_cast<int>(m_sendBuffer.size()), 
                        0);
    
    if (bytesSent > 0) {
        m_bytesSent += bytesSent;
        
        // 移除已发送的数据
        if (bytesSent == static_cast<int>(m_sendBuffer.size())) {
            m_sendBuffer.clear();
        } else {
            m_sendBuffer.erase(m_sendBuffer.begin(), m_sendBuffer.begin() + bytesSent);
        }
        
        return true;
    } else if (bytesSent == 0) {
        return false;
    } else {
#ifdef _WIN32
        int error = WSAGetLastError();
        if (error != WSAEWOULDBLOCK) {
            m_connected = false;
            return false;
        }
#else
        if (errno != EAGAIN && errno != EWOULDBLOCK) {
            m_connected = false;
            return false;
        }
#endif
        return true; // 非阻塞模式下的正常情况
    }
}

} // namespace Network
} // namespace MirServer 