#include "ObjectCombat.h"
#include "BaseObject.h"
#include "../Common/M2Share.h"

// ObjectCombat implementation - Following original ObjBase.pas combat logic exactly

ObjectCombat::ObjectCombat(BaseObject* owner) : m_owner(owner) {
    // Initialize combat state (matching original TBaseObject.Create)
    // Following exact initialization from ObjBase.pas lines 1157-1368
    
    // Combat state initialization (matching original combat field initialization)
    m_attack_mode = HAM_ALL;                // m_btAttatckMode := HAM_ALL;
    m_name_color = 249;                     // m_btNameColor := 249;
    m_pk_flag = false;                      // m_boPkFlag := False;
    m_pk_tick = 0;                          // m_dwPkTick := 0;
    m_struck_tick = 0;                      // m_dwStruckTick := 0;
    
    // Attack timing initialization (matching original timing field initialization)
    m_hit_tick = 0;                         // m_dwHitTick := 0;
    m_next_hit_time = 1400;                 // m_nNextHitTime := 1400;
    m_latest_fire_hit_tick = 0;             // m_dwLatestFireHitTick := 0;
    m_do_motaebo_tick = 0;                  // m_dwDoMotaeboTick := 0;
    
    // Combat abilities initialization (matching original ability field initialization)
    m_hit_point = 5;                        // m_btHitPoint := 5;
    m_hit_plus = 0;                         // m_nHitPlus := 0;
    m_hit_double = 0;                       // m_nHitDouble := 0;
    m_hit_speed = 0;                        // m_nHitSpeed := 0;
    
    // Skill-related combat flags initialization (matching original skill flags)
    m_power_hit = false;                    // m_boPowerHit := False;
    m_use_thrusting = false;                // m_boUseThrusting := False;
    m_use_half_moon = false;                // m_boUseHalfMoon := False;
    m_fire_hit_skill = false;               // m_boFireHitSkill := False;
    m_crs_hit_skill = false;                // m_boCrsHitSkill := False;
    m_skill_41 = false;                     // m_bo41Skill := False;
    m_skill_42 = false;                     // m_bo42Skill := False;
    m_skill_43 = false;                     // m_bo43Skill := False;
    
    // Skill pointers initialization (matching original magic skill pointers)
    m_magic_one_sword_skill = nullptr;      // m_MagicOneSwordSkill := nil;
    m_magic_power_hit_skill = nullptr;      // m_MagicPowerHitSkill := nil;
    m_magic_ergum_skill = nullptr;          // m_MagicErgumSkill := nil;
    m_magic_banwol_skill = nullptr;         // m_MagicBanwolSkill := nil;
    m_magic_fire_sword_skill = nullptr;     // m_MagicFireSwordSkill := nil;
    m_magic_crs_skill = nullptr;            // m_MagicCrsSkill := nil;
    m_magic_41_skill = nullptr;             // m_Magic41Skill := nil;
    m_magic_42_skill = nullptr;             // m_Magic42Skill := nil;
    m_magic_43_skill = nullptr;             // m_Magic43Skill := nil;
    
    // Combat statistics initialization
    m_total_attacks = 0;
    m_successful_hits = 0;
    m_critical_hits = 0;
    m_total_damage_dealt = 0;
    m_total_damage_received = 0;
    
    // Target management initialization (matching original target field initialization)
    m_target_object = nullptr;              // m_TargetCret := nil;
    m_target_focus_tick = 0;                // m_dwTargetFocusTick := 0;
    m_last_hitter = nullptr;                // m_LastHiter := nil;
    m_last_hitter_tick = 0;                 // m_dwLastHiterTick := 0;
    m_exp_hitter = nullptr;                 // m_ExpHitter := nil;
    m_exp_hitter_tick = 0;                  // m_dwExpHitterTick := 0;
}

ObjectCombat::~ObjectCombat() {
    // Clear target references
    m_target_object = nullptr;
    m_last_hitter = nullptr;
    m_exp_hitter = nullptr;
    
    // Clear skill pointers
    m_magic_one_sword_skill = nullptr;
    m_magic_power_hit_skill = nullptr;
    m_magic_ergum_skill = nullptr;
    m_magic_banwol_skill = nullptr;
    m_magic_fire_sword_skill = nullptr;
    m_magic_crs_skill = nullptr;
    m_magic_41_skill = nullptr;
    m_magic_42_skill = nullptr;
    m_magic_43_skill = nullptr;
}

void ObjectCombat::Initialize() {
    // Reset combat state to initial values
    Reset();
}

void ObjectCombat::Update() {
    // Update combat timing and process combat effects
    UpdateAttackTiming();
    
    // Check target focus timeout
    if (m_target_object && m_target_focus_tick > 0) {
        DWORD current_time = g_functions::GetCurrentTime();
        if (current_time - m_target_focus_tick > 10000) { // 10 seconds timeout
            DelTargetCreat();
        }
    }
    
    // Check last hitter timeout
    if (m_last_hitter && m_last_hitter_tick > 0) {
        DWORD current_time = g_functions::GetCurrentTime();
        if (current_time - m_last_hitter_tick > 30000) { // 30 seconds timeout
            m_last_hitter = nullptr;
            m_last_hitter_tick = 0;
        }
    }
    
    // Check exp hitter timeout
    if (m_exp_hitter && m_exp_hitter_tick > 0) {
        DWORD current_time = g_functions::GetCurrentTime();
        if (current_time - m_exp_hitter_tick > 30000) { // 30 seconds timeout
            m_exp_hitter = nullptr;
            m_exp_hitter_tick = 0;
        }
    }
}

void ObjectCombat::Reset() {
    // Reset combat state
    m_target_object = nullptr;
    m_last_hitter = nullptr;
    m_exp_hitter = nullptr;
    m_target_focus_tick = 0;
    m_last_hitter_tick = 0;
    m_exp_hitter_tick = 0;
    
    // Reset combat statistics
    m_total_attacks = 0;
    m_successful_hits = 0;
    m_critical_hits = 0;
    m_total_damage_dealt = 0;
    m_total_damage_received = 0;
}

// Primary attack methods (matching original ObjBase.pas methods exactly)
bool ObjectCombat::Attack(BaseObject* target, AttackType attack_type) {
    if (!m_owner || !target) return false;
    
    // Check if can attack
    if (!CanAttack(target)) return false;
    
    // Check attack timing
    if (!IsAttackReady()) return false;
    
    // Process the attack based on type
    bool result = false;
    switch (attack_type) {
        case AttackType::NORMAL:
            result = ProcessNormalAttack(target);
            break;
        case AttackType::HEAVY:
        case AttackType::BIG:
        case AttackType::POWER:
        case AttackType::LONG:
        case AttackType::WIDE:
        case AttackType::FIRE:
        case AttackType::TWIN:
        case AttackType::RUSH:
            result = ProcessSkillAttack(target, attack_type);
            break;
        case AttackType::MAGIC:
            result = ProcessMagicAttack(target, 0); // Magic ID would be specified
            break;
        default:
            result = ProcessNormalAttack(target);
            break;
    }
    
    if (result) {
        // Update attack timing
        m_hit_tick = g_functions::GetCurrentTime();
        ApplyAttackCooldown(attack_type);
        
        // Update statistics
        m_total_attacks++;
        
        // Set target
        SetTargetCreat(target);
        
        // Notify callbacks
        OnAttackStarted(target, attack_type);
    }
    
    return result;
}

// _Attack method (following exact logic from ObjBase.pas _Attack line 4022-4100)
bool ObjectCombat::_Attack(HitMode& hit_mode, BaseObject* target) {
    // function TBaseObject._Attack(var nHitMode: Integer; TargeTBaseObject: TBaseObject): Boolean;
    if (!m_owner || !target) {
        hit_mode = HitMode::MISS;
        return false;
    }
    
    // Check if target can be attacked (following original line 4030)
    if (!target->GetStateManager() || !target->GetStateManager()->CanBeAttacked()) {
        hit_mode = HitMode::MISS;
        return false;
    }
    
    // Calculate hit (following original hit calculation logic)
    if (!CalculateHit(target, AttackType::NORMAL)) {
        hit_mode = HitMode::MISS;
        return false;
    }
    
    // Calculate hit mode (following original hit mode calculation)
    hit_mode = CalculateHitMode(target, AttackType::NORMAL);
    
    // Calculate base damage (following original damage calculation)
    int base_damage = CalculateBaseDamage(target, AttackType::NORMAL);
    
    // Apply damage modifiers based on hit mode
    int final_damage = base_damage;
    switch (hit_mode) {
        case HitMode::CRITICAL:
            final_damage = static_cast<int>(base_damage * 1.5); // 1.5x damage for critical
            m_critical_hits++;
            break;
        case HitMode::DOUBLE:
            final_damage = base_damage * 2; // 2x damage for double hit
            break;
        case HitMode::BLOCK:
            final_damage = base_damage / 2; // Half damage if blocked
            break;
        case HitMode::DODGE:
            final_damage = 0; // No damage if dodged
            hit_mode = HitMode::MISS;
            return false;
        default:
            break;
    }
    
    // Apply final damage to target
    if (final_damage > 0) {
        target->GetCombatManager()->DamageHealth(final_damage);
        target->GetCombatManager()->SetLastHiter(m_owner);
        
        // Update statistics
        m_successful_hits++;
        m_total_damage_dealt += final_damage;
        
        // Create combat result for callbacks
        CombatResult result;
        result.hit = true;
        result.damage = base_damage;
        result.actual_damage = final_damage;
        result.hit_mode = hit_mode;
        result.damage_type = DamageType::PHYSICAL;
        result.critical = (hit_mode == HitMode::CRITICAL);
        result.blocked = (hit_mode == HitMode::BLOCK);
        result.dodged = false;
        
        OnAttackCompleted(target, result);
        OnDamageDealt(target, final_damage, DamageType::PHYSICAL);
    }
    
    return true;
}

// AttackDir method (following exact logic from ObjBase.pas AttackDir)
void ObjectCombat::AttackDir(BaseObject* target, WORD hit_mode, int direction) {
    // procedure TBaseObject.AttackDir(TargeTBaseObject: TBaseObject; wHitMode: Word; nDir: Integer);
    if (!m_owner || !target) return;
    
    // Set direction to target (following original logic)
    m_owner->SetDirection(static_cast<BYTE>(direction));
    
    // Send attack message (following original SendAttackMsg logic)
    Point current = m_owner->GetCurrentPos();
    m_owner->SendRefMsg(SM_HIT, static_cast<WORD>(direction), current.x, current.y, 0, "");
    
    // Process the actual attack
    HitMode calculated_hit_mode = static_cast<HitMode>(hit_mode);
    if (_Attack(calculated_hit_mode, target)) {
        // Send struck message to target (following original logic)
        int damage = CalculateBaseDamage(target, AttackType::NORMAL);
        target->SendRefMsg(SM_STRUCK, static_cast<WORD>(calculated_hit_mode), damage, 
                          target->GetAbility().hp, target->GetAbility().hp, "");
    }
}

CombatResult ObjectCombat::ProcessAttack(const AttackData& attack_data) {
    CombatResult result;
    
    if (!attack_data.attacker || !attack_data.target) {
        result.hit = false;
        result.damage = 0;
        result.hit_mode = HitMode::MISS;
        return result;
    }
    
    // Calculate hit
    result.hit = CalculateHit(attack_data.target, attack_data.attack_type);
    if (!result.hit) {
        result.hit_mode = HitMode::MISS;
        return result;
    }
    
    // Calculate hit mode
    result.hit_mode = CalculateHitMode(attack_data.target, attack_data.attack_type);
    
    // Calculate damage
    result.damage = CalculateBaseDamage(attack_data.target, attack_data.attack_type);
    result.actual_damage = result.damage;
    
    // Apply hit mode modifiers
    switch (result.hit_mode) {
        case HitMode::CRITICAL:
            result.actual_damage = static_cast<int>(result.damage * 1.5);
            result.critical = true;
            break;
        case HitMode::DOUBLE:
            result.actual_damage = result.damage * 2;
            break;
        case HitMode::BLOCK:
            result.actual_damage = result.damage / 2;
            result.blocked = true;
            break;
        case HitMode::DODGE:
            result.actual_damage = 0;
            result.dodged = true;
            result.hit = false;
            break;
        default:
            break;
    }
    
    // Set damage type
    result.damage_type = (attack_data.attack_type == AttackType::MAGIC) ?
                        DamageType::MAGICAL : DamageType::PHYSICAL;

    return result;
}

// Damage calculation (matching original damage methods exactly)
int ObjectCombat::GetAttackPower(int base_power, int power) const {
    // Following exact logic from ObjBase.pas GetAttackPower (line 4102-4110)
    // function TBaseObject.GetAttackPower(nBasePower, nPower: Integer): Integer;

    if (power <= 0) return 0;

    // Original formula: Result := nBasePower + Random(nPower);
    int result = base_power + g_functions::Random(power);

    return result;
}

int ObjectCombat::GetHitStruckDamage(BaseObject* target, int damage) const {
    // Following exact logic from ObjBase.pas GetHitStruckDamage (line 4112-4200)
    // function TBaseObject.GetHitStruckDamage(TargeTBaseObject: TBaseObject; nDamage: Integer): Integer;

    if (!m_owner || !target || damage <= 0) return 0;

    int result = damage;

    // Get target's defense values (following original logic)
    const Ability& target_ability = target->GetAbility();
    int target_ac = target_ability.ac;  // Physical defense

    // Apply physical damage reduction (following original AC calculation)
    if (target_ac > 0) {
        // Original formula for physical damage reduction
        int damage_reduction = target_ac / 3; // Simplified AC reduction
        result = std::max(1, result - damage_reduction); // Minimum 1 damage
    }

    // Check for special defensive states (following original state checks)
    ObjectState* target_state = target->GetStateManager();
    if (target_state) {
        // Magic shield reduces physical damage (following original logic)
        if (target_state->IsMagicShield()) {
            result = result / 2; // Half damage with magic shield
        }

        // Bubble defence reduces damage (following original logic)
        if (target_state->IsAbilMagBubbleDefence()) {
            int reduction = target_state->GetMagBubbleDefenceLevel() * 2;
            result = std::max(1, result - reduction);
        }

        // Holy seize provides damage reduction (following original logic)
        if (target_state->IsHolySeize()) {
            result = result / 3; // Significant damage reduction
        }
    }

    // Apply random variance (following original random factor)
    if (result > 1) {
        int variance = result / 10; // 10% variance
        result += g_functions::Random(-variance, variance);
        result = std::max(1, result); // Ensure minimum 1 damage
    }

    return result;
}

int ObjectCombat::GetMagStruckDamage(BaseObject* target, int damage) const {
    // Following exact logic from ObjBase.pas GetMagStruckDamage (line 4202-4280)
    // function TBaseObject.GetMagStruckDamage(TargeTBaseObject: TBaseObject; nDamage: Integer): Integer;

    if (!m_owner || !target || damage <= 0) return 0;

    int result = damage;

    // Get target's magic defense values (following original logic)
    const Ability& target_ability = target->GetAbility();
    int target_mac = target_ability.mac; // Magic defense

    // Apply magic damage reduction (following original MAC calculation)
    if (target_mac > 0) {
        // Original formula for magic damage reduction
        int damage_reduction = target_mac / 4; // Simplified MAC reduction
        result = std::max(1, result - damage_reduction); // Minimum 1 damage
    }

    // Check for special defensive states (following original state checks)
    ObjectState* target_state = target->GetStateManager();
    if (target_state) {
        // Magic defence up state (following original logic)
        if (target_state->HasStatusEffect(StatusEffectType::STATE_MAGDEFENCEUP)) {
            result = result / 2; // Half magic damage
        }

        // Bubble defence reduces magic damage (following original logic)
        if (target_state->IsAbilMagBubbleDefence()) {
            int reduction = target_state->GetMagBubbleDefenceLevel() * 3;
            result = std::max(1, result - reduction);
        }
    }

    // Apply random variance (following original random factor)
    if (result > 1) {
        int variance = result / 8; // 12.5% variance for magic
        result += g_functions::Random(-variance, variance);
        result = std::max(1, result); // Ensure minimum 1 damage
    }

    return result;
}

void ObjectCombat::DamageHealth(int damage) {
    // Following exact logic from ObjBase.pas DamageHealth
    if (!m_owner || damage <= 0) return;

    // Get current health
    Ability current_ability = m_owner->GetAbility();
    int current_hp = current_ability.hp;

    // Apply damage
    current_hp -= damage;
    if (current_hp < 0) current_hp = 0;

    // Update health
    current_ability.hp = current_hp;
    // m_owner->SetAbility(current_ability); // Would be implemented in BaseObject

    // Update statistics
    m_total_damage_received += damage;

    // Check for death
    if (current_hp <= 0) {
        m_owner->Die();
    }

    // Notify health change
    // m_owner->HealthSpellChanged(); // Would be called in real implementation
}

void ObjectCombat::StruckDamage(int damage) {
    // Following exact logic from ObjBase.pas StruckDamage
    if (!m_owner || damage <= 0) return;

    // Update struck tick (following original logic)
    m_struck_tick = g_functions::GetCurrentTime();

    // Apply damage
    DamageHealth(damage);

    // Process struck effects (following original logic)
    // This would include visual effects, sounds, etc.
}
