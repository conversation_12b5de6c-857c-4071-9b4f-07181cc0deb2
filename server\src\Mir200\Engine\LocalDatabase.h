#pragma once

// Mir200 LocalDatabase - Local data management system
// Based on delphi/EM2Engine/LocalDB.pas - Following original project structure
// Phase 1 Implementation - Basic placeholder for M2Server integration

#include "Common/M2Share.h"
#include <memory>
#include <vector>
#include <string>
#include <mutex>

// LocalDatabase class - Main local data management system
class LocalDatabase {
private:
    // Database state
    bool m_initialized;
    bool m_running;
    
    // Data paths
    std::string m_data_path;
    std::string m_items_file;
    std::string m_magic_file;
    std::string m_monster_file;
    
    // Thread safety
    std::mutex m_database_mutex;
    
public:
    LocalDatabase();
    ~LocalDatabase();
    
    // Core lifecycle
    bool Initialize();
    void Finalize();
    bool Start();
    void Stop();
    
    // Data loading
    bool LoadItemsDB();
    bool LoadMagicDB();
    bool LoadMonsterDB();
    bool LoadAdminList();
    
    // Data processing
    void ProcessOperations();
    
    // Emergency operations
    void EmergencyStop();
    
    // State management
    bool IsInitialized() const { return m_initialized; }
    bool IsRunning() const { return m_running; }
    
    // Configuration
    void SetDataPath(const std::string& path) { m_data_path = path; }
    const std::string& GetDataPath() const { return m_data_path; }
};
