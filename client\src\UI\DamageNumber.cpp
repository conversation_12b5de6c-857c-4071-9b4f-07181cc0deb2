#include "DamageNumber.h"
#include <iostream>

DamageNumber::DamageNumber(int x, int y, int value, DamageType type, TTF_Font* font, SDL_Renderer* renderer)
    : m_x(x)
    , m_y(y)
    , m_value(value)
    , m_type(type)
    , m_duration(1500)  // 1.5 seconds
    , m_timer(m_duration)
    , m_speed(30.0f)    // 30 pixels per second
    , m_active(true)
{
    // Create texture
    CreateTexture(font, renderer);
}

DamageNumber::~DamageNumber()
{
}

void DamageNumber::Update(int deltaTime)
{
    // Update timer
    m_timer -= deltaTime;
    if (m_timer <= 0) {
        m_active = false;
        return;
    }

    // Move upward
    float moveAmount = (m_speed * deltaTime) / 1000.0f;
    m_y -= static_cast<int>(moveAmount);
}

void DamageNumber::Render()
{
    if (!m_active || !m_texture) {
        return;
    }

    // Calculate alpha based on remaining time
    Uint8 alpha = static_cast<Uint8>((m_timer * 255) / m_duration);
    m_texture->SetAlpha(alpha);

    // Render the damage number
    m_texture->Render(m_x, m_y);
}

void DamageNumber::CreateTexture(TTF_Font* font, SDL_Renderer* renderer)
{
    if (!font || !renderer) {
        std::cerr << "Failed to create damage number texture: font or renderer is null" << std::endl;
        return;
    }

    // Convert value to string
    std::string text = std::to_string(m_value);

    // Set color based on damage type
    SDL_Color color;
    switch (m_type) {
        case DamageType::NORMAL:
            color = {255, 255, 255, 255};  // White
            break;
        case DamageType::CRITICAL:
            color = {255, 0, 0, 255};      // Red
            break;
        case DamageType::HEAL:
            color = {0, 255, 0, 255};      // Green
            break;
        case DamageType::MISS:
            color = {128, 128, 128, 255};  // Gray
            text = "Miss";
            break;
    }

    // Create texture
    m_texture = std::make_shared<Texture>(renderer);
    if (!m_texture->LoadFromText(text, font, color)) {
        std::cerr << "Failed to create damage number texture: " << TTF_GetError() << std::endl;
        m_texture.reset();
    }

    // Center the texture on the given position
    if (m_texture) {
        m_x -= m_texture->GetWidth() / 2;
    }
}

DamageNumberManager::DamageNumberManager(SDL_Renderer* renderer)
    : m_font(nullptr)
    , m_renderer(renderer)
{
    // Load font
    m_font = TTF_OpenFont("assets/data/font.ttf", 16);
    if (!m_font) {
        std::cerr << "Failed to load font for damage numbers: " << TTF_GetError() << std::endl;
    }
}

DamageNumberManager::~DamageNumberManager()
{
    // Clear damage numbers
    m_damageNumbers.clear();

    // Close font
    if (m_font) {
        TTF_CloseFont(m_font);
        m_font = nullptr;
    }
}

void DamageNumberManager::Update(int deltaTime)
{
    // Update all damage numbers
    for (auto it = m_damageNumbers.begin(); it != m_damageNumbers.end();) {
        (*it)->Update(deltaTime);
        
        // Remove inactive damage numbers
        if (!(*it)->IsActive()) {
            it = m_damageNumbers.erase(it);
        } else {
            ++it;
        }
    }
}

void DamageNumberManager::Render()
{
    // Render all damage numbers
    for (const auto& damageNumber : m_damageNumbers) {
        damageNumber->Render();
    }
}

std::shared_ptr<DamageNumber> DamageNumberManager::AddDamageNumber(int x, int y, int value, DamageType type)
{
    // Check if font is available
    if (!m_font) {
        std::cerr << "Cannot create damage number: font not loaded" << std::endl;
        return nullptr;
    }

    // Create damage number
    std::shared_ptr<DamageNumber> damageNumber = std::make_shared<DamageNumber>(
        x, y, value, type, m_font, m_renderer
    );

    // Add to list
    m_damageNumbers.push_back(damageNumber);

    return damageNumber;
}
