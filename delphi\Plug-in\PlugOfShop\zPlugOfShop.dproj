﻿<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ProjectGuid>{c5753158-2d76-44f1-ad77-2f4f82ef1e4f}</ProjectGuid>
    <MainSource>zPlugOfShop.dpr</MainSource>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <DCC_DCCCompiler>DCC32</DCC_DCCCompiler>
    <DCC_DependencyCheckOutputName>..\..\MirServer\Mir200\zPlugOfShop.dll</DCC_DependencyCheckOutputName>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_DebugInformation>False</DCC_DebugInformation>
    <DCC_LocalDebugSymbols>False</DCC_LocalDebugSymbols>
    <DCC_SymbolReferenceInfo>0</DCC_SymbolReferenceInfo>
    <DCC_ExeOutput>..\..\MirServer</DCC_ExeOutput>
    <DCC_DcuOutput>.\Temp</DCC_DcuOutput>
    <DCC_ObjOutput>.\Temp</DCC_ObjOutput>
    <DCC_HppOutput>.\Temp</DCC_HppOutput>
    <DCC_Define>RELEASE</DCC_Define>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <Version>7.0</Version>
    <DCC_ExeOutput>..\..\MirServer\Mir200</DCC_ExeOutput>
    <DCC_DcuOutput>..\..\Build\zPlugOfShop</DCC_DcuOutput>
    <DCC_ObjOutput>..\..\Build\zPlugOfShop</DCC_ObjOutput>
    <DCC_HppOutput>..\..\Build\zPlugOfShop</DCC_HppOutput>
    <DCC_Define>DEBUG</DCC_Define>
  </PropertyGroup>
  <ProjectExtensions>
    <Borland.Personality>Delphi.Personality</Borland.Personality>
    <Borland.ProjectType>VCLApplication</Borland.ProjectType>
    <BorlandProject>
<BorlandProject xmlns=""> <Delphi.Personality>   <Parameters>
      <Parameters Name="UseLauncher">False</Parameters>
      <Parameters Name="LoadAllSymbols">True</Parameters>
      <Parameters Name="LoadUnspecifiedSymbols">False</Parameters>
    </Parameters>
    <VersionInfo>
      <VersionInfo Name="IncludeVerInfo">False</VersionInfo>
      <VersionInfo Name="AutoIncBuild">False</VersionInfo>
      <VersionInfo Name="MajorVer">1</VersionInfo>
      <VersionInfo Name="MinorVer">0</VersionInfo>
      <VersionInfo Name="Release">0</VersionInfo>
      <VersionInfo Name="Build">0</VersionInfo>
      <VersionInfo Name="Debug">False</VersionInfo>
      <VersionInfo Name="PreRelease">False</VersionInfo>
      <VersionInfo Name="Special">False</VersionInfo>
      <VersionInfo Name="Private">False</VersionInfo>
      <VersionInfo Name="DLL">False</VersionInfo>
      <VersionInfo Name="Locale">2052</VersionInfo>
      <VersionInfo Name="CodePage">936</VersionInfo>
    </VersionInfo>
    <VersionInfoKeys>
      <VersionInfoKeys Name="CompanyName"></VersionInfoKeys>
      <VersionInfoKeys Name="FileDescription"></VersionInfoKeys>
      <VersionInfoKeys Name="FileVersion">*******</VersionInfoKeys>
      <VersionInfoKeys Name="InternalName"></VersionInfoKeys>
      <VersionInfoKeys Name="LegalCopyright"></VersionInfoKeys>
      <VersionInfoKeys Name="LegalTrademarks"></VersionInfoKeys>
      <VersionInfoKeys Name="OriginalFilename"></VersionInfoKeys>
      <VersionInfoKeys Name="ProductName"></VersionInfoKeys>
      <VersionInfoKeys Name="ProductVersion">*******</VersionInfoKeys>
      <VersionInfoKeys Name="Comments"></VersionInfoKeys>
    </VersionInfoKeys>
    <Source>
      <Source Name="MainSource">zPlugOfShop.dpr</Source>
    </Source>
  </Delphi.Personality> </BorlandProject></BorlandProject>
  </ProjectExtensions>
  <ItemGroup />
  <ItemGroup>
    <DelphiCompile Include="zPlugOfShop.dpr">
      <MainSource>MainSource</MainSource>
    </DelphiCompile>
    <DCCReference Include="..\PlugInCommon\EngineAPI.pas" />
    <DCCReference Include="..\PlugInCommon\EngineType.pas" />
    <DCCReference Include="..\PlugInCommon\HUtil32.pas" />
    <DCCReference Include="PlayShop.pas" />
    <DCCReference Include="PlugMain.pas" />
    <DCCReference Include="PlugShare.pas" />
    <DCCReference Include="ShopConfig.pas">
      <Form>FrmShopItem</Form>
    </DCCReference>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Borland.Delphi.Targets" />
</Project>