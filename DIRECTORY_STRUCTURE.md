# 项目目录结构规划

## 建议的新目录结构

```
mir-delphi-refactored/
├── client/                  # 客户端重构项目
│   ├── src/                # 客户端源代码
│   │   ├── Actor/          # 角色相关
│   │   ├── Game/           # 游戏逻辑
│   │   ├── Graphics/       # 图形渲染
│   │   ├── Item/           # 物品系统
│   │   ├── Map/            # 地图系统
│   │   ├── Network/        # 网络通信
│   │   ├── Skill/          # 技能系统
│   │   ├── Sound/          # 音效系统
│   │   ├── UI/             # 用户界面
│   │   ├── Utils/          # 工具类
│   │   └── main.cpp        # 客户端入口
│   ├── assets/             # 客户端资源文件
│   ├── tests/              # 客户端测试
│   └── CMakeLists.txt      # 客户端构建配置
│
├── server/                  # 服务器重构项目（预留）
│   ├── src/                # 服务器源代码
│   │   ├── LoginServer/    # 登录服务器
│   │   ├── GameServer/     # 游戏服务器
│   │   ├── DBServer/       # 数据库服务器
│   │   ├── GateServer/     # 网关服务器
│   │   └── Common/         # 服务器公共代码
│   ├── config/             # 服务器配置文件
│   ├── scripts/            # 服务器脚本
│   ├── tests/              # 服务器测试
│   └── CMakeLists.txt      # 服务器构建配置
│
├── common/                  # 客户端和服务器共享代码
│   ├── Protocol/           # 网络协议定义
│   ├── Messages/           # 消息定义
│   ├── Utils/              # 通用工具类
│   └── Types/              # 共享数据类型
│
├── delphi/                  # 原始Delphi代码（参考）
│   ├── MirClient/          # 原客户端代码
│   ├── MirServer/          # 原服务器代码
│   └── Component/          # 原组件代码
│
├── docs/                    # 项目文档
│   ├── 功能对比清单.md
│   ├── 缺失网络协议对比.md
│   └── 重构版本缺失功能实现建议.md
│
├── tools/                   # 开发工具
├── build/                   # 构建输出目录
├── CMakeLists.txt          # 根目录构建配置
├── README.md               # 项目说明
└── .gitignore              # Git忽略配置
```

## 目录调整说明

1. **client/** - 专门存放客户端重构代码
2. **server/** - 预留给服务器重构项目
3. **common/** - 存放客户端和服务器共享的代码，如协议定义、消息格式等
4. **docs/** - 集中存放所有项目文档
5. **delphi/** - 保留原始Delphi代码作为参考

这种结构的优点：
- 客户端和服务器代码清晰分离
- 共享代码集中管理
- 便于独立构建和测试
- 易于扩展和维护 