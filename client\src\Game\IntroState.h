#pragma once

#include "../GameState.h"
#include "../Graphics/Texture.h"
#include <memory>
#include <vector>

/**
 * @class IntroState
 * @brief Game state for the intro screen
 *
 * This class represents the intro screen of the game, which shows the game logo,
 * credits, and provides options to start the game or exit.
 */
class IntroState : public GameState {
private:
    std::shared_ptr<Texture> m_backgroundTexture;  ///< Background texture
    std::shared_ptr<Texture> m_logoTexture;        ///< Logo texture
    std::shared_ptr<Texture> m_statusTexture;      ///< Status message texture

    std::vector<std::shared_ptr<Texture>> m_textTextures;  ///< Text textures

    int m_fadeTimer;                               ///< Timer for fade effects
    int m_fadeDirection;                           ///< Fade direction (1 = in, -1 = out)
    int m_fadeAlpha;                               ///< Current fade alpha

    bool m_showMenu;                               ///< Whether to show the menu (kept for compatibility but not used)
    int m_selectedOption;                          ///< Selected menu option (kept for compatibility but not used)
    int m_hoverOption;                             ///< Option being hovered over (kept for compatibility but not used)
    SDL_Cursor* m_handCursor;                       ///< Hand cursor for buttons (kept for compatibility but not used)
    SDL_Cursor* m_arrowCursor;                      ///< Default arrow cursor (kept for compatibility but not used)

    /**
     * @brief Create text textures
     */
    void CreateTextTextures();

    /**
     * @brief Handle menu selection
     */
    void HandleMenuSelection();

public:
    /**
     * @brief Constructor
     * @param app Pointer to the application
     */
    IntroState(Application* app);

    /**
     * @brief Destructor
     */
    virtual ~IntroState();

    /**
     * @brief Called when entering the state
     */
    virtual void Enter() override;

    /**
     * @brief Called when exiting the state
     */
    virtual void Exit() override;

    /**
     * @brief Update the state
     * @param deltaTime Time elapsed since last frame in seconds
     */
    virtual void Update(float deltaTime) override;

    /**
     * @brief Render the state
     */
    virtual void Render() override;

    /**
     * @brief Handle SDL events
     * @param event SDL event to handle
     */
    virtual void HandleEvents(SDL_Event& event) override;
};
