#!/bin/bash

echo "===================================="
echo "  GameEngine 测试套件"
echo "  GameEngine Test Suite"
echo "===================================="

BUILD_DIR="build"
BIN_DIR="$BUILD_DIR/bin"
TEST_DIR="$BUILD_DIR/test"

echo ""
echo "正在检查构建目录..."
echo "Checking build directory..."

if [ ! -d "$BUILD_DIR" ]; then
    echo "错误：构建目录不存在，请先运行 cmake 构建"
    echo "Error: Build directory does not exist, please run cmake build first"
    exit 1
fi

echo ""
echo "正在构建测试程序..."
echo "Building test programs..."

cd "$BUILD_DIR"
cmake --build . --config Release

if [ $? -ne 0 ]; then
    echo "错误：构建失败"
    echo "Error: Build failed"
    exit 1
fi

echo ""
echo "构建成功！开始运行测试..."
echo "Build successful! Starting tests..."

cd bin

echo ""
echo "===================================="
echo "1. 运行单元测试"
echo "   Running Unit Tests"
echo "===================================="

if [ -f "GameEngineUnitTests" ]; then
    ./GameEngineUnitTests
    UNIT_TEST_RESULT=$?
else
    echo "警告：单元测试程序不存在"
    echo "Warning: Unit test program does not exist"
    UNIT_TEST_RESULT=1
fi

echo ""
echo "===================================="
echo "2. 运行集成测试"
echo "   Running Integration Tests"
echo "===================================="

if [ -f "GameEngineIntegrationTests" ]; then
    ./GameEngineIntegrationTests
    INTEGRATION_TEST_RESULT=$?
else
    echo "警告：集成测试程序不存在"
    echo "Warning: Integration test program does not exist"
    INTEGRATION_TEST_RESULT=1
fi

echo ""
echo "===================================="
echo "3. 运行性能测试"
echo "   Running Performance Tests"
echo "===================================="

if [ -f "GameEnginePerformanceTests" ]; then
    ./GameEnginePerformanceTests
    PERFORMANCE_TEST_RESULT=$?
else
    echo "警告：性能测试程序不存在"
    echo "Warning: Performance test program does not exist"
    PERFORMANCE_TEST_RESULT=1
fi

echo ""
echo "===================================="
echo "4. 运行Environment测试"
echo "   Running Environment Tests"
echo "===================================="

if [ -f "EnvironmentTests" ]; then
    ./EnvironmentTests
    ENVIRONMENT_TEST_RESULT=$?
else
    echo "警告：Environment测试程序不存在"
    echo "Warning: Environment test program does not exist"
    ENVIRONMENT_TEST_RESULT=1
fi

echo ""
echo "===================================="
echo "5. 运行GameEngine功能演示"
echo "   Running GameEngine Demo"
echo "===================================="

if [ -f "GameEngine" ]; then
    echo "运行GameEngine演示（5秒后自动退出）..."
    echo "Running GameEngine demo (will exit after 5 seconds)..."
    timeout 5s ./GameEngine --test 2>/dev/null || true
    echo "GameEngine演示完成"
    echo "GameEngine demo completed"
    DEMO_RESULT=0
else
    echo "错误：GameEngine程序不存在"
    echo "Error: GameEngine program does not exist"
    DEMO_RESULT=1
fi

echo ""
echo "===================================="
echo "测试结果汇总"
echo "Test Results Summary"
echo "===================================="

echo "单元测试 Unit Tests: $UNIT_TEST_RESULT"
echo "集成测试 Integration Tests: $INTEGRATION_TEST_RESULT"
echo "性能测试 Performance Tests: $PERFORMANCE_TEST_RESULT"
echo "Environment测试 Environment Tests: $ENVIRONMENT_TEST_RESULT"
echo "GameEngine演示 GameEngine Demo: $DEMO_RESULT"

TOTAL_FAILURES=$((UNIT_TEST_RESULT + INTEGRATION_TEST_RESULT + PERFORMANCE_TEST_RESULT + ENVIRONMENT_TEST_RESULT + DEMO_RESULT))

if [ $TOTAL_FAILURES -eq 0 ]; then
    echo ""
    echo "✓ 所有测试通过！"
    echo "✓ All tests passed!"
    echo ""
else
    echo ""
    echo "✗ 有 $TOTAL_FAILURES 个测试失败"
    echo "✗ $TOTAL_FAILURES test(s) failed"
    echo ""
fi

echo "测试完成"
echo "Tests completed"

cd ../..
exit $TOTAL_FAILURES
