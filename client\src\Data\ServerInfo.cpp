#include "ServerInfo.h"

ServerInfo::ServerInfo(
    const std::string& name,
    const std::string& address,
    uint16_t port,
    ServerStatus status,
    int playerCount,
    int maxPlayers
)
    : m_name(name)
    , m_address(address)
    , m_port(port)
    , m_status(status)
    , m_playerCount(playerCount)
    , m_maxPlayers(maxPlayers)
{
}

ServerInfo::~ServerInfo()
{
}

std::string ServerInfo::GetStatusString() const
{
    switch (m_status) {
        case ServerStatus::OFFLINE:
            return "Offline";
        case ServerStatus::ONLINE:
            return "Online";
        case ServerStatus::BUSY:
            return "Busy";
        case ServerStatus::FULL:
            return "Full";
        default:
            return "Unknown";
    }
}

std::string ServerInfo::GetPlayerCountString() const
{
    return std::to_string(m_playerCount) + "/" + std::to_string(m_maxPlayers);
}
