#include "EffectManager.h"
#include <SDL2/SDL.h>
#include <iostream>
#include <algorithm>

// 初始化静态成员变量
EffectManager* EffectManager::s_instance = nullptr;

EffectManager::EffectManager()
    : m_renderer(nullptr)
{
}

EffectManager* EffectManager::GetInstance()
{
    if (!s_instance) {
        s_instance = new EffectManager();
    }
    return s_instance;
}

void EffectManager::ReleaseInstance()
{
    if (s_instance) {
        delete s_instance;
        s_instance = nullptr;
    }
}

EffectManager::~EffectManager()
{
    ClearEffects();
    m_effectTextures.clear();
}

bool EffectManager::Initialize(std::shared_ptr<WILManager> wilManager, SDL_Renderer* renderer)
{
    m_wilManager = wilManager;
    m_renderer = renderer;

    if (!m_wilManager || !m_renderer) {
        std::cerr << "Failed to initialize EffectManager: WILManager or Render<PERSON> is null" << std::endl;
        return false;
    }

    return true;
}

void EffectManager::Update(int deltaTime)
{
    int currentTime = SDL_GetTicks();

    // 更新所有效果
    for (auto it = m_effects.begin(); it != m_effects.end();) {
        Effect& effect = *it;

        // 检查效果是否过期
        if (currentTime - effect.startTime >= effect.duration) {
            it = m_effects.erase(it);
            continue;
        }

        // 计算当前帧
        float progress = static_cast<float>(currentTime - effect.startTime) / effect.duration;
        effect.currentFrame = static_cast<int>(progress * effect.totalFrames);

        // 确保帧索引在有效范围内
        effect.currentFrame = std::min(effect.currentFrame, effect.totalFrames - 1);

        // 如果效果是移动的，更新位置
        if (effect.isMoving) {
            float moveProgress = std::min(progress * 2.0f, 1.0f); // 移动速度是持续时间的两倍
            effect.x = static_cast<int>(effect.x + (effect.targetX - effect.x) * moveProgress);
            effect.y = static_cast<int>(effect.y + (effect.targetY - effect.y) * moveProgress);
        }

        // 获取当前帧的纹理
        effect.texture = GetEffectTexture(effect.type, effect.effectValue, effect.currentFrame);

        ++it;
    }
}

void EffectManager::Render()
{
    if (!m_renderer) {
        return;
    }

    // 渲染所有效果
    for (const auto& effect : m_effects) {
        if (effect.texture) {
            // 如果效果需要混合，设置混合模式
            if (effect.isBlending) {
                SDL_SetTextureBlendMode(effect.texture->GetSDLTexture(), SDL_BLENDMODE_ADD);
            }

            // 渲染效果
            effect.texture->Render(effect.x, effect.y);

            // 恢复默认混合模式
            if (effect.isBlending) {
                SDL_SetTextureBlendMode(effect.texture->GetSDLTexture(), SDL_BLENDMODE_BLEND);
            }
        }
    }
}

bool EffectManager::AddEffect(int x, int y, int targetX, int targetY, EffectType type, int effectValue, int duration)
{
    Effect effect;
    effect.x = x;
    effect.y = y;
    effect.targetX = targetX;
    effect.targetY = targetY;
    effect.startTime = SDL_GetTicks();
    effect.duration = duration;
    effect.currentFrame = 0;
    effect.type = type;
    effect.effectValue = effectValue;
    effect.isMoving = (x != targetX || y != targetY);
    effect.isBlending = false;
    effect.light = 0;

    // 根据效果类型设置总帧数和混合模式
    switch (type) {
        case EffectType::FIRE:
            effect.totalFrames = 10;
            effect.isBlending = true;
            effect.light = 1;
            break;
        case EffectType::ICE:
            effect.totalFrames = 8;
            effect.isBlending = true;
            break;
        case EffectType::LIGHTNING:
            effect.totalFrames = 12;
            effect.isBlending = true;
            effect.light = 1;
            break;
        case EffectType::WIND:
            effect.totalFrames = 6;
            break;
        case EffectType::HOLY:
            effect.totalFrames = 10;
            effect.isBlending = true;
            effect.light = 1;
            break;
        case EffectType::DARK:
            effect.totalFrames = 8;
            break;
        case EffectType::PHYSICAL:
            effect.totalFrames = 5;
            break;
        case EffectType::HEAL:
            effect.totalFrames = 8;
            effect.isBlending = true;
            effect.light = 1;
            break;
        case EffectType::BUFF:
            effect.totalFrames = 6;
            effect.isBlending = true;
            break;
        case EffectType::DEBUFF:
            effect.totalFrames = 6;
            break;
        case EffectType::SUMMON:
            effect.totalFrames = 12;
            effect.isBlending = true;
            effect.light = 1;
            break;
        case EffectType::TELEPORT:
            effect.totalFrames = 8;
            effect.isBlending = true;
            break;
        default:
            effect.totalFrames = 1;
            break;
    }

    // 获取第一帧的纹理
    effect.texture = GetEffectTexture(type, effectValue, 0);
    if (!effect.texture) {
        std::cerr << "Failed to get effect texture for type " << static_cast<int>(type) << std::endl;
        return false;
    }

    // 添加到效果列表
    m_effects.push_back(effect);

    return true;
}

void EffectManager::ClearEffects()
{
    m_effects.clear();
}

std::shared_ptr<Texture> EffectManager::GetEffectTexture(EffectType type, int effectValue, int frame)
{
    if (!m_wilManager || !m_renderer) {
        return nullptr;
    }

    // 创建纹理缓存键
    std::string key = std::to_string(static_cast<int>(type)) + "_" +
                      std::to_string(effectValue) + "_" +
                      std::to_string(frame);

    // 检查缓存
    auto it = m_effectTextures.find(key);
    if (it != m_effectTextures.end()) {
        return it->second;
    }

    // 根据效果类型和值获取WIL文件名和索引
    std::string wilFile;
    int index = 0;

    switch (type) {
        case EffectType::FIRE:
            wilFile = "magic";
            index = 1000 + (effectValue * 100) + frame;
            break;
        case EffectType::ICE:
            wilFile = "magic";
            index = 2000 + (effectValue * 100) + frame;
            break;
        case EffectType::LIGHTNING:
            wilFile = "magic";
            index = 3000 + (effectValue * 100) + frame;
            break;
        case EffectType::WIND:
            wilFile = "magic";
            index = 4000 + (effectValue * 100) + frame;
            break;
        case EffectType::HOLY:
            wilFile = "magic";
            index = 5000 + (effectValue * 100) + frame;
            break;
        case EffectType::DARK:
            wilFile = "magic";
            index = 6000 + (effectValue * 100) + frame;
            break;
        case EffectType::PHYSICAL:
            wilFile = "magic";
            index = 7000 + (effectValue * 100) + frame;
            break;
        case EffectType::HEAL:
            wilFile = "magic";
            index = 8000 + (effectValue * 100) + frame;
            break;
        case EffectType::BUFF:
            wilFile = "magic";
            index = 9000 + (effectValue * 100) + frame;
            break;
        case EffectType::DEBUFF:
            wilFile = "magic";
            index = 9500 + (effectValue * 100) + frame;
            break;
        case EffectType::SUMMON:
            wilFile = "magic";
            index = 10000 + (effectValue * 100) + frame;
            break;
        case EffectType::TELEPORT:
            wilFile = "magic";
            index = 11000 + (effectValue * 100) + frame;
            break;
        default:
            return nullptr;
    }

    // 从WIL文件获取表面
    SDL_Surface* surface = m_wilManager->GetSurface(wilFile, index);
    if (!surface) {
        std::cerr << "Failed to get surface for effect: " << wilFile << " " << index << std::endl;
        return nullptr;
    }

    // 创建纹理
    std::shared_ptr<Texture> texture = std::make_shared<Texture>(m_renderer);
    if (!texture->LoadFromSurface(surface)) {
        std::cerr << "Failed to create texture from surface for effect" << std::endl;
        return nullptr;
    }

    // 缓存纹理
    m_effectTextures[key] = texture;

    return texture;
}

bool EffectManager::PlaySkillEffect(std::shared_ptr<Skill> skill, int x, int y, int targetX, int targetY)
{
    if (!skill) {
        return false;
    }

    // 获取技能效果类型和值
    EffectType effectType = skill->GetEffectType();
    int effectValue = skill->GetEffectValue();

    // 根据效果类型选择特定的效果创建方法
    switch (effectType) {
        case EffectType::FIRE:
            return CreateFireEffect(x, y, targetX, targetY, effectValue);
        case EffectType::ICE:
            return CreateIceEffect(x, y, targetX, targetY, effectValue);
        case EffectType::LIGHTNING:
            return CreateLightningEffect(x, y, targetX, targetY, effectValue);
        case EffectType::HEAL:
            return CreateHealEffect(x, y, targetX, targetY, effectValue);
        case EffectType::BUFF:
            return CreateBuffEffect(x, y, targetX, targetY, effectValue);
        case EffectType::DEBUFF:
            return CreateDebuffEffect(x, y, targetX, targetY, effectValue);
        default:
            // 根据技能攻击范围设置默认效果
            switch (skill->GetAttackRange()) {
                case AttackRange::SINGLE:
                    // 单体目标效果
                    return AddEffect(x, y, targetX, targetY, effectType, effectValue, 1000);

                case AttackRange::AREA:
                    // 范围效果 - 在目标位置创建效果
                    return AddEffect(targetX, targetY, targetX, targetY, effectType, effectValue, 1500);

                case AttackRange::SCREEN:
                    // 全屏效果 - 在屏幕中心创建效果
                    return AddEffect(x, y, x, y, effectType, effectValue, 2000);

                default:
                    return false;
            }
    }
}

bool EffectManager::CreateFireEffect(int x, int y, int targetX, int targetY, int effectValue)
{
    // 创建火焰效果
    bool success = true;

    // 根据攻击范围创建不同的效果
    if (x == targetX && y == targetY) {
        // 原地施放 - 创建爆炸效果
        success &= AddEffect(x, y, x, y, EffectType::FIRE, effectValue, 1500);
    } else {
        // 向目标施放 - 创建飞行效果和爆炸效果
        success &= AddEffect(x, y, targetX, targetY, EffectType::FIRE, effectValue, 800);

        // 延迟800毫秒后在目标位置创建爆炸效果
        SDL_Delay(800);
        success &= AddEffect(targetX, targetY, targetX, targetY, EffectType::FIRE, effectValue + 1, 1000);
    }

    return success;
}

bool EffectManager::CreateIceEffect(int x, int y, int targetX, int targetY, int effectValue)
{
    // 创建冰冻效果
    bool success = true;

    // 根据攻击范围创建不同的效果
    if (x == targetX && y == targetY) {
        // 原地施放 - 创建冰冻区域效果
        success &= AddEffect(x, y, x, y, EffectType::ICE, effectValue, 2000);
    } else {
        // 向目标施放 - 创建冰锥效果
        success &= AddEffect(x, y, targetX, targetY, EffectType::ICE, effectValue, 1000);

        // 在目标位置创建冰冻效果
        success &= AddEffect(targetX, targetY, targetX, targetY, EffectType::ICE, effectValue + 1, 1500);
    }

    return success;
}

bool EffectManager::CreateLightningEffect(int x, int y, int targetX, int targetY, int effectValue)
{
    // 创建闪电效果
    bool success = true;

    // 闪电效果通常是直接在目标位置出现
    success &= AddEffect(targetX, targetY, targetX, targetY, EffectType::LIGHTNING, effectValue, 1200);

    // 如果是范围闪电，在周围也创建效果
    if (effectValue >= 2) {
        // 在目标周围创建额外的闪电效果
        for (int i = 0; i < 4; i++) {
            int offsetX = (i % 2 == 0) ? -1 : 1;
            int offsetY = (i < 2) ? -1 : 1;
            success &= AddEffect(targetX + offsetX, targetY + offsetY, targetX + offsetX, targetY + offsetY,
                               EffectType::LIGHTNING, effectValue - 1, 1000);
        }
    }

    return success;
}

bool EffectManager::CreateHealEffect(int x, int y, int targetX, int targetY, int effectValue)
{
    // 创建治疗效果
    bool success = true;

    // 治疗效果通常是在目标位置出现
    success &= AddEffect(targetX, targetY, targetX, targetY, EffectType::HEAL, effectValue, 1500);

    // 如果是高级治疗，添加额外的光环
    if (effectValue >= 2) {
        success &= AddEffect(targetX, targetY, targetX, targetY, EffectType::HOLY, effectValue - 1, 1200);
    }

    return success;
}

bool EffectManager::CreateBuffEffect(int x, int y, int targetX, int targetY, int effectValue)
{
    // 创建增益效果
    bool success = true;

    // 增益效果通常是在目标位置出现
    success &= AddEffect(targetX, targetY, targetX, targetY, EffectType::BUFF, effectValue, 1800);

    // 如果是高级增益，添加额外的光环
    if (effectValue >= 2) {
        // 在目标周围创建光环效果
        success &= AddEffect(targetX, targetY, targetX, targetY, EffectType::HOLY, effectValue - 1, 1500);
    }

    return success;
}

bool EffectManager::CreateDebuffEffect(int x, int y, int targetX, int targetY, int effectValue)
{
    // 创建减益效果
    bool success = true;

    // 减益效果通常是在目标位置出现
    success &= AddEffect(targetX, targetY, targetX, targetY, EffectType::DEBUFF, effectValue, 1800);

    // 如果是高级减益，添加额外的暗效果
    if (effectValue >= 2) {
        // 在目标周围创建暗影效果
        success &= AddEffect(targetX, targetY, targetX, targetY, EffectType::DARK, effectValue - 1, 1500);
    }

    return success;
}

