#pragma once

#include "Actor.h"
#include <vector>
#include <memory>
#include <unordered_map>
#include "../Skill/Skill.h"
#include "../Item/Inventory.h"
#include "../Data/PlayerClass.h"

/**
 * @class Player
 * @brief Represents the player character
 *
 * This class represents the player character, which is controlled by the user.
 * It extends the Actor class with player-specific functionality.
 */
class Player : public Actor {
private:
    PlayerClass m_class;                ///< Character class
    int m_sex;                          ///< Character sex/gender (0 = male, 1 = female)

    int m_level;                        ///< Character level
    int m_experience;                   ///< Experience points
    int m_maxExperience;                ///< Experience needed for next level

    int m_hp;                           ///< Current HP
    int m_maxHp;                        ///< Maximum HP
    int m_mp;                           ///< Current MP
    int m_maxMp;                        ///< Maximum MP

    int m_magicAttack;                  ///< Magic attack

    std::string m_guildName;            ///< Guild name
    std::string m_guildRank;            ///< Guild rank

    int m_health;                       ///< Current health
    int m_maxHealth;                    ///< Maximum health
    int m_mana;                         ///< Current mana
    int m_maxMana;                      ///< Maximum mana

    int m_strength;                     ///< Strength attribute
    int m_intelligence;                 ///< Intelligence attribute
    int m_dexterity;                    ///< Dexterity attribute

    int m_attackPower;                  ///< Attack power
    int m_magicPower;                   ///< Magic power
    int m_defense;                      ///< Defense
    int m_magicDefense;                 ///< Magic defense

    std::shared_ptr<Inventory> m_inventory;  ///< Player's inventory
    std::unordered_map<int, std::shared_ptr<Skill>> m_skills;  ///< Known skills
    std::vector<int> m_hotkeys;         ///< Skill hotkeys (stores skill IDs)

    int m_targetX;                      ///< Target X coordinate for skills
    int m_targetY;                      ///< Target Y coordinate for skills

    bool m_isLocalPlayer;               ///< Whether this is the local player

    /**
     * @brief Apply effects from an equipped item
     * @param item Item to apply effects from
     */
    void ApplyItemEffects(std::shared_ptr<Item> item);

    /**
     * @brief Remove effects from an unequipped item
     * @param item Item to remove effects from
     */
    void RemoveItemEffects(std::shared_ptr<Item> item);

public:
    /**
     * @brief Constructor
     * @param id Player ID
     * @param name Player name
     * @param playerClass Character class
     */
    Player(int id, const std::string& name, PlayerClass playerClass);

    /**
     * @brief Destructor
     */
    virtual ~Player();

    /**
     * @brief Update the player
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    virtual void Update(int deltaTime) override;

    /**
     * @brief Render the player
     */
    virtual void Render() override;

    /**
     * @brief Handle input
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    void HandleInput(int deltaTime);

    /**
     * @brief Level up the player
     * @return true if successful, false otherwise
     */
    bool LevelUp();

    /**
     * @brief Add experience points
     * @param exp Experience points to add
     * @return true if leveled up, false otherwise
     */
    bool AddExperience(int exp);

    /**
     * @brief Add an item to the inventory
     * @param item Item to add
     * @param slot Slot to add the item to (-1 for auto-assign)
     * @return true if successful, false otherwise
     */
    bool AddItem(std::shared_ptr<Item> item, int slot = -1);

    /**
     * @brief Remove an item from the inventory
     * @param slot Slot of the item to remove
     * @return Removed item, or nullptr if the slot is empty
     */
    std::shared_ptr<Item> RemoveItem(int slot);

    /**
     * @brief Get an item from the inventory
     * @param slot Slot of the item to get
     * @return Item at the specified slot, or nullptr if the slot is empty
     */
    std::shared_ptr<Item> GetItem(int slot) const;

    /**
     * @brief Equip an item
     * @param slot Inventory slot of the item to equip
     * @param equipSlot Equipment slot to equip the item to
     * @return true if successful, false otherwise
     */
    bool EquipItem(int slot, Inventory::EquipmentSlot equipSlot);

    /**
     * @brief Unequip an item
     * @param equipSlot Equipment slot to unequip
     * @return true if successful, false otherwise
     */
    bool UnequipItem(Inventory::EquipmentSlot equipSlot);

    /**
     * @brief Get the inventory
     * @return Player's inventory
     */
    std::shared_ptr<Inventory> GetInventory() const { return m_inventory; }

    /**
     * @brief Learn a skill
     * @param skillId Skill ID
     * @return true if successful, false otherwise
     */
    bool LearnSkill(int skillId);

    /**
     * @brief Use a skill
     * @param skillId Skill ID
     * @return true if successful, false otherwise
     */
    bool UseSkill(int skillId);

    /**
     * @brief Set target position for skills
     * @param x Target X coordinate
     * @param y Target Y coordinate
     */
    void SetSkillTarget(int x, int y);

    /**
     * @brief Get a skill by ID
     * @param skillId Skill ID
     * @return Skill pointer, or nullptr if not found
     */
    std::shared_ptr<Skill> GetSkill(int skillId) const;

    /**
     * @brief Get all skills
     * @return Map of skill ID to skill
     */
    const std::unordered_map<int, std::shared_ptr<Skill>>& GetSkills() const { return m_skills; }

    /**
     * @brief Clear all skills
     */
    void ClearSkills() { m_skills.clear(); }

    /**
     * @brief Add a skill to the player
     * @param skill Skill to add
     * @return true if successful, false otherwise
     */
    bool AddSkill(std::shared_ptr<Skill> skill);

    /**
     * @brief Remove a skill from the player
     * @param skillId Skill ID
     * @return true if successful, false otherwise
     */
    bool RemoveSkill(int skillId);

    /**
     * @brief Use a skill by hotkey
     * @param hotkeyIndex Hotkey index
     * @return true if successful, false otherwise
     */
    bool UseHotkey(int hotkeyIndex);

    /**
     * @brief Set a hotkey for a skill
     * @param hotkeyIndex Hotkey index
     * @param skillId Skill ID
     */
    void SetHotkey(int hotkeyIndex, int skillId);

    /**
     * @brief Get the skill ID assigned to a hotkey
     * @param hotkeyIndex Hotkey index
     * @return Skill ID, or 0 if no skill is assigned
     */
    int GetHotkey(int hotkeyIndex) const;

    /**
     * @brief Add training points to a skill
     * @param skillId Skill ID
     * @param points Training points to add
     * @return true if the skill leveled up, false otherwise
     */
    bool TrainSkill(int skillId, int points);

    /**
     * @brief Add experience to a skill
     * @param skillId Skill ID
     * @param experience Experience to add
     * @return true if the skill leveled up, false otherwise
     */
    bool AddSkillExperience(int skillId, int experience);

    /**
     * @brief Take damage
     * @param damage Damage amount
     * @return true if the player is still alive, false otherwise
     */
    virtual bool TakeDamage(int damage) override;

    /**
     * @brief Heal the player
     * @param amount Healing amount
     */
    void Heal(int amount);

    /**
     * @brief Restore mana
     * @param amount Mana amount
     */
    void RestoreMana(int amount);

    /**
     * @brief Set whether this is the local player
     * @param isLocal Local player flag
     */
    void SetLocalPlayer(bool isLocal);

    /**
     * @brief Get the character class
     * @return Character class
     */
    PlayerClass GetClass() const { return m_class; }

    /**
     * @brief Get the character job class (for compatibility with original code)
     * @return Character job class as an integer
     */
    int GetJobClass() const { return static_cast<int>(m_class); }

    /**
     * @brief Get the character job name
     * @return Character job name as a string
     */
    std::string GetJob() const {
        switch (m_class) {
            case PlayerClass::WARRIOR: return "Warrior";
            case PlayerClass::WIZARD: return "Wizard";
            case PlayerClass::TAOIST: return "Taoist";
            case PlayerClass::ASSASSIN: return "Assassin";
            default: return "Unknown";
        }
    }

    /**
     * @brief Get the character sex/gender (for compatibility with original code)
     * @return Character sex (0 = male, 1 = female)
     */
    int GetSex() const { return m_sex; }

    /**
     * @brief Get the character's current HP
     * @return Current HP
     */
    int GetHP() const { return m_hp; }

    /**
     * @brief Get the character's maximum HP
     * @return Maximum HP
     */
    int GetMaxHP() const { return m_maxHp; }

    /**
     * @brief Get the character's current MP
     * @return Current MP
     */
    int GetMP() const { return m_mp; }

    /**
     * @brief Get the character's maximum MP
     * @return Maximum MP
     */
    int GetMaxMP() const { return m_maxMp; }

    /**
     * @brief Get the character's attack power
     * @return Attack power
     */
    int GetAttack() const { return (m_attackMin + m_attackMax) / 2; }

    /**
     * @brief Get the character's magic attack power
     * @return Magic attack power
     */
    int GetMagicAttack() const { return m_magicAttack; }

    /**
     * @brief Get the character's magic defense
     * @return Magic defense
     */
    int GetMagicDefense() const { return m_magicDefense; }

    /**
     * @brief Get the character's accuracy
     * @return Accuracy
     */
    int GetAccuracy() const { return m_accuracy; }

    /**
     * @brief Get the character's defense
     * @return Defense
     */
    int GetDefense() const { return m_defense; }

    /**
     * @brief Get the character's agility
     * @return Agility
     */
    int GetAgility() const { return m_agility; }

    /**
     * @brief Get the character's experience points
     * @return Experience points
     */
    int GetExp() const { return m_experience; }

    /**
     * @brief Get the character's maximum experience points needed for next level
     * @return Maximum experience points
     */
    int GetMaxExp() const { return m_maxExperience; }

    /**
     * @brief Get the character's guild name
     * @return Guild name
     */
    std::string GetGuildName() const { return m_guildName; }

    /**
     * @brief Get the character's guild rank
     * @return Guild rank
     */
    std::string GetGuildRank() const { return m_guildRank; }

    /**
     * @brief Get the level
     * @return Character level
     */
    int GetLevel() const { return m_level; }

    /**
     * @brief Get the experience points
     * @return Experience points
     */
    int GetExperience() const { return m_experience; }

    /**
     * @brief Get the maximum experience points
     * @return Maximum experience points
     */
    int GetMaxExperience() const { return m_maxExperience; }

    /**
     * @brief Get the current health
     * @return Current health
     */
    int GetHealth() const { return m_health; }

    /**
     * @brief Get the maximum health
     * @return Maximum health
     */
    int GetMaxHealth() const { return m_maxHealth; }

    /**
     * @brief Get the current mana
     * @return Current mana
     */
    int GetMana() const { return m_mana; }

    /**
     * @brief Get the maximum mana
     * @return Maximum mana
     */
    int GetMaxMana() const { return m_maxMana; }

    /**
     * @brief Check if this is the local player
     * @return true if local player, false otherwise
     */
    bool IsLocalPlayer() const { return m_isLocalPlayer; }

    /**
     * @brief Set the player's level
     * @param level New level
     */
    void SetLevel(int level) { m_level = level; }

    /**
     * @brief Set the player's experience
     * @param experience New experience
     */
    void SetExperience(int experience) { m_experience = experience; }

    /**
     * @brief Set the player's maximum experience
     * @param maxExperience New maximum experience
     */
    void SetMaxExperience(int maxExperience) { m_maxExperience = maxExperience; }

    /**
     * @brief Set the player's health
     * @param health New health
     */
    void SetHealth(int health) override { m_health = health; }

    /**
     * @brief Set the player's maximum health
     * @param maxHealth New maximum health
     */
    void SetMaxHealth(int maxHealth) override { m_maxHealth = maxHealth; }

    /**
     * @brief Set the player's mana
     * @param mana New mana
     */
    void SetMana(int mana) { m_mana = mana; }

    /**
     * @brief Set the player's maximum mana
     * @param maxMana New maximum mana
     */
    void SetMaxMana(int maxMana) { m_maxMana = maxMana; }

    /**
     * @brief Set the player's appearance
     * @param appearance Appearance ID
     */
    void SetAppearance(int appearance) override;

    /**
     * @brief Set the player's status
     * @param status Status flags
     */
    void SetStatus(int status) override;

    /**
     * @brief Set the player's name color
     * @param color Color value
     */
    void SetNameColor(int color) override;

    /**
     * @brief Set the player's user state
     * @param state User state flags
     */
    void SetUserState(int state) { SetStatus(state); } // Alias for SetStatus

    /**
     * @brief Get the player's strength
     * @return Strength value
     */
    int GetStrength() const { return m_strength; }

    /**
     * @brief Set the player's strength
     * @param strength New strength value
     */
    void SetStrength(int strength) { m_strength = strength; }

    /**
     * @brief Get the player's dexterity
     * @return Dexterity value
     */
    int GetDexterity() const { return m_dexterity; }

    /**
     * @brief Set the player's dexterity
     * @param dexterity New dexterity value
     */
    void SetDexterity(int dexterity) { m_dexterity = dexterity; }

    /**
     * @brief Get the player's vitality
     * @return Vitality value
     */
    int GetVitality() const { return m_vitality; }

    /**
     * @brief Set the player's vitality
     * @param vitality New vitality value
     */
    void SetVitality(int vitality) { m_vitality = vitality; }

    /**
     * @brief Get the player's energy
     * @return Energy value
     */
    int GetEnergy() const { return m_energy; }

    /**
     * @brief Set the player's energy
     * @param energy New energy value
     */
    void SetEnergy(int energy) { m_energy = energy; }

    /**
     * @brief Get the player's magic
     * @return Magic value
     */
    int GetMagic() const { return m_magic; }

    /**
     * @brief Set the player's magic
     * @param magic New magic value
     */
    void SetMagic(int magic) { m_magic = magic; }

    /**
     * @brief Set the player's minimum attack
     * @param attackMin New minimum attack value
     */
    void SetAttackMin(int attackMin) { m_attackMin = attackMin; }

    /**
     * @brief Set the player's maximum attack
     * @param attackMax New maximum attack value
     */
    void SetAttackMax(int attackMax) { m_attackMax = attackMax; }

    /**
     * @brief Set the player's accuracy
     * @param accuracy New accuracy value
     */
    void SetAccuracy(int accuracy) { m_accuracy = accuracy; }

    /**
     * @brief Set the player's agility
     * @param agility New agility value
     */
    void SetAgility(int agility) { m_agility = agility; }

    /**
     * @brief Set the player's defense
     * @param defense New defense value
     */
    void SetDefense(int defense) { m_defense = defense; }

    /**
     * @brief Set the player's AC (Armor Class)
     * @param ac New AC value
     */
    void SetAC(int ac) { m_ac = ac; }

    /**
     * @brief Get the player's AC (Armor Class)
     * @return AC value
     */
    int GetAC() const { return m_ac; }

    /**
     * @brief Get the player's minimum attack
     * @return Minimum attack value
     */
    int GetAttackMin() const { return m_attackMin; }

    /**
     * @brief Get the player's maximum attack
     * @return Maximum attack value
     */
    int GetAttackMax() const { return m_attackMax; }

    /**
     * @brief Set the player's MAC (Magic Armor Class)
     * @param mac New MAC value
     */
    void SetMAC(int mac) { m_mac = mac; }

    /**
     * @brief Set the player's DC (Damage Class)
     * @param dc New DC value
     */
    void SetDC(int dc) { m_dc = dc; }

    /**
     * @brief Set the player's MC (Magic Class)
     * @param mc New MC value
     */
    void SetMC(int mc) { m_mc = mc; }

    /**
     * @brief Set the player's SC (Soul Class)
     * @param sc New SC value
     */
    void SetSC(int sc) { m_sc = sc; }

    /**
     * @brief Set the player's hand weight
     * @param weight New hand weight value
     */
    void SetHandWeight(int weight) { m_handWeight = weight; }

    /**
     * @brief Set the player's wear weight
     * @param weight New wear weight value
     */
    void SetWearWeight(int weight) { m_wearWeight = weight; }

    /**
     * @brief Set the player's bag weight
     * @param weight New bag weight value
     */
    void SetBagWeight(int weight) { m_bagWeight = weight; }

    /**
     * @brief Set the player's face
     * @param face New face value
     */
    void SetFace(int face) { m_face = face; }

private:
    // Additional attributes
    int m_vitality = 0;           ///< Vitality attribute
    int m_energy = 0;             ///< Energy attribute
    int m_magic = 0;              ///< Magic attribute
    int m_attackMin = 0;          ///< Minimum attack
    int m_attackMax = 0;          ///< Maximum attack
    int m_accuracy = 0;           ///< Accuracy
    int m_agility = 0;            ///< Agility
    int m_ac = 0;                 ///< Armor Class
    int m_mac = 0;                ///< Magic Armor Class
    int m_dc = 0;                 ///< Damage Class
    int m_mc = 0;                 ///< Magic Class
    int m_sc = 0;                 ///< Soul Class
    int m_handWeight = 0;         ///< Hand weight
    int m_wearWeight = 0;         ///< Wear weight
    int m_bagWeight = 0;          ///< Bag weight
    int m_face = 0;               ///< Face
};
