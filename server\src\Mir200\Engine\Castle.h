#pragma once

// Mir200 Castle - Castle management system
// Based on delphi/EM2Engine/Castle.pas - Following original project structure
// Phase 1 Implementation - Basic placeholder for M2Server integration

#include "Common/M2Share.h"
#include <string>

// Castle class - Castle management
class Castle {
private:
    std::string m_castle_name;
    bool m_active;
    bool m_initialized;
    
public:
    Castle(const std::string& castle_name = "Sabuk");
    ~Castle();
    
    // Core lifecycle
    bool Initialize();
    void Finalize();
    
    // Processing
    void ProcessCastle();
    
    // State management
    bool IsActive() const { return m_active; }
    bool IsInitialized() const { return m_initialized; }
    
    // Castle management
    const std::string& GetCastleName() const { return m_castle_name; }
    
    // Data management
    void SaveCastleData();
};
