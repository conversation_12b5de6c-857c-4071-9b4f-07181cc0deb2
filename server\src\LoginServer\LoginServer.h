#ifndef LOGINSERVER_H
#define LOGINSERVER_H

#include <string>
#include <memory>
#include <vector>
#include <map>
#include <chrono>
#include <mutex>
#include <thread>
#include <atomic>
#include "../Common/Types.h"
#include "../Protocol/NetworkManager.h"
#include "../Protocol/PacketTypes.h"
#include "../Protocol/MessageConverter.h"
#include "AccountTypes.h"

namespace MirServer {

// Forward declarations
class AccountDB;
class GateConnection;
class UserSession;
class LoginServerConfig;
class MsgServerManager;
class MonitorServer;

// Login server statistics
struct LoginServerStats {
    std::atomic<uint32_t> totalLogins{0};
    std::atomic<uint32_t> failedLogins{0};
    std::atomic<uint32_t> onlineUsers{0};
    std::atomic<uint32_t> newAccounts{0};
    std::atomic<uint32_t> passwordChanges{0};
};

// User session information
class UserSession {
public:
    std::string sAccount;                       // Account name
    std::string sUserIPaddr;                    // User IP address
    std::string sGateIPaddr;                    // Gate IP address
    std::string sSockIndex;                     // Socket index
    int32_t nSessionID = 0;                     // Session ID
    int32_t nVersionDate = 0;                   // Client version
    bool boCertificationOK = false;             // Protocol verification
    bool boPayCost = false;                     // Is paid account
    bool boSelServer = false;                   // Has selected server
    std::chrono::steady_clock::time_point lastActivity;
    std::string sReceiveMsg;                    // Receive buffer
    std::weak_ptr<GateConnection> gate;        // Associated gate connection
    
    UserSession(const std::string& sockIndex) : sSockIndex(sockIndex) {
        lastActivity = std::chrono::steady_clock::now();
    }
};

// Gate connection information
class GateConnection {
public:
    std::shared_ptr<Network::ClientConnection> connection;
    std::string sIPaddr;                        // Gate IP address
    std::string sReceiveMsg;                    // Receive buffer
    std::map<std::string, std::shared_ptr<UserSession>> userList;  // Connected users
    std::chrono::steady_clock::time_point lastKeepAlive;
    
    GateConnection(std::shared_ptr<Network::ClientConnection> conn);
    void AddUser(const std::string& sockIndex, std::shared_ptr<UserSession> user);
    void RemoveUser(const std::string& sockIndex);
    std::shared_ptr<UserSession> GetUser(const std::string& sockIndex);
};

// Server route information
struct ServerRoute {
    std::string sServerName;
    std::string sTitle;
    std::string sRemoteAddr;
    std::string sPublicAddr;
    int32_t nSelIdx = 0;
    struct GateInfo {
        std::string sIPaddr;
        int32_t nPort = 0;
        bool boEnable = false;
    };
    std::vector<GateInfo> gates;
};

// Account cost information
struct AccountCost {
    int32_t nIDDay = 0;
    int32_t nIDHour = 0;
    int32_t nIPDay = 0;
    int32_t nIPHour = 0;
};

// Login server configuration
class LoginServerConfig {
public:
    // Database configuration
    std::string sDBServer = "127.0.0.1";
    int32_t nDBSPort = 16300;
    std::string sIdDir = "./DB/";
    
    // Network configuration
    std::string sGateAddr = "0.0.0.0";
    int32_t nGatePort = 5500;
    std::string sServerAddr = "0.0.0.0";
    int32_t nServerPort = 5600;
    std::string sMonAddr = "0.0.0.0";
    int32_t nMonPort = 3000;
    
    // Feature toggles
    bool boEnableMakingID = true;
    bool boEnableGetbackPassword = true;
    bool boAutoClearID = true;
    uint32_t dwAutoClearTime = 1000;
    bool boUnLockAccount = false;
    uint32_t dwUnLockAccountTime = 10;
    bool boDynamicIPMode = false;
    bool boShowDetailMsg = false;
    
    // Route configuration
    std::vector<ServerRoute> routes;
    std::vector<std::string> serverNameList;
    
    // Account cost configuration
    std::map<std::string, AccountCost> accountCostList;
    std::map<std::string, AccountCost> ipCostList;
    
    bool LoadConfig(const std::string& filename);
    bool LoadRouteTable(const std::string& filename);
    bool LoadAccountCost(const std::string& filename);
};

// Main LoginServer class
class LoginServer : public Network::PacketHandler {
public:
    LoginServer();
    ~LoginServer();
    
    // Server control
    bool Initialize(const std::string& configFile);
    bool Start();
    void Stop();
    void Run();
    bool IsRunning() const { return m_running; }
    
    // PacketHandler interface
    void OnClientConnected(std::shared_ptr<Network::ClientConnection> connection) override;
    void OnClientDisconnected(std::shared_ptr<Network::ClientConnection> connection) override;
    void HandlePacket(std::shared_ptr<Network::ClientConnection> connection, 
                         const Protocol::PacketHeader& header, 
                         const uint8_t* data, 
                         size_t dataSize) override;
    
    // Statistics
    const LoginServerStats& GetStats() const { return m_stats; }
    
    // Get MsgServerManager for external access
    MsgServerManager* GetMsgServerManager() { return m_msgServerManager.get(); }
    
private:
    // Configuration and state
    LoginServerConfig m_config;
    std::atomic<bool> m_running{false};
    std::unique_ptr<Network::NetworkManager> m_network;
    std::unique_ptr<AccountDB> m_accountDB;
    std::unique_ptr<MsgServerManager> m_msgServerManager;
    std::unique_ptr<MonitorServer> m_monitorServer;
    LoginServerStats m_stats;
    
    // Connection management
    std::mutex m_gateMutex;
    std::map<std::shared_ptr<Network::ClientConnection>, std::shared_ptr<GateConnection>> m_gateList;
    
    // Session management
    std::mutex m_sessionMutex;
    std::map<int32_t, std::shared_ptr<UserSession>> m_sessionList;
    std::atomic<int32_t> m_sessionIDCounter{1};
    
    // Processing threads
    std::thread m_processThread;
    std::thread m_cleanupThread;
    
    // Gate message processing
    void ProcessGateMessage(std::shared_ptr<GateConnection> gate, const std::string& msg);
    void DecodeGateData(std::shared_ptr<GateConnection> gate);
    void DecodeUserData(std::shared_ptr<UserSession> user);
    
    // User message handlers
    void ProcessUserMessage(std::shared_ptr<UserSession> user, const std::string& msg);
    void HandleProtocol(std::shared_ptr<UserSession> user, int32_t version);
    void HandleLogin(std::shared_ptr<UserSession> user, const std::string& data);
    void HandleSelectServer(std::shared_ptr<UserSession> user, const std::string& data);
    void HandleNewAccount(std::shared_ptr<UserSession> user, const std::string& data);
    void HandleChangePassword(std::shared_ptr<UserSession> user, const std::string& data);
    void HandleUpdateUserInfo(std::shared_ptr<UserSession> user, const std::string& data);
    void HandleGetBackPassword(std::shared_ptr<UserSession> user, const std::string& data);
    
    // Gate communication
    void SendGateMsg(std::shared_ptr<GateConnection> gate, const std::string& sockIndex, const std::string& msg);
    void SendGateKickMsg(std::shared_ptr<GateConnection> gate, const std::string& sockIndex);
    void SendGateAddBlockList(std::shared_ptr<GateConnection> gate, const std::string& sockIndex);
    void SendGateAddTempBlockList(std::shared_ptr<GateConnection> gate, const std::string& sockIndex);
    void SendKeepAlivePacket(std::shared_ptr<GateConnection> gate);
    
    // Session management
    int32_t GetNewSessionID();
    void SessionAdd(const std::string& account, const std::string& ipaddr, int32_t sessionID, bool payCost);
    void SessionDel(int32_t sessionID);
    void SessionKick(const std::string& account);
    void SessionUpdate(int32_t sessionID, const std::string& serverName, bool payCost);
    bool IsLogin(const std::string& account);
    bool IsLogin(int32_t sessionID);
    
    // Utility functions
    std::string GetServerListInfo();
    void GetSelGateInfo(const std::string& serverName, const std::string& ipaddr, 
                       std::string& selGateIP, int32_t& selGatePort);
    bool KickUser(std::shared_ptr<UserSession> user, int32_t kickType);
    void CloseUser(const std::string& account, int32_t sessionID);
    
    // Background tasks
    void ProcessLoop();
    void CleanupLoop();
    void ProcessGates();
    void CleanupSessions();
    void SaveCountLog();
    
    // Logging
    void WriteLogMsg(const std::string& type, const TUserEntry& userEntry, const TUserEntryAdd& userAddEntry);
    void SaveContLogMsg(const std::string& logMsg);
    
    // OnPacketReceived is a wrapper for compatibility
    void OnPacketReceived(std::shared_ptr<Network::ClientConnection> connection, 
                         const Network::GamePacket& packet);
};

} // namespace MirServer

#endif // LOGINSERVER_H 