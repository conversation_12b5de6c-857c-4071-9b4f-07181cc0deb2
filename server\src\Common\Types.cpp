#include "Types.h"
#include <random>
#include <ctime>

namespace MirServer {

// 获取当前时间戳
DWORD GetCurrentTime() {
#ifdef _WIN32
    return ::GetTickCount();  // 使用全局命名空间的GetTickCount避免递归
#else
    return static_cast<DWORD>(time(nullptr) * 1000);
#endif
}

// 生成随机数
int GenerateRandom(int min, int max) {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(min, max);
    return dis(gen);
}

// 获取方向的相对坐标偏移
Point GetDirectionOffset(DirectionType direction) {
    switch (direction) {
        case DirectionType::UP:         return Point(0, -1);
        case DirectionType::UP_RIGHT:   return Point(1, -1);
        case DirectionType::RIGHT:      return Point(1, 0);
        case DirectionType::DOWN_RIGHT: return Point(1, 1);
        case DirectionType::DOWN:       return Point(0, 1);
        case DirectionType::DOWN_LEFT:  return Point(-1, 1);
        case DirectionType::LEFT:       return Point(-1, 0);
        case DirectionType::UP_LEFT:    return Point(-1, -1);
        default:                        return Point(0, 0);
    }
}

// 计算两点之间的方向
DirectionType GetDirectionFromPoints(const Point& from, const Point& to) {
    int dx = to.x - from.x;
    int dy = to.y - from.y;

    if (dx == 0 && dy == 0) return DirectionType::DOWN;

    if (abs(dx) >= abs(dy)) {
        if (dx > 0) {
            if (dy > 0) return DirectionType::DOWN_RIGHT;
            else if (dy < 0) return DirectionType::UP_RIGHT;
            else return DirectionType::RIGHT;
        } else {
            if (dy > 0) return DirectionType::DOWN_LEFT;
            else if (dy < 0) return DirectionType::UP_LEFT;
            else return DirectionType::LEFT;
        }
    } else {
        if (dy > 0) {
            if (dx > 0) return DirectionType::DOWN_RIGHT;
            else if (dx < 0) return DirectionType::DOWN_LEFT;
            else return DirectionType::DOWN;
        } else {
            if (dx > 0) return DirectionType::UP_RIGHT;
            else if (dx < 0) return DirectionType::UP_LEFT;
            else return DirectionType::UP;
        }
    }
}

// 计算两点之间的距离
double GetDistance(const Point& p1, const Point& p2) {
    double dx = static_cast<double>(p2.x - p1.x);
    double dy = static_cast<double>(p2.y - p1.y);
    return sqrt(dx * dx + dy * dy);
}

// 检查点是否在范围内
bool IsInRange(const Point& center, const Point& target, int range) {
    int dx = abs(target.x - center.x);
    int dy = abs(target.y - center.y);
    return dx <= range && dy <= range;
}

// 初始化角色基础属性
void InitializeDefaultAbility(Ability& ability, JobType job, BYTE level) {
    ability.Level = level;

    // 根据职业设置基础属性
    switch (job) {
        case JobType::WARRIOR: // 战士
            ability.MaxHP = 20 + level * 2;
            ability.MaxMP = 10 + level;
            ability.DC.min = 2 + level / 5;
            ability.DC.max = ability.DC.min + 5;
            ability.AC.min = 1 + level / 10;
            ability.AC.max = ability.AC.min + 3;
            break;

        case JobType::WIZARD: // 法师
            ability.MaxHP = 15 + level;
            ability.MaxMP = 20 + level * 3;
            ability.MC.min = 2 + level / 3;
            ability.MC.max = ability.MC.min + 4;
            ability.AC.min = level / 15;
            ability.AC.max = ability.AC.min + 2;
            break;

        case JobType::TAOIST: // 道士
            ability.MaxHP = 18 + level * 1.5;
            ability.MaxMP = 15 + level * 2;
            ability.SC.min = 2 + level / 4;
            ability.SC.max = ability.SC.min + 3;
            ability.AC.min = 1 + level / 12;
            ability.AC.max = ability.AC.min + 2;
            break;
    }

    // 设置当前生命值和魔法值
    ability.HP = ability.MaxHP;
    ability.MP = ability.MaxMP;

    // 设置负重
    ability.MaxWeight = 30 + level * 2;
    ability.MaxWearWeight = 10 + level;
    ability.MaxHandWeight = 5 + level / 2;

    // 设置经验值
    if (level > 1) {
        ability.Exp = 0;
        ability.MaxExp = GetLevelUpExp(level);
    } else {
        ability.Exp = 0;
        ability.MaxExp = 100;
    }
}

// 计算升级所需经验值
DWORD GetLevelUpExp(BYTE level) {
    if (level >= MAXLEVEL) return 0;

    // 使用传奇经典的经验值计算公式
    DWORD baseExp = 100;
    for (int i = 1; i < level; i++) {
        baseExp = static_cast<DWORD>(baseExp * 1.2 + i * 10);
    }

    return baseExp;
}

// 验证角色名称
bool IsValidCharacterName(const std::string& name) {
    if (name.empty() || name.length() > 14) return false;

    // 检查字符是否合法（只允许字母、数字和中文）
    for (char c : name) {
        if (!((c >= 'a' && c <= 'z') ||
              (c >= 'A' && c <= 'Z') ||
              (c >= '0' && c <= '9') ||
              (static_cast<unsigned char>(c) >= 0x80))) { // 中文字符
            return false;
        }
    }

    return true;
}

// 验证账号名称
bool IsValidAccountName(const std::string& account) {
    if (account.empty() || account.length() > 20) return false;

    // 只允许字母和数字
    for (char c : account) {
        if (!((c >= 'a' && c <= 'z') ||
              (c >= 'A' && c <= 'Z') ||
              (c >= '0' && c <= '9'))) {
            return false;
        }
    }

    return true;
}

} // namespace MirServer