#ifndef EXCEPTION_HANDLER_H
#define EXCEPTION_HANDLER_H

#include <string>
#include <exception>
#include <functional>
#include "Logger.h"

/**
 * @brief Exception handler class
 * 
 * This class provides methods for handling exceptions and logging them.
 */
class ExceptionHandler {
public:
    /**
     * @brief Execute a function and catch any exceptions
     * 
     * @tparam Func Function type
     * @tparam Args Argument types
     * @param func The function to execute
     * @param args The arguments to pass to the function
     * @return true if the function executed without exceptions
     * @return false if an exception was caught
     */
    template<typename Func, typename... Args>
    static bool TryCatch(Func&& func, Args&&... args) {
        try {
            std::invoke(std::forward<Func>(func), std::forward<Args>(args)...);
            return true;
        } catch (const std::exception& e) {
            HandleException(e);
            return false;
        } catch (...) {
            HandleUnknownException();
            return false;
        }
    }

    /**
     * @brief Execute a function and catch any exceptions, with return value
     * 
     * @tparam RetType Return type of the function
     * @tparam Func Function type
     * @tparam Args Argument types
     * @param defaultValue The default value to return if an exception is caught
     * @param func The function to execute
     * @param args The arguments to pass to the function
     * @return RetType The return value of the function or the default value
     */
    template<typename RetType, typename Func, typename... Args>
    static RetType TryCatchWithReturn(RetType defaultValue, Func&& func, Args&&... args) {
        try {
            return std::invoke(std::forward<Func>(func), std::forward<Args>(args)...);
        } catch (const std::exception& e) {
            HandleException(e);
            return defaultValue;
        } catch (...) {
            HandleUnknownException();
            return defaultValue;
        }
    }

private:
    /**
     * @brief Handle a standard exception
     * 
     * @param e The exception to handle
     */
    static void HandleException(const std::exception& e) {
        Logger::GetInstance().LogException(e.what(), __FILE__, __LINE__);
    }

    /**
     * @brief Handle an unknown exception
     */
    static void HandleUnknownException() {
        Logger::GetInstance().LogException("Unknown exception", __FILE__, __LINE__);
    }
};

// Convenience macros for exception handling
#define TRY_CATCH(func, ...) ExceptionHandler::TryCatch(func, ##__VA_ARGS__)
#define TRY_CATCH_RETURN(defaultValue, func, ...) ExceptionHandler::TryCatchWithReturn(defaultValue, func, ##__VA_ARGS__)

#endif // EXCEPTION_HANDLER_H
