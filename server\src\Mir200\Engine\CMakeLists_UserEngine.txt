# CMakeLists.txt for UserEngine standalone compilation
cmake_minimum_required(VERSION 3.16)
project(UserEngine_Test)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)

# Define source files
set(USERENGINE_SOURCES
    UserEngine.cpp
    ../Common/M2Share.cpp
)

# Define header files
set(USERENGINE_HEADERS
    UserEngine.h
    ../Common/M2Share.h
    ../Common/Types.h
    ../Common/Protocol.h
)

# Create UserEngine library
add_library(UserEngine_Lib STATIC ${USERENGINE_SOURCES} ${USERENGINE_HEADERS})

# Create standalone test executable
add_executable(UserEngine_Test test_UserEngine_standalone.cpp)
target_link_libraries(UserEngine_Test UserEngine_Lib)

# Compiler-specific options
if(MSVC)
    target_compile_options(UserEngine_Lib PRIVATE /W4)
    target_compile_options(UserEngine_Test PRIVATE /W4)
else()
    target_compile_options(UserEngine_Lib PRIVATE -Wall -Wextra -Wpedantic)
    target_compile_options(UserEngine_Test PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Add definitions
target_compile_definitions(UserEngine_Lib PRIVATE 
    -DUNICODE 
    -D_UNICODE
    -DWIN32_LEAN_AND_MEAN
)

target_compile_definitions(UserEngine_Test PRIVATE 
    -DUNICODE 
    -D_UNICODE
    -DWIN32_LEAN_AND_MEAN
)

# Set output directory
set_target_properties(UserEngine_Test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/bin
)

# Enable testing
enable_testing()
add_test(NAME UserEngine_BasicTest COMMAND UserEngine_Test)
