# SelGateServer CMake配置文件

set(TARGET_NAME SelGateServer)

# 源文件
set(SOURCES
    main.cpp
    SelGateServer.cpp
)

# 头文件
set(HEADERS
    SelGateServer.h
)

# 创建可执行文件
add_executable(${TARGET_NAME} ${SOURCES} ${HEADERS})

# 设置C++标准
target_compile_features(${TARGET_NAME} PRIVATE cxx_std_17)

# 包含目录
target_include_directories(${TARGET_NAME} PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/src/Common
    ${CMAKE_SOURCE_DIR}/src/Protocol
)

# 链接库
target_link_libraries(${TARGET_NAME} PRIVATE
    Common
    Protocol
)

# Windows特定设置
if(WIN32)
    target_link_libraries(${TARGET_NAME} PRIVATE
        ws2_32
        wsock32
    )
    
    # 设置Windows控制台应用程序
    set_target_properties(${TARGET_NAME} PROPERTIES
        WIN32_EXECUTABLE FALSE
    )
endif()

# 编译选项
if(MSVC)
    target_compile_options(${TARGET_NAME} PRIVATE
        /W4
        /WX-
        /permissive-
        /Zc:__cplusplus
    )
    
    # 禁用特定警告
    target_compile_options(${TARGET_NAME} PRIVATE
        /wd4996  # 禁用不安全函数警告
    )
else()
    target_compile_options(${TARGET_NAME} PRIVATE
        -Wall
        -Wextra
        -Wpedantic
        -Wno-unused-parameter
    )
    
    # Debug模式下的额外选项
    target_compile_options(${TARGET_NAME} PRIVATE
        $<$<CONFIG:Debug>:-g -O0>
        $<$<CONFIG:Release>:-O3>
    )
endif()

# 预编译宏定义
target_compile_definitions(${TARGET_NAME} PRIVATE
    $<$<CONFIG:Debug>:_DEBUG>
    $<$<CONFIG:Release>:NDEBUG>
    UNICODE
    _UNICODE
)

# 设置输出目录
set_target_properties(${TARGET_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/bin/Debug
    RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/bin/Release
)

# 安装规则
install(TARGETS ${TARGET_NAME}
    RUNTIME DESTINATION bin
)

# 安装配置文件模板
install(FILES
    ${CMAKE_CURRENT_SOURCE_DIR}/SelGate.ini.example
    DESTINATION bin
    OPTIONAL
)

# 创建配置文件模板（如果不存在）
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/SelGate.ini.in
    ${CMAKE_BINARY_DIR}/bin/SelGate.ini.example
    @ONLY
    NEWLINE_STYLE LF
)

message(STATUS "Configured SelGateServer") 