#include "NewAccountDialog.h"
#include "ResourcePaths.h"
#include "UIConstants.h"
#include <iostream>

NewAccountDialog::NewAccountDialog(int x, int y, int width, int height, const std::string& title, std::shared_ptr<WILManager> wilManager)
    : Dialog(x, y, width, height, title)
    , m_wilManager(wilManager)
    , m_dialogManager(std::make_shared<DialogManager>(nullptr))
{
    // Set WIL manager and resource file for the dialog
    SetWILManager(wilManager);
    SetResourceFile(ResourcePaths::MAIN);
    SetBackgroundImageIndex(63); // Image index 63 from original Delphi project
}

NewAccountDialog::NewAccountDialog(int x, int y, const std::string& title, std::shared_ptr<WILManager> wilManager)
    : Dialog(x, y, title)
    , m_wil<PERSON>anager(wilManager)
    , m_dialogManager(std::make_shared<DialogManager>(nullptr))
{
    // Set WIL manager and resource file for the dialog
    SetWILManager(wilManager);
    SetResourceFile(ResourcePaths::MAIN);
    SetBackgroundImageIndex(63); // Image index 63 from original Delphi project
}

NewAccountDialog::~NewAccountDialog()
{
}

void NewAccountDialog::Initialize(SDL_Renderer* renderer)
{
    Dialog::Initialize(renderer);

    // Initialize dialog manager with renderer
    m_dialogManager = std::make_shared<DialogManager>(renderer);

    // Load font
    TTF_Font* font = TTF_OpenFont("assets/data/font.ttf", 16);
    if (!font) {
        std::cerr << "Failed to load font: " << TTF_GetError() << std::endl;
        return;
    }

    // Calculate positions based on the original Delphi project
    // In the original project, input fields are positioned relative to the dialog
    // The dialog is centered on screen
    int labelX = 20;
    int inputX = 150; // Relative to dialog
    int startY = 100; // Relative to dialog - adjusted to match original
    int lineHeight = 30;
    int inputWidth = 137; // 调整为宽度137像素，匹配原始Delphi项目
    int inputHeight = 18; // 调整为高度18像素，匹配原始Delphi项目

    // // Create title label
    // m_titleLabel = std::make_shared<Label>(m_width / 2 - 100, 20, 200, 30, "Create New Account");
    // m_titleLabel->SetFont(font);
    // m_titleLabel->SetTextColor({255, 255, 255, 255});
    // m_titleLabel->SetAlignment(TextAlignment::CENTER);
    // m_titleLabel->SetWILManager(m_wilManager);
    // AddChild(m_titleLabel);

    // // Create account label and input
    // m_accountLabel = std::make_shared<Label>(labelX, startY, 100, 30, "Account:");
    // m_accountLabel->SetFont(font);
    // m_accountLabel->SetTextColor({255, 255, 255, 255});
    // m_accountLabel->SetWILManager(m_wilManager);
    // AddChild(m_accountLabel);

    // Account input field - positioned relative to dialog
    // In the original project, account input field is positioned at the top of the form
    // 确保输入框的宽度和高度正确设置
    m_accountInput = std::make_shared<TextInput>(160, 100, inputWidth, inputHeight);
    // m_accountInput->SetFont(font);
    m_accountInput->SetTextColor({255, 255, 255, 255});
    m_accountInput->SetBackgroundColor({0, 0, 0, 255});
    m_accountInput->SetMaxLength(20);
    m_accountInput->SetWILManager(m_wilManager);
    m_accountInput->SetResourceFile(ResourcePaths::MAIN);
    AddChild(m_accountInput, true);

    // // Create password label and input
    // m_passwordLabel = std::make_shared<Label>(labelX, startY + lineHeight, 100, 30, "Password:");
    // m_passwordLabel->SetFont(font);
    // m_passwordLabel->SetTextColor({255, 255, 255, 255});
    // m_passwordLabel->SetWILManager(m_wilManager);
    // AddChild(m_passwordLabel);

    // Password input field - positioned relative to dialog
    // In the original project, password input field is positioned below account input
    // 确保输入框的宽度和高度正确设置
    m_passwordInput = std::make_shared<TextInput>(160, 130, inputWidth, inputHeight);
    // m_passwordInput->SetFont(font);
    m_passwordInput->SetTextColor({255, 255, 255, 255});
    m_passwordInput->SetBackgroundColor({0, 0, 0, 255});
    m_passwordInput->SetMaxLength(20);
    m_passwordInput->SetPasswordMode(true);
    m_passwordInput->SetWILManager(m_wilManager);
    m_passwordInput->SetResourceFile(ResourcePaths::MAIN);
    AddChild(m_passwordInput, true);

    // // Create confirm password label and input
    // m_confirmLabel = std::make_shared<Label>(labelX, startY + lineHeight * 2, 100, 30, "Confirm:");
    // m_confirmLabel->SetFont(font);
    // m_confirmLabel->SetTextColor({255, 255, 255, 255});
    // m_confirmLabel->SetWILManager(m_wilManager);
    // AddChild(m_confirmLabel);

    // Confirm password input field - positioned relative to dialog
    // In the original project, confirm password input field is positioned below password input
    // 确保输入框的宽度和高度正确设置
    m_confirmInput = std::make_shared<TextInput>(160, 160, inputWidth, inputHeight);
    // m_confirmInput->SetFont(font);
    m_confirmInput->SetTextColor({255, 255, 255, 255});
    m_confirmInput->SetBackgroundColor({0, 0, 0, 255});
    m_confirmInput->SetMaxLength(20);
    m_confirmInput->SetPasswordMode(true);
    m_confirmInput->SetWILManager(m_wilManager);
    m_confirmInput->SetResourceFile(ResourcePaths::MAIN);
    AddChild(m_confirmInput, true);

    // // Create user name label and input
    // m_userNameLabel = std::make_shared<Label>(labelX, startY + lineHeight * 3, 100, 30, "Your Name:");
    // m_userNameLabel->SetFont(font);
    // m_userNameLabel->SetTextColor({255, 255, 255, 255});
    // m_userNameLabel->SetWILManager(m_wilManager);
    // AddChild(m_userNameLabel);

    // User name input field - positioned relative to dialog
    // In the original project, user name input field is positioned below confirm password input
    // 确保输入框的宽度和高度正确设置
    m_userNameInput = std::make_shared<TextInput>(160, 190, inputWidth, inputHeight);
    // m_userNameInput->SetFont(font);
    m_userNameInput->SetTextColor({255, 255, 255, 255});
    m_userNameInput->SetBackgroundColor({0, 0, 0, 255});
    m_userNameInput->SetMaxLength(20);
    m_userNameInput->SetWILManager(m_wilManager);
    m_userNameInput->SetResourceFile(ResourcePaths::MAIN);
    AddChild(m_userNameInput, true);

    // // Create security question 1 label and input
    // m_question1Label = std::make_shared<Label>(labelX, startY + lineHeight * 4, 100, 30, "Question 1:");
    // m_question1Label->SetFont(font);
    // m_question1Label->SetTextColor({255, 255, 255, 255});
    // m_question1Label->SetWILManager(m_wilManager);
    // AddChild(m_question1Label);

    // Security question 1 input field - positioned relative to dialog
    // 确保输入框的宽度和高度正确设置
    m_question1Input = std::make_shared<TextInput>(160, 220, inputWidth, inputHeight);
    // m_question1Input->SetFont(font);
    m_question1Input->SetTextColor({255, 255, 255, 255});
    m_question1Input->SetBackgroundColor({0, 0, 0, 255});
    m_question1Input->SetMaxLength(50);
    m_question1Input->SetWILManager(m_wilManager);
    m_question1Input->SetResourceFile(ResourcePaths::MAIN);
    AddChild(m_question1Input, true);

    // // Create security answer 1 label and input
    // m_answer1Label = std::make_shared<Label>(labelX, startY + lineHeight * 5, 100, 30, "Answer 1:");
    // m_answer1Label->SetFont(font);
    // m_answer1Label->SetTextColor({255, 255, 255, 255});
    // m_answer1Label->SetWILManager(m_wilManager);
    // AddChild(m_answer1Label);

    // Security answer 1 input field - positioned relative to dialog
    // 确保输入框的宽度和高度正确设置
    m_answer1Input = std::make_shared<TextInput>(160, 250, inputWidth, inputHeight);
    // m_answer1Input->SetFont(font);
    m_answer1Input->SetTextColor({255, 255, 255, 255});
    m_answer1Input->SetBackgroundColor({0, 0, 0, 255});
    m_answer1Input->SetMaxLength(50);
    m_answer1Input->SetWILManager(m_wilManager);
    m_answer1Input->SetResourceFile(ResourcePaths::MAIN);
    AddChild(m_answer1Input, true);

    // // Create security question 2 label and input
    // m_question2Label = std::make_shared<Label>(labelX, startY + lineHeight * 6, 100, 30, "Question 2:");
    // m_question2Label->SetFont(font);
    // m_question2Label->SetTextColor({255, 255, 255, 255});
    // m_question2Label->SetWILManager(m_wilManager);
    // AddChild(m_question2Label);

    // Security question 2 input field - positioned relative to dialog
    // 确保输入框的宽度和高度正确设置
    m_question2Input = std::make_shared<TextInput>(160, 280, inputWidth, inputHeight);
    // m_question2Input->SetFont(font);
    m_question2Input->SetTextColor({255, 255, 255, 255});
    m_question2Input->SetBackgroundColor({0, 0, 0, 255});
    m_question2Input->SetMaxLength(50);
    m_question2Input->SetWILManager(m_wilManager);
    m_question2Input->SetResourceFile(ResourcePaths::MAIN);
    AddChild(m_question2Input, true);

    // // Create security answer 2 label and input
    // m_answer2Label = std::make_shared<Label>(labelX, startY + lineHeight * 7, 100, 30, "Answer 2:");
    // m_answer2Label->SetFont(font);
    // m_answer2Label->SetTextColor({255, 255, 255, 255});
    // m_answer2Label->SetWILManager(m_wilManager);
    // AddChild(m_answer2Label);

    // Security answer 2 input field - positioned relative to dialog
    // 确保输入框的宽度和高度正确设置
    m_answer2Input = std::make_shared<TextInput>(160, 310, inputWidth, inputHeight);
    // m_answer2Input->SetFont(font);
    m_answer2Input->SetTextColor({255, 255, 255, 255});
    m_answer2Input->SetBackgroundColor({0, 0, 0, 255});
    m_answer2Input->SetMaxLength(50);
    m_answer2Input->SetWILManager(m_wilManager);
    m_answer2Input->SetResourceFile(ResourcePaths::MAIN);
    AddChild(m_answer2Input, true);

    // // Create email label and input
    // m_emailLabel = std::make_shared<Label>(labelX, startY + lineHeight * 8, 100, 30, "Email:");
    // m_emailLabel->SetFont(font);
    // m_emailLabel->SetTextColor({255, 255, 255, 255});
    // m_emailLabel->SetWILManager(m_wilManager);
    // AddChild(m_emailLabel);

    // Email input field - positioned relative to dialog
    // 确保输入框的宽度和高度正确设置
    m_emailInput = std::make_shared<TextInput>(160, 340, inputWidth, inputHeight);
    // m_emailInput->SetFont(font);
    m_emailInput->SetTextColor({255, 255, 255, 255});
    m_emailInput->SetBackgroundColor({0, 0, 0, 255});
    m_emailInput->SetMaxLength(50);
    m_emailInput->SetWILManager(m_wilManager);
    m_emailInput->SetResourceFile(ResourcePaths::MAIN);
    AddChild(m_emailInput, true);

    // // Create phone label and input
    // m_phoneLabel = std::make_shared<Label>(labelX, startY + lineHeight * 9, 100, 30, "Phone:");
    // m_phoneLabel->SetFont(font);
    // m_phoneLabel->SetTextColor({255, 255, 255, 255});
    // m_phoneLabel->SetWILManager(m_wilManager);
    // AddChild(m_phoneLabel);

    // Phone input field - positioned relative to dialog
    // 确保输入框的宽度和高度正确设置
    m_phoneInput = std::make_shared<TextInput>(160, 370, inputWidth, inputHeight);
    // m_phoneInput->SetFont(font);
    m_phoneInput->SetTextColor({255, 255, 255, 255});
    m_phoneInput->SetBackgroundColor({0, 0, 0, 255});
    m_phoneInput->SetMaxLength(20);
    m_phoneInput->SetWILManager(m_wilManager);
    m_phoneInput->SetResourceFile(ResourcePaths::MAIN);
    AddChild(m_phoneInput, true);

    // // Create mobile phone label and input
    // m_mobileLabel = std::make_shared<Label>(labelX, startY + lineHeight * 10, 100, 30, "Mobile:");
    // m_mobileLabel->SetFont(font);
    // m_mobileLabel->SetTextColor({255, 255, 255, 255});
    // m_mobileLabel->SetWILManager(m_wilManager);
    // AddChild(m_mobileLabel);

    // Mobile phone input field - positioned relative to dialog
    // 确保输入框的宽度和高度正确设置
    m_mobileInput = std::make_shared<TextInput>(160, 400, inputWidth, inputHeight);
    // m_mobileInput->SetFont(font);
    m_mobileInput->SetTextColor({255, 255, 255, 255});
    m_mobileInput->SetBackgroundColor({0, 0, 0, 255});
    m_mobileInput->SetMaxLength(20);
    m_mobileInput->SetWILManager(m_wilManager);
    m_mobileInput->SetResourceFile(ResourcePaths::MAIN);
    AddChild(m_mobileInput, true);

    // // Create birth date label and input
    // m_birthDateLabel = std::make_shared<Label>(labelX, startY + lineHeight * 11, 100, 30, "Birth Date:");
    // m_birthDateLabel->SetFont(font);
    // m_birthDateLabel->SetTextColor({255, 255, 255, 255});
    // m_birthDateLabel->SetWILManager(m_wilManager);
    // AddChild(m_birthDateLabel);

    // Birth date input field - positioned relative to dialog
    // 确保输入框的宽度和高度正确设置
    m_birthDateInput = std::make_shared<TextInput>(160, 430, inputWidth, inputHeight);
    // m_birthDateInput->SetFont(font);
    m_birthDateInput->SetTextColor({255, 255, 255, 255});
    m_birthDateInput->SetBackgroundColor({0, 0, 0, 255});
    m_birthDateInput->SetMaxLength(10);
    m_birthDateInput->SetWILManager(m_wilManager);
    m_birthDateInput->SetResourceFile(ResourcePaths::MAIN);
    AddChild(m_birthDateInput, true);

    // Create OK button - positioned according to original Delphi project
    // In the original project, DNewAccountOk.Left = 160, DNewAccountOk.Top = 417
    m_okButton = std::make_shared<Button>(160, 417, "OK");
    // m_okButton->SetFont(font);
    m_okButton->SetTextColor({255, 255, 255, 255});
    m_okButton->SetOnClick([this]() { OnOkButtonClick(); });
    m_okButton->SetWILManager(m_wilManager);
    m_okButton->SetResourceFile(ResourcePaths::MAIN);
    m_okButton->SetNormalImageIndex(NO_IMAGE);  // DNewAccountOk image index from original project
    m_okButton->SetHoverImageIndex(NO_IMAGE);   // Same as normal for consistency
    m_okButton->SetPressedImageIndex(62); // Same as normal for consistency
    AddChild(m_okButton, true);

    // Create Cancel button - positioned according to original Delphi project
    // In the original project, DNewAccountCancel.Left = 448, DNewAccountCancel.Top = 419
    m_cancelButton = std::make_shared<Button>(448, 419, "Cancel");
    // m_cancelButton->SetFont(font);
    m_cancelButton->SetTextColor({255, 255, 255, 255});
    m_cancelButton->SetOnClick([this]() { OnCancelButtonClick(); });
    m_cancelButton->SetWILManager(m_wilManager);
    m_cancelButton->SetResourceFile(ResourcePaths::MAIN);
    m_cancelButton->SetNormalImageIndex(NO_IMAGE);  // DNewAccountCancel image index from original project
    m_cancelButton->SetHoverImageIndex(NO_IMAGE);   // Same as normal for consistency
    m_cancelButton->SetPressedImageIndex(52); // Same as normal for consistency
    AddChild(m_cancelButton, true);

    // Create Close button - positioned according to original Delphi project
    // In the original project, DNewAccountClose.Left = 587, DNewAccountClose.Top = 33
    auto closeButton = std::make_shared<Button>(587, 33, "Close");
    // closeButton->SetFont(font);
    closeButton->SetTextColor({255, 255, 255, 255});
    closeButton->SetOnClick([this]() { OnCancelButtonClick(); }); // Same action as cancel
    closeButton->SetWILManager(m_wilManager);
    closeButton->SetResourceFile(ResourcePaths::MAIN);
    closeButton->SetNormalImageIndex(NO_IMAGE);  // DNewAccountClose image index from original project
    closeButton->SetHoverImageIndex(NO_IMAGE);   // Hover state
    closeButton->SetPressedImageIndex(64); // Pressed state
    AddChild(closeButton, true);

    // Close font
    TTF_CloseFont(font);
}

void NewAccountDialog::SetOnOkCallback(std::function<void(const std::string&, const std::string&, const std::string&, const std::string&, const std::string&, const std::string&, const std::string&, const std::string&)> callback)
{
    m_onOkCallback = callback;
}

void NewAccountDialog::SetOnCancelCallback(std::function<void()> callback)
{
    m_onCancelCallback = callback;
}

std::string NewAccountDialog::GetAccountName() const
{
    return m_accountInput ? m_accountInput->GetText() : "";
}

std::string NewAccountDialog::GetPassword() const
{
    return m_passwordInput ? m_passwordInput->GetText() : "";
}

std::string NewAccountDialog::GetConfirmPassword() const
{
    return m_confirmInput ? m_confirmInput->GetText() : "";
}

std::string NewAccountDialog::GetUserName() const
{
    return m_userNameInput ? m_userNameInput->GetText() : "";
}

std::string NewAccountDialog::GetSecurityQuestion1() const
{
    return m_question1Input ? m_question1Input->GetText() : "";
}

std::string NewAccountDialog::GetSecurityAnswer1() const
{
    return m_answer1Input ? m_answer1Input->GetText() : "";
}

std::string NewAccountDialog::GetSecurityQuestion2() const
{
    return m_question2Input ? m_question2Input->GetText() : "";
}

std::string NewAccountDialog::GetSecurityAnswer2() const
{
    return m_answer2Input ? m_answer2Input->GetText() : "";
}

std::string NewAccountDialog::GetEmail() const
{
    return m_emailInput ? m_emailInput->GetText() : "";
}

std::string NewAccountDialog::GetPhoneNumber() const
{
    return m_phoneInput ? m_phoneInput->GetText() : "";
}

std::string NewAccountDialog::GetMobilePhoneNumber() const
{
    return m_mobileInput ? m_mobileInput->GetText() : "";
}

std::string NewAccountDialog::GetBirthDate() const
{
    return m_birthDateInput ? m_birthDateInput->GetText() : "";
}

void NewAccountDialog::OnOkButtonClick()
{
    // Validate inputs
    if (GetAccountName().empty()) {
        // Show error message
        m_dialogManager->ShowMessageDialog("Error", "Please enter an account name", MessageType::ERROR);
        return;
    }

    if (GetPassword().empty()) {
        // Show error message
        m_dialogManager->ShowMessageDialog("Error", "Please enter a password", MessageType::ERROR);
        return;
    }

    if (GetPassword() != GetConfirmPassword()) {
        // Show error message
        m_dialogManager->ShowMessageDialog("Error", "Passwords do not match", MessageType::ERROR);
        return;
    }

    if (GetUserName().empty()) {
        // Show error message
        m_dialogManager->ShowMessageDialog("Error", "Please enter your name", MessageType::ERROR);
        return;
    }

    if (GetSecurityQuestion1().empty() || GetSecurityAnswer1().empty()) {
        // Show error message
        m_dialogManager->ShowMessageDialog("Error", "Please enter security question 1 and answer", MessageType::ERROR);
        return;
    }

    if (GetSecurityQuestion2().empty() || GetSecurityAnswer2().empty()) {
        // Show error message
        m_dialogManager->ShowMessageDialog("Error", "Please enter security question 2 and answer", MessageType::ERROR);
        return;
    }

    // Call callback
    if (m_onOkCallback) {
        m_onOkCallback(
            GetAccountName(),
            GetPassword(),
            GetUserName(),
            GetSecurityQuestion1(),
            GetSecurityAnswer1(),
            GetSecurityQuestion2(),
            GetSecurityAnswer2(),
            GetEmail()
        );
    }

    // Hide dialog
    SetVisible(false);
}

void NewAccountDialog::OnCancelButtonClick()
{
    // Call callback
    if (m_onCancelCallback) {
        m_onCancelCallback();
    }

    // Hide dialog
    SetVisible(false);
}
