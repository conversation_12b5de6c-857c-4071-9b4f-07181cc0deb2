@echo off
echo ========================================
echo    SelGateServer 编译测试结果
echo ========================================
echo.

echo 1. 检查可执行文件:
if exist "bin\Debug\SelGateServer.exe" (
    echo    ✓ SelGateServer.exe 编译成功
    dir bin\Debug\SelGateServer.exe | findstr /C:"SelGateServer.exe"
) else (
    echo    ✗ SelGateServer.exe 编译失败
)

echo.
echo 2. 测试帮助信息:
bin\Debug\SelGateServer.exe -h
echo.

echo 3. 测试版本信息:
bin\Debug\SelGateServer.exe -v
echo.

echo 4. 检查配置文件:
if exist "SelGate.ini" (
    echo    ✓ 配置文件已准备
) else (
    echo    ✗ 配置文件不存在
)

if exist "RunGateList.txt" (
    echo    ✓ RunGate列表文件已准备
) else (
    echo    ✗ RunGate列表文件不存在
)

echo.
echo ========================================
echo    SelGateServer 编译测试完成
echo ========================================
pause 