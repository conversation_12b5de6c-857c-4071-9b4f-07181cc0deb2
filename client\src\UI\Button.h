#pragma once

#include "UIControl.h"
#include "../Graphics/Texture.h"
#include "../Graphics/ResourceManager.h"
#include <memory>
#include <string>

/**
 * @class Button
 * @brief Button UI control
 *
 * This class represents a button UI control, which can be clicked by the user.
 */
class Button : public UIControl {
private:
    std::shared_ptr<Texture> m_normalTexture;    ///< Normal state texture
    std::shared_ptr<Texture> m_hoverTexture;     ///< Hover state texture
    std::shared_ptr<Texture> m_pressedTexture;   ///< Pressed state texture
    std::shared_ptr<Texture> m_disabledTexture;  ///< Disabled state texture

    std::string m_text;                          ///< Button text
    SDL_Color m_textColor;                       ///< Text color
    TTF_Font* m_font;                            ///< Text font
    std::shared_ptr<Texture> m_textTexture;      ///< Text texture

    /**
     * @brief Create the text texture
     */
    void CreateTextTexture();

public:
    /**
     * @brief Constructor with width and height
     * @param x X position
     * @param y Y position
     * @param width Width (default: 0, will use image width if available)
     * @param height Height (default: 0, will use image height if available)
     * @param text Button text
     * @param name Control name
     */
    Button(int x, int y, int width, int height, const std::string& text = "", const std::string& name = "");

    /**
     * @brief Constructor without width and height (will use image dimensions)
     * @param x X position
     * @param y Y position
     * @param text Button text
     * @param name Control name
     */
    Button(int x, int y, const std::string& text = "", const std::string& name = "");

    /**
     * @brief Destructor
     */
    virtual ~Button();

    /**
     * @brief Render the button
     * @param renderer SDL renderer
     */
    virtual void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Set the normal texture
     * @param texture Normal state texture
     */
    void SetNormalTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Set the hover texture
     * @param texture Hover state texture
     */
    void SetHoverTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Set the pressed texture
     * @param texture Pressed state texture
     */
    void SetPressedTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Set the disabled texture
     * @param texture Disabled state texture
     */
    void SetDisabledTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Set the text
     * @param text Button text
     */
    void SetText(const std::string& text);

    /**
     * @brief Set the text color
     * @param color Text color
     */
    void SetTextColor(const SDL_Color& color);

    /**
     * @brief Set the font
     * @param font Text font
     */
    void SetFont(TTF_Font* font);

    /**
     * @brief Set the resource file and indices
     * @param resourceFile Resource file name (WIL file)
     * @param normalIndex Normal state image index
     * @param hoverIndex Hover state image index
     * @param pressedIndex Pressed state image index
     * @param disabledIndex Disabled state image index
     */
    void SetResourceIndices(const std::string& resourceFile, int normalIndex, int hoverIndex, int pressedIndex, int disabledIndex);

    /**
     * @brief Set the resource type and indices using ResourceManager
     * @param resourceType Resource type from ResourceManager
     * @param normalIndex Normal state image index
     * @param hoverIndex Hover state image index
     * @param pressedIndex Pressed state image index
     * @param disabledIndex Disabled state image index
     */
    void SetResourceType(ResourceManager::ResourceType resourceType, int normalIndex, int hoverIndex, int pressedIndex, int disabledIndex);

    /**
     * @brief Get the text
     * @return Button text
     */
    const std::string& GetText() const { return m_text; }

    /**
     * @brief Get the text color
     * @return Text color
     */
    const SDL_Color& GetTextColor() const { return m_textColor; }

    /**
     * @brief Get the font
     * @return Text font
     */
    TTF_Font* GetFont() const { return m_font; }

    /**
     * @brief Set the click event handler
     * @param callback Click event handler
     */
    void SetOnClick(std::function<void()> callback);
};
