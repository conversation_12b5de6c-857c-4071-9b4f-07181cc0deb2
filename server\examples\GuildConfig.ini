; Guild Configuration File Example
; 行会配置文件示例
; Generated at 1234567890

[General]
; 基本设置
AutoSave=1
SaveInterval=300000
MaxNotices=10
MaxMembers=200

[Permissions]
; 权限设置
AllowMemberInvite=0
AllowMemberKick=0
AllowMemberNotice=0
AllowMemberWarehouse=0
RequireApproval=1

[War]
; 战争设置
DefaultDuration=10800000
AllowAllyHelp=1
AutoAcceptWar=0
WarCooldown=86400000

[Skills]
; 技能设置
AutoUpgrade=0
MaxSkillLevel=10
UpgradeCostMultiplier=1.0

[Warehouse]
; 仓库设置
MaxItems=1000
LogOperations=1
AccessLevel=2

[Donation]
; 捐献设置
MinGoldAmount=1000
MaxGoldAmount=1000000
ExpRewardRate=0.01
ShowDonationRank=1

[Upgrade]
; 升级设置
ExpFormula=level*1000
MaxLevel=50
AutoLevelUp=1

[Ranking]
; 排名设置
UpdateInterval=300000
ShowInList=1
ScoreFormula=default

; 配置说明：
; 
; [General] - 基本设置
; AutoSave: 是否自动保存 (0=否, 1=是)
; SaveInterval: 保存间隔时间(毫秒)
; MaxNotices: 最大公告数量
; MaxMembers: 最大成员数量
;
; [Permissions] - 权限设置
; AllowMemberInvite: 是否允许普通成员邀请新成员
; AllowMemberKick: 是否允许普通成员踢出其他成员
; AllowMemberNotice: 是否允许普通成员发布公告
; AllowMemberWarehouse: 是否允许普通成员访问仓库
; RequireApproval: 加入行会是否需要审批
;
; [War] - 战争设置
; DefaultDuration: 默认战争持续时间(毫秒)
; AllowAllyHelp: 是否允许联盟行会帮助
; AutoAcceptWar: 是否自动接受宣战
; WarCooldown: 战争冷却时间(毫秒)
;
; [Skills] - 技能设置
; AutoUpgrade: 是否自动升级技能
; MaxSkillLevel: 最大技能等级
; UpgradeCostMultiplier: 升级费用倍数
;
; [Warehouse] - 仓库设置
; MaxItems: 最大物品数量
; LogOperations: 是否记录操作日志
; AccessLevel: 访问权限等级 (0=所有人, 1=队长以上, 2=副会长以上)
;
; [Donation] - 捐献设置
; MinGoldAmount: 最小金币捐献数量
; MaxGoldAmount: 最大金币捐献数量
; ExpRewardRate: 经验奖励比率
; ShowDonationRank: 是否显示捐献排行
;
; [Upgrade] - 升级设置
; ExpFormula: 升级经验公式
; MaxLevel: 最大等级
; AutoLevelUp: 是否自动升级
;
; [Ranking] - 排名设置
; UpdateInterval: 排名更新间隔(毫秒)
; ShowInList: 是否在排行榜显示
; ScoreFormula: 排名分数计算公式
