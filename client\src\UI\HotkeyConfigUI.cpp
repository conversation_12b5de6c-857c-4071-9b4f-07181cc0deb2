#include "HotkeyConfigUI.h"
#include "UIConstants.h"
#include "UILayout.h"
#include "ResourcePaths.h"
#include <iostream>

HotkeyConfigUI::HotkeyConfigUI(int x, int y, int width, int height,
                             std::shared_ptr<Player> player,
                             std::shared_ptr<SkillManager> skillManager)
    : UIControl(x, y, width, height)
    , m_player(player)
    , m_skillManager(skillManager)
    , m_isSelectingHotkey(false)
    , m_selectedHotkeyIndex(-1)
{
    SetName("HotkeyConfigUI");
    CreateControls();
    RefreshHotkeys();
    Hide(); // Start hidden
}

HotkeyConfigUI::~HotkeyConfigUI()
{
}

void HotkeyConfigUI::CreateControls()
{
    // Set resource file for UI elements
    SetResourceFile(ResourcePaths::INTERFACE);

    // Create background (using normal image index)
    SetNormalImageIndex(UIConstants::HOTKEY_UI_BG_INDEX);

    // Create close button
    m_closeButton = std::make_shared<Button>(
        m_x + m_width + UILayout::HotkeyUI::CLOSE_BTN_X_OFFSET,
        m_y + UILayout::HotkeyUI::CLOSE_BTN_Y_OFFSET,
        UILayout::HotkeyUI::CLOSE_BTN_WIDTH,
        UILayout::HotkeyUI::CLOSE_BTN_HEIGHT,
        "X"
    );
    m_closeButton->SetResourceFile(ResourcePaths::INTERFACE);
    m_closeButton->SetResourceIndices(
        ResourcePaths::INTERFACE,
        UIConstants::HOTKEY_UI_CLOSE_BTN_NORMAL,
        UIConstants::HOTKEY_UI_CLOSE_BTN_HOVER,
        UIConstants::HOTKEY_UI_CLOSE_BTN_PRESSED,
        UIConstants::HOTKEY_UI_CLOSE_BTN_DISABLED
    );
    m_closeButton->SetOnClick([this]() { OnCloseButtonClick(nullptr); });
    AddChild(m_closeButton);

    // Create reset button
    m_resetButton = std::make_shared<Button>(
        m_x + UILayout::HotkeyUI::HOTKEY_GRID_X_OFFSET,
        m_y + m_height - 40,
        80,
        30,
        "Reset All"
    );
    m_resetButton->SetResourceFile(ResourcePaths::INTERFACE);
    m_resetButton->SetResourceIndices(ResourcePaths::INTERFACE, 155, 156, 157, 158); // Placeholder indices
    m_resetButton->SetOnClick([this]() { OnResetButtonClick(nullptr); });
    AddChild(m_resetButton);

    // Create hotkey buttons, labels, and clear buttons
    for (int i = 0; i < 10; i++) {
        // Hotkey button
        int buttonX = m_x + UILayout::HotkeyUI::HOTKEY_GRID_X_OFFSET;
        int buttonY = m_y + UILayout::HotkeyUI::HOTKEY_GRID_Y_OFFSET + i * (UILayout::HotkeyUI::HOTKEY_ITEM_HEIGHT + UILayout::HotkeyUI::HOTKEY_ITEM_SPACING);

        auto hotkeyButton = std::make_shared<Button>(
            buttonX,
            buttonY,
            UILayout::HotkeyUI::HOTKEY_ITEM_WIDTH,
            UILayout::HotkeyUI::HOTKEY_ITEM_HEIGHT,
            std::to_string(i == 9 ? 0 : i + 1)
        );
        hotkeyButton->SetResourceFile(ResourcePaths::INTERFACE);
        hotkeyButton->SetResourceIndices(ResourcePaths::INTERFACE, 160 + i, 170 + i, 180 + i, 190 + i); // Placeholder indices
        // Store index in a closure instead of using SetTag
        hotkeyButton->SetOnClick([this, i]() {
            // Use the captured index instead of getting it from the control
            m_selectedHotkeyIndex = i;
            OnHotkeyButtonClick(nullptr);
        });
        AddChild(hotkeyButton);
        m_hotkeyButtons.push_back(hotkeyButton);

        // Hotkey label
        auto hotkeyLabel = std::make_shared<Label>(
            buttonX + UILayout::HotkeyUI::HOTKEY_ITEM_WIDTH + 10,
            buttonY + (UILayout::HotkeyUI::HOTKEY_ITEM_HEIGHT - 20) / 2,
            UILayout::HotkeyUI::HOTKEY_GRID_WIDTH - UILayout::HotkeyUI::HOTKEY_ITEM_WIDTH - 50,
            20,
            ""
        );
        hotkeyLabel->SetResourceFile(ResourcePaths::INTERFACE);
        AddChild(hotkeyLabel);
        m_hotkeyLabels.push_back(hotkeyLabel);

        // Clear button
        auto clearButton = std::make_shared<Button>(
            buttonX + UILayout::HotkeyUI::HOTKEY_GRID_WIDTH - 40,
            buttonY,
            30,
            30,
            "X"
        );
        clearButton->SetResourceFile(ResourcePaths::INTERFACE);
        clearButton->SetResourceIndices(ResourcePaths::INTERFACE, 200, 201, 202, 203); // Placeholder indices
        // Store index in a closure instead of using SetTag
        clearButton->SetOnClick([this, i]() {
            // Use the captured index instead of getting it from the control
            OnClearButtonClick(nullptr, i);
        });
        AddChild(clearButton);
        m_clearButtons.push_back(clearButton);
    }
}

void HotkeyConfigUI::RefreshHotkeys()
{
    if (!m_player) {
        return;
    }

    // Update hotkey labels
    for (int i = 0; i < 10; i++) {
        int skillId = m_player->GetHotkey(i);
        std::string labelText;

        if (skillId > 0) {
            auto skill = m_skillManager->GetSkill(skillId);
            if (skill) {
                labelText = skill->GetName() + " (Lv." + std::to_string(skill->GetLevel()) + ")";
            } else {
                labelText = "Unknown Skill";
            }
        } else {
            labelText = "Not Assigned";
        }

        m_hotkeyLabels[i]->SetText(labelText);

        // Update button state if selecting
        // Note: Button class doesn't have SetPressed method, so we'll use visual cues in Render method instead
    }
}

void HotkeyConfigUI::OnHotkeyButtonClick(UIControl* control)
{
    // Toggle selection state
    if (m_isSelectingHotkey) {
        // Deselect
        m_isSelectingHotkey = false;
        m_selectedHotkeyIndex = -1;
    } else {
        // Select
        m_isSelectingHotkey = true;
        // m_selectedHotkeyIndex is already set in the lambda
    }

    RefreshHotkeys();

    std::cout << "Hotkey button clicked: " << m_selectedHotkeyIndex << ", selecting: " << m_isSelectingHotkey << std::endl;
}

void HotkeyConfigUI::OnClearButtonClick(UIControl* control, int hotkeyIndex)
{
    // Clear the hotkey
    m_player->SetHotkey(hotkeyIndex, 0);

    // Refresh display
    RefreshHotkeys();

    std::cout << "Hotkey cleared: " << hotkeyIndex << std::endl;
}

void HotkeyConfigUI::OnCloseButtonClick(UIControl* control)
{
    Hide();
}

void HotkeyConfigUI::OnResetButtonClick(UIControl* control)
{
    // Clear all hotkeys
    for (int i = 0; i < 10; i++) {
        m_player->SetHotkey(i, 0);
    }

    // Refresh display
    RefreshHotkeys();

    std::cout << "All hotkeys reset" << std::endl;
}

void HotkeyConfigUI::Update(int deltaTime)
{
    UIControl::Update(deltaTime);
}

void HotkeyConfigUI::Render(SDL_Renderer* renderer)
{
    if (!IsVisible()) {
        return;
    }

    // Render background and controls
    UIControl::Render(renderer);

    // Render title
    SDL_Rect titleRect = {
        m_x + UILayout::HotkeyUI::TITLE_X_OFFSET,
        m_y + UILayout::HotkeyUI::TITLE_Y_OFFSET,
        200,
        30
    };
    SDL_SetRenderDrawColor(renderer, 255, 255, 255, 255); // White

    // TODO: Render title text
    // This would typically use a font rendering system

    // Render selection indicator if selecting
    if (m_isSelectingHotkey && m_selectedHotkeyIndex >= 0) {
        SDL_Rect selectionRect = {
            m_x + UILayout::HotkeyUI::HOTKEY_GRID_X_OFFSET - 5,
            m_y + UILayout::HotkeyUI::HOTKEY_GRID_Y_OFFSET + m_selectedHotkeyIndex * (UILayout::HotkeyUI::HOTKEY_ITEM_HEIGHT + UILayout::HotkeyUI::HOTKEY_ITEM_SPACING),
            UILayout::HotkeyUI::HOTKEY_GRID_WIDTH + 10,
            UILayout::HotkeyUI::HOTKEY_ITEM_HEIGHT
        };
        SDL_SetRenderDrawColor(renderer, 255, 255, 0, 50); // Yellow with alpha
        SDL_RenderFillRect(renderer, &selectionRect);
    }
}

bool HotkeyConfigUI::HandleMouseButton(Uint8 button, bool pressed, int x, int y)
{
    if (!IsVisible()) {
        return false;
    }

    return UIControl::HandleMouseButton(button, pressed, x, y);
}

bool HotkeyConfigUI::HandleKey(SDL_Keycode key, bool pressed)
{
    if (!IsVisible()) {
        return false;
    }

    // Handle escape key to close the UI
    if (key == SDLK_ESCAPE && pressed) {
        Hide();
        return true;
    }

    // Handle number keys for quick assignment
    if (pressed && m_isSelectingHotkey && m_selectedHotkeyIndex >= 0) {
        // Check if a number key was pressed
        if (key >= SDLK_1 && key <= SDLK_9) {
            int skillIndex = key - SDLK_1;

            // Get the skill at this index from player's skill list
            auto skills = m_player->GetSkills();
            if (skills.size() > skillIndex) {
                // Find the skill at this index
                int currentIndex = 0;
                for (const auto& skillPair : skills) {
                    if (currentIndex == skillIndex) {
                        // Assign this skill to the selected hotkey
                        m_player->SetHotkey(m_selectedHotkeyIndex, skillPair.first);

                        // End selection
                        m_isSelectingHotkey = false;
                        m_selectedHotkeyIndex = -1;

                        // Refresh display
                        RefreshHotkeys();

                        std::cout << "Assigned skill " << skillPair.first << " to hotkey " << m_selectedHotkeyIndex << std::endl;
                        return true;
                    }
                    currentIndex++;
                }
            }
        }
        else if (key == SDLK_0) {
            // Handle 0 key (10th skill)
            int skillIndex = 9;

            // Get the skill at this index from player's skill list
            auto skills = m_player->GetSkills();
            if (skills.size() > skillIndex) {
                // Find the skill at this index
                int currentIndex = 0;
                for (const auto& skillPair : skills) {
                    if (currentIndex == skillIndex) {
                        // Assign this skill to the selected hotkey
                        m_player->SetHotkey(m_selectedHotkeyIndex, skillPair.first);

                        // End selection
                        m_isSelectingHotkey = false;
                        m_selectedHotkeyIndex = -1;

                        // Refresh display
                        RefreshHotkeys();

                        std::cout << "Assigned skill " << skillPair.first << " to hotkey " << m_selectedHotkeyIndex << std::endl;
                        return true;
                    }
                    currentIndex++;
                }
            }
        }
    }

    return UIControl::HandleKey(key, pressed);
}

void HotkeyConfigUI::Show()
{
    SetVisible(true);
    m_isSelectingHotkey = false;
    m_selectedHotkeyIndex = -1;
    RefreshHotkeys();
}

void HotkeyConfigUI::Hide()
{
    SetVisible(false);
    m_isSelectingHotkey = false;
    m_selectedHotkeyIndex = -1;
}

void HotkeyConfigUI::SetHotkey(int hotkeyIndex, int skillId)
{
    if (m_player && hotkeyIndex >= 0 && hotkeyIndex < 10) {
        m_player->SetHotkey(hotkeyIndex, skillId);
        RefreshHotkeys();
    }
}
