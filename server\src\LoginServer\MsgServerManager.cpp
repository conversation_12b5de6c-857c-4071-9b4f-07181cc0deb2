#include "MsgServerManager.h"
#include "../Common/Logger.h"
#include "../Protocol/MessageConverter.h"
#include "AccountTypes.h"
#include <fstream>
#include <sstream>
#include <algorithm>

namespace MirServer {

// Protocol constants for server communication
constexpr uint16_t SS_OPENSESSION = 100;
constexpr uint16_t SS_CLOSESESSION = 101;
constexpr uint16_t SS_SOFTOUTSESSION = 102;
constexpr uint16_t SS_SERVERINFO = 103;
constexpr uint16_t SS_KEEPALIVE = 104;
constexpr uint16_t SS_KICKUSER = 105;
constexpr uint16_t UNKNOWMSG = 9999;

MsgServerManager::MsgServerManager() {
    m_network = std::make_unique<Network::NetworkManager>();
}

MsgServerManager::~MsgServerManager() {
    Stop();
}

bool MsgServerManager::Initialize(const std::string& serverAddr, int32_t serverPort) {
    if (!m_network->Initialize()) {
        return false;
    }
    
    // Set up packet handler
    auto handler = [this](std::shared_ptr<Network::ClientConnection> connection,
                         const Network::GamePacket& packet) {
        std::string msg(reinterpret_cast<const char*>(packet.data.data()), packet.data.size());
        OnServerMessage(connection, msg);
    };
    
    // Start listening for game servers
    if (!m_network->StartServer(serverPort)) {
        Logger::Error("Failed to start MsgServer on port " + std::to_string(serverPort));
        return false;
    }
    
    Logger::Info("MsgServerManager listening on " + serverAddr + ":" + std::to_string(serverPort));
    return true;
}

bool MsgServerManager::Start() {
    return m_network != nullptr;
}

void MsgServerManager::Stop() {
    if (m_network) {
        m_network->StopServer();
    }
}

void MsgServerManager::LoadServerAddr(const std::string& filename) {
    std::ifstream file(filename);
    if (!file) {
        Logger::Warning("Server address file not found: " + filename);
        return;
    }
    
    m_serverAddrList.clear();
    std::string line;
    
    while (std::getline(file, line)) {
        // Trim whitespace
        line.erase(0, line.find_first_not_of(" \t"));
        line.erase(line.find_last_not_of(" \t") + 1);
        
        if (line.empty() || line[0] == ';') continue;
        
        // Check if it's a valid IP address (simple check for dots)
        if (std::count(line.begin(), line.end(), '.') == 3) {
            m_serverAddrList.push_back(line);
        }
    }
    
    Logger::Info("Loaded " + std::to_string(m_serverAddrList.size()) + " server addresses");
}

void MsgServerManager::LoadUserLimit(const std::string& filename) {
    std::ifstream file(filename);
    if (!file) {
        Logger::Warning("User limit file not found: " + filename);
        return;
    }
    
    m_userLimits.clear();
    std::string line;
    
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == ';') continue;
        
        std::istringstream iss(line);
        LimitServerUserInfo limit;
        int minCount, maxCount;
        
        if (iss >> limit.sServerName >> limit.sName >> minCount >> maxCount) {
            limit.nLimitCountMin = minCount;
            limit.nLimitCountMax = maxCount;
            m_userLimits[limit.sServerName] = limit;
        }
    }
}

bool MsgServerManager::CheckReadyServers() {
    std::lock_guard<std::mutex> lock(m_serverListMutex);
    // Check if we have at least one ready server
    return !m_serverList.empty();
}

void MsgServerManager::SendServerMsg(uint16_t wIdent, const std::string& sServerName, const std::string& sMsg) {
    std::lock_guard<std::mutex> lock(m_serverListMutex);
    
    std::string limitName = LimitName(sServerName);
    std::string sendMsg = "(" + std::to_string(wIdent) + "/" + sMsg + ")";
    
    for (auto& server : m_serverList) {
        if (server->connection && server->connection->IsConnected()) {
            if (limitName.empty() || server->sServerName.empty() ||
                server->sServerName == limitName || server->nServerIndex == 99) {
                server->connection->Send(sendMsg.data(), sendMsg.size());
            }
        }
    }
}

void MsgServerManager::SendServerMsgA(uint16_t wIdent, const std::string& sMsg) {
    std::lock_guard<std::mutex> lock(m_serverListMutex);
    
    std::string sendMsg = "(" + std::to_string(wIdent) + "/" + sMsg + "/游戏中心)";
    
    for (auto& server : m_serverList) {
        if (server->connection && server->connection->IsConnected()) {
            server->connection->Send(sendMsg.data(), sendMsg.size());
        }
    }
}

bool MsgServerManager::IsNotUserFull(const std::string& sServerName) {
    auto it = m_userLimits.find(sServerName);
    if (it != m_userLimits.end()) {
        return it->second.nLimitCountMin <= it->second.nLimitCountMax;
    }
    return true;
}

int32_t MsgServerManager::ServerStatus(const std::string& sServerName) {
    std::lock_guard<std::mutex> lock(m_serverListMutex);
    
    for (const auto& server : m_serverList) {
        if (server->sServerName == sServerName) {
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
                now - server->dwKeepAliveTick).count();
            
            if (elapsed < 30) {
                return 1; // Online
            }
        }
    }
    return 0; // Offline
}

int32_t MsgServerManager::GetOnlineHumCount() {
    std::lock_guard<std::mutex> lock(m_serverListMutex);
    
    int32_t count = 0;
    for (const auto& server : m_serverList) {
        if (server->nServerIndex != 99) {
            count += server->nOnlineCount;
        }
    }
    return count;
}

void MsgServerManager::OnServerConnected(std::shared_ptr<Network::ClientConnection> connection) {
    std::string remoteAddr = connection->GetRemoteIP();
    
    // Check if IP is allowed
    bool allowed = false;
    for (const auto& addr : m_serverAddrList) {
        if (addr == remoteAddr) {
            allowed = true;
            break;
        }
    }
    
    if (allowed) {
        auto server = std::make_shared<MsgServerInfo>();
        server->connection = connection;
        server->sIPaddr = remoteAddr;
        server->dwKeepAliveTick = std::chrono::steady_clock::now();
        
        std::lock_guard<std::mutex> lock(m_serverListMutex);
        m_serverList.push_back(server);
        
        Logger::Info("GameServer connected from: " + remoteAddr);
    } else {
        Logger::Warning("Unauthorized server connection from: " + remoteAddr);
        connection->Disconnect();
    }
}

void MsgServerManager::OnServerDisconnected(std::shared_ptr<Network::ClientConnection> connection) {
    std::lock_guard<std::mutex> lock(m_serverListMutex);
    
    m_serverList.erase(
        std::remove_if(m_serverList.begin(), m_serverList.end(),
            [connection](const std::shared_ptr<MsgServerInfo>& server) {
                return server->connection == connection;
            }),
        m_serverList.end()
    );
    
    Logger::Info("GameServer disconnected");
}

void MsgServerManager::OnServerMessage(std::shared_ptr<Network::ClientConnection> connection, 
                                      const std::string& msg) {
    std::lock_guard<std::mutex> lock(m_serverListMutex);
    
    // Find the server
    std::shared_ptr<MsgServerInfo> msgServer;
    for (auto& server : m_serverList) {
        if (server->connection == connection) {
            msgServer = server;
            break;
        }
    }
    
    if (!msgServer) return;
    
    msgServer->sReceiveMsg += msg;
    
    // Process messages in format (code/data)
    while (true) {
        size_t startPos = msgServer->sReceiveMsg.find('(');
        if (startPos == std::string::npos) break;
        
        size_t endPos = msgServer->sReceiveMsg.find(')', startPos);
        if (endPos == std::string::npos) break;
        
        std::string message = msgServer->sReceiveMsg.substr(startPos + 1, endPos - startPos - 1);
        msgServer->sReceiveMsg = msgServer->sReceiveMsg.substr(endPos + 1);
        
        ProcessServerMessage(msgServer, message);
    }
}

void MsgServerManager::ProcessServerMessage(std::shared_ptr<MsgServerInfo> server, 
                                           const std::string& msg) {
    // Parse format: code/param1/param2/...
    std::istringstream iss(msg);
    std::string codeStr;
    
    if (!std::getline(iss, codeStr, '/')) return;
    
    int32_t code = std::stoi(codeStr);
    
    switch (code) {
        case SS_SERVERINFO: {
            // Format: 103/ServerName/Index/OnlineCount
            std::string serverName, indexStr, countStr;
            
            if (std::getline(iss, serverName, '/') &&
                std::getline(iss, indexStr, '/') &&
                std::getline(iss, countStr, '/')) {
                
                server->sServerName = serverName;
                server->nServerIndex = std::stoi(indexStr);
                server->nOnlineCount = std::stoi(countStr);
                server->dwKeepAliveTick = std::chrono::steady_clock::now();
                
                // Update server limit
                RefServerLimit(serverName);
                
                // Send keep alive
                int32_t totalCount = GetOnlineHumCount();
                SendServerMsgA(SS_KEEPALIVE, std::to_string(totalCount));
                
                Logger::Debug("Server info: " + serverName + " Index:" + indexStr + 
                            " Online:" + countStr);
            }
            break;
        }
        
        case SS_SOFTOUTSESSION: {
            // Handle session close request
            // Format would be implemented based on actual usage
            break;
        }
        
        case UNKNOWMSG: {
            std::string data;
            std::getline(iss, data);
            SendServerMsgA(UNKNOWMSG, data);
            break;
        }
    }
}

void MsgServerManager::RefServerLimit(const std::string& sServerName) {
    int32_t count = 0;
    
    for (const auto& server : m_serverList) {
        if (server->nServerIndex != 99 && server->sServerName == sServerName) {
            count += server->nOnlineCount;
        }
    }
    
    auto it = m_userLimits.find(sServerName);
    if (it != m_userLimits.end()) {
        it->second.nLimitCountMin = count;
    }
}

std::string MsgServerManager::LimitName(const std::string& sServerName) {
    auto it = m_userLimits.find(sServerName);
    if (it != m_userLimits.end()) {
        return it->second.sName;
    }
    return "";
}

} // namespace MirServer 