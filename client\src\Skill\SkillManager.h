#pragma once

#include "Skill.h"
#include <unordered_map>
#include <memory>
#include <string>
#include <vector>

/**
 * @class SkillManager
 * @brief 管理游戏中的所有技能
 */
class SkillManager {
private:
    std::unordered_map<int, std::shared_ptr<Skill>> m_skills;  // 技能ID到技能的映射

    // 单例实例
    static SkillManager* s_instance;

    /**
     * @brief 私有构造函数（单例模式）
     */
    SkillManager();

public:
    /**
     * @brief 获取单例实例
     * @return SkillManager实例
     */
    static SkillManager* GetInstance();

    /**
     * @brief 释放单例实例
     */
    static void ReleaseInstance();

    /**
     * @brief 析构函数
     */
    ~SkillManager();

    /**
     * @brief 初始化技能管理器
     * @return 是否成功初始化
     */
    bool Initialize();

    /**
     * @brief 加载技能数据
     * @param filename 技能数据文件名
     * @return 是否成功加载
     */
    bool LoadSkills(const std::string& filename);

    /**
     * @brief 添加技能
     * @param skill 技能
     */
    void AddSkill(std::shared_ptr<Skill> skill);

    /**
     * @brief 获取技能
     * @param skillId 技能ID
     * @return 技能指针，如果不存在则返回nullptr
     */
    std::shared_ptr<Skill> GetSkill(int skillId) const;

    /**
     * @brief 获取所有技能
     * @return 所有技能的列表
     */
    std::vector<std::shared_ptr<Skill>> GetAllSkills() const;

    /**
     * @brief 获取指定职业的技能
     * @param jobClass 职业
     * @return 指定职业的技能列表
     */
    std::vector<std::shared_ptr<Skill>> GetSkillsByJob(JobClass jobClass) const;

    /**
     * @brief 获取指定效果类型的技能
     * @param effectType 效果类型
     * @return 指定效果类型的技能列表
     */
    std::vector<std::shared_ptr<Skill>> GetSkillsByEffectType(EffectType effectType) const;

    /**
     * @brief 保存技能数据到文件
     * @param filename 文件名
     * @return 是否成功保存
     */
    bool SaveSkillsToFile(const std::string& filename) const;

    /**
     * @brief 检查玩家是否可以学习技能
     * @param skillId 技能ID
     * @param playerLevel 玩家等级
     * @param playerClass 玩家职业
     * @return 是否可以学习
     */
    bool CanLearnSkill(int skillId, int playerLevel, JobClass playerClass) const;

    /**
     * @brief 创建一个新的技能
     * @param id 技能ID
     * @param name 技能名称
     * @param effectType 效果类型
     * @param effectValue 效果值
     * @param manaCost 魔法消耗
     * @param power 威力
     * @param maxPower 最大威力
     * @param jobClass 职业要求
     * @param cooldown 冷却时间
     * @return 创建的技能
     */
    std::shared_ptr<Skill> CreateSkill(int id, const std::string& name, EffectType effectType, int effectValue,
                                      int manaCost, int power, int maxPower, JobClass jobClass, int cooldown);
};
