package DCP_d3;

{$R *.RES}
{$ALIGN ON}
{$ASSERTIONS ON}
{$BOOLEVAL OFF}
{$DEBU<PERSON>NFO ON}
{$EXTENDEDSYNTAX ON}
{$IMPORTEDDATA ON}
{$IOCHECKS ON}
{$LOCALSYMBOLS ON}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION ON}
{$OVERFLOWCHECKS OFF}
{$RANGECHECKS OFF}
{$REFERENCEINFO OFF}
{$SAFEDIVIDE OFF}
{$STACKFRAMES OFF}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST ON}
{$MINENUMSIZE 1}
{$IMAGEBASE $00400000}
{$DESCRIPTION 'DCPcrypt cryptographic components library v2.0'}
{$DESIGNONLY}
{$IMPLICITBUILD ON}

requires
  vcl30;

contains
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ry<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>fish,
  <PERSON>128,
  <PERSON>256,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>2,
  <PERSON>4,
  <PERSON>5,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>d4,
  <PERSON>d5,
  <PERSON>md160,
  <PERSON>ha1;

end.
