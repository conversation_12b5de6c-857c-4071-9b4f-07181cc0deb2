#pragma once

#include "../GameState.h"
#include "../Map/MapManager.h"
#include "../Actor/ActorManager.h"
#include "../Graphics/WILLoader.h"
#include "../Graphics/ResourceManager.h"
#include "../Graphics/LightManager.h"
#include "../Skill/EffectManager.h"
#include "../Skill/SkillManager.h"
#include "../Network/NetworkManager.h"
#include "../UI/ChatManager.h"
#include "../UI/SkillPanel.h"
#include "../UI/HotkeyConfigUI.h"
#include "../UI/StatusPanel.h"
#include "../UI/UIManager.h"
#include "../UI/DamageNumber.h"
#include "../UI/GameUI.h"
#include <memory>

/**
 * @class PlayState
 * @brief Game state for the main gameplay
 *
 * This class represents the main gameplay state, where the player can move around
 * the map, interact with NPCs, fight monsters, etc.
 */
class PlayState : public GameState {
private:
    std::shared_ptr<WILManager> m_wilManager;      ///< WIL manager
    ResourceManager* m_resourceManager;            ///< Resource manager
    std::shared_ptr<MapManager> m_mapManager;      ///< Map manager
    std::shared_ptr<ActorManager> m_actorManager;  ///< Actor manager
    std::shared_ptr<LightManager> m_lightManager;  ///< Light manager
    std::shared_ptr<NetworkManager> m_networkManager;  ///< Network manager
    std::shared_ptr<ChatManager> m_chatManager;    ///< Chat manager
    std::shared_ptr<UIManager> m_uiManager;        ///< UI manager
    std::shared_ptr<SkillPanel> m_skillPanel;       ///< Skill Panel
    std::shared_ptr<StatusPanel> m_statusPanel;    ///< Status Panel
    std::shared_ptr<HotkeyConfigUI> m_hotkeyConfigUI; ///< Hotkey configuration UI
    std::shared_ptr<GameUI> m_gameUI;              ///< Main game UI
    std::shared_ptr<DamageNumberManager> m_damageNumberManager; ///< Damage number manager
    TTF_Font* m_chatFont;                          ///< Font for chat

    bool m_paused;                                 ///< Whether the game is paused
    bool m_viewFog;                                ///< Whether to view fog

    /**
     * @brief Load the current map
     * @param mapName Map name
     * @return true if successful, false otherwise
     */
    bool LoadMap(const std::string& mapName);

    /**
     * @brief Create the player character
     * @return true if successful, false otherwise
     */
    bool CreatePlayer();

    /**
     * @brief Spawn monsters on the map
     * @return true if successful, false otherwise
     */
    bool SpawnMonsters();

    // Map packet handlers
    void HandleMapData(const Packet& packet);
    void HandleMapChange(const Packet& packet);
    void HandleChangeMap(const Packet& packet);
    void HandleClearObjects(const Packet& packet);
    void HandleAreaState(const Packet& packet);
    void HandleDayChanging(const Packet& packet);

    // Entity spawn packet handlers
    void HandleSpawnPlayer(const Packet& packet);
    void HandleSpawnMonster(const Packet& packet);
    void HandleSpawnNPC(const Packet& packet);
    void HandleSpawnItem(const Packet& packet);
    void HandleDespawnEntity(const Packet& packet);

    // Entity action packet handlers
    void HandleEntityMove(const Packet& packet);
    void HandleEntityAttack(const Packet& packet);
    void HandleEntityCast(const Packet& packet);
    void HandleEntityEffect(const Packet& packet);
    void HandleEntityState(const Packet& packet);
    void HandleEntityStruck(const Packet& packet);
    void HandleEntityDeath(const Packet& packet);
    void HandleEntitySkeleton(const Packet& packet);
    void HandleEntityAlive(const Packet& packet);
    void HandleEntityLevelUp(const Packet& packet);
    void HandleEntityChangeNameColor(const Packet& packet);
    void HandleEntityChangeLight(const Packet& packet);
    void HandleEntityWinExp(const Packet& packet);

    // Chat packet handlers
    void HandleChatMessage(const Packet& packet);
    void HandleWhisperMessage(const Packet& packet);
    void HandleGuildMessage(const Packet& packet);
    void HandleSystemMessage(const Packet& packet);
    void HandleCryMessage(const Packet& packet);
    void HandleGroupMessage(const Packet& packet);
    void HandleMerchantSay(const Packet& packet);

    // Item packet handlers
    void HandleAddItem(const Packet& packet);
    void HandleBagItems(const Packet& packet);
    void HandleDeleteItem(const Packet& packet);
    void HandleUpdateItem(const Packet& packet);
    void HandleDropItemResult(const Packet& packet);
    void HandleGoldChanged(const Packet& packet);
    void HandleWeightChanged(const Packet& packet);
    void HandleDuraChanged(const Packet& packet);

    // Door packet handlers
    void HandleOpenDoorResult(const Packet& packet);
    void HandleCloseDoor(const Packet& packet);

    // Magic packet handlers
    void HandleAddMagic(const Packet& packet);
    void HandleSendMyMagic(const Packet& packet);
    void HandleDeleteMagic(const Packet& packet);
    void HandleMagicFire(const Packet& packet);
    void HandleMagicFireFail(const Packet& packet);
    void HandleMagicLevelExp(const Packet& packet);

    // Status packet handlers
    void HandleUserState(const Packet& packet);
    void HandleHealthSpellChanged(const Packet& packet);
    void HandleAbility(const Packet& packet);
    void HandleSubAbility(const Packet& packet);
    void HandleAdjustBonus(const Packet& packet);
    void HandleOpenHealth(const Packet& packet);
    void HandleCloseHealth(const Packet& packet);
    void HandleChangeFace(const Packet& packet);
    void HandleBreakWeapon(const Packet& packet);

    // Game flow packet handlers
    void HandleStartPlay(const Packet& packet);
    void HandleReconnect(const Packet& packet);

public:
    /**
     * @brief Constructor
     * @param app Pointer to the application
     * @param networkManager Network manager
     */
    PlayState(Application* app, std::shared_ptr<NetworkManager> networkManager = nullptr);

    /**
     * @brief Destructor
     */
    virtual ~PlayState();

    /**
     * @brief Called when entering the state
     */
    virtual void Enter() override;

    /**
     * @brief Called when exiting the state
     */
    virtual void Exit() override;

    /**
     * @brief Update the state
     * @param deltaTime Time elapsed since last frame in seconds
     */
    virtual void Update(float deltaTime) override;

    /**
     * @brief Render the state
     */
    virtual void Render() override;

    /**
     * @brief Handle SDL events
     * @param event SDL event to handle
     */
    virtual void HandleEvents(SDL_Event& event) override;

    /**
     * @brief Pause the game
     */
    void Pause();

    /**
     * @brief Resume the game
     */
    void Resume();

    /**
     * @brief Check if the game is paused
     * @return true if paused, false otherwise
     */
    bool IsPaused() const { return m_paused; }

    /**
     * @brief Set whether to view fog
     * @param viewFog Whether to view fog
     */
    void SetViewFog(bool viewFog);

    /**
     * @brief Check if fog is visible
     * @return true if fog is visible, false otherwise
     */
    bool IsViewFog() const;

    /**
     * @brief Add a light to the light map
     * @param x X coordinate in map coordinates
     * @param y Y coordinate in map coordinates
     * @param shiftX X shift
     * @param shiftY Y shift
     * @param light Light level (0-5)
     * @param nocheck Whether to skip overlap check
     */
    void AddLight(int x, int y, int shiftX, int shiftY, int light, bool nocheck);
};
