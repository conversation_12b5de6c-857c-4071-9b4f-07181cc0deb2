# MirServer Manager架构设计方案

## 文档信息
- **文档版本**: 1.0
- **创建日期**: 2024年
- **维护者**: 开发团队
- **最后更新**: 当前日期

## 1. 概述

本文档描述了MirServer重构版本的Manager架构设计方案，旨在将原版Delphi的单体架构重构为现代化的模块化Manager架构。

### 1.1 设计目标
- **模块化**: 将功能按职责分离到不同的Manager中
- **低耦合**: 通过接口和事件实现Manager间通信
- **高内聚**: 相关功能集中在同一Manager内
- **可扩展**: 支持插件和配置扩展
- **线程安全**: 支持多线程环境下的并发访问

### 1.2 原版问题分析
- `TBaseObject` 类25127行代码，职责过多
- `TUserEngine` 类3575行代码，功能混杂
- 功能模块间紧密耦合，难以独立修改
- 缺乏统一的事件和配置管理机制

## 2. 当前Manager模块现状

### 2.1 已实现的Manager
- ✅ **UserEngine**: 用户管理
- ✅ **MapManager**: 地图管理
- ✅ **ItemManager**: 物品管理
- ✅ **MagicManager**: 魔法管理
- ✅ **NPCManager**: NPC管理
- ✅ **MonsterManager**: 怪物管理
- ✅ **StorageManager**: 仓库管理
- ✅ **TradeManager**: 交易管理
- ✅ **QuestManager**: 任务管理
- ✅ **MiniMapManager**: 小地图管理
- ✅ **PKManager**: PK管理
- ✅ **RepairManager**: 修理管理
- ✅ **GroupManager**: 队伍管理
- ✅ **GuildManager**: 行会管理
- ✅ **CastleManager**: 城堡管理

### 2.2 待实现的Manager
根据原版功能分析，还需要以下Manager模块：

## 3. 缺失Manager模块设计

### 3.1 配置管理相关

#### GameConfigManager
```cpp
class GameConfigManager : public IManager {
private:
    ServerConfig m_serverConfig;
    GameRuleConfig m_gameRuleConfig;
    RateConfig m_rateConfig;
    MapConfig m_mapConfig;
    
public:
    bool LoadConfig(const std::string& configPath);
    bool SaveConfig(const std::string& configPath);
    void ReloadConfig();
    
    const ServerConfig& GetServerConfig() const;
    const GameRuleConfig& GetGameRuleConfig() const;
    const RateConfig& GetRateConfig() const;
};
```

**分割原因**: 原版`M2Share.pas`和`GameConfig.pas`包含大量配置管理代码，需要统一管理。

#### GameCommandManager
```cpp
class GameCommandManager : public IManager {
private:
    struct GameCommand {
        std::string command;
        int minPermission;
        int maxPermission;
        CommandHandler handler;
    };
    
    std::unordered_map<std::string, GameCommand> m_commands;
    
public:
    bool RegisterCommand(const std::string& cmd, int minPerm, CommandHandler handler);
    bool ExecuteCommand(PlayObject* player, const std::string& cmd, const std::vector<std::string>& args);
    bool LoadCommands(const std::string& configFile);
};
```

**分割原因**: 原版`GameCommand.pas`管理所有GM命令，需要独立模块处理。

### 3.2 数据管理相关

#### DatabaseManager
```cpp
class DatabaseManager : public IManager {
private:
    std::unique_ptr<DatabaseConnection> m_connection;
    std::unique_ptr<QueryBuilder> m_queryBuilder;
    
public:
    bool LoadPlayerData(const std::string& account, const std::string& charName, PlayerData& data);
    bool SavePlayerData(const std::string& account, const std::string& charName, const PlayerData& data);
    bool LoadStdItems();
    bool LoadMagicData();
    bool LoadMonsterData();
    
    class Transaction BeginTransaction();
    bool Commit(Transaction& trans);
    bool Rollback(Transaction& trans);
};
```

**分割原因**: 原版`LocalDB.pas`、`RunDB.pas`包含复杂的数据库操作，需要统一管理。

#### SaveDataManager
```cpp
class SaveDataManager : public IManager {
private:
    struct SaveTask {
        std::string playerName;
        PlayerData data;
        DWORD priority;
        std::chrono::steady_clock::time_point deadline;
    };
    
    std::priority_queue<SaveTask> m_saveQueue;
    std::thread m_saveThread;
    std::atomic<bool> m_running;
    
public:
    void QueueSave(const std::string& playerName, const PlayerData& data, DWORD priority = 0);
    void SetAutoSave(bool enabled, DWORD interval);
    void FlushAll();
    bool IsQueueFull() const;
};
```

**分割原因**: 原版`FrnEngn.pas`负责数据持久化，需要异步处理提高性能。

### 3.3 事件与脚本管理

#### EventManager
```cpp
class EventManager : public IManager {
private:
    struct GameEvent {
        uint32_t eventId;
        EventType type;
        Point position;
        Environment* envir;
        DWORD startTime;
        DWORD duration;
        bool active;
        std::function<void()> callback;
    };
    
    std::vector<std::unique_ptr<GameEvent>> m_events;
    std::mutex m_eventMutex;
    
public:
    uint32_t CreateEvent(EventType type, Environment* envir, int x, int y, DWORD duration);
    bool RemoveEvent(uint32_t eventId);
    void ProcessEvents();
    
    uint32_t CreateMineEvent(Environment* envir, int x, int y);
    uint32_t CreateTimedEvent(DWORD delay, std::function<void()> callback);
};
```

**分割原因**: 原版`Event.pas`管理游戏事件系统，需要独立的事件管理机制。

#### ScriptManager
```cpp
class ScriptManager : public IManager {
private:
    struct ScriptEngine {
        std::string scriptPath;
        std::unordered_map<std::string, ScriptFunction> functions;
        std::unordered_map<std::string, std::string> variables;
    };
    
    std::unordered_map<std::string, std::unique_ptr<ScriptEngine>> m_scripts;
    
public:
    bool LoadScript(const std::string& npcName, const std::string& scriptPath);
    bool ExecuteScript(const std::string& npcName, PlayObject* player, const std::string& label);
    bool ReloadScript(const std::string& npcName);
    
    bool ProcessCondition(const std::string& condition, PlayObject* player);
    bool ProcessAction(const std::string& action, PlayObject* player);
};
```

**分割原因**: 原版NPC脚本处理分散在多个类中，需要统一的脚本引擎。

## 4. Manager间通信架构

### 4.1 接口设计

#### 核心接口定义
```cpp
// 通用Manager接口
class IManager {
public:
    virtual ~IManager() = default;
    virtual bool Initialize() = 0;
    virtual void Finalize() = 0;
    virtual void Update() = 0;
    virtual const std::string& GetManagerName() const = 0;
};

// 事件发布者接口
class IEventPublisher {
public:
    virtual void PublishEvent(const std::string& eventType, const EventData& data) = 0;
};

// 事件订阅者接口
class IEventSubscriber {
public:
    virtual void OnEvent(const std::string& eventType, const EventData& data) = 0;
};

// 玩家相关操作接口
class IPlayerProvider {
public:
    virtual PlayObject* FindPlayer(const std::string& playerName) = 0;
    virtual std::vector<PlayObject*> GetPlayersInRange(const Point& center, int range) = 0;
    virtual int GetOnlinePlayerCount() const = 0;
};

// 物品相关操作接口
class IItemProvider {
public:
    virtual const StdItem* GetStdItem(int itemIndex) const = 0;
    virtual UserItem CreateItem(int itemIndex) const = 0;
    virtual bool ValidateItem(const UserItem& item) const = 0;
};

// 地图相关操作接口
class IMapProvider {
public:
    virtual Environment* GetEnvironment(const std::string& mapName) = 0;
    virtual bool CanWalk(const std::string& mapName, int x, int y) = 0;
    virtual std::vector<BaseObject*> GetObjectsInRange(const std::string& mapName, 
                                                       const Point& center, int range) = 0;
};
```

### 4.2 事件系统设计

#### 事件数据结构
```cpp
// 事件数据基类
struct EventData {
    DWORD timestamp = GetCurrentTime();
    virtual ~EventData() = default;
};

// 具体事件数据类型
struct PlayerLoginEventData : public EventData {
    std::string playerName;
    PlayObject* player;
    DWORD loginTime;
};

struct ItemDropEventData : public EventData {
    std::string playerName;
    UserItem item;
    Point position;
    std::string mapName;
};

struct PlayerDeathEventData : public EventData {
    std::string playerName;
    std::string killerName;
    Point position;
    std::string mapName;
    int lostExp;
};
```

#### 事件总线实现
```cpp
class EventBus {
private:
    std::unordered_map<std::string, std::vector<EventHandler>> m_handlers;
    std::mutex m_mutex;
    std::queue<std::pair<std::string, std::unique_ptr<EventData>>> m_eventQueue;
    
public:
    void Subscribe(const std::string& eventType, EventHandler handler);
    void PublishAsync(const std::string& eventType, std::unique_ptr<EventData> data);
    void Publish(const std::string& eventType, const EventData& data);
    void ProcessEvents();
};
```

### 4.3 依赖注入容器

```cpp
class ServiceContainer {
private:
    std::unordered_map<std::string, std::shared_ptr<IManager>> m_managers;
    std::unique_ptr<EventBus> m_eventBus;

public:
    template<typename T>
    void RegisterManager(std::shared_ptr<T> manager);
    
    template<typename T>
    T* GetManager();
    
    template<typename Interface>
    Interface* GetService();
    
    EventBus* GetEventBus();
};
```

## 5. 分层架构设计

### 5.1 架构层次
```
第四层：系统服务Manager
├── NetworkManager        # 网络通信管理
├── SaveDataManager       # 数据保存管理  
├── SystemMonitor         # 系统监控
└── LogManager            # 日志管理

第三层：高级功能Manager  
├── EventManager          # 事件管理
├── ScriptManager         # 脚本管理
├── PluginManager         # 插件管理
└── TaskScheduler         # 定时任务

第二层：游戏逻辑Manager (已有)
├── ItemManager           # 物品管理
├── MapManager            # 地图管理
├── NPCManager            # NPC管理
├── MonsterManager        # 怪物管理
└── MagicManager          # 魔法管理

第一层：核心基础Manager
├── GameConfigManager     # 配置管理
├── GameCommandManager    # 命令管理
├── DatabaseManager       # 数据库管理
└── LogManager            # 日志管理
```

### 5.2 依赖关系原则
- 高层Manager可以依赖低层Manager的接口
- 同层Manager通过事件系统通信
- 核心Manager不依赖高层Manager

## 6. 实施建议

### 6.1 实施优先级

#### 第一阶段：核心基础Manager
1. **GameConfigManager** - 统一配置管理
2. **LogManager** - 统一日志系统
3. **DatabaseManager** - 统一数据访问

#### 第二阶段：系统服务Manager
1. **EventManager** - 建立事件系统
2. **SaveDataManager** - 优化数据保存
3. **NetworkManager** - 统一网络管理

#### 第三阶段：高级功能Manager
1. **ScriptManager** - NPC脚本系统
2. **TaskScheduler** - 定时任务管理
3. **SystemMonitor** - 系统监控

#### 第四阶段：扩展Manager
1. **PluginManager** - 插件系统
2. **GameCommandManager** - GM命令系统

### 6.2 迁移策略

#### 渐进式重构
1. 先创建接口定义
2. 实现新Manager，保持原代码运行
3. 逐步迁移功能到新Manager
4. 移除原有代码

#### 兼容性保证
1. 保持公开API不变
2. 内部实现逐步切换
3. 提供配置开关控制新旧系统

### 6.3 测试策略

#### 单元测试
- 每个Manager独立测试
- 使用Mock对象模拟依赖
- 接口合约测试

#### 集成测试
- Manager间协作测试
- 事件系统测试
- 性能回归测试

## 7. 技术规范

### 7.1 编码规范
- 使用现代C++特性（C++17及以上）
- RAII内存管理
- 异常安全保证
- 线程安全设计

### 7.2 命名约定
- Manager类以Manager结尾
- 接口以I开头
- 事件数据以EventData结尾
- 配置结构以Config结尾

### 7.3 错误处理
- 使用异常处理错误
- 提供错误码兼容性
- 详细的错误日志记录

## 8. 性能考虑

### 8.1 内存管理
- 智能指针管理生命周期
- 对象池减少内存分配
- 延迟初始化非关键组件

### 8.2 并发控制
- 读写锁优化并发读取
- 无锁数据结构用于高频操作
- 异步事件处理

### 8.3 缓存策略
- 配置信息缓存
- 常用数据预加载
- LRU缓存淘汰机制

## 9. 监控与维护

### 9.1 运行时监控
- Manager状态监控
- 性能指标收集
- 内存使用监控

### 9.2 调试支持
- 详细的调试日志
- 运行时配置修改
- 性能分析工具集成

## 10. 未来扩展

### 10.1 插件化架构
- 动态加载Manager
- 热更新支持
- 第三方扩展接口

### 10.2 分布式支持
- Manager间远程通信
- 负载均衡机制
- 故障转移支持

---

## 附录

### A. 参考资料
- 原版Delphi代码分析报告
- 现代C++设计模式
- 游戏服务器架构最佳实践

### B. 术语表
- **Manager**: 管理特定功能领域的模块
- **Provider**: 提供特定服务的接口
- **EventBus**: 事件总线，用于Manager间通信
- **ServiceContainer**: 服务容器，管理依赖注入

### C. 变更记录
| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| 1.0  | 2024 | 初始版本 | 开发团队 | 