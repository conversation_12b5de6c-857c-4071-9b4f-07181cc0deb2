#pragma once

#include "UIControl.h"
#include "../Graphics/Texture.h"
#include <memory>
#include <string>

/**
 * @enum TextAlignment
 * @brief Text alignment options
 */
enum class TextAlignment {
    LEFT,       ///< Left alignment
    CENTER,     ///< Center alignment
    RIGHT       ///< Right alignment
};

/**
 * @class Label
 * @brief Label UI control
 *
 * This class represents a label UI control, which displays text.
 */
class Label : public UIControl {
private:
    std::string m_text;                    ///< Label text
    SDL_Color m_textColor;                 ///< Text color
    TTF_Font* m_font;                      ///< Text font
    std::shared_ptr<Texture> m_texture;    ///< Text texture
    TextAlignment m_alignment;             ///< Text alignment
    bool m_dirty = true;                   ///< 标记是否需要重建纹理

    /**
     * @brief Create the text texture
     * @param renderer SDL renderer
     */
    void CreateTexture(SDL_Renderer* renderer);

public:
    /**
     * @brief Constructor with width and height
     * @param x X position
     * @param y Y position
     * @param width Width (default: 0, will use image width if available)
     * @param height Height (default: 0, will use image height if available)
     * @param text Label text
     * @param name Control name
     */
    Label(int x, int y, int width, int height, const std::string& text = "", const std::string& name = "");

    /**
     * @brief Constructor without width and height (will use image dimensions)
     * @param x X position
     * @param y Y position
     * @param text Label text
     * @param name Control name
     */
    Label(int x, int y, const std::string& text = "", const std::string& name = "");

    /**
     * @brief Destructor
     */
    virtual ~Label();

    /**
     * @brief Render the label
     * @param renderer SDL renderer
     */
    virtual void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Set the text
     * @param text Label text
     */
    void SetText(const std::string& text);

    /**
     * @brief Set the text color
     * @param color Text color
     */
    void SetTextColor(const SDL_Color& color);

    /**
     * @brief Set the font
     * @param font Text font
     */
    void SetFont(TTF_Font* font);

    /**
     * @brief Get the text
     * @return Label text
     */
    const std::string& GetText() const { return m_text; }

    /**
     * @brief Get the text color
     * @return Text color
     */
    const SDL_Color& GetTextColor() const { return m_textColor; }

    /**
     * @brief Get the font
     * @return Text font
     */
    TTF_Font* GetFont() const { return m_font; }

    /**
     * @brief Set the text alignment
     * @param alignment Text alignment
     */
    void SetAlignment(TextAlignment alignment) { m_alignment = alignment; }

    /**
     * @brief Get the text alignment
     * @return Text alignment
     */
    TextAlignment GetAlignment() const { return m_alignment; }
};
