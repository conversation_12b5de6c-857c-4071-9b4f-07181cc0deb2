cmake_minimum_required(VERSION 3.16)
project(SimpleGuildTest)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 创建可执行文件
add_executable(SimpleGuildTest SimpleGuildTest.cpp)

# 编译选项
if(MSVC)
    target_compile_options(SimpleGuildTest PRIVATE /W4)
else()
    target_compile_options(SimpleGuildTest PRIVATE -Wall -Wextra -pedantic)
endif()

# 定义宏
target_compile_definitions(SimpleGuildTest PRIVATE 
    _CRT_SECURE_NO_WARNINGS
    WIN32_LEAN_AND_MEAN
    NOMINMAX
)
