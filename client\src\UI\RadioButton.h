#pragma once

#include "UIControl.h"
#include <SDL2/SDL_ttf.h>
#include <string>
#include <vector>
#include <functional>

/**
 * @class RadioButton
 * @brief Radio button UI control
 *
 * This class represents a radio button UI control, which allows the user to select
 * one option from a group of options.
 */
class RadioButton : public UIControl {
private:
    std::string m_text;                 ///< Button text
    TTF_Font* m_font;                   ///< Font
    SDL_Color m_textColor;              ///< Text color
    SDL_Color m_backgroundColor;        ///< Background color
    SDL_Color m_borderColor;            ///< Border color
    SDL_Color m_checkColor;             ///< Check color
    bool m_checked;                     ///< Checked flag
    std::vector<RadioButton*> m_group;  ///< Group of radio buttons

    // Callbacks
    std::function<void()> m_onClick;    ///< Click callback

public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param text Button text
     * @param name Control name
     */
    RadioButton(int x, int y, int width, int height, const std::string& text = "", const std::string& name = "");

    /**
     * @brief Destructor
     */
    virtual ~RadioButton();

    /**
     * @brief Update the radio button
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    virtual void Update(int deltaTime) override;

    /**
     * @brief Render the radio button
     * @param renderer SDL renderer
     */
    virtual void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Handle SDL event
     * @param event SDL event
     * @return true if the event was handled, false otherwise
     */
    virtual bool HandleEvent(const SDL_Event& event);

    /**
     * @brief Get the button text
     * @return Button text
     */
    const std::string& GetText() const { return m_text; }

    /**
     * @brief Set the button text
     * @param text Button text
     */
    void SetText(const std::string& text) { m_text = text; }

    /**
     * @brief Set the font
     * @param font Font
     */
    void SetFont(TTF_Font* font) { m_font = font; }

    /**
     * @brief Set the text color
     * @param color Text color
     */
    void SetTextColor(const SDL_Color& color) { m_textColor = color; }

    /**
     * @brief Set the background color
     * @param color Background color
     */
    void SetBackgroundColor(const SDL_Color& color) { m_backgroundColor = color; }

    /**
     * @brief Set the border color
     * @param color Border color
     */
    void SetBorderColor(const SDL_Color& color) { m_borderColor = color; }

    /**
     * @brief Set the check color
     * @param color Check color
     */
    void SetCheckColor(const SDL_Color& color) { m_checkColor = color; }

    /**
     * @brief Check if the radio button is checked
     * @return true if checked, false otherwise
     */
    bool IsChecked() const { return m_checked; }

    /**
     * @brief Set the checked state
     * @param checked Checked state
     */
    void SetChecked(bool checked);

    /**
     * @brief Add a radio button to the group
     * @param radioButton Radio button to add
     */
    void AddToGroup(RadioButton* radioButton);

    /**
     * @brief Set the click callback
     * @param callback Click callback
     */
    void SetOnClick(std::function<void()> callback) { m_onClick = callback; }
};

