#pragma once

#include "ChatWindow.h"
#include "ChatInput.h"
#include "../Network/NetworkManager.h"
#include <memory>
#include <string>
#include <functional>
#include <SDL2/SDL.h>
#include <SDL2/SDL_ttf.h>

/**
 * @class ChatManager
 * @brief Manages chat UI and message handling
 */
class ChatManager {
private:
    std::shared_ptr<ChatWindow> m_chatWindow;       ///< Chat window
    std::shared_ptr<ChatInput> m_chatInput;         ///< Chat input
    std::shared_ptr<NetworkManager> m_networkManager;  ///< Network manager
    TTF_Font* m_font;                               ///< Font for rendering text
    std::string m_lastWhisperTarget;                ///< Last whisper target
    bool m_guildChatMode;                           ///< Whether guild chat mode is enabled
    
    /**
     * @brief Handle chat input submission
     * @param text The submitted text
     */
    void HandleChatInput(const std::string& text);
    
    /**
     * @brief Process chat command
     * @param command The command to process
     * @return true if the command was processed, false otherwise
     */
    bool ProcessCommand(const std::string& command);
    
public:
    /**
     * @brief Constructor
     * @param networkManager Network manager
     * @param font Font for rendering text
     */
    ChatManager(std::shared_ptr<NetworkManager> networkManager, TTF_Font* font);
    
    /**
     * @brief Destructor
     */
    ~ChatManager();
    
    /**
     * @brief Initialize the chat manager
     * @param x X position of the chat window
     * @param y Y position of the chat window
     * @param width Width of the chat window
     * @param height Height of the chat window
     * @param inputX X position of the chat input
     * @param inputY Y position of the chat input
     * @param inputWidth Width of the chat input
     * @param inputHeight Height of the chat input
     */
    void Initialize(int x, int y, int width, int height, 
                    int inputX, int inputY, int inputWidth, int inputHeight);
    
    /**
     * @brief Update the chat manager
     * @param deltaTime Time elapsed since last frame
     */
    void Update(int deltaTime);
    
    /**
     * @brief Render the chat manager
     * @param renderer The renderer to use
     */
    void Render(SDL_Renderer* renderer);
    
    /**
     * @brief Handle key down events
     * @param event The key event
     * @return true if the event was handled, false otherwise
     */
    bool OnKeyDown(const SDL_KeyboardEvent& event);
    
    /**
     * @brief Handle text input events
     * @param event The text input event
     * @return true if the event was handled, false otherwise
     */
    bool OnTextInput(const SDL_TextInputEvent& event);
    
    /**
     * @brief Handle mouse down events
     * @param event The mouse button event
     * @return true if the event was handled, false otherwise
     */
    bool OnMouseDown(const SDL_MouseButtonEvent& event);
    
    /**
     * @brief Handle mouse up events
     * @param event The mouse button event
     * @return true if the event was handled, false otherwise
     */
    bool OnMouseUp(const SDL_MouseButtonEvent& event);
    
    /**
     * @brief Handle mouse move events
     * @param event The mouse motion event
     * @return true if the event was handled, false otherwise
     */
    bool OnMouseMove(const SDL_MouseMotionEvent& event);
    
    /**
     * @brief Handle mouse wheel events
     * @param event The mouse wheel event
     * @return true if the event was handled, false otherwise
     */
    bool OnMouseWheel(const SDL_MouseWheelEvent& event);
    
    /**
     * @brief Add a normal chat message
     * @param senderId ID of the sender
     * @param senderName Name of the sender
     * @param message The message
     */
    void AddChatMessage(int senderId, const std::string& senderName, const std::string& message);
    
    /**
     * @brief Add a whisper message
     * @param senderName Name of the sender
     * @param message The message
     */
    void AddWhisperMessage(const std::string& senderName, const std::string& message);
    
    /**
     * @brief Add a guild message
     * @param senderName Name of the sender
     * @param message The message
     */
    void AddGuildMessage(const std::string& senderName, const std::string& message);
    
    /**
     * @brief Add a group message
     * @param senderName Name of the sender
     * @param message The message
     */
    void AddGroupMessage(const std::string& senderName, const std::string& message);
    
    /**
     * @brief Add a system message
     * @param message The message
     */
    void AddSystemMessage(const std::string& message);
    
    /**
     * @brief Add a cry message
     * @param message The message
     */
    void AddCryMessage(const std::string& message);
    
    /**
     * @brief Add a merchant message
     * @param senderId ID of the sender
     * @param senderName Name of the sender
     * @param message The message
     */
    void AddMerchantMessage(int senderId, const std::string& senderName, const std::string& message);
    
    /**
     * @brief Show the chat input
     */
    void ShowChatInput();
    
    /**
     * @brief Hide the chat input
     */
    void HideChatInput();
    
    /**
     * @brief Toggle guild chat mode
     */
    void ToggleGuildChatMode();
    
    /**
     * @brief Get the chat window
     * @return The chat window
     */
    std::shared_ptr<ChatWindow> GetChatWindow() const;
    
    /**
     * @brief Get the chat input
     * @return The chat input
     */
    std::shared_ptr<ChatInput> GetChatInput() const;
};

