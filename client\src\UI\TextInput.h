#pragma once

#include "UIControl.h"
#include "Label.h"  // For TextAlignment enum
#include "../Graphics/WILLoader.h"
#include "../Graphics/Texture.h"
#include <SDL2/SDL_ttf.h>
#include <string>
#include <memory>
#include "UIManager.h"
#include <memory>

/**
 * @class TextInput
 * @brief Text input UI control
 *
 * This class represents a text input UI control, which allows the user to enter text.
 */
class TextInput : public UIControl, public std::enable_shared_from_this<TextInput> {
private:
    std::string m_text;                 ///< Input text
    std::string m_displayText;          ///< Display text (for password mode)
    TTF_Font* m_font;                   ///< Font
    SDL_Color m_textColor;              ///< Text color
    SDL_Color m_backgroundColor;        ///< Background color
    SDL_Color m_borderColor;            ///< Border color
    TextAlignment m_alignment;          ///< Text alignment
    int m_maxLength;                    ///< Maximum text length
    bool m_passwordMode;                ///< Password mode flag
    bool m_focused;                     ///< Focus flag
    size_t m_cursorPosition;            ///< Cursor position
    int m_cursorBlinkTime;              ///< Cursor blink time
    bool m_cursorVisible;               ///< Cursor visibility flag

    // WIL resources
    std::shared_ptr<WILManager> m_wilManager;  ///< WIL manager
    std::string m_resourceFile;                ///< Resource file
    int m_backgroundImageIndex;                ///< Background image index
    std::shared_ptr<Texture> m_backgroundTexture; ///< Background texture

    // Add new member variables for IME support
    std::string m_compositionText;
    int m_compositionCursor;

    UIManager* m_uiManager = nullptr;

    std::string m_fontPath; // 字体路径
    bool m_autoFontSize = true; // 是否自动字号

public:
    /**
     * @brief Constructor with width and height
     * @param x X position
     * @param y Y position
     * @param width Width (default: 0, will use image width if available)
     * @param height Height (default: 0, will use image height if available)
     * @param text Initial text
     * @param name Control name
     */
    TextInput(int x, int y, int width, int height, const std::string& text = "", const std::string& name = "");

    /**
     * @brief Constructor without width and height (will use image dimensions)
     * @param x X position
     * @param y Y position
     * @param text Initial text
     * @param name Control name
     */
    TextInput(int x, int y, const std::string& text = "", const std::string& name = "");

    /**
     * @brief Destructor
     */
    virtual ~TextInput();

    /**
     * @brief Update the text input
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    virtual void Update(int deltaTime) override;

    /**
     * @brief Render the text input
     * @param renderer SDL renderer
     */
    virtual void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Handle SDL event
     * @param event SDL event
     * @return true if the event was handled, false otherwise
     */
    virtual bool HandleEvent(const SDL_Event& event) override;

    /**
     * @brief Update the display text
     * @return void
     * */
    void UpdateDisplayText();

    /**
     * @brief Get the text
     * @return Text
     */
    const std::string& GetText() const { return m_text; }

    /**
     * @brief Set the text
     * @param text Text
     */
    void SetText(const std::string& text);

    /**
     * @brief Set the font
     * @param font Font
     */
    void SetFont(TTF_Font* font);

    /**
     * @brief Set the text color
     * @param color Text color
     */
    void SetTextColor(const SDL_Color& color) { m_textColor = color; }

    /**
     * @brief Set the background color
     * @param color Background color
     */
    void SetBackgroundColor(const SDL_Color& color) { m_backgroundColor = color; }

    /**
     * @brief Set the border color
     * @param color Border color
     */
    void SetBorderColor(const SDL_Color& color) { m_borderColor = color; }

    /**
     * @brief Set the text alignment
     * @param alignment Text alignment
     */
    void SetAlignment(TextAlignment alignment) { m_alignment = alignment; }

    /**
     * @brief Set the maximum text length
     * @param maxLength Maximum text length
     */
    void SetMaxLength(int maxLength) { m_maxLength = maxLength; }

    /**
     * @brief Set password mode
     * @param passwordMode Password mode flag
     */
    void SetPasswordMode(bool passwordMode);

    /**
     * @brief Set focus
     * @param focused Focus flag
     */
    void SetFocused(bool focused);

    /**
     * @brief Check if the text input is focused
     * @return true if focused, false otherwise
     */
    bool IsFocused() const { return m_focused; }

    /**
     * @brief Focus the input and start text input
     */
    void Focus() {
        if(m_focused) return;
        m_focused = true;
        GetUIManager()->SetFocusedControl(shared_from_this());
        SDL_StartTextInput();
    }

    /**
     * @brief Unfocus the input and stop text input
     */
    void Unfocus() override {
        if (!m_focused) return;
        
        m_focused = false;
        GetUIManager()->ClearFocusedControl();
        SDL_StopTextInput();
        
    }

    /**
     * @brief Set the WIL manager
     * @param wilManager WIL manager
     */
    void SetWILManager(std::shared_ptr<WILManager> wilManager) { m_wilManager = wilManager; }

    /**
     * @brief Set the resource file
     * @param resourceFile Resource file
     */
    void SetResourceFile(const std::string& resourceFile) { m_resourceFile = resourceFile; }

    /**
     * @brief Set the background image index
     * @param index Background image index
     */
    void SetBackgroundImageIndex(int index) { m_backgroundImageIndex = index; }

    // Add new method to get the control's rectangle
    SDL_Rect GetRect() const {
        return {GetAbsoluteX(), GetAbsoluteY(), m_width, m_height};
    }

    void SetUIManager(UIManager* uiManager) { m_uiManager = uiManager; }

    void SetFontPath(const std::string& path);
    void SetAutoFontSize(bool autoSize);
    void UpdateAutoFont();

    void SetSize(int w, int h) override;

    virtual bool HandleMouseButton(Uint8 button, bool pressed, int x, int y) override;
    virtual bool HandleKey(SDL_Keycode key, bool pressed) override;

protected:
    // ... existing code ...

private:
    // ... existing code ...
};

