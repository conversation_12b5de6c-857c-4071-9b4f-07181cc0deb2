#include "ChangePasswordDialog.h"
#include "ResourcePaths.h"
#include "UIConstants.h"
#include <iostream>

ChangePasswordDialog::ChangePasswordDialog(int x, int y, int width, int height, const std::string& title, std::shared_ptr<WILManager> wilManager)
    : Dialog(x, y, width, height, title)
    , m_wilManager(wilManager)
    , m_dialogManager(std::make_shared<DialogManager>(nullptr))
{
    // Set WIL manager and resource file for the dialog
    SetWILManager(wilManager);
    SetResourceFile(ResourcePaths::MAIN);
    SetBackgroundImageIndex(UIConstants::DIALOG_BG_INDEX);
}

ChangePasswordDialog::~ChangePasswordDialog()
{
}

void ChangePasswordDialog::Initialize(SDL_Renderer* renderer)
{
    Dialog::Initialize(renderer);

    // Initialize dialog manager with renderer
    m_dialogManager = std::make_shared<DialogManager>(renderer);

    // Load font
    TTF_Font* font = TTF_OpenFont("assets/data/font.ttf", 16);
    if (!font) {
        std::cerr << "Failed to load font: " << TTF_GetError() << std::endl;
        return;
    }

    // Calculate positions
    int labelX = 20;
    int inputX = 150;
    int startY = 50;
    int lineHeight = 30;
    int inputWidth = m_width - inputX - 20;
    int inputHeight = 24;

    // Create title label
    m_titleLabel = std::make_shared<Label>(m_width / 2 - 100, 20, 200, 30, "Change Password");
    m_titleLabel->SetFont(font);
    m_titleLabel->SetTextColor({255, 255, 255, 255});
    m_titleLabel->SetAlignment(TextAlignment::CENTER);
    m_titleLabel->SetWILManager(m_wilManager);
    AddChild(m_titleLabel);

    // Create account label and input
    m_accountLabel = std::make_shared<Label>(labelX, startY, 100, 30, "Account:");
    m_accountLabel->SetFont(font);
    m_accountLabel->SetTextColor({255, 255, 255, 255});
    m_accountLabel->SetWILManager(m_wilManager);
    AddChild(m_accountLabel);

    m_accountInput = std::make_shared<TextInput>(inputX, startY, inputWidth, inputHeight);
    m_accountInput->SetFont(font);
    m_accountInput->SetTextColor({255, 255, 255, 255});
    m_accountInput->SetBackgroundColor({0, 0, 0, 255});
    m_accountInput->SetMaxLength(20);
    m_accountInput->SetWILManager(m_wilManager);
    m_accountInput->SetResourceFile(ResourcePaths::MAIN);
    m_accountInput->SetBackgroundImageIndex(UIConstants::TEXT_INPUT_BG_INDEX);
    AddChild(m_accountInput);

    // Create current password label and input
    m_currentPasswordLabel = std::make_shared<Label>(labelX, startY + lineHeight, 100, 30, "Current PW:");
    m_currentPasswordLabel->SetFont(font);
    m_currentPasswordLabel->SetTextColor({255, 255, 255, 255});
    m_currentPasswordLabel->SetWILManager(m_wilManager);
    AddChild(m_currentPasswordLabel);

    m_currentPasswordInput = std::make_shared<TextInput>(inputX, startY + lineHeight, inputWidth, inputHeight);
    m_currentPasswordInput->SetFont(font);
    m_currentPasswordInput->SetTextColor({255, 255, 255, 255});
    m_currentPasswordInput->SetBackgroundColor({0, 0, 0, 255});
    m_currentPasswordInput->SetMaxLength(20);
    m_currentPasswordInput->SetPasswordMode(true);
    m_currentPasswordInput->SetWILManager(m_wilManager);
    m_currentPasswordInput->SetResourceFile(ResourcePaths::MAIN);
    m_currentPasswordInput->SetBackgroundImageIndex(UIConstants::TEXT_INPUT_BG_INDEX);
    AddChild(m_currentPasswordInput);

    // Create new password label and input
    m_newPasswordLabel = std::make_shared<Label>(labelX, startY + lineHeight * 2, 100, 30, "New PW:");
    m_newPasswordLabel->SetFont(font);
    m_newPasswordLabel->SetTextColor({255, 255, 255, 255});
    m_newPasswordLabel->SetWILManager(m_wilManager);
    AddChild(m_newPasswordLabel);

    m_newPasswordInput = std::make_shared<TextInput>(inputX, startY + lineHeight * 2, inputWidth, inputHeight);
    m_newPasswordInput->SetFont(font);
    m_newPasswordInput->SetTextColor({255, 255, 255, 255});
    m_newPasswordInput->SetBackgroundColor({0, 0, 0, 255});
    m_newPasswordInput->SetMaxLength(20);
    m_newPasswordInput->SetPasswordMode(true);
    m_newPasswordInput->SetWILManager(m_wilManager);
    m_newPasswordInput->SetResourceFile(ResourcePaths::MAIN);
    m_newPasswordInput->SetBackgroundImageIndex(UIConstants::TEXT_INPUT_BG_INDEX);
    AddChild(m_newPasswordInput);

    // Create confirm password label and input
    m_confirmPasswordLabel = std::make_shared<Label>(labelX, startY + lineHeight * 3, 100, 30, "Confirm PW:");
    m_confirmPasswordLabel->SetFont(font);
    m_confirmPasswordLabel->SetTextColor({255, 255, 255, 255});
    m_confirmPasswordLabel->SetWILManager(m_wilManager);
    AddChild(m_confirmPasswordLabel);

    m_confirmPasswordInput = std::make_shared<TextInput>(inputX, startY + lineHeight * 3, inputWidth, inputHeight);
    m_confirmPasswordInput->SetFont(font);
    m_confirmPasswordInput->SetTextColor({255, 255, 255, 255});
    m_confirmPasswordInput->SetBackgroundColor({0, 0, 0, 255});
    m_confirmPasswordInput->SetMaxLength(20);
    m_confirmPasswordInput->SetPasswordMode(true);
    m_confirmPasswordInput->SetWILManager(m_wilManager);
    m_confirmPasswordInput->SetResourceFile(ResourcePaths::MAIN);
    m_confirmPasswordInput->SetBackgroundImageIndex(UIConstants::TEXT_INPUT_BG_INDEX);
    AddChild(m_confirmPasswordInput);

    // Create OK button
    int buttonY = startY + lineHeight * 4 + 10;
    m_okButton = std::make_shared<Button>(m_width / 2 - 110, buttonY, 100, 30, "OK");
    m_okButton->SetFont(font);
    m_okButton->SetTextColor({255, 255, 255, 255});
    m_okButton->SetOnClick([this]() { OnOkButtonClick(); });
    m_okButton->SetWILManager(m_wilManager);
    m_okButton->SetResourceFile(ResourcePaths::MAIN);
    m_okButton->SetNormalImageIndex(361);  // DLoginOk normal image index
    m_okButton->SetHoverImageIndex(362);   // DLoginOk hover image index
    m_okButton->SetPressedImageIndex(363); // DLoginOk pressed image index
    AddChild(m_okButton);

    // Create Cancel button
    m_cancelButton = std::make_shared<Button>(m_width / 2 + 10, buttonY, 100, 30, "Cancel");
    m_cancelButton->SetFont(font);
    m_cancelButton->SetTextColor({255, 255, 255, 255});
    m_cancelButton->SetOnClick([this]() { OnCancelButtonClick(); });
    m_cancelButton->SetWILManager(m_wilManager);
    m_cancelButton->SetResourceFile(ResourcePaths::MAIN);
    m_cancelButton->SetNormalImageIndex(64);  // DLoginClose normal image index
    m_cancelButton->SetHoverImageIndex(65);   // DLoginClose hover image index
    m_cancelButton->SetPressedImageIndex(66); // DLoginClose pressed image index
    AddChild(m_cancelButton);

    // Close font
    TTF_CloseFont(font);
}

void ChangePasswordDialog::SetOnOkCallback(std::function<void(const std::string&, const std::string&, const std::string&)> callback)
{
    m_onOkCallback = callback;
}

void ChangePasswordDialog::SetOnCancelCallback(std::function<void()> callback)
{
    m_onCancelCallback = callback;
}

std::string ChangePasswordDialog::GetAccountName() const
{
    return m_accountInput ? m_accountInput->GetText() : "";
}

std::string ChangePasswordDialog::GetCurrentPassword() const
{
    return m_currentPasswordInput ? m_currentPasswordInput->GetText() : "";
}

std::string ChangePasswordDialog::GetNewPassword() const
{
    return m_newPasswordInput ? m_newPasswordInput->GetText() : "";
}

std::string ChangePasswordDialog::GetConfirmPassword() const
{
    return m_confirmPasswordInput ? m_confirmPasswordInput->GetText() : "";
}

void ChangePasswordDialog::OnOkButtonClick()
{
    // Validate inputs
    if (GetAccountName().empty()) {
        // Show error message
        m_dialogManager->ShowMessageDialog("Error", "Please enter an account name", MessageType::ERROR);
        return;
    }

    if (GetCurrentPassword().empty()) {
        // Show error message
        m_dialogManager->ShowMessageDialog("Error", "Please enter your current password", MessageType::ERROR);
        return;
    }

    if (GetNewPassword().empty()) {
        // Show error message
        m_dialogManager->ShowMessageDialog("Error", "Please enter a new password", MessageType::ERROR);
        return;
    }

    if (GetNewPassword() != GetConfirmPassword()) {
        // Show error message
        m_dialogManager->ShowMessageDialog("Error", "New passwords do not match", MessageType::ERROR);
        return;
    }

    // Call callback
    if (m_onOkCallback) {
        m_onOkCallback(
            GetAccountName(),
            GetCurrentPassword(),
            GetNewPassword()
        );
    }

    // Hide dialog
    SetVisible(false);
}

void ChangePasswordDialog::OnCancelButtonClick()
{
    // Call callback
    if (m_onCancelCallback) {
        m_onCancelCallback();
    }

    // Hide dialog
    SetVisible(false);
}
