#include "SkillPanel.h"
#include "Button.h"
#include "Label.h"
#include "UIConstants.h"
#include "UILayout.h"
#include "HotkeyConfigUI.h"
#include "UIManager.h"
#include "ResourcePaths.h"
#include <iostream>
#include <sstream>

SkillPanel::SkillPanel(int x, int y, int width, int height, std::shared_ptr<Player> player, std::shared_ptr<SkillManager> skillManager)
    : UIControl(x, y, width, height, "SkillPanel")
    , m_player(player)
    , m_skillManager(skillManager)
    , m_currentPage(0)
    , m_totalPages(1)
    , m_skillsPerPage(10)
    , m_isDragging(false)
    , m_draggedSkillId(0)
    , m_dragX(0)
    , m_dragY(0)
    , m_resourceFile(ResourcePaths::MAGIC_ICON)
{
    // Create UI controls
    CreateControls();

    // Hide by default
    SetVisible(false);
}

SkillPanel::~SkillPanel()
{
}

void SkillPanel::CreateControls()
{
    // Set resource file for UI elements
    SetResourceFile(ResourcePaths::INTERFACE);

    // Create background (using normal image index)
    SetNormalImageIndex(UIConstants::SKILL_UI_BG_INDEX);

    // Create close button
    m_closeButton = std::make_shared<Button>(
        m_x + m_width - 30,
        m_y + 10,
        20,
        20,
        "X"
    );
    m_closeButton->SetResourceFile(ResourcePaths::INTERFACE);
    m_closeButton->SetResourceIndices(
        ResourcePaths::INTERFACE,
        UIConstants::SKILL_PANEL_CLOSE_BTN_NORMAL,
        UIConstants::SKILL_PANEL_CLOSE_BTN_HOVER,
        UIConstants::SKILL_PANEL_CLOSE_BTN_PRESSED,
        UIConstants::SKILL_PANEL_CLOSE_BTN_DISABLED
    );
    m_closeButton->SetOnClick([this]() { OnCloseButtonClick(nullptr); });
    AddChild(m_closeButton);

    // Create configure hotkeys button
    m_configHotkeysButton = std::make_shared<Button>(
        m_x + m_width - 100,
        m_y + m_height - 40,
        80,
        30,
        "Hotkeys"
    );
    m_configHotkeysButton->SetResourceFile(ResourcePaths::INTERFACE);
    m_configHotkeysButton->SetResourceIndices(ResourcePaths::INTERFACE, 105, 106, 107, 108); // Placeholder indices
    m_configHotkeysButton->SetOnClick([this]() { OnConfigHotkeysButtonClick(nullptr); });
    AddChild(m_configHotkeysButton);

    // Create page navigation buttons
    m_prevPageButton = std::make_shared<Button>(
        m_x + 20,
        m_y + m_height - 40,
        30,
        30,
        "<"
    );
    m_prevPageButton->SetResourceFile(ResourcePaths::INTERFACE);
    m_prevPageButton->SetResourceIndices(ResourcePaths::INTERFACE, 109, 110, 111, 112); // Placeholder indices
    m_prevPageButton->SetOnClick([this]() { OnPrevPageButtonClick(nullptr); });
    AddChild(m_prevPageButton);

    m_nextPageButton = std::make_shared<Button>(
        m_x + 100,
        m_y + m_height - 40,
        30,
        30,
        ">"
    );
    m_nextPageButton->SetResourceFile(ResourcePaths::INTERFACE);
    m_nextPageButton->SetResourceIndices(ResourcePaths::INTERFACE, 113, 114, 115, 116); // Placeholder indices
    m_nextPageButton->SetOnClick([this]() { OnNextPageButtonClick(nullptr); });
    AddChild(m_nextPageButton);

    // Create page label
    m_pageLabel = std::make_shared<Label>(
        m_x + 60,
        m_y + m_height - 35,
        30,
        20,
        "1/1"
    );
    m_pageLabel->SetTextColor({255, 255, 255, 255});
    AddChild(m_pageLabel);

    // Create skill buttons and labels
    for (int i = 0; i < m_skillsPerPage; i++) {
        int row = i / 2;
        int col = i % 2;

        int buttonX = m_x + 20 + col * (UILayout::SkillUI::SKILL_ITEM_WIDTH + 10);
        int buttonY = m_y + 50 + row * (UILayout::SkillUI::SKILL_ITEM_HEIGHT + 10);

        auto skillButton = std::make_shared<Button>(
            buttonX,
            buttonY,
            UILayout::SkillUI::SKILL_ITEM_WIDTH,
            UILayout::SkillUI::SKILL_ITEM_HEIGHT,
            ""
        );
        skillButton->SetResourceFile(ResourcePaths::MAGIC_ICON);
        // Store index in user data
        skillButton->SetUserData(i);
        skillButton->SetOnClick([this, i]() {
            // Find the button and get its user data
            if (i < m_skillButtons.size()) {
                auto button = m_skillButtons[i];
                OnSkillButtonClick(button.get());
            }
        });
        skillButton->Hide(); // Hide initially
        AddChild(skillButton);
        m_skillButtons.push_back(skillButton);

        auto skillLabel = std::make_shared<Label>(
            buttonX + UILayout::SkillUI::SKILL_ITEM_WIDTH + 5,
            buttonY + (UILayout::SkillUI::SKILL_ITEM_HEIGHT - 20) / 2,
            UILayout::SkillUI::SKILL_ITEM_WIDTH * 2,
            20,
            ""
        );
        skillLabel->SetTextColor({255, 255, 255, 255});
        skillLabel->Hide(); // Hide initially
        AddChild(skillLabel);
        m_skillLabels.push_back(skillLabel);
    }
}

void SkillPanel::Update(int deltaTime)
{
    // Update base class
    UIControl::Update(deltaTime);
}

void SkillPanel::Render(SDL_Renderer* renderer)
{
    // Skip if not visible
    if (!m_visible) {
        return;
    }

    // Render background and controls
    UIControl::Render(renderer);

    // Render dragged skill if dragging
    if (m_isDragging && m_draggedSkillId > 0) {
        // Get skill
        auto skill = m_skillManager->GetSkill(m_draggedSkillId);
        if (skill) {
            // Render skill icon at drag position
            int imageIndex = 200 + skill->GetEffectTypeValue() * 100 + skill->GetEffectValue(); // Placeholder calculation

            // Use the WILManager to get the surface and render it
            if (m_wilManager) {
                SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, imageIndex);
                if (surface) {
                    // Create a temporary texture from the surface
                    SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
                    if (texture) {
                        // Render the texture
                        SDL_Rect destRect = {m_dragX - surface->w / 2, m_dragY - surface->h / 2, surface->w, surface->h};
                        SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                        // Free the texture
                        SDL_DestroyTexture(texture);
                    }
                } else {
                    // Fallback to a placeholder rectangle if surface not found
                    SDL_Rect rect = {m_dragX - 20, m_dragY - 20, 40, 40};
                    SDL_SetRenderDrawColor(renderer, 255, 255, 0, 200); // Yellow with alpha
                    SDL_RenderFillRect(renderer, &rect);
                }
            } else {
                // Fallback to a placeholder rectangle if WILManager not available
                SDL_Rect rect = {m_dragX - 20, m_dragY - 20, 40, 40};
                SDL_SetRenderDrawColor(renderer, 255, 255, 0, 200); // Yellow with alpha
                SDL_RenderFillRect(renderer, &rect);
            }
        }
    }
}

void SkillPanel::RefreshSkillList()
{
    if (!m_player) {
        return;
    }

    // Get player skills
    auto skills = m_player->GetSkills();

    // Calculate total pages
    m_totalPages = (skills.size() + m_skillsPerPage - 1) / m_skillsPerPage;
    if (m_totalPages == 0) {
        m_totalPages = 1;
    }

    // Ensure current page is valid
    if (m_currentPage >= m_totalPages) {
        m_currentPage = m_totalPages - 1;
    }

    // Update page label
    m_pageLabel->SetText(std::to_string(m_currentPage + 1) + "/" + std::to_string(m_totalPages));

    // Hide all skill buttons and labels
    for (auto& button : m_skillButtons) {
        button->Hide();
    }
    for (auto& label : m_skillLabels) {
        label->Hide();
    }

    // Calculate start index for current page
    int startIndex = m_currentPage * m_skillsPerPage;
    int index = 0;

    // Show skills for current page
    for (const auto& skillPair : skills) {
        if (index >= startIndex && index < startIndex + m_skillsPerPage) {
            int buttonIndex = index - startIndex;

            if (buttonIndex < m_skillButtons.size()) {
                // Get the skill from the pair
                auto skill = skillPair.second;

                // Set button properties
                auto button = m_skillButtons[buttonIndex];
                button->SetUserData(skill->GetId()); // Store skill ID in user data
                button->Show();

                // Set button image based on skill type
                int imageIndex = 200 + skill->GetEffectTypeValue() * 100 + skill->GetEffectValue(); // Placeholder calculation
                button->SetNormalImageIndex(imageIndex);

                // Set label text
                auto label = m_skillLabels[buttonIndex];
                label->SetText(skill->GetName() + " (Lv." + std::to_string(skill->GetLevel()) + ")");
                label->Show();
            }
        }
        index++;
    }

    // Update page navigation
    UpdatePageNavigation();
}

void SkillPanel::UpdatePageNavigation()
{
    // Enable/disable previous page button
    m_prevPageButton->SetEnabled(m_currentPage > 0);

    // Enable/disable next page button
    m_nextPageButton->SetEnabled(m_currentPage < m_totalPages - 1);
}

bool SkillPanel::HandleMouseButton(Uint8 button, bool pressed, int x, int y)
{
    // Skip if not visible
    if (!m_visible) {
        return false;
    }

    // Check if a child control handled the event
    if (UIControl::HandleMouseButton(button, pressed, x, y)) {
        return true;
    }

    // Handle left mouse button
    if (button == SDL_BUTTON_LEFT) {
        if (!pressed && m_isDragging) {
            // Mouse button released while dragging
            m_isDragging = false;

            // TODO: Handle dropping the skill onto a hotkey slot

            return true;
        }
    }

    return false;
}

bool SkillPanel::HandleMouseMotion(int x, int y)
{
    // Skip if not visible
    if (!m_visible) {
        return false;
    }

    // Check if a child control handled the event
    if (UIControl::HandleMouseMotion(x, y)) {
        return true;
    }

    // Update drag position if dragging
    if (m_isDragging) {
        m_dragX = x;
        m_dragY = y;
        return true;
    }

    return false;
}

bool SkillPanel::HandleKey(SDL_Keycode key, bool pressed)
{
    // Skip if not visible
    if (!m_visible) {
        return false;
    }

    // Check if a child control handled the event
    if (UIControl::HandleKey(key, pressed)) {
        return true;
    }

    // Handle escape key to close the UI
    if (key == SDLK_ESCAPE && pressed) {
        Hide();
        return true;
    }

    return false;
}

void SkillPanel::Show()
{
    SetVisible(true);
    RefreshSkillList();
}

void SkillPanel::Hide()
{
    SetVisible(false);
    m_isDragging = false;
    m_draggedSkillId = 0;
}

void SkillPanel::OnSkillButtonClick(UIControl* control)
{
    Button* button = dynamic_cast<Button*>(control);
    if (button) {
        int skillId = static_cast<int>(button->GetUserData());

        // Start dragging the skill
        m_isDragging = true;
        m_draggedSkillId = skillId;
        m_dragX = button->GetX() + button->GetWidth() / 2;
        m_dragY = button->GetY() + button->GetHeight() / 2;

        std::cout << "Skill clicked: " << skillId << std::endl;
    }
}

void SkillPanel::OnCloseButtonClick(UIControl* control)
{
    Hide();
}

void SkillPanel::OnConfigHotkeysButtonClick(UIControl* control)
{
    // Hide skill UI and show hotkey configuration UI
    Hide();

    // Find the HotkeyConfigUI in the parent hierarchy
    auto parent = GetParent();
    while (parent) {
        auto uiManager = dynamic_cast<UIManager*>(parent);
        if (uiManager) {
            // Find the HotkeyConfigUI in the UI manager's controls
            for (auto& childControl : uiManager->GetControls()) {
                auto hotkeyUI = std::dynamic_pointer_cast<HotkeyConfigUI>(childControl);
                if (hotkeyUI) {
                    hotkeyUI->Show();
                    return;
                }
            }
            break;
        }
        parent = parent->GetParent();
    }

    std::cout << "HotkeyConfigUI not found" << std::endl;
}

void SkillPanel::OnPrevPageButtonClick(UIControl* control)
{
    if (m_currentPage > 0) {
        m_currentPage--;
        RefreshSkillList();
    }
}

void SkillPanel::OnNextPageButtonClick(UIControl* control)
{
    if (m_currentPage < m_totalPages - 1) {
        m_currentPage++;
        RefreshSkillList();
    }
}
