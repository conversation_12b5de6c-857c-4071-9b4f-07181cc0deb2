@echo off
echo Building SDL test program...

REM 创建构建目录
if not exist test_build mkdir test_build
cd test_build

REM 创建CMakeLists.txt文件
echo cmake_minimum_required(VERSION 3.10) > CMakeLists.txt
echo project(SDL_Test) >> CMakeLists.txt
echo. >> CMakeLists.txt
echo set(CMAKE_CXX_STANDARD 17) >> CMakeLists.txt
echo set(CMAKE_CXX_STANDARD_REQUIRED ON) >> CMakeLists.txt
echo. >> CMakeLists.txt
echo set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin) >> CMakeLists.txt
echo. >> CMakeLists.txt
echo set(SDL2_PATH "C:/msys64/mingw64" CACHE PATH "SDL2安装路径") >> CMakeLists.txt
echo set(SDL2_INCLUDE_DIRS "${SDL2_PATH}/include/SDL2") >> CMakeLists.txt
echo set(SDL2_LIBRARIES "${SDL2_PATH}/lib/libSDL2.dll.a;${SDL2_PATH}/lib/libSDL2main.a") >> CMakeLists.txt
echo. >> CMakeLists.txt
echo include_directories(${SDL2_INCLUDE_DIRS}) >> CMakeLists.txt
echo. >> CMakeLists.txt
echo add_executable(test_sdl WIN32 ../test_sdl.cpp) >> CMakeLists.txt
echo. >> CMakeLists.txt
echo target_link_libraries(test_sdl ${SDL2_LIBRARIES} -lmingw32) >> CMakeLists.txt

REM 使用CMake生成构建文件
cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Debug .

REM 构建项目
cmake --build .

REM 运行测试程序
echo Running SDL test program...
bin\test_sdl.exe

REM 返回原目录
cd ..

echo Done.
pause 