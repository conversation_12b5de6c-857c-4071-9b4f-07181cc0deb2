cmake_minimum_required(VERSION 3.15)
project(MirDelphi-Refactored)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Options
option(BUILD_CLIENT "Build client" ON)
option(BUILD_SERVER "Build server" ON)
option(BUILD_TESTS "Build tests" ON)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE "Debug" CACHE STRING "Choose the type of build (Debug or Release)" FORCE)
endif()

# Output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Add subdirectories
if(BUILD_CLIENT)
    add_subdirectory(client)
endif()

if(BUILD_SERVER AND EXISTS "${CMAKE_SOURCE_DIR}/server/CMakeLists.txt")
    add_subdirectory(server)
endif()

# Common library (if needed in the future)
if(EXISTS "${CMAKE_SOURCE_DIR}/common/CMakeLists.txt")
    add_subdirectory(common)
endif()