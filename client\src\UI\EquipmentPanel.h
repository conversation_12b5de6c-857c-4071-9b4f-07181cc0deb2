#pragma once

#include "UIControl.h"
#include "Font.h"
#include "Button.h"
#include "Label.h"
#include "../Item/Inventory.h"
#include "../Actor/Player.h"
#include <memory>
#include <vector>
#include <string>
#include <SDL2/SDL.h>

/**
 * @class EquipmentPanel
 * @brief UI for displaying and managing character equipment
 *
 * This panel is integrated into the StatusPanel as a tab.
 * It follows the original Delphi project's structure where
 * StatusPanel contains tabs for different sections.
 */
class EquipmentPanel : public UIControl {
private:
    std::shared_ptr<Player> m_player;                  ///< Player reference
    std::shared_ptr<Button> m_closeButton;             ///< Close button
    std::shared_ptr<Label> m_titleLabel;               ///< Title label
    SDL_Color m_textColor;                             ///< Text color
    SDL_Color m_textShadowColor;                       ///< Text shadow color
    SDL_Color m_slotColor;                             ///< Slot background color
    SDL_Color m_selectedSlotColor;                     ///< Selected slot background color
    SDL_Color m_borderColor;                           ///< Slot border color
    SDL_Color m_slotTypeColor;                         ///< Slot type indicator color
    SDL_Color m_hoverColor;                            ///< Hover color for slots
    int m_slotSize;                                    ///< Size of equipment slots
    int m_selectedSlot;                                ///< Currently selected slot
    int m_hoverSlot;                                   ///< Slot being hovered over
    bool m_isDragging;                                 ///< Whether an item is being dragged
    Inventory::EquipmentSlot m_draggedSlot;            ///< Slot of the item being dragged
    int m_dragX;                                       ///< X position of the dragged item
    int m_dragY;                                       ///< Y position of the dragged item
    std::string m_resourceFile;                        ///< Resource file for items
    std::shared_ptr<Font> m_font;                      ///< Font for rendering text

    // 装备属性显示
    bool m_showItemInfo;                               ///< Whether to show item info
    int m_infoItemSlot;                                ///< Slot of the item to show info for

    // 回调函数
    std::function<void(Inventory::EquipmentSlot)> m_onItemUnequipped;  ///< Called when an item is unequipped
    std::function<void(std::shared_ptr<Item>, Inventory::EquipmentSlot)> m_onItemEquipped;  ///< Called when an item is equipped
    std::function<void(std::shared_ptr<Item>)> m_onItemInfoRequested;  ///< Called when item info is requested

public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param player Player reference
     */
    EquipmentPanel(int x, int y, int width, int height, std::shared_ptr<Player> player);

    /**
     * @brief Destructor
     */
    virtual ~EquipmentPanel();

    /**
     * @brief Create UI controls
     */
    void CreateControls();

    /**
     * @brief Update the UI
     * @param deltaTime Time since last update
     */
    void Update(int deltaTime) override;

    /**
     * @brief Render the UI
     * @param renderer SDL renderer
     */
    void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Get the position of an equipment slot
     * @param slot Equipment slot
     * @param x Output X position
     * @param y Output Y position
     */
    void GetSlotPosition(Inventory::EquipmentSlot slot, int& x, int& y) const;

    /**
     * @brief Render an item
     * @param renderer SDL renderer
     * @param item Item to render
     * @param x X position
     * @param y Y position
     * @param size Size of the item
     */
    void RenderItem(SDL_Renderer* renderer, const Item& item, int x, int y, int size);

    /**
     * @brief Render the character model with equipment
     * @param renderer SDL renderer
     */
    void RenderCharacterModel(SDL_Renderer* renderer);

    /**
     * @brief Handle mouse button events
     * @param button Mouse button
     * @param pressed Whether the button was pressed or released
     * @param x X position
     * @param y Y position
     * @return Whether the event was handled
     */
    bool HandleMouseButton(int button, bool pressed, int x, int y);

    /**
     * @brief Handle mouse motion events
     * @param x X position
     * @param y Y position
     * @param relX Relative X movement
     * @param relY Relative Y movement
     * @return Whether the event was handled
     */
    bool HandleMouseMotion(int x, int y, int relX, int relY);

    /**
     * @brief Handle key events
     * @param key Key code
     * @param pressed Whether the key was pressed or released
     * @return Whether the event was handled
     */
    bool HandleKey(SDL_Keycode key, bool pressed) override;

    /**
     * @brief Show the UI
     */
    void Show();

    /**
     * @brief Hide the UI
     */
    void Hide();

    /**
     * @brief Refresh the equipment display
     */
    void RefreshEquipment();

    /**
     * @brief Handle close button click
     * @param control Control that was clicked
     */
    void OnCloseButtonClick(UIControl* control);

    /**
     * @brief Set the callback for when an item is unequipped
     * @param callback Callback function
     */
    void SetOnItemUnequipped(std::function<void(Inventory::EquipmentSlot)> callback) {
        m_onItemUnequipped = callback;
    }

    /**
     * @brief Set the callback for when an item is equipped
     * @param callback Callback function
     */
    void SetOnItemEquipped(std::function<void(std::shared_ptr<Item>, Inventory::EquipmentSlot)> callback) {
        m_onItemEquipped = callback;
    }

    /**
     * @brief Set the callback for when item info is requested
     * @param callback Callback function
     */
    void SetOnItemInfoRequested(std::function<void(std::shared_ptr<Item>)> callback) {
        m_onItemInfoRequested = callback;
    }

    /**
     * @brief Try to equip an item to a specific slot
     * @param item Item to equip
     * @param slot Slot to equip to
     * @return true if the item was equipped, false otherwise
     */
    bool TryEquipItem(std::shared_ptr<Item> item, Inventory::EquipmentSlot slot);

    /**
     * @brief Render item information
     * @param renderer SDL renderer
     * @param item Item to render info for
     * @param x X position
     * @param y Y position
     */
    void RenderItemInfo(SDL_Renderer* renderer, const Item& item, int x, int y);

    /**
     * @brief Get the slot at the specified position
     * @param x X position
     * @param y Y position
     * @return Equipment slot, or -1 if no slot at the position
     */
    int GetSlotAt(int x, int y) const;

    /**
     * @brief Set the font for rendering text
     * @param font Font to use
     */
    void SetFont(std::shared_ptr<Font> font) {
        m_font = font;
    }
};

