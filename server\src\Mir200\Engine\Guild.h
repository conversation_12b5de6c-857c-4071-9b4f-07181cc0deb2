#pragma once

// Mir200 Guild - Guild management system
// Based on delphi/EM2Engine/Guild.pas - Following original project structure
// Phase 1 Implementation - Basic placeholder for M2Server integration

#include "Common/M2Share.h"
#include <string>

// Guild class - Guild management
class Guild {
private:
    std::string m_guild_name;
    bool m_active;
    bool m_initialized;
    
public:
    Guild(const std::string& guild_name);
    ~Guild();
    
    // Core lifecycle
    bool Initialize();
    void Finalize();
    
    // Processing
    void ProcessGuild();
    
    // State management
    bool IsActive() const { return m_active; }
    bool IsInitialized() const { return m_initialized; }
    
    // Guild management
    const std::string& GetGuildName() const { return m_guild_name; }
    
    // Data management
    void SaveGuildData();
};
