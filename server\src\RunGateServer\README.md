# RunGateServer - 运行网关服务器

## 概述

RunGateServer是MirServer项目中的运行网关服务器组件，负责在客户端和游戏服务器之间转发消息。它对应原版Delphi项目中的ERunGate模块，提供了完整的网关功能，包括连接管理、消息转发、速度控制、IP过滤和攻击防护。

## 主要功能

### 1. 消息转发
- 客户端与游戏服务器之间的双向消息转发
- 支持大消息的分块传输
- 流量控制和发送队列管理

### 2. 连接管理
- 支持最多1000个并发连接
- 连接超时检测和自动清理
- 每IP连接数限制

### 3. 速度控制
- 攻击、魔法、跑步、走路、转向速度检测
- 可配置的时间间隔和计数限制
- 支持警告和断开连接两种处理模式

### 4. IP过滤与攻击防护
- 永久和临时IP阻止列表
- 攻击检测和自动阻止
- 支持三种阻止模式：断开连接、临时阻止、永久阻止

### 5. 聊天消息过滤
- 敏感词过滤功能
- 可配置的替换字符
- 支持自定义敏感词列表

### 6. 配置管理
- INI格式配置文件
- 支持热重载配置
- 详细的配置选项

## 项目结构

```
RunGateServer/
├── RunGateServer.h          # 主类头文件
├── RunGateServer.cpp        # 主类实现
├── main.cpp                 # 主程序入口
├── CMakeLists.txt          # 构建脚本
├── config/
│   └── RunGateServer.ini.example  # 示例配置文件
└── README.md               # 项目文档
```

## 编译要求

- C++17 或更高版本
- CMake 3.12 或更高版本
- 支持的编译器：
  - Windows: Visual Studio 2017+, MinGW-w64
  - Linux: GCC 7+, Clang 6+

## 编译方法

### Windows (Visual Studio)
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 16 2019"
cmake --build . --config Release
```

### Windows (MinGW)
```bash
mkdir build
cd build
cmake .. -G "MinGW Makefiles"
cmake --build .
```

### Linux
```bash
mkdir build
cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
```

## 配置文件

配置文件使用INI格式，包含以下主要部分：

### [Server] - 服务器配置
- `ServerAddr`: 游戏服务器地址
- `ServerPort`: 游戏服务器端口

### [Gate] - 网关配置
- `GateAddr`: 网关监听地址
- `GatePort`: 网关监听端口

### [Limit] - 连接限制
- `MaxConnOfIP`: 每IP最大连接数
- `MaxClientPacketSize`: 客户端最大包大小
- `ClientSendBlockSize`: 发送块大小

### [Speed] - 速度控制
- `CheckHit/CheckSpell/CheckRun/CheckWalk/CheckTurn`: 是否检查各种动作速度
- `HitTime/SpellTime/RunTime/WalkTime/TurnTime`: 各种动作的时间间隔限制
- `SpeedControlMode`: 速度控制模式 (0=关闭, 1=警告, 2=断开)

### [Attack] - 攻击防护
- `AttackTick`: 攻击检测时间窗口
- `AttackCount`: 攻击计数阈值
- `BlockMethod`: 阻止方法 (0=断开, 1=临时阻止, 2=永久阻止)

## 运行方式

### 自动模式
程序启动后选择"auto"模式，服务器将自动启动并在后台运行：
```bash
./RunGateServer
# 输入: auto
```

### 交互模式
程序启动后选择"menu"模式，可以通过菜单控制服务器：
```bash
./RunGateServer
# 输入: menu
```

菜单选项：
1. Start Server - 启动服务器
2. Stop Server - 停止服务器
3. Show Status - 显示状态
4. Reload Configuration - 重新加载配置
5. Show Version Info - 显示版本信息
6. Close All Connections - 关闭所有连接
0. Exit - 退出程序

## 日志系统

RunGateServer使用分级日志系统：
- Level 0: INFO - 一般信息
- Level 1: ERROR - 错误信息
- Level 2: WARN - 警告信息

日志会输出到控制台，也可以配置输出到文件。

## 网络协议

RunGateServer使用自定义的网关协议与游戏服务器通信：

### 消息头格式
```cpp
struct RunGateMessageHeader {
    DWORD dwCode;           // 识别码 (0xAA55AA55)
    int nSocket;            // Socket句柄
    WORD wGSocketIdx;       // 网关Socket索引
    WORD wIdent;            // 消息标识
    WORD wUserListIndex;    // 用户列表索引
    int nLength;            // 数据长度
};
```

### 消息类型
- `GM_OPEN` (1): 打开连接
- `GM_CLOSE` (2): 关闭连接
- `GM_CHECKSERVER` (3): 服务器检查信号
- `GM_CHECKCLIENT` (4): 客户端检查信号
- `GM_DATA` (5): 转发游戏数据
- `GM_SERVERUSERINDEX` (6): 用户索引管理
- `GM_RECEIVE_OK` (7): 接收确认

## 性能优化

1. **多线程架构**: 使用独立的线程处理接收、发送和检查任务
2. **内存池**: 复用会话对象，减少内存分配开销
3. **消息队列**: 异步处理消息，提高并发性能
4. **流量控制**: 防止客户端发送过量数据

## 兼容性

RunGateServer与原版Delphi ERunGate完全兼容：
- 相同的网络协议格式
- 相同的配置选项
- 相同的功能特性
- 相同的性能表现

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查端口是否被占用
   - 检查防火墙设置
   - 验证配置文件格式

2. **客户端连接失败**
   - 检查网关监听地址和端口
   - 检查IP是否在阻止列表中
   - 验证游戏服务器连接状态

3. **消息转发异常**
   - 检查游戏服务器连接状态
   - 验证消息格式是否正确
   - 查看日志了解详细错误信息

### 调试模式

编译Debug版本可以获得更详细的调试信息：
```bash
cmake .. -DCMAKE_BUILD_TYPE=Debug
```

## 开发说明

### 代码结构
- `RunGateServer` 类是核心服务器类
- 使用现代C++特性，如智能指针、lambda表达式
- 遵循RAII原则，自动管理资源

### 扩展功能
要添加新功能，可以：
1. 在配置文件中添加新的配置项
2. 在`LoadConfig()`方法中加载配置
3. 在相应的处理方法中实现功能逻辑

### 线程安全
所有共享数据都使用适当的锁保护：
- `m_sessionMutex`: 保护会话数组
- `m_queueMutex`: 保护消息队列
- `m_ipMutex`: 保护IP管理数据

## 许可证

本项目遵循与MirServer项目相同的许可证。

## 联系方式

如有问题或建议，请通过GitHub Issues联系开发团队。 