#include "Packet.h"
#include <cstring>

Packet::Packet(PacketType type)
    : m_type(type)
    , m_readPos(0)
{
}

Packet::~Packet()
{
}

void Packet::Clear()
{
    m_data.clear();
    m_readPos = 0;
}

Packet& Packet::operator<<(uint8_t value)
{
    m_data.push_back(value);
    return *this;
}

Packet& Packet::operator<<(int8_t value)
{
    m_data.push_back(static_cast<uint8_t>(value));
    return *this;
}

Packet& Packet::operator<<(uint16_t value)
{
    m_data.push_back(static_cast<uint8_t>(value & 0xFF));
    m_data.push_back(static_cast<uint8_t>((value >> 8) & 0xFF));
    return *this;
}

Packet& Packet::operator<<(int16_t value)
{
    return *this << static_cast<uint16_t>(value);
}

Packet& Packet::operator<<(uint32_t value)
{
    m_data.push_back(static_cast<uint8_t>(value & 0xFF));
    m_data.push_back(static_cast<uint8_t>((value >> 8) & 0xFF));
    m_data.push_back(static_cast<uint8_t>((value >> 16) & 0xFF));
    m_data.push_back(static_cast<uint8_t>((value >> 24) & 0xFF));
    return *this;
}

Packet& Packet::operator<<(int32_t value)
{
    return *this << static_cast<uint32_t>(value);
}

Packet& Packet::operator<<(uint64_t value)
{
    m_data.push_back(static_cast<uint8_t>(value & 0xFF));
    m_data.push_back(static_cast<uint8_t>((value >> 8) & 0xFF));
    m_data.push_back(static_cast<uint8_t>((value >> 16) & 0xFF));
    m_data.push_back(static_cast<uint8_t>((value >> 24) & 0xFF));
    m_data.push_back(static_cast<uint8_t>((value >> 32) & 0xFF));
    m_data.push_back(static_cast<uint8_t>((value >> 40) & 0xFF));
    m_data.push_back(static_cast<uint8_t>((value >> 48) & 0xFF));
    m_data.push_back(static_cast<uint8_t>((value >> 56) & 0xFF));
    return *this;
}

Packet& Packet::operator<<(int64_t value)
{
    return *this << static_cast<uint64_t>(value);
}

Packet& Packet::operator<<(bool value)
{
    m_data.push_back(value ? 1 : 0);
    return *this;
}

Packet& Packet::operator<<(float value)
{
    uint32_t temp;
    std::memcpy(&temp, &value, sizeof(value));
    return *this << temp;
}

Packet& Packet::operator<<(double value)
{
    uint64_t temp;
    std::memcpy(&temp, &value, sizeof(value));
    return *this << temp;
}

Packet& Packet::operator<<(const std::string& value)
{
    // Write string length
    *this << static_cast<uint16_t>(value.length());

    // Write string data
    for (char c : value) {
        m_data.push_back(static_cast<uint8_t>(c));
    }

    return *this;
}

Packet& Packet::operator>>(uint8_t& value) const
{
    if (m_readPos < m_data.size()) {
        value = m_data[m_readPos++];
    }
    return const_cast<Packet&>(*this);
}

Packet& Packet::operator>>(int8_t& value) const
{
    if (m_readPos < m_data.size()) {
        value = static_cast<int8_t>(m_data[m_readPos++]);
    }
    return const_cast<Packet&>(*this);
}

Packet& Packet::operator>>(uint16_t& value) const
{
    if (m_readPos + 1 < m_data.size()) {
        value = static_cast<uint16_t>(m_data[m_readPos]) |
                (static_cast<uint16_t>(m_data[m_readPos + 1]) << 8);
        m_readPos += 2;
    }
    return const_cast<Packet&>(*this);
}

Packet& Packet::operator>>(int16_t& value) const
{
    uint16_t temp;
    *this >> temp;
    value = static_cast<int16_t>(temp);
    return const_cast<Packet&>(*this);
}

Packet& Packet::operator>>(uint32_t& value) const
{
    if (m_readPos + 3 < m_data.size()) {
        value = static_cast<uint32_t>(m_data[m_readPos]) |
                (static_cast<uint32_t>(m_data[m_readPos + 1]) << 8) |
                (static_cast<uint32_t>(m_data[m_readPos + 2]) << 16) |
                (static_cast<uint32_t>(m_data[m_readPos + 3]) << 24);
        m_readPos += 4;
    }
    return const_cast<Packet&>(*this);
}

Packet& Packet::operator>>(int32_t& value) const
{
    uint32_t temp;
    *this >> temp;
    value = static_cast<int32_t>(temp);
    return const_cast<Packet&>(*this);
}

Packet& Packet::operator>>(uint64_t& value) const
{
    if (m_readPos + 7 < m_data.size()) {
        value = static_cast<uint64_t>(m_data[m_readPos]) |
                (static_cast<uint64_t>(m_data[m_readPos + 1]) << 8) |
                (static_cast<uint64_t>(m_data[m_readPos + 2]) << 16) |
                (static_cast<uint64_t>(m_data[m_readPos + 3]) << 24) |
                (static_cast<uint64_t>(m_data[m_readPos + 4]) << 32) |
                (static_cast<uint64_t>(m_data[m_readPos + 5]) << 40) |
                (static_cast<uint64_t>(m_data[m_readPos + 6]) << 48) |
                (static_cast<uint64_t>(m_data[m_readPos + 7]) << 56);
        m_readPos += 8;
    }
    return const_cast<Packet&>(*this);
}

Packet& Packet::operator>>(int64_t& value) const
{
    uint64_t temp;
    *this >> temp;
    value = static_cast<int64_t>(temp);
    return const_cast<Packet&>(*this);
}

Packet& Packet::operator>>(bool& value) const
{
    if (m_readPos < m_data.size()) {
        value = (m_data[m_readPos++] != 0);
    }
    return const_cast<Packet&>(*this);
}

Packet& Packet::operator>>(float& value) const
{
    uint32_t temp;
    *this >> temp;
    std::memcpy(&value, &temp, sizeof(value));
    return const_cast<Packet&>(*this);
}

Packet& Packet::operator>>(double& value) const
{
    uint64_t temp;
    *this >> temp;
    std::memcpy(&value, &temp, sizeof(value));
    return const_cast<Packet&>(*this);
}

Packet& Packet::operator>>(std::string& value) const
{
    value.clear();

    // Read string length
    uint16_t length;
    *this >> length;

    // Read string data
    if (m_readPos + length <= m_data.size()) {
        value.reserve(length);
        for (uint16_t i = 0; i < length; i++) {
            value.push_back(static_cast<char>(m_data[m_readPos++]));
        }
    }

    return const_cast<Packet&>(*this);
}
