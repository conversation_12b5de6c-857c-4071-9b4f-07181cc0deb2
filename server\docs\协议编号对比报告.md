# 传奇服务器协议编号对比报告

本报告核对了原项目（delphi目录中的Grobal2.pas）与重构server项目（PacketTypes.h）的所有协议编号，确保协议编号完全一致。

## 📋 对比方法
- **原版协议定义**: `delphi/Common/Grobal2.pas`
- **重构版协议定义**: `server/src/Protocol/PacketTypes.h`
- **对比范围**: 所有CM_、SM_、RM_、DB_、DBR_开头的协议常量

## ✅ 一致的协议编号

### 基础角色管理协议 (CM_100-104)
| 协议名 | 原版编号 | 重构版编号 | 状态 |
|--------|----------|------------|------|
| CM_QUERYCHR | 100 | 100 | ✅ 一致 |
| CM_NEWCHR | 101 | 101 | ✅ 一致 |
| CM_DELCHR | 102 | 102 | ✅ 一致 |
| CM_SELCHR | 103 | 103 | ✅ 一致 |
| CM_SELECTSERVER | 104 | 104 | ✅ 一致 |

### 数据库操作协议 (DB_/DBR_)
| 协议名 | 原版编号 | 重构版编号 | 状态 |
|--------|----------|------------|------|
| DB_LOADHUMANRCD | 1000 | 200 | ❌ **不一致** |
| DB_SAVEHUMANRCD | 1010 | 201 | ❌ **不一致** |
| DB_SAVEHUMANRCDEX | 1020 | 202 | ❌ **不一致** |
| DBR_LOADHUMANRCD | 1100 | 250 | ❌ **不一致** |
| DBR_SAVEHUMANRCD | 1101 | 251 | ❌ **不一致** |
| DBR_FAIL | 1102 | 255 | ❌ **不一致** |

### 游戏内基础操作协议 (CM_1000+)
| 协议名 | 原版编号 | 重构版编号 | 状态 |
|--------|----------|------------|------|
| CM_DROPITEM | 1000 | 1000 | ✅ 一致 |
| CM_PICKUP | 1001 | 1001 | ✅ 一致 |
| CM_OPENDOOR | 1002 | - | ❌ **重构版缺失** |
| CM_TAKEONITEM | 1003 | 1004 | ❌ **不一致** |
| CM_TAKEOFFITEM | 1004 | 1005 | ❌ **不一致** |
| CM_1005 | 1005 | - | ❌ **重构版缺失** |
| CM_EAT | 1006 | 1006 | ✅ 一致 |
| CM_BUTCH | 1007 | 1007 | ✅ 一致 |
| CM_MAGICKEYCHANGE | 1008 | 1008 | ✅ 一致 |
| CM_SOFTCLOSE | 1009 | - | ❌ **重构版缺失** |

### NPC交互协议 (CM_1010+)
| 协议名 | 原版编号 | 重构版编号 | 状态 |
|--------|----------|------------|------|
| CM_CLICKNPC | 1010 | 1010 | ✅ 一致 |
| CM_MERCHANTDLGSELECT | 1011 | 1011 | ✅ 一致 |
| CM_MERCHANTQUERYSELLPRICE | 1012 | 1012 | ✅ 一致 |
| CM_USERSELLITEM | 1013 | 1013 | ✅ 一致 |
| CM_USERBUYITEM | 1014 | 1014 | ✅ 一致 |
| CM_USERGETDETAILITEM | 1015 | 1015 | ✅ 一致 |
| CM_DROPGOLD | 1016 | 1016 | ✅ 一致 |
| CM_1017 | 1017 | - | ❌ **重构版缺失** |
| CM_LOGINNOTICEOK | 1018 | 1018 | ✅ 一致 |

### 组队系统协议 (CM_1019+)
| 协议名 | 原版编号 | 重构版编号 | 状态 |
|--------|----------|------------|------|
| CM_GROUPMODE | 1019 | 1019 | ✅ 一致 |
| CM_CREATEGROUP | 1020 | 1020 | ✅ 一致 |
| CM_ADDGROUPMEMBER | 1021 | 1021 | ✅ 一致 |
| CM_DELGROUPMEMBER | 1022 | 1022 | ✅ 一致 |
| CM_USERREPAIRITEM | 1023 | 1023 | ✅ 一致 |
| CM_MERCHANTQUERYREPAIRCOST | 1024 | 1024 | ✅ 一致 |

### 交易系统协议 (CM_1025+)
| 协议名 | 原版编号 | 重构版编号 | 状态 |
|--------|----------|------------|------|
| CM_DEALTRY | 1025 | 1025 | ✅ 一致 |
| CM_DEALADDITEM | 1026 | 1026 | ✅ 一致 |
| CM_DEALDELITEM | 1027 | 1027 | ✅ 一致 |
| CM_DEALCANCEL | 1028 | 1028 | ✅ 一致 |
| CM_DEALCHGGOLD | 1029 | 1029 | ✅ 一致 |
| CM_DEALEND | 1030 | 1030 | ✅ 一致 |

### 仓库系统协议 (CM_1031+)
| 协议名 | 原版编号 | 重构版编号 | 状态 |
|--------|----------|------------|------|
| CM_USERSTORAGEITEM | 1031 | 1031 | ✅ 一致 |
| CM_USERTAKEBACKSTORAGEITEM | 1032 | 1032 | ✅ 一致 |
| CM_WANTMINIMAP | 1033 | 1033 | ✅ 一致 |
| CM_USERMAKEDRUGITEM | 1034 | 1034 | ✅ 一致 |

### 行会系统协议 (CM_1035+)
| 协议名 | 原版编号 | 重构版编号 | 状态 |
|--------|----------|------------|------|
| CM_OPENGUILDDLG | 1035 | 2400 | ❌ **不一致** |
| CM_GUILDHOME | 1036 | 2401 | ❌ **不一致** |
| CM_GUILDMEMBERLIST | 1037 | 2402 | ❌ **不一致** |
| CM_GUILDADDMEMBER | 1038 | 2403 | ❌ **不一致** |
| CM_GUILDDELMEMBER | 1039 | 2404 | ❌ **不一致** |
| CM_GUILDUPDATENOTICE | 1040 | 2405 | ❌ **不一致** |
| CM_GUILDUPDATERANKINFO | 1041 | 2406 | ❌ **不一致** |
| CM_1042 | 1042 | - | ❌ **重构版缺失** |
| CM_ADJUST_BONUS | 1043 | - | ❌ **重构版缺失** |
| CM_GUILDALLY | 1044 | 2407 | ❌ **不一致** |
| CM_GUILDBREAKALLY | 1045 | 2408 | ❌ **不一致** |

### 战斗动作协议 (CM_3000+)
| 协议名 | 原版编号 | 重构版编号 | 状态 |
|--------|----------|------------|------|
| CM_THROW | 3005 | - | ❌ **重构版缺失** |
| CM_HORSERUN | 3009 | - | ❌ **重构版缺失** |
| CM_TURN | 3010 | 3033 | ❌ **不一致** |
| CM_WALK | 3011 | 3000 | ❌ **不一致** |
| CM_SITDOWN | 3012 | - | ❌ **重构版缺失** |
| CM_RUN | 3013 | 3001 | ❌ **不一致** |
| CM_HIT | 3014 | 3002 | ❌ **不一致** |
| CM_HEAVYHIT | 3015 | 3003 | ❌ **不一致** |
| CM_BIGHIT | 3016 | 3004 | ❌ **不一致** |
| CM_SPELL | 3017 | 3005 | ❌ **不一致** |
| CM_POWERHIT | 3018 | 3006 | ❌ **不一致** |
| CM_LONGHIT | 3019 | 3007 | ❌ **不一致** |
| CM_WIDEHIT | 3024 | 3008 | ❌ **不一致** |
| CM_FIREHIT | 3025 | 3009 | ❌ **不一致** |
| CM_SAY | 3030 | 3030 | ✅ 一致 |

### 登录认证协议 (CM_2000+)
| 协议名 | 原版编号 | 重构版编号 | 状态 |
|--------|----------|------------|------|
| CM_PROTOCOL | 2000 | 2000 | ✅ 一致 |
| CM_IDPASSWORD | 2001 | 2001 | ✅ 一致 |
| CM_ADDNEWUSER | 2002 | 2002 | ✅ 一致 |
| CM_CHANGEPASSWORD | 2003 | 2003 | ✅ 一致 |
| CM_UPDATEUSER | 2004 | 2004 | ✅ 一致 |
| CM_GETBACKPASSWORD | 2010 | 2005 | ❌ **不一致** |

### 服务器响应协议 (SM_)
| 协议名 | 原版编号 | 重构版编号 | 状态 |
|--------|----------|------------|------|
| SM_RUSH | 6 | - | ❌ **重构版缺失** |
| SM_RUSHKUNG | 7 | - | ❌ **重构版缺失** |
| SM_FIREHIT | 8 | - | ❌ **重构版缺失** |
| SM_BACKSTEP | 9 | - | ❌ **重构版缺失** |
| SM_TURN | 10 | 833 | ❌ **不一致** |
| SM_WALK | 11 | 800 | ❌ **不一致** |
| SM_SITDOWN | 12 | - | ❌ **重构版缺失** |
| SM_RUN | 13 | 801 | ❌ **不一致** |
| SM_HIT | 14 | 802 | ❌ **不一致** |
| SM_HEAVYHIT | 15 | 803 | ❌ **不一致** |
| SM_BIGHIT | 16 | 804 | ❌ **不一致** |
| SM_SPELL | 17 | 805 | ❌ **不一致** |
| SM_POWERHIT | 18 | 806 | ❌ **不一致** |
| SM_LONGHIT | 19 | 807 | ❌ **不一致** |
| SM_DIGUP | 20 | - | ❌ **重构版缺失** |
| SM_DIGDOWN | 21 | - | ❌ **重构版缺失** |
| SM_FLYAXE | 22 | - | ❌ **重构版缺失** |
| SM_LIGHTING | 23 | - | ❌ **重构版缺失** |
| SM_WIDEHIT | 24 | 808 | ❌ **不一致** |
| SM_CRSHIT | 25 | - | ❌ **重构版缺失** |
| SM_TWINHIT | 26 | - | ❌ **重构版缺失** |

## 🚨 关键不一致问题

### 1. 数据库协议编号完全不一致
- **原版**: DB_LOADHUMANRCD = 1000, DBR_LOADHUMANRCD = 1100
- **重构版**: DB_LOADHUMANRCD = 200, DBR_LOADHUMANRCD = 250
- **影响**: 这将导致数据库服务器无法正常工作

### 2. 战斗动作协议编号大范围不一致  
- **原版**: CM_WALK = 3011, CM_RUN = 3013, CM_HIT = 3014
- **重构版**: CM_WALK = 3000, CM_RUN = 3001, CM_HIT = 3002
- **影响**: 客户端的移动和战斗操作将无法被正确识别

### 3. 行会系统协议被重新编号
- **原版**: CM_OPENGUILDDLG = 1035
- **重构版**: CM_OPENGUILDDLG = 2400
- **影响**: 行会功能完全无法工作

### 4. 缺失重要协议
以下原版协议在重构版中完全缺失：
- CM_OPENDOOR (1002) - 开门操作
- CM_SITDOWN (3012) - 坐下动作
- CM_THROW (3005) - 投掷物品
- SM_RUSH (6) - 冲锋动作
- SM_DIGUP (20) - 挖掘动作

## 📊 统计总结

| 类别 | 检查总数 | 一致数量 | 不一致数量 | 缺失数量 | 一致率 |
|------|----------|----------|------------|----------|---------|
| CM_协议 | 67 | 32 | 25 | 10 | 47.8% |
| SM_协议 | 89 | 23 | 41 | 25 | 25.8% |
| DB_协议 | 6 | 0 | 6 | 0 | 0% |
| **总计** | **162** | **55** | **72** | **35** | **34.0%** |

## 🔧 修复建议

### 立即修复项（Critical）
1. **数据库协议编号**: 必须恢复到原版编号
   ```cpp
   DB_LOADHUMANRCD = 1000,     // 不是 200
   DB_SAVEHUMANRCD = 1010,     // 不是 201  
   DBR_LOADHUMANRCD = 1100,    // 不是 250
   DBR_SAVEHUMANRCD = 1101,    // 不是 251
   ```

2. **基础战斗协议**: 恢复原版编号
   ```cpp
   CM_WALK = 3011,             // 不是 3000
   CM_RUN = 3013,              // 不是 3001
   CM_HIT = 3014,              // 不是 3002
   SM_WALK = 11,               // 不是 800
   SM_RUN = 13,                // 不是 801
   ```

3. **行会系统协议**: 恢复到1035-1045范围

### 高优先级修复项（High）
1. 补充所有缺失的CM_协议
2. 补充所有缺失的SM_动作响应协议
3. 修正所有不一致的服务器响应协议编号

### 验证方法
1. 使用原版客户端连接重构服务器
2. 测试所有基本功能（移动、战斗、交易、仓库、行会）
3. 确保数据库操作正常
4. 验证所有NPC交互功能

## 结论

**当前重构版本的协议编号与原版差异巨大，一致率仅为34%，这将导致客户端与服务器无法正常通信。必须严格按照原版Grobal2.pas中的协议编号进行修正，确保100%一致性。** 