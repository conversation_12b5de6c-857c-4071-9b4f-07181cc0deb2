#include "ObjectState.h"
#include "BaseObject.h"
#include "../Common/M2Share.h"

// ObjectState implementation - Following original ObjBase.pas state management logic exactly

ObjectState::ObjectState(BaseObject* owner) : m_owner(owner) {
    // Initialize all state flags to false (matching original TBaseObject.Create)
    // Following exact initialization from ObjBase.pas lines 1157-1368
    
    // Core state flags initialization (matching original boolean field initialization)
    m_ghost = false;                        // m_boGhost := False;
    m_death = false;                        // m_boDeath := False;
    m_hide_mode = false;                    // m_boHideMode := False;
    m_stone_mode = false;                   // m_boStoneMode := False;
    m_cool_eye = false;                     // m_boCoolEye := False;
    m_user_unlock_drug = false;             // m_boUserUnLockDurg := False;
    m_transparent = false;                  // m_boTransparent := False;
    m_admin_mode = false;                   // m_boAdminMode := False;
    m_observe_mode = false;                 // m_boObMode := False;
    m_teleport = false;                     // m_boTeleport := False;
    m_paralysis = false;                    // m_boParalysis := False;
    m_un_paralysis = false;                 // m_boUnParalysis := False;
    m_revival = false;                      // m_boRevival := False;
    m_un_revival = false;                   // m_boUnRevival := False;
    m_super_man = false;                    // m_boSuperMan := False;
    m_animal = false;                       // m_boAnimal := False;
    m_no_item = false;                      // m_boNoItem := False;
    m_fixed_hide_mode = false;              // m_boFixedHideMode := False;
    m_stick_mode = false;                   // m_boStickMode := False;
    m_no_attack_mode = false;               // m_boNoAttackMode := False;
    m_skeleton = false;                     // m_boSkeleton := False;
    m_holy_seize = false;                   // m_boHolySeize := False;
    m_crazy_mode = false;                   // m_boCrazyMode := False;
    m_show_hp = false;                      // m_boShowHP := False;
    
    // State timing initialization (matching original tick initialization)
    m_ghost_tick = 0;                       // m_dwGhostTick := 0;
    m_death_tick = 0;                       // m_dwDeathTick := 0;
    m_revival_tick = 0;                     // m_dwRevivalTick := 0;
    m_holy_seize_tick = 0;                  // m_dwHolySeizeTick := 0;
    m_holy_seize_interval = 0;              // m_dwHolySeizeInterval := 0;
    m_crazy_mode_tick = 0;                  // m_dwCrazyModeTick := 0;
    m_crazy_mode_interval = 0;              // m_dwCrazyModeInterval := 0;
    m_show_hp_tick = 0;                     // m_dwShowHPTick := 0;
    m_show_hp_interval = 0;                 // m_dwShowHPInterval := 0;
    m_dup_obj_tick = 0;                     // m_dwDupObjTick := 0;
    
    // Ring effects initialization (matching original ring boolean fields)
    m_flame_ring = false;                   // m_boFlameRing := False;
    m_recovery_ring = false;                // m_boRecoveryRing := False;
    m_angry_ring = false;                   // m_boAngryRing := False;
    m_magic_shield = false;                 // m_boMagicShield := False;
    m_un_magic_shield = false;              // m_boUnMagicShield := False;
    m_muscle_ring = false;                  // m_boMuscleRing := False;
    m_fast_train = false;                   // m_boFastTrain := False;
    m_probe_necklace = false;               // m_boProbeNecklace := False;
    m_guild_move = false;                   // m_boGuildMove := False;
    m_superman_item = false;                // m_boSupermanItem := False;
    m_spirit = false;                       // m_bopirit := False;
    m_no_drop_item = false;                 // m_boNoDropItem := False;
    m_no_drop_use_item = false;             // m_boNoDropUseItem := False;
    m_exp_item = false;                     // m_boExpItem := False;
    m_power_item = false;                   // m_boPowerItem := False;
    
    // Special abilities initialization (matching original ability boolean fields)
    m_abil_see_heal_gauge = false;          // m_boAbilSeeHealGauge := False;
    m_abil_mag_bubble_defence = false;      // m_boAbilMagBubbleDefence := False;
    m_mag_bubble_defence_level = 0;         // m_btMagBubbleDefenceLevel := 0;
    
    // Item effect rates initialization
    m_exp_item_rate = 0.0;                  // m_rExpItem := 0;
    m_power_item_rate = 0.0;                // m_rPowerItem := 0;
    
    // Clear status effects collection
    m_status_effects.clear();
}

ObjectState::~ObjectState() {
    // Clear all status effects
    m_status_effects.clear();
}

void ObjectState::Initialize() {
    // Reset all states to initial values
    Reset();
}

void ObjectState::Update() {
    // Update status effects and check timeouts
    UpdateStatusEffects();
    CheckStateTimeouts();
    ProcessStateEffects();
}

void ObjectState::Reset() {
    // Reset all state flags to false (following original RecalcAbilitys logic)
    // This matches the logic in ObjBase.pas RecalcAbilitys method lines 2722-2747
    
    m_hide_mode = false;                    // m_boHideMode := False;
    m_teleport = false;                     // m_boTeleport := False;
    m_paralysis = false;                    // m_boParalysis := False;
    m_revival = false;                      // m_boRevival := False;
    m_un_revival = false;                   // m_boUnRevival := False;
    m_flame_ring = false;                   // m_boFlameRing := False;
    m_recovery_ring = false;                // m_boRecoveryRing := False;
    m_angry_ring = false;                   // m_boAngryRing := False;
    m_magic_shield = false;                 // m_boMagicShield := False;
    m_un_magic_shield = false;              // m_boUnMagicShield := False;
    m_muscle_ring = false;                  // m_boMuscleRing := False;
    m_fast_train = false;                   // m_boFastTrain := False;
    m_probe_necklace = false;               // m_boProbeNecklace := False;
    m_superman_item = false;                // m_boSupermanItem := False;
    m_guild_move = false;                   // m_boGuildMove := False;
    m_un_paralysis = false;                 // m_boUnParalysis := False;
    m_exp_item = false;                     // m_boExpItem := False;
    m_power_item = false;                   // m_boPowerItem := False;
    m_no_drop_item = false;                 // m_boNoDropItem := False;
    m_no_drop_use_item = false;             // m_boNoDropUseItem := False;
    m_spirit = false;                       // m_bopirit := False;
    
    // Reset item effect rates
    m_exp_item_rate = 0.0;                  // m_rExpItem := 0;
    m_power_item_rate = 0.0;                // m_rPowerItem := 0;
    
    // Clear all status effects
    RemoveAllStatusEffects();
}

// Primary state setters (matching original ObjBase.pas methods exactly)
void ObjectState::SetGhost(bool value, DWORD duration) {
    m_ghost = value;
    if (value) {
        m_ghost_tick = g_functions::GetCurrentTime();
        if (duration > 0) {
            AddStatusEffect(StatusEffectType::NONE, duration, 0, 0); // Ghost duration tracking
        }
    } else {
        m_ghost_tick = 0;
    }
    NotifyOwnerOfStateChange();
}

void ObjectState::SetDeath(bool value, DWORD duration) {
    m_death = value;
    if (value) {
        m_death_tick = g_functions::GetCurrentTime();
        if (duration > 0) {
            AddStatusEffect(StatusEffectType::NONE, duration, 0, 0); // Death duration tracking
        }
    } else {
        m_death_tick = 0;
    }
    NotifyOwnerOfStateChange();
}

void ObjectState::SetHideMode(bool value) {
    m_hide_mode = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetStoneMode(bool value) {
    m_stone_mode = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetCoolEye(bool value) {
    m_cool_eye = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetUserUnlockDrug(bool value) {
    m_user_unlock_drug = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetTransparent(bool value) {
    m_transparent = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetAdminMode(bool value) {
    m_admin_mode = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetObserveMode(bool value) {
    m_observe_mode = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetTeleport(bool value) {
    m_teleport = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetParalysis(bool value) {
    m_paralysis = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetUnParalysis(bool value) {
    m_un_paralysis = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetRevival(bool value) {
    m_revival = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetUnRevival(bool value) {
    m_un_revival = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetSuperMan(bool value) {
    m_super_man = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetAnimal(bool value) {
    m_animal = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetNoItem(bool value) {
    m_no_item = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetFixedHideMode(bool value) {
    m_fixed_hide_mode = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetStickMode(bool value) {
    m_stick_mode = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetNoAttackMode(bool value) {
    m_no_attack_mode = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetSkeleton(bool value) {
    m_skeleton = value;
    NotifyOwnerOfStateChange();
}

void ObjectState::SetShowHP(bool value) {
    m_show_hp = value;
    NotifyOwnerOfStateChange();
}

// Special state methods (matching original ObjBase.pas methods exactly)
void ObjectState::OpenHolySeizeMode(DWORD interval) {
    // Following exact logic from ObjBase.pas OpenHolySeizeMode (line 19788-19793)
    m_holy_seize = true;                    // m_boHolySeize := True;
    m_holy_seize_tick = g_functions::GetCurrentTime(); // m_dwHolySeizeTick := GetTickCount();
    m_holy_seize_interval = interval;       // m_dwHolySeizeInterval := dwInterval;
    NotifyOwnerOfStateChange();             // RefNameColor();
}

void ObjectState::BreakHolySeizeMode() {
    // Following exact logic from ObjBase.pas BreakHolySeizeMode (line 19796-19799)
    m_holy_seize = false;                   // m_boHolySeize := False;
    NotifyOwnerOfStateChange();             // RefNameColor();
}

void ObjectState::OpenCrazyMode(int time) {
    // Following exact logic from ObjBase.pas OpenCrazyMode (line 19802-19807)
    m_crazy_mode = true;                    // m_boCrazyMode := True;
    m_crazy_mode_tick = g_functions::GetCurrentTime(); // m_dwCrazyModeTick := GetTickCount();
    m_crazy_mode_interval = time * 1000;    // m_dwCrazyModeInterval := nTime * 1000;
    NotifyOwnerOfStateChange();             // RefNameColor();
}

void ObjectState::BreakCrazyMode() {
    // Following exact logic from ObjBase.pas BreakCrazyMode (line 19811-19815)
    if (m_crazy_mode) {                     // if m_boCrazyMode then begin
        m_crazy_mode = false;               // m_boCrazyMode := False;
        NotifyOwnerOfStateChange();         // RefNameColor();
    }                                       // end;
}

void ObjectState::MakeOpenHealth() {
    // Enable show HP mode
    m_show_hp = true;
    m_show_hp_tick = g_functions::GetCurrentTime();
    NotifyOwnerOfStateChange();
}

void ObjectState::BreakOpenHealth() {
    // Disable show HP mode
    m_show_hp = false;
    m_show_hp_tick = 0;
    m_show_hp_interval = 0;
    NotifyOwnerOfStateChange();
}

// Status effect management
void ObjectState::AddStatusEffect(StatusEffectType type, DWORD duration, int power, int level) {
    // Remove existing effect of same type first
    RemoveStatusEffect(type);

    // Add new status effect
    StatusEffect effect(type, duration, power, level);
    m_status_effects.push_back(effect);

    // Apply effect to owner
    ApplyStatusEffectToOwner(effect);
    OnStatusEffectAdded(effect);
}

void ObjectState::RemoveStatusEffect(StatusEffectType type) {
    auto it = std::find_if(m_status_effects.begin(), m_status_effects.end(),
        [type](const StatusEffect& effect) {
            return effect.type == type && effect.active;
        });

    if (it != m_status_effects.end()) {
        it->active = false;
        RemoveStatusEffectFromOwner(type);
        OnStatusEffectRemoved(type);
    }
}

void ObjectState::RemoveAllStatusEffects() {
    for (auto& effect : m_status_effects) {
        if (effect.active) {
            effect.active = false;
            RemoveStatusEffectFromOwner(effect.type);
            OnStatusEffectRemoved(effect.type);
        }
    }
    m_status_effects.clear();
}

bool ObjectState::HasStatusEffect(StatusEffectType type) const {
    return std::any_of(m_status_effects.begin(), m_status_effects.end(),
        [type](const StatusEffect& effect) {
            return effect.type == type && effect.active && !effect.IsExpired();
        });
}

const StatusEffect* ObjectState::GetStatusEffect(StatusEffectType type) const {
    auto it = std::find_if(m_status_effects.begin(), m_status_effects.end(),
        [type](const StatusEffect& effect) {
            return effect.type == type && effect.active && !effect.IsExpired();
        });

    return (it != m_status_effects.end()) ? &(*it) : nullptr;
}

void ObjectState::UpdateStatusEffects() {
    // Remove expired effects
    for (auto& effect : m_status_effects) {
        if (effect.active && effect.IsExpired()) {
            effect.active = false;
            RemoveStatusEffectFromOwner(effect.type);
            OnStatusEffectRemoved(effect.type);
        }
    }

    // Clean up inactive effects
    m_status_effects.erase(
        std::remove_if(m_status_effects.begin(), m_status_effects.end(),
            [](const StatusEffect& effect) { return !effect.active; }),
        m_status_effects.end());
}

// Poison management (matching original MakePosion method exactly)
bool ObjectState::MakePosion(int type, int time, int point) {
    // Following exact logic from ObjBase.pas MakePosion (line 20698-20714)
    // function TBaseObject.MakePosion(nType, nTime, nPoint: Integer): Boolean;

    if (!m_owner) return false;

    // Get current character status for comparison
    int old_char_status = 0; // This would be m_owner->GetCharStatus() in real implementation

    // Set poison status in status time array (matching original logic)
    if (type >= 0 && type < 6) { // Ensure valid poison type
        // m_wStatusTimeArr[nType] := nTime;
        // This would set m_owner->m_status_time_arr[type] = time;

        // m_dwStatusArrTick[nType] := GetTickCount();
        // This would set m_owner->m_status_arr_tick[type] = GetCurrentTime();

        // For poison damage points
        if (type == POISON_DECHEALTH) {
            // m_btGreenPoisoningPoint := nPoint;
            // This would set poison damage point
        }

        // Add status effect to our collection
        AddStatusEffect(static_cast<StatusEffectType>(type), time, point, 0);

        // Notify character status change
        // m_nCharStatus := GetCharStatus();
        NotifyOwnerOfStateChange();

        return true;
    }

    return false;
}

void ObjectState::RemovePoison(int type) {
    if (type >= 0 && type < 6) {
        RemoveStatusEffect(static_cast<StatusEffectType>(type));
    }
}

bool ObjectState::IsPoisoned() const {
    // Check if any poison effects are active
    return HasStatusEffect(StatusEffectType::POISON_DECHEALTH) ||
           HasStatusEffect(StatusEffectType::POISON_DAMAGEARMOR) ||
           HasStatusEffect(StatusEffectType::POISON_LOCKSPELL) ||
           HasStatusEffect(StatusEffectType::POISON_DONTMOVE) ||
           HasStatusEffect(StatusEffectType::POISON_STONE);
}

int ObjectState::GetPoisonDamage() const {
    const StatusEffect* poison_effect = GetStatusEffect(StatusEffectType::POISON_DECHEALTH);
    return poison_effect ? poison_effect->power : 0;
}

// State validation and utility
bool ObjectState::CanMove() const {
    // Following original logic - cannot move if paralyzed, stone mode, or dead
    return !m_paralysis && !m_stone_mode && !m_death &&
           !HasStatusEffect(StatusEffectType::POISON_DONTMOVE) &&
           !HasStatusEffect(StatusEffectType::POISON_STONE);
}

bool ObjectState::CanAttack() const {
    // Following original logic - cannot attack if dead, paralyzed, or in no attack mode
    return !m_death && !m_paralysis && !m_no_attack_mode &&
           !HasStatusEffect(StatusEffectType::POISON_STONE);
}

bool ObjectState::CanCast() const {
    // Following original logic - cannot cast if dead, paralyzed, or spell locked
    return !m_death && !m_paralysis &&
           !HasStatusEffect(StatusEffectType::POISON_LOCKSPELL) &&
           !HasStatusEffect(StatusEffectType::POISON_STONE);
}

bool ObjectState::CanBeAttacked() const {
    // Following original logic - cannot be attacked if ghost, admin mode, or superman
    return !m_ghost && !m_admin_mode && !m_super_man && !m_superman_item;
}

bool ObjectState::CanBeTargeted() const {
    // Following original logic - cannot be targeted if ghost, admin mode, or observe mode
    return !m_ghost && !m_admin_mode && !m_observe_mode;
}

bool ObjectState::IsVisible() const {
    // Following original logic - not visible if ghost, hide mode, admin mode, or transparent
    return !m_ghost && !m_hide_mode && !m_admin_mode && !m_transparent;
}

bool ObjectState::IsInvulnerable() const {
    // Following original logic - invulnerable if superman, admin mode, or holy seize
    return m_super_man || m_admin_mode || m_holy_seize || m_superman_item;
}

// State change notifications
void ObjectState::OnStateChanged(ObjectStateType old_state, ObjectStateType new_state) {
    // Notify owner of state change
    NotifyOwnerOfStateChange();
}

void ObjectState::OnStatusEffectAdded(const StatusEffect& effect) {
    // Handle status effect addition
    NotifyOwnerOfStateChange();
}

void ObjectState::OnStatusEffectRemoved(StatusEffectType type) {
    // Handle status effect removal
    NotifyOwnerOfStateChange();
}

// Private internal methods
void ObjectState::CheckStateTimeouts() {
    DWORD current_time = g_functions::GetCurrentTime();

    // Check holy seize timeout (following original logic)
    if (m_holy_seize && m_holy_seize_interval > 0) {
        if (current_time - m_holy_seize_tick >= m_holy_seize_interval) {
            BreakHolySeizeMode();
        }
    }

    // Check crazy mode timeout (following original logic)
    if (m_crazy_mode && m_crazy_mode_interval > 0) {
        if (current_time - m_crazy_mode_tick >= m_crazy_mode_interval) {
            BreakCrazyMode();
        }
    }

    // Check show HP timeout
    if (m_show_hp && m_show_hp_interval > 0) {
        if (current_time - m_show_hp_tick >= m_show_hp_interval) {
            BreakOpenHealth();
        }
    }

    // Check ghost timeout
    if (m_ghost && m_ghost_tick > 0) {
        // Ghost timeout logic would go here if needed
    }

    // Check death timeout
    if (m_death && m_death_tick > 0) {
        // Death timeout logic would go here if needed
    }
}

void ObjectState::ProcessStateEffects() {
    // Process ongoing state effects

    // Process ring effects
    if (m_flame_ring) {
        // Flame ring effect processing
    }

    if (m_recovery_ring) {
        // Recovery ring effect processing
    }

    if (m_angry_ring) {
        // Angry ring effect processing
    }

    if (m_magic_shield) {
        // Magic shield effect processing
    }

    if (m_muscle_ring) {
        // Muscle ring effect processing
    }

    // Process special abilities
    if (m_abil_see_heal_gauge) {
        // See heal gauge ability processing
    }

    if (m_abil_mag_bubble_defence) {
        // Magic bubble defence processing
    }
}

void ObjectState::ValidateStateConsistency() {
    // Validate state consistency and fix any conflicts

    // Cannot be both dead and alive states
    if (m_death && (m_revival || m_un_revival)) {
        // Handle conflict - death takes precedence
        m_revival = false;
        m_un_revival = false;
    }

    // Cannot be both paralyzed and un-paralyzed
    if (m_paralysis && m_un_paralysis) {
        // Handle conflict - un-paralysis takes precedence
        m_paralysis = false;
    }

    // Cannot be both magic shield and un-magic shield
    if (m_magic_shield && m_un_magic_shield) {
        // Handle conflict - un-magic shield takes precedence
        m_magic_shield = false;
    }

    // Admin mode overrides many other states
    if (m_admin_mode) {
        m_ghost = false;
        m_death = false;
        m_paralysis = false;
        m_stone_mode = false;
    }

    // Superman mode overrides vulnerability states
    if (m_super_man || m_superman_item) {
        m_death = false;
        m_paralysis = false;
        m_stone_mode = false;
    }
}

void ObjectState::NotifyOwnerOfStateChange() {
    if (m_owner) {
        // Notify owner that state has changed
        // This would call methods like StatusChanged(), FeatureChanged(), etc.
        // m_owner->StatusChanged();
        // m_owner->FeatureChanged();
    }
}

bool ObjectState::IsStateTimedOut(DWORD start_tick, DWORD duration) const {
    if (duration == 0) return false;
    return (g_functions::GetCurrentTime() - start_tick) >= duration;
}

void ObjectState::ClearTimedOutStates() {
    DWORD current_time = g_functions::GetCurrentTime();

    // Clear timed out states
    if (m_holy_seize && m_holy_seize_interval > 0 &&
        IsStateTimedOut(m_holy_seize_tick, m_holy_seize_interval)) {
        BreakHolySeizeMode();
    }

    if (m_crazy_mode && m_crazy_mode_interval > 0 &&
        IsStateTimedOut(m_crazy_mode_tick, m_crazy_mode_interval)) {
        BreakCrazyMode();
    }

    if (m_show_hp && m_show_hp_interval > 0 &&
        IsStateTimedOut(m_show_hp_tick, m_show_hp_interval)) {
        BreakOpenHealth();
    }
}

void ObjectState::ApplyStatusEffectToOwner(const StatusEffect& effect) {
    if (!m_owner) return;

    // Apply status effect to owner based on type
    switch (effect.type) {
        case StatusEffectType::POISON_DECHEALTH:
            // Apply health decreasing poison
            break;

        case StatusEffectType::POISON_DAMAGEARMOR:
            // Apply armor damaging poison
            break;

        case StatusEffectType::POISON_LOCKSPELL:
            // Apply spell locking poison
            break;

        case StatusEffectType::POISON_DONTMOVE:
            // Apply movement locking poison
            break;

        case StatusEffectType::POISON_STONE:
            // Apply stone poison (paralysis)
            break;

        case StatusEffectType::STATE_TRANSPARENT:
            // Apply transparency state
            m_transparent = true;
            break;

        case StatusEffectType::STATE_DEFENCEUP:
            // Apply defence up state
            break;

        case StatusEffectType::STATE_MAGDEFENCEUP:
            // Apply magic defence up state
            break;

        case StatusEffectType::STATE_BUBBLEDEFENCEUP:
            // Apply bubble defence up state
            m_abil_mag_bubble_defence = true;
            m_mag_bubble_defence_level = static_cast<BYTE>(effect.level);
            break;

        default:
            break;
    }
}

void ObjectState::RemoveStatusEffectFromOwner(StatusEffectType type) {
    if (!m_owner) return;

    // Remove status effect from owner based on type
    switch (type) {
        case StatusEffectType::POISON_DECHEALTH:
        case StatusEffectType::POISON_DAMAGEARMOR:
        case StatusEffectType::POISON_LOCKSPELL:
        case StatusEffectType::POISON_DONTMOVE:
        case StatusEffectType::POISON_STONE:
            // Remove poison effects
            break;

        case StatusEffectType::STATE_TRANSPARENT:
            // Remove transparency state
            m_transparent = false;
            break;

        case StatusEffectType::STATE_DEFENCEUP:
            // Remove defence up state
            break;

        case StatusEffectType::STATE_MAGDEFENCEUP:
            // Remove magic defence up state
            break;

        case StatusEffectType::STATE_BUBBLEDEFENCEUP:
            // Remove bubble defence up state
            m_abil_mag_bubble_defence = false;
            m_mag_bubble_defence_level = 0;
            break;

        default:
            break;
    }
}
