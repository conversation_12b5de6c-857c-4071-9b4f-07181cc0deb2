cmake_minimum_required(VERSION 3.15)
project(MirClient CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find packages
find_package(SDL2 REQUIRED)

# Include directories
include_directories(
    src
    ../common
    ${SDL2_INCLUDE_DIRS}
)

# Collect source files
file(GLOB_RECURSE CLIENT_SOURCES 
    "src/*.cpp"
    "src/**/*.cpp"
)

# Create executable
add_executable(MirClient ${CLIENT_SOURCES})

# Link libraries
target_link_libraries(MirClient 
    ${SDL2_LIBRARIES}
    SDL2_image
    SDL2_ttf
    SDL2_mixer
    SDL2_net
    z
)

# Copy assets to build directory
file(COPY assets DESTINATION ${CMAKE_CURRENT_BINARY_DIR})

# Windows specific settings
if(WIN32)
    set_property(TARGET MirClient PROPERTY WIN32_EXECUTABLE TRUE)
endif() 