#pragma once

#include <SDL2/SDL.h>
#include <SDL2/SDL_ttf.h>
#include <string>
#include <memory>

/**
 * @class Texture
 * @brief Wrapper for SDL_Texture
 *
 * This class provides a convenient wrapper for SDL_Texture with additional
 * functionality for rendering and manipulation.
 */
class Texture {
private:
    SDL_Texture* m_texture;     ///< SDL texture handle
    SDL_Renderer* m_renderer;   ///< SDL renderer handle
    int m_width;                ///< Texture width
    int m_height;               ///< Texture height
    bool m_isColorKeySet;       ///< Flag indicating if color key is set
    SDL_Color m_colorKey;       ///< Color key for transparency

public:
    /**
     * @brief Default constructor
     */
    Texture();

    /**
     * @brief Constructor
     * @param renderer SDL renderer handle
     */
    Texture(SDL_Renderer* renderer);

    /**
     * @brief Destructor
     */
    ~Texture();

    /**
     * @brief Load texture from file
     * @param filename File path
     * @return true if successful, false otherwise
     */
    bool LoadFromFile(const std::string& filename);

    /**
     * @brief Load texture from SDL_Surface
     * @param surface SDL surface
     * @return true if successful, false otherwise
     */
    bool LoadFromSurface(SDL_Surface* surface);

    /**
     * @brief Create a blank texture
     * @param width Texture width
     * @param height Texture height
     * @param access Texture access mode
     * @return true if successful, false otherwise
     */
    bool CreateBlank(int width, int height, int access);

    /**
     * @brief Create texture from raw data
     * @param data Raw pixel data
     * @param width Texture width
     * @param height Texture height
     * @param depth Color depth in bits
     * @return true if successful, false otherwise
     */
    bool CreateFromData(const uint8_t* data, int width, int height, int depth);

    /**
     * @brief Create texture from text
     * @param text Text to render
     * @param font Font to use
     * @param color Text color
     * @return true if successful, false otherwise
     */
    bool LoadFromText(const std::string& text, TTF_Font* font, SDL_Color color);

    /**
     * @brief Set color key for transparency
     * @param r Red component
     * @param g Green component
     * @param b Blue component
     */
    void SetColorKey(Uint8 r, Uint8 g, Uint8 b);

    /**
     * @brief Set blend mode
     * @param blendMode SDL blend mode
     */
    void SetBlendMode(SDL_BlendMode blendMode);

    /**
     * @brief Set alpha modulation
     * @param alpha Alpha value (0-255)
     */
    void SetAlpha(Uint8 alpha);

    /**
     * @brief Set color modulation
     * @param r Red component
     * @param g Green component
     * @param b Blue component
     */
    void SetColor(Uint8 r, Uint8 g, Uint8 b);

    /**
     * @brief Render texture at position
     * @param x X coordinate
     * @param y Y coordinate
     * @param clip Clip rectangle (optional)
     */
    void Render(int x, int y, SDL_Rect* clip = nullptr);

    /**
     * @brief Render texture with additional options
     * @param x X coordinate
     * @param y Y coordinate
     * @param clip Clip rectangle (optional)
     * @param angle Rotation angle in degrees (optional)
     * @param center Rotation center (optional)
     * @param flip Flip mode (optional)
     */
    void RenderEx(int x, int y, SDL_Rect* clip = nullptr, double angle = 0.0,
                 SDL_Point* center = nullptr, SDL_RendererFlip flip = SDL_FLIP_NONE);

    /**
     * @brief Get texture width
     * @return Texture width in pixels
     */
    int GetWidth() const { return m_width; }

    /**
     * @brief Get texture height
     * @return Texture height in pixels
     */
    int GetHeight() const { return m_height; }

    /**
     * @brief Check if texture is loaded
     * @return true if loaded, false otherwise
     */
    bool IsLoaded() const { return m_texture != nullptr; }

    /**
     * @brief Get the renderer
     * @return SDL renderer
     */
    SDL_Renderer* GetRenderer() const { return m_renderer; }

    /**
     * @brief Get the SDL texture
     * @return SDL texture
     */
    SDL_Texture* GetSDLTexture() const { return m_texture; }

    /**
     * @brief Free texture resources
     */
    void Free();
};

