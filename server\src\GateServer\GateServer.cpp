#include "GateServer.h"
#include "../Common/Logger.h"
#include "../Protocol/MessageConverter.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <regex>
#include <iostream>
#include <ctime>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#endif

namespace MirServer {

GateServer::GateServer() 
    : m_sessionCount(0), m_isRunning(false), m_serverReady(false), m_gateReady(false)
    , m_serverAddr("127.0.0.1"), m_serverPort(5000), m_gateAddr("0.0.0.0"), m_gatePort(7200)
    , m_maxConnOfIP(50), m_maxClientPacketSize(8000), m_nomClientPacketSize(200)
    , m_maxClientMsgCount(20), m_block<PERSON>eth<PERSON>(DISCONNECT), m_clientSendBlockSize(1000)
    , m_clientTimeOut(5000), m_sessionTimeOut(3600000), m_sayMsgMaxLen(70)
    , m_sayMsgTime(1000), m_attackTick(300), m_attackCount(5)
{
    // 初始化会话数组
    RestSessionArray();
}

GateServer::~GateServer() {
    Stop();
}

bool GateServer::Initialize(const std::string& configFile) {
    m_configFile = configFile;
    
    // 加载配置
    LoadConfig();
    
    // 创建客户端网络管理器
    m_clientManager = std::make_unique<NetworkManager>();
    if (!m_clientManager) {
        AddMainLogMsg("Failed to create client network manager", 1);
        return false;
    }
    
    AddMainLogMsg("GateServer initialized successfully", 0);
    return true;
}

bool GateServer::Start() {
    if (m_isRunning) {
        return true;
    }
    
    AddMainLogMsg("Starting GateServer...", 0);
    
    m_isRunning = true;
    m_serverReady = true;
    m_gateReady = true;
    
    // 启动处理线程
    m_processThread = std::thread(&GateServer::ProcessThread, this);
    m_checkThread = std::thread(&GateServer::CheckThread, this);
    
    AddMainLogMsg("GateServer started successfully on port " + std::to_string(m_gatePort), 0);
    return true;
}

void GateServer::Stop() {
    if (!m_isRunning) {
        return;
    }
    
    AddMainLogMsg("Stopping GateServer...", 0);
    
    m_isRunning = false;
    m_serverReady = false;
    m_gateReady = false;
    
    // 等待线程结束
    if (m_processThread.joinable()) {
        m_processThread.join();
    }
    if (m_checkThread.joinable()) {
        m_checkThread.join();
    }
    
    // 保存配置
    SaveConfig();
    SaveBlockIPFile();
    
    AddMainLogMsg("GateServer stopped", 0);
}

void GateServer::LoadConfig() {
    AddMainLogMsg("Loading configuration...", 3);
    
    // 使用简单的INI文件解析
    std::ifstream file(m_configFile);
    if (!file.is_open()) {
        AddMainLogMsg("Config file not found, using defaults", 2);
        return;
    }
    
    std::string line, section;
    while (std::getline(file, line)) {
        // 移除首尾空白字符
        line.erase(0, line.find_first_not_of(" \t\r\n"));
        line.erase(line.find_last_not_of(" \t\r\n") + 1);
        
        if (line.empty() || line[0] == ';' || line[0] == '#') {
            continue;
        }
        
        if (line[0] == '[' && line.back() == ']') {
            section = line.substr(1, line.length() - 2);
            continue;
        }
        
        size_t pos = line.find('=');
        if (pos == std::string::npos) {
            continue;
        }
        
        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);
        
        // 移除key和value的空白字符
        key.erase(0, key.find_first_not_of(" \t"));
        key.erase(key.find_last_not_of(" \t") + 1);
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t") + 1);
        
        if (section == "Server") {
            if (key == "ServerAddr") m_serverAddr = value;
            else if (key == "ServerPort") m_serverPort = std::stoi(value);
            else if (key == "GateAddr") m_gateAddr = value;
            else if (key == "GatePort") m_gatePort = std::stoi(value);
            else if (key == "MaxConnOfIPaddr") m_maxConnOfIP = std::stoi(value);
            else if (key == "MaxClientPacketSize") m_maxClientPacketSize = std::stoi(value);
            else if (key == "NomClientPacketSize") m_nomClientPacketSize = std::stoi(value);
            else if (key == "ClientSendBlockSize") m_clientSendBlockSize = std::stoi(value);
            else if (key == "ClientTimeOutTime") m_clientTimeOut = std::stoi(value);
            else if (key == "SessionTimeOutTime") m_sessionTimeOut = std::stoi(value);
            else if (key == "AttackTick") m_attackTick = std::stoi(value);
            else if (key == "AttackCount") m_attackCount = std::stoi(value);
            else if (key == "BlockMethod") m_blockMethod = static_cast<BlockIPMethod>(std::stoi(value));
        } else if (section == "Setup") {
            if (key == "HitSpeed") m_config.boHit = (value == "1" || value == "true");
            else if (key == "SpellSpeed") m_config.boSpell = (value == "1" || value == "true");
            else if (key == "RunSpeed") m_config.boRun = (value == "1" || value == "true");
            else if (key == "WalkSpeed") m_config.boWalk = (value == "1" || value == "true");
            else if (key == "TurnSpeed") m_config.boTurn = (value == "1" || value == "true");
            else if (key == "HitTime") m_config.nHitTime = std::stoi(value);
            else if (key == "SpellTime") m_config.nSpellTime = std::stoi(value);
            else if (key == "RunTime") m_config.nRunTime = std::stoi(value);
            else if (key == "WalkTime") m_config.nWalkTime = std::stoi(value);
            else if (key == "TurnTime") m_config.nTurnTime = std::stoi(value);
            else if (key == "HitCount") m_config.nHitCount = std::stoi(value);
            else if (key == "SpellCount") m_config.nSpellCount = std::stoi(value);
            else if (key == "RunCount") m_config.nRunCount = std::stoi(value);
            else if (key == "WalkCount") m_config.nWalkCount = std::stoi(value);
            else if (key == "TurnCount") m_config.nTurnCount = std::stoi(value);
            else if (key == "SpeedControlMode") m_config.btSpeedControlMode = static_cast<BYTE>(std::stoi(value));
            else if (key == "HintSpeed") m_config.boSpeedShowMsg = (value == "1" || value == "true");
            else if (key == "HintSpeedMsg") m_config.sSpeedShowMsg = value;
        }
    }
    
    file.close();
    
    // 加载其他配置文件
    LoadAbuseFile();
    LoadBlockIPFile();
    
    AddMainLogMsg("Configuration loaded", 3);
}

void GateServer::SaveConfig() {
    std::ofstream file(m_configFile);
    if (!file.is_open()) {
        AddMainLogMsg("Failed to save configuration", 1);
        return;
    }
    
    file << "[Server]\n";
    file << "ServerAddr=" << m_serverAddr << "\n";
    file << "ServerPort=" << m_serverPort << "\n";
    file << "GateAddr=" << m_gateAddr << "\n";
    file << "GatePort=" << m_gatePort << "\n";
    file << "MaxConnOfIPaddr=" << m_maxConnOfIP << "\n";
    file << "MaxClientPacketSize=" << m_maxClientPacketSize << "\n";
    file << "NomClientPacketSize=" << m_nomClientPacketSize << "\n";
    file << "ClientSendBlockSize=" << m_clientSendBlockSize << "\n";
    file << "ClientTimeOutTime=" << m_clientTimeOut << "\n";
    file << "SessionTimeOutTime=" << m_sessionTimeOut << "\n";
    file << "AttackTick=" << m_attackTick << "\n";
    file << "AttackCount=" << m_attackCount << "\n";
    file << "BlockMethod=" << static_cast<int>(m_blockMethod) << "\n";
    
    file << "\n[Setup]\n";
    file << "HitSpeed=" << (m_config.boHit ? "1" : "0") << "\n";
    file << "SpellSpeed=" << (m_config.boSpell ? "1" : "0") << "\n";
    file << "RunSpeed=" << (m_config.boRun ? "1" : "0") << "\n";
    file << "WalkSpeed=" << (m_config.boWalk ? "1" : "0") << "\n";
    file << "TurnSpeed=" << (m_config.boTurn ? "1" : "0") << "\n";
    file << "HitTime=" << m_config.nHitTime << "\n";
    file << "SpellTime=" << m_config.nSpellTime << "\n";
    file << "RunTime=" << m_config.nRunTime << "\n";
    file << "WalkTime=" << m_config.nWalkTime << "\n";
    file << "TurnTime=" << m_config.nTurnTime << "\n";
    file << "HitCount=" << m_config.nHitCount << "\n";
    file << "SpellCount=" << m_config.nSpellCount << "\n";
    file << "RunCount=" << m_config.nRunCount << "\n";
    file << "WalkCount=" << m_config.nWalkCount << "\n";
    file << "TurnCount=" << m_config.nTurnCount << "\n";
    file << "SpeedControlMode=" << static_cast<int>(m_config.btSpeedControlMode) << "\n";
    file << "HintSpeed=" << (m_config.boSpeedShowMsg ? "1" : "0") << "\n";
    file << "HintSpeedMsg=" << m_config.sSpeedShowMsg << "\n";
    
    file.close();
}

void GateServer::ProcessThread() {
    while (m_isRunning) {
        try {
            // 处理消息队列的逻辑
            std::this_thread::sleep_for(std::chrono::milliseconds(30));
        } catch (const std::exception& e) {
            AddMainLogMsg("ProcessThread exception: " + std::string(e.what()), 1);
        }
    }
}

void GateServer::CheckThread() {
    auto lastCheckTime = std::chrono::steady_clock::now();
    
    while (m_isRunning) {
        auto now = std::chrono::steady_clock::now();
        
        // 每2秒检查一次
        if (std::chrono::duration_cast<std::chrono::seconds>(now - lastCheckTime).count() >= 2) {
            lastCheckTime = now;
            
            // 检查会话超时等逻辑
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

void GateServer::RestSessionArray() {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    for (int i = 0; i < GATE_MAX_SESSION; ++i) {
        m_sessions[i] = GateSessionInfo();
    }
    m_sessionCount = 0;
}

void GateServer::AddMainLogMsg(const std::string& msg, int level) {
    std::time_t now = std::time(nullptr);
    std::tm* timeinfo = std::localtime(&now);
    char timestamp[64];
    std::strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", timeinfo);
    
    std::string logMsg = "[" + std::string(timestamp) + "] " + msg;
    
    // 输出到控制台
    if (level <= 3) {
        std::cout << logMsg << std::endl;
    }
    
    // 修正日志级别映射
    switch(level) {
        case 0: Logger::Info(msg); break;      // 重要信息
        case 1: Logger::Warning(msg); break;  // 警告信息
        case 2: Logger::Info(msg); break;     // 一般信息
        case 3: Logger::Debug(msg); break;    // 调试信息
        default: Logger::Debug(msg); break;   // 详细调试
    }
}

void GateServer::LoadAbuseFile() {
    std::ifstream file("WordFilter.txt");
    if (!file.is_open()) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_abuseMutex);
    m_abuseList.clear();
    
    std::string line;
    while (std::getline(file, line)) {
        if (!line.empty()) {
            m_abuseList.push_back(line);
        }
    }
    
    file.close();
    AddMainLogMsg("Loaded " + std::to_string(m_abuseList.size()) + " abuse words", 4);
}

void GateServer::LoadBlockIPFile() {
    std::ifstream file("BlockIPList.txt");
    if (!file.is_open()) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_ipMutex);
    m_blockIPList.clear();
    
    std::string line;
    while (std::getline(file, line)) {
        if (!line.empty()) {
            SockAddr addr;
#ifdef _WIN32
            addr.nIPaddr = inet_addr(line.c_str());
            if (addr.nIPaddr != INADDR_NONE) {
#else
            struct in_addr inaddr;
            if (inet_aton(line.c_str(), &inaddr)) {
                addr.nIPaddr = inaddr.s_addr;
#endif
                m_blockIPList.push_back(addr);
            }
        }
    }
    
    file.close();
    AddMainLogMsg("Loaded " + std::to_string(m_blockIPList.size()) + " blocked IPs", 4);
}

void GateServer::SaveBlockIPFile() {
    std::ofstream file("BlockIPList.txt");
    if (!file.is_open()) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_ipMutex);
    for (const auto& addr : m_blockIPList) {
        struct in_addr inAddr;
        inAddr.s_addr = addr.nIPaddr;
#ifdef _WIN32
        file << inet_ntoa(inAddr) << "\n";
#else
        char ip_str[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &inAddr, ip_str, INET_ADDRSTRLEN);
        file << ip_str << "\n";
#endif
    }
    
    file.close();
}

bool GateServer::IsBlockedIP(const std::string& ipAddress) {
#ifdef _WIN32
    int ipAddr = inet_addr(ipAddress.c_str());
    if (ipAddr == INADDR_NONE) {
        return false;
    }
#else
    struct in_addr inaddr;
    if (!inet_aton(ipAddress.c_str(), &inaddr)) {
        return false;
    }
    int ipAddr = inaddr.s_addr;
#endif
    
    std::lock_guard<std::mutex> lock(m_ipMutex);
    for (const auto& addr : m_blockIPList) {
        if (addr.nIPaddr == ipAddr) {
            return true;
        }
    }
    
    for (const auto& addr : m_tempBlockIPList) {
        if (addr.nIPaddr == ipAddr) {
            return true;
        }
    }
    
    return false;
}

bool GateServer::IsConnectionLimited(const std::string& ipAddress, int socketHandle) {
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    auto it = m_currentIPList.find(ipAddress);
    if (it == m_currentIPList.end()) {
        m_currentIPList[ipAddress] = std::vector<SockAddr>();
        it = m_currentIPList.find(ipAddress);
    }
    
    // 添加当前连接
    SockAddr addr;
#ifdef _WIN32
    addr.nIPaddr = inet_addr(ipAddress.c_str());
#else
    struct in_addr inaddr;
    inet_aton(ipAddress.c_str(), &inaddr);
    addr.nIPaddr = inaddr.s_addr;
#endif
    addr.nSocketHandle = socketHandle;
    it->second.push_back(addr);
    
    // 检查连接数量
    return it->second.size() > static_cast<size_t>(m_maxConnOfIP);
}

// 基础实现，其他方法暂时保持简单
void GateServer::OnClientConnect(std::shared_ptr<ClientConnection> connection) {
    AddMainLogMsg("Client connected: " + connection->GetRemoteIP(), 3);
}

void GateServer::OnClientDisconnect(std::shared_ptr<ClientConnection> connection) {
    AddMainLogMsg("Client disconnected", 3);
}

void GateServer::OnClientMessage(std::shared_ptr<ClientConnection> connection, const std::vector<BYTE>& data) {
    // 处理客户端消息
}

void GateServer::OnServerConnect() {
    m_serverReady = true;
    AddMainLogMsg("Connected to game server", 0);
}

void GateServer::OnServerDisconnect() {
    m_serverReady = false;
    AddMainLogMsg("Disconnected from game server", 1);
}

void GateServer::OnServerMessage(const std::vector<BYTE>& data) {
    // 处理服务器消息
}

void GateServer::ProcessUserPacket(const SendUserData& userData) {
    // 处理用户数据包
}

void GateServer::ProcessPacket(const SendUserData& userData) {
    // 处理发送数据包
}

void GateServer::SendServerMsg(int nIdent, WORD wSocketIndex, int nSocket, 
                              int nUserListIndex, int nLen, const char* data) {
    // 发送消息到服务器
}

void GateServer::FilterSayMsg(std::string& msg) {
    // 过滤聊天消息
}

int GateServer::CheckDefaultMessage(const Protocol::DefaultMessage* defMsg, GateSessionInfo* sessionInfo) {
    return 0;
}

void GateServer::SendActionFail(std::shared_ptr<ClientConnection> connection) {
    // 发送动作失败消息
}

void GateServer::SendWarnMsg(GateSessionInfo* sessionInfo) {
    // 发送警告消息
}

int GateServer::AddBlockIP(const std::string& ipAddress) {
    return 0;
}

int GateServer::AddTempBlockIP(const std::string& ipAddress) {
    return 0;
}

int GateServer::GetConnectCountOfIP(const std::string& ipAddress) {
    return 0;
}

int GateServer::GetAttackIPCount(const std::string& ipAddress) {
    return 0;
}

void GateServer::CloseConnectByIP(const std::string& ipAddress) {
    // 关闭指定IP的连接
}

int GateServer::GetActiveConnections() const {
    return m_sessionCount;
}

} // namespace MirServer 