#include "Application.h"
#include "GameState.h"
#include <SDL2/SDL_image.h>
#include <SDL2/SDL_ttf.h>
#include <iostream>
#include <typeinfo>
#include "Utils/Logger.h"
#include "Utils/ExceptionHandler.h"

Application::Application(const std::string& title, int width, int height)
    : m_window(nullptr)
    , m_renderer(nullptr)
    , m_running(false)
    , m_screenWidth(width)
    , m_screenHeight(height)
    , m_title(title)
    , m_currentState(nullptr)
    , m_lastFrameTime(0)
    , m_deltaTime(0.0f)
{
}

Application::~Application()
{
    Shutdown();
}

bool Application::Initialize()
{
    // Initialize logger
    if (!Logger::GetInstance().Initialize("logs/application.log", LogLevel::LOG_LEVEL_INFO)) {
        std::cerr << "Failed to initialize logger" << std::endl;
        // Continue anyway, as logging is not critical
    }

    LOG_INFO("Initializing application");

    // Initialize SDL
    if (!InitSDL()) {
        LOG_ERROR("Failed to initialize SDL");
        return false;
    }

    // Create window
    m_window = SDL_CreateWindow(
        m_title.c_str(),
        SDL_WINDOWPOS_CENTERED,
        SDL_WINDOWPOS_CENTERED,
        m_screenWidth,
        m_screenHeight,
        SDL_WINDOW_SHOWN
    );

    if (!m_window) {
        LOG_ERROR("Failed to create window: " + std::string(SDL_GetError()));
        return false;
    }

    // Create renderer
    m_renderer = SDL_CreateRenderer(
        m_window,
        -1,
        SDL_RENDERER_ACCELERATED | SDL_RENDERER_PRESENTVSYNC
    );

    if (!m_renderer) {
        LOG_ERROR("Failed to create renderer: " + std::string(SDL_GetError()));
        return false;
    }

    // Set renderer draw color
    SDL_SetRenderDrawColor(m_renderer, 0, 0, 0, 255);

    // Initialize timing
    m_lastFrameTime = SDL_GetTicks();
    m_running = true;

    LOG_INFO("Application initialized successfully");

    return true;
}

void Application::Run()
{
    LOG_INFO("Starting main game loop");

    SDL_Event event;

    // Main game loop
    while (m_running) {
        try {
            // Calculate delta time
            Uint32 currentTime = SDL_GetTicks();
            m_deltaTime = (currentTime - m_lastFrameTime) / 1000.0f;
            m_lastFrameTime = currentTime;

            // Handle events
            while (SDL_PollEvent(&event)) {
                if (event.type == SDL_QUIT) {
                    m_running = false;
                }

                // Let the current state handle events
                if (m_currentState) {
                    TRY_CATCH([this, &event]() {
                        m_currentState->HandleEvents(event);
                    });
                }
            }

            // Update game state
            if (m_currentState) {
                TRY_CATCH([this]() {
                    m_currentState->Update(m_deltaTime);
                });
            }

            // Clear screen
            SDL_SetRenderDrawColor(m_renderer, 0, 0, 0, 255);
            SDL_RenderClear(m_renderer);

            // Render game state
            if (m_currentState) {
                TRY_CATCH([this]() {
                    m_currentState->Render();
                });
            }

            // Present renderer
            SDL_RenderPresent(m_renderer);
        }
        catch (const std::exception& e) {
            // Log any uncaught exceptions
            LOG_EXCEPTION(e.what());
        }
        catch (...) {
            // Log unknown exceptions
            LOG_ERROR("Unknown exception in main game loop");
        }
    }

    LOG_INFO("Main game loop ended");
}

void Application::Shutdown()
{
    LOG_INFO("Shutting down application");

    // Clean up current state
    if (m_currentState) {
        m_currentState->Exit();
        m_currentState.reset();
    }

    // Clean up SDL
    CleanupSDL();

    LOG_INFO("Application shutdown complete");

    // Close logger
    Logger::GetInstance().Close();
}

void Application::ChangeState(std::unique_ptr<GameState> state)
{
    // Exit current state
    if (m_currentState) {
        LOG_INFO("Exiting state: " + std::string(typeid(*m_currentState).name()));
        m_currentState->Exit();
    }

    // Change state
    m_currentState = std::move(state);

    // Enter new state
    if (m_currentState) {
        LOG_INFO("Entering state: " + std::string(typeid(*m_currentState).name()));

        // Use exception handler to catch any exceptions during state initialization
        bool success = TRY_CATCH([this]() {
            m_currentState->Enter();
        });

        if (!success) {
            LOG_ERROR("Failed to enter state: " + std::string(typeid(*m_currentState).name()));
        }
    }
}

bool Application::InitSDL()
{
    // Initialize SDL
    if (SDL_Init(SDL_INIT_VIDEO | SDL_INIT_TIMER) < 0) {
        std::cerr << "Failed to initialize SDL: " << SDL_GetError() << std::endl;
        return false;
    }

    // Initialize SDL_image
    int imgFlags = IMG_INIT_PNG | IMG_INIT_JPG;
    if (!(IMG_Init(imgFlags) & imgFlags)) {
        std::cerr << "Failed to initialize SDL_image: " << IMG_GetError() << std::endl;
        return false;
    }

    // Initialize SDL_ttf
    if (TTF_Init() < 0) {
        std::cerr << "Failed to initialize SDL_ttf: " << TTF_GetError() << std::endl;
        return false;
    }

    return true;
}

void Application::CleanupSDL()
{
    // Destroy renderer
    if (m_renderer) {
        SDL_DestroyRenderer(m_renderer);
        m_renderer = nullptr;
    }

    // Destroy window
    if (m_window) {
        SDL_DestroyWindow(m_window);
        m_window = nullptr;
    }

    // Quit SDL subsystems
    TTF_Quit();
    IMG_Quit();
    SDL_Quit();
}

