# MirClient C++ with SDL Refactoring Plan

## Overview
This document outlines the plan to refactor the MirClient from Delphi with DirectX to C++ with SDL for graphics rendering. The goal is to create a modern, cross-platform client that maintains the same functionality as the original.

## Project Structure

```
MirClient-CPP/
├── src/
│   ├── main.cpp
│   ├── Application.cpp
│   ├── Application.h
│   ├── Graphics/
│   │   ├── Renderer.cpp
│   │   ├── Renderer.h
│   │   ├── Texture.cpp
│   │   ├── Texture.h
│   │   ├── WILLoader.cpp
│   │   ├── WILLoader.h
│   │   ├── Sprite.cpp
│   │   ├── Sprite.h
│   │   ├── Animation.cpp
│   │   └── Animation.h
│   ├── UI/
│   │   ├── UIManager.cpp
│   │   ├── UIManager.h
│   │   ├── UIControl.cpp
│   │   ├── UIControl.h
│   │   ├── Button.cpp
│   │   └── Button.h
│   ├── Game/
│   │   ├── GameState.cpp
│   │   ├── GameState.h
│   │   ├── PlayState.cpp
│   │   ├── PlayState.h
│   │   ├── IntroState.cpp
│   │   └── IntroState.h
│   ├── Map/
│   │   ├── MapManager.cpp
│   │   ├── MapManager.h
│   │   ├── MapCell.cpp
│   │   └── MapCell.h
│   ├── Actor/
│   │   ├── Actor.cpp
│   │   ├── Actor.h
│   │   ├── Player.cpp
│   │   ├── Player.h
│   │   ├── Monster.cpp
│   │   └── Monster.h
│   └── Network/
│       ├── NetworkManager.cpp
│       ├── NetworkManager.h
│       ├── Packet.cpp
│       └── Packet.h
├── include/
│   └── SDL2/
├── lib/
├── assets/
│   ├── data/
│   ├── maps/
│   └── sounds/
├── CMakeLists.txt
└── README.md
```

## Implementation Plan

### 1. Core Framework

#### 1.1 Application Class
This will replace the main form (frmMain) in the Delphi version.

```cpp
// Application.h
class Application {
private:
    SDL_Window* m_window;
    SDL_Renderer* m_renderer;
    bool m_running;
    
    // Game states
    std::unique_ptr<GameState> m_currentState;
    
public:
    Application();
    ~Application();
    
    bool Initialize();
    void Run();
    void Shutdown();
    
    void ChangeState(std::unique_ptr<GameState> state);
    
    SDL_Renderer* GetRenderer() const { return m_renderer; }
};
```

#### 1.2 Main Loop
```cpp
// main.cpp
int main(int argc, char* argv[]) {
    Application app;
    
    if (!app.Initialize()) {
        return 1;
    }
    
    app.Run();
    app.Shutdown();
    
    return 0;
}
```

### 2. Graphics System

#### 2.1 WIL File Loader
This will replace the WIL.pas functionality for loading game assets.

```cpp
// WILLoader.h
class WILLoader {
private:
    struct WILHeader {
        uint32_t count;
        uint32_t* offsets;
    };
    
    WILHeader m_header;
    std::string m_filename;
    std::vector<SDL_Surface*> m_surfaces;
    
public:
    WILLoader();
    ~WILLoader();
    
    bool Load(const std::string& filename);
    SDL_Surface* GetSurface(int index);
    int GetCount() const { return m_header.count; }
};
```

#### 2.2 Texture Manager
```cpp
// Texture.h
class Texture {
private:
    SDL_Texture* m_texture;
    SDL_Renderer* m_renderer;
    int m_width;
    int m_height;
    
public:
    Texture(SDL_Renderer* renderer);
    ~Texture();
    
    bool LoadFromSurface(SDL_Surface* surface);
    void Render(int x, int y, SDL_Rect* clip = nullptr);
    void RenderEx(int x, int y, SDL_Rect* clip = nullptr, double angle = 0.0, 
                 SDL_Point* center = nullptr, SDL_RendererFlip flip = SDL_FLIP_NONE);
    
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }
};
```

#### 2.3 Sprite System
```cpp
// Sprite.h
class Sprite {
private:
    std::shared_ptr<Texture> m_texture;
    SDL_Rect m_clip;
    int m_x;
    int m_y;
    
public:
    Sprite(std::shared_ptr<Texture> texture);
    ~Sprite();
    
    void SetClip(int x, int y, int w, int h);
    void SetPosition(int x, int y);
    void Render();
};
```

### 3. Game States

#### 3.1 Game State Interface
```cpp
// GameState.h
class GameState {
public:
    virtual ~GameState() {}
    
    virtual void Enter() = 0;
    virtual void Exit() = 0;
    virtual void Update(float deltaTime) = 0;
    virtual void Render() = 0;
    virtual void HandleEvents(SDL_Event& event) = 0;
};
```

#### 3.2 Play State
This will replace PlayScn.pas functionality.

```cpp
// PlayState.h
class PlayState : public GameState {
private:
    std::unique_ptr<MapManager> m_mapManager;
    std::unique_ptr<ActorManager> m_actorManager;
    
public:
    PlayState(Application* app);
    ~PlayState();
    
    void Enter() override;
    void Exit() override;
    void Update(float deltaTime) override;
    void Render() override;
    void HandleEvents(SDL_Event& event) override;
};
```

### 4. Map System

#### 4.1 Map Manager
```cpp
// MapManager.h
class MapManager {
private:
    std::vector<std::vector<MapCell>> m_cells;
    int m_width;
    int m_height;
    
public:
    MapManager();
    ~MapManager();
    
    bool LoadMap(const std::string& filename);
    void Render(int cameraX, int cameraY);
    
    MapCell& GetCell(int x, int y);
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }
};
```

### 5. Actor System

#### 5.1 Actor Base Class
```cpp
// Actor.h
class Actor {
protected:
    int m_x;
    int m_y;
    int m_direction;
    std::shared_ptr<Animation> m_animation;
    
public:
    Actor(int x, int y);
    virtual ~Actor();
    
    virtual void Update(float deltaTime);
    virtual void Render(int cameraX, int cameraY);
    
    void SetPosition(int x, int y);
    void SetDirection(int direction);
    
    int GetX() const { return m_x; }
    int GetY() const { return m_y; }
    int GetDirection() const { return m_direction; }
};
```

### 6. Network System

#### 6.1 Network Manager
```cpp
// NetworkManager.h
class NetworkManager {
private:
    // Socket implementation (could use SDL_net or another library)
    
public:
    NetworkManager();
    ~NetworkManager();
    
    bool Connect(const std::string& host, int port);
    void Disconnect();
    
    bool SendPacket(const Packet& packet);
    bool ReceivePacket(Packet& packet);
    
    bool IsConnected() const;
};
```

## Implementation Phases

### Phase 1: Basic Framework
- Set up project structure
- Implement Application class
- Create basic SDL window and renderer
- Implement main loop

### Phase 2: Graphics System
- Implement WIL file loader
- Create texture management system
- Implement sprite and animation systems

### Phase 3: Game States
- Implement game state system
- Create intro state
- Implement play state

### Phase 4: Map System
- Implement map loading
- Create map rendering system

### Phase 5: Actor System
- Implement actor base class
- Create player and monster classes
- Implement actor rendering and animation

### Phase 6: UI System
- Implement UI controls
- Create UI manager
- Implement event handling

### Phase 7: Network System
- Implement network manager
- Create packet system
- Implement client-server communication

### Phase 8: Testing and Optimization
- Test all systems
- Optimize rendering
- Fix bugs

## Dependencies
- SDL2
- SDL2_image
- SDL2_ttf
- SDL2_mixer
- SDL2_net (optional)
