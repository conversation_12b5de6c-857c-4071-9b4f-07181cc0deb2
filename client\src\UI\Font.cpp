#include "Font.h"
#include <iostream>

Font::Font(const std::string& path, int size)
    : m_font(nullptr)
    , m_size(size)
    , m_path(path)
{
    // Load font
    m_font = TTF_OpenFont(path.c_str(), size);
    if (!m_font) {
        std::cerr << "Failed to load font: " << path << " - " << TTF_GetError() << std::endl;
    }
}

Font::~Font()
{
    // Free font
    if (m_font) {
        TTF_CloseFont(m_font);
        m_font = nullptr;
    }
}

SDL_Surface* Font::RenderText(const std::string& text, SDL_Color color) const
{
    if (!m_font) {
        return nullptr;
    }

    // Render text
    SDL_Surface* surface = TTF_RenderText_Blended(m_font, text.c_str(), color);
    if (!surface) {
        std::cerr << "Failed to render text: " << TTF_GetError() << std::endl;
    }

    return surface;
}

SDL_Surface* Font::RenderTextWrapped(const std::string& text, SDL_Color color, int wrapLength) const
{
    if (!m_font) {
        return nullptr;
    }

    // Render text with wrapping
    SDL_Surface* surface = TTF_RenderText_Blended_Wrapped(m_font, text.c_str(), color, wrapLength);
    if (!surface) {
        std::cerr << "Failed to render text: " << TTF_GetError() << std::endl;
    }

    return surface;
}

bool Font::GetTextSize(const std::string& text, int& width, int& height) const
{
    if (!m_font) {
        return false;
    }

    // Get text size
    if (TTF_SizeText(m_font, text.c_str(), &width, &height) != 0) {
        std::cerr << "Failed to get text size: " << TTF_GetError() << std::endl;
        return false;
    }

    return true;
}

bool Font::RenderText(SDL_Renderer* renderer, const std::string& text, int x, int y, SDL_Color color) const
{
    if (!m_font || !renderer) {
        return false;
    }

    // Render text to surface
    SDL_Surface* surface = TTF_RenderText_Blended(m_font, text.c_str(), color);
    if (!surface) {
        std::cerr << "Failed to render text: " << TTF_GetError() << std::endl;
        return false;
    }

    // Create texture from surface
    SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
    if (!texture) {
        std::cerr << "Failed to create texture from text surface: " << SDL_GetError() << std::endl;
        SDL_FreeSurface(surface);
        return false;
    }

    // Set up destination rectangle
    SDL_Rect destRect = {x, y, surface->w, surface->h};

    // Render texture
    SDL_RenderCopy(renderer, texture, nullptr, &destRect);

    // Clean up
    SDL_FreeSurface(surface);
    SDL_DestroyTexture(texture);

    return true;
}
