# EGameOfMir2
传奇2服务器端源码, delphi版本

之前那个vc版本的源码, 封包的格式好像不太对, 没有/隔开
这个应该是对的

这个也不太对, 物品的结构体, Name之前应该有个byte 表示name len

## C++ Client Build and Debug Environment Setup

The C++ client implementation uses C<PERSON>ake for build configuration and MinGW64 for compilation. Visual Studio Code is configured for development and debugging.

### Prerequisites

1. **MinGW64** - Install MSYS2 from https://www.msys2.org/ and then install MinGW64 toolchain:
   ```
   pacman -S mingw-w64-x86_64-toolchain
   ```

2. **SDL2 Libraries** - Install SDL2 and its extensions:
   ```
   pacman -S mingw-w64-x86_64-SDL2 mingw-w64-x86_64-SDL2_image mingw-w64-x86_64-SDL2_ttf mingw-w64-x86_64-SDL2_mixer
   ```

3. **CMake** - Install CMake:
   ```
   pacman -S mingw-w64-x86_64-cmake
   ```

4. **Visual Studio Code** - Install VS Code and the following extensions:
   - C/C++ Extension (ms-vscode.cpptools)
   - CMake Tools (ms-vscode.cmake-tools)

### Building the Project

#### Using the Command Line

1. Open MSYS2 MinGW64 terminal
2. Navigate to the project directory
3. Run the build script:
   ```
   ./build.sh
   ```

   Or manually:
   ```
   mkdir -p build
   cd build
   cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Debug ..
   cmake --build . -- -j4
   ```

4. The executable will be in the `build/bin` directory

#### Using Visual Studio Code

1. Open the project folder in VS Code
2. Use the provided tasks:
   - Press `Ctrl+Shift+B` to build the project (default build task)
   - Or use the Terminal > Run Task menu to select:
     - "CMake Configure" - to configure the project
     - "CMake Build" - to build the project
     - "CMake Clean" - to clean the build
     - "Run" - to run the executable

### Debugging

#### Using Visual Studio Code

1. Set breakpoints in your code
2. Press F5 to start debugging
3. Use the debug toolbar to control execution (continue, step over, step into, etc.)

### Troubleshooting

#### SDL2 Libraries Not Found

If CMake cannot find SDL2 libraries, you may need to adjust the SDL2 paths in CMakeLists.txt:

```cmake
set(SDL2_PATH "C:/msys64/mingw64" CACHE PATH "Path to SDL2 installation")
```

#### Debugging Not Working

Make sure:
1. You have built the project in Debug mode
2. The path to GDB in launch.json is correct
3. The executable path in launch.json matches your build output

#### Common MinGW Issues

1. **Path Issues**: Ensure MinGW's bin directory is in your PATH environment variable
2. **DLL Not Found**: Copy required DLLs (SDL2.dll, etc.) to your executable directory
3. **Permission Issues**: Make sure build.sh has execute permissions (`chmod +x build.sh`)