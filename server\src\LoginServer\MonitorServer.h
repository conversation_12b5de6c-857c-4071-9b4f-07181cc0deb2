#ifndef MONITOR_SERVER_H
#define MONITOR_SERVER_H

#include <string>
#include <memory>
#include <thread>
#include <atomic>
#include "../Protocol/NetworkManager.h"

namespace MirServer {

// Forward declarations
class MsgServerManager;

class MonitorServer {
public:
    MonitorServer(MsgServerManager* msgManager);
    ~MonitorServer();
    
    bool Initialize(const std::string& monAddr, int32_t monPort);
    bool Start();
    void Stop();
    
private:
    std::unique_ptr<Network::NetworkManager> m_network;
    MsgServerManager* m_msgServerManager;
    std::atomic<bool> m_running{false};
    std::thread m_broadcastThread;
    
    void BroadcastLoop();
    void SendStatusToAllClients();
    std::string GetServerStatusString();
};

} // namespace MirServer

#endif // MONITOR_SERVER_H 