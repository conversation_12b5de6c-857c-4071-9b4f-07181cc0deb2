#include "IntroState.h"
#include "../Application.h"
#include "LoginState.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>
#include <cstdint>  // For uint8_t
#include <algorithm> // For std::min
#include "../Utils/Logger.h" // For logging

IntroState::IntroState(Application* app)
    : GameState(app)
    , m_fadeTimer(0)
    , m_fadeDirection(1)
    , m_fadeAlpha(0)
    , m_showMenu(false)
    , m_selectedOption(0)
    , m_hoverOption(-1)
    , m_handCursor(nullptr)
    , m_arrowCursor(nullptr)
{
    LOG_INFO("IntroState: Initializing");

    // Create cursors
    m_handCursor = SDL_CreateSystemCursor(SDL_SYSTEM_CURSOR_HAND);
    if (!m_handCursor) {
        LOG_WARNING("IntroState: Failed to create hand cursor: " + std::string(SDL_GetError()));
    }

    m_arrowCursor = SDL_CreateSystemCursor(SDL_SYSTEM_CURSOR_ARROW);
    if (!m_arrowCursor) {
        LOG_WARNING("IntroState: Failed to create arrow cursor: " + std::string(SDL_GetError()));
    }

    LOG_DEBUG("IntroState: Initialization complete");
}

IntroState::~IntroState()
{
    // Free cursors
    if (m_handCursor) {
        SDL_FreeCursor(m_handCursor);
        m_handCursor = nullptr;
    }

    if (m_arrowCursor) {
        SDL_FreeCursor(m_arrowCursor);
        m_arrowCursor = nullptr;
    }
}

void IntroState::Enter()
{
    // Force a small delay at startup to ensure the fade effect is noticeable
    SDL_Delay(500);  // 500ms delay

    // Load background texture
    m_backgroundTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (!m_backgroundTexture->LoadFromFile("assets/data/intro_background.png")) {
        LOG_ERROR("IntroState: Failed to load intro background texture: assets/data/intro_background.png");
    } else {
        LOG_DEBUG("IntroState: Background texture loaded successfully");
    }

    // Load logo texture with black color key (for transparency)
    m_logoTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (!m_logoTexture->LoadFromFile("assets/data/logo.png")) {
        LOG_ERROR("IntroState: Failed to load logo texture: assets/data/logo.png");
    } else {
        // Set black as the transparent color
        m_logoTexture->SetColorKey(0, 0, 0);
        m_logoTexture->SetBlendMode(SDL_BLENDMODE_BLEND);
        LOG_DEBUG("IntroState: Logo texture loaded successfully with transparency");
    }

    // Create text textures
    CreateTextTextures();

    // Set initial fade state
    m_fadeDirection = 1;
    m_fadeAlpha = 0;
    m_fadeTimer = 0;
    m_showMenu = false;

    // Create status message - even if font fails, create a fallback message
    LOG_DEBUG("IntroState: Creating status message");
    TTF_Font* statusFont = TTF_OpenFont("assets/data/font.ttf", 24);
    if (statusFont) {
        LOG_DEBUG("IntroState: Font loaded successfully for status message");
        // Use bright yellow for visibility
        SDL_Color statusColor = {255, 255, 0, 255};
        m_statusTexture = std::make_shared<Texture>(m_app->GetRenderer());

        // Create a text surface with outline for better readability
        SDL_Surface* textSurface = TTF_RenderText_Blended(statusFont, "Loading resources... Please wait", statusColor);
        if (textSurface) {
            LOG_DEBUG("IntroState: Text surface created successfully");
            // Create a slightly larger surface for adding a black outline
            SDL_Surface* outlineSurface = SDL_CreateRGBSurface(0,
                                                              textSurface->w + 4,
                                                              textSurface->h + 4,
                                                              32,
                                                              0xFF000000,
                                                              0x00FF0000,
                                                              0x0000FF00,
                                                              0x000000FF);
            if (outlineSurface) {
                // Fill with black background
                SDL_FillRect(outlineSurface, NULL, SDL_MapRGBA(outlineSurface->format, 0, 0, 0, 200));

                // Copy the text surface to the center of the outline surface
                SDL_Rect destRect = {2, 2, textSurface->w, textSurface->h};
                SDL_BlitSurface(textSurface, NULL, outlineSurface, &destRect);

                // Create texture from the combined surface
                m_statusTexture->LoadFromSurface(outlineSurface);
                LOG_DEBUG("IntroState: Status texture created with outline");

                // Free the surface
                SDL_FreeSurface(outlineSurface);
            } else {
                // If creating outline surface fails, use the text surface directly
                LOG_WARNING("IntroState: Failed to create outline surface: " + std::string(SDL_GetError()));
                m_statusTexture->LoadFromSurface(textSurface);
            }

            SDL_FreeSurface(textSurface);
        } else {
            // If rendering text fails, fall back to simple text rendering
            LOG_WARNING("IntroState: Failed to render text surface: " + std::string(TTF_GetError()));
            m_statusTexture->LoadFromText("Loading resources... Please wait", statusFont, statusColor);
        }

        TTF_CloseFont(statusFont);
    } else {
        // Create a fallback status message using SDL_Surface
        LOG_ERROR("IntroState: Failed to load font for status message: " + std::string(TTF_GetError()));
        LOG_INFO("IntroState: Creating fallback status message");

        SDL_Surface* surface = SDL_CreateRGBSurface(0, 400, 30, 32,
                                                  0xFF000000,
                                                  0x00FF0000,
                                                  0x0000FF00,
                                                  0x000000FF);
        if (surface) {
            SDL_FillRect(surface, NULL, SDL_MapRGBA(surface->format, 0, 0, 0, 128));
            m_statusTexture = std::make_shared<Texture>(m_app->GetRenderer());
            m_statusTexture->LoadFromSurface(surface);
            SDL_FreeSurface(surface);
            LOG_DEBUG("IntroState: Fallback status texture created");
        } else {
            LOG_ERROR("IntroState: Failed to create fallback surface: " + std::string(SDL_GetError()));
        }
    }

    // Play intro music
    // TODO: Play intro music
}

void IntroState::Exit()
{
    LOG_INFO("IntroState: Exiting state");

    // Clear textures
    LOG_DEBUG("IntroState: Clearing textures");
    m_backgroundTexture.reset();
    m_logoTexture.reset();
    m_statusTexture.reset();
    m_textTextures.clear();

    // Stop music
    // TODO: Stop intro music

    LOG_DEBUG("IntroState: Exit complete");
}

void IntroState::Update(float deltaTime)
{
    // Update fade effect
    m_fadeTimer += static_cast<int>(deltaTime * 1000);
    // If we've been fully faded in for 1 second, transition to LoginState
    if (m_fadeDirection == 0) {
        // We're already in the waiting state, check if we've waited long enough
        if (m_fadeTimer >= 1000) {  // 1 second wait after fade-in
            // Go to login screen
            m_app->ChangeState(std::make_unique<LoginState>(m_app));
        }
    }
    if (m_fadeDirection > 0) {
        // Fading in - faster fade (10 instead of 50 for a quicker effect)
        m_fadeAlpha = std::min(255, m_fadeTimer / 20);

        // When fade-in is complete, wait a short time then transition to LoginState
        if (m_fadeAlpha >= 255) {
            // Just reached full fade-in, start the wait timer
            m_fadeDirection = 0;  // Fully visible, waiting to transition
            m_fadeTimer = 0;      // Reset timer for the waiting period
            LOG_INFO("IntroState: Fade-in complete, waiting before transition");
            
        }
    }

    // Fade-out effect is removed as requested
}

void IntroState::Render()
{
    // Clear the screen with black
    SDL_SetRenderDrawColor(m_app->GetRenderer(), 0, 0, 0, 255);
    SDL_RenderClear(m_app->GetRenderer());

    // Render background with fade effect
    if (m_backgroundTexture) {
        // Set blend mode to ensure alpha works correctly
        m_backgroundTexture->SetBlendMode(SDL_BLENDMODE_BLEND);
        m_backgroundTexture->SetAlpha(m_fadeAlpha);
        m_backgroundTexture->Render(0, 0);
    } else {
        // If no background texture, draw a colored rectangle with fade
        SDL_SetRenderDrawColor(m_app->GetRenderer(), 0, 0, 128, m_fadeAlpha); // Dark blue
        SDL_Rect rect = {0, 0, m_app->GetScreenWidth(), m_app->GetScreenHeight()};
        SDL_SetRenderDrawBlendMode(m_app->GetRenderer(), SDL_BLENDMODE_BLEND);
        SDL_RenderFillRect(m_app->GetRenderer(), &rect);
    }

    // Render logo with fade effect, scaling, and transparency
    if (m_logoTexture) {
        // Ensure blend mode is set for transparency and alpha
        m_logoTexture->SetBlendMode(SDL_BLENDMODE_BLEND);
        m_logoTexture->SetAlpha(m_fadeAlpha);

        // Define maximum dimensions for the logo
        const int maxLogoWidth = 300;  // Maximum width in pixels
        const int maxLogoHeight = 150; // Maximum height in pixels

        // Calculate scaling factors
        float scaleX = 1.0f;
        float scaleY = 1.0f;

        if (m_logoTexture->GetWidth() > maxLogoWidth) {
            scaleX = static_cast<float>(maxLogoWidth) / m_logoTexture->GetWidth();
        }

        if (m_logoTexture->GetHeight() > maxLogoHeight) {
            scaleY = static_cast<float>(maxLogoHeight) / m_logoTexture->GetHeight();
        }

        // Use the smaller scale to maintain aspect ratio
        float scale = std::min(scaleX, scaleY);

        // Calculate scaled dimensions
        int scaledWidth = static_cast<int>(m_logoTexture->GetWidth() * scale);
        int scaledHeight = static_cast<int>(m_logoTexture->GetHeight() * scale);

        // Calculate centered position
        int logoX = (m_app->GetScreenWidth() - scaledWidth) / 2;
        int logoY = 100;

        // Create destination rectangle for scaled rendering
        SDL_Rect destRect = {logoX, logoY, scaledWidth, scaledHeight};

        // Save current render target
        SDL_Texture* currentTarget = SDL_GetRenderTarget(m_app->GetRenderer());

        // Create a temporary texture for rendering with transparency
        SDL_Texture* tempTexture = SDL_CreateTexture(
            m_app->GetRenderer(),
            SDL_PIXELFORMAT_RGBA8888,
            SDL_TEXTUREACCESS_TARGET,
            scaledWidth,
            scaledHeight
        );

        if (tempTexture) {
            // Set blend mode for the temporary texture
            SDL_SetTextureBlendMode(tempTexture, SDL_BLENDMODE_BLEND);

            // Set the temporary texture as the render target
            SDL_SetRenderTarget(m_app->GetRenderer(), tempTexture);

            // Clear the temporary texture with transparent color
            SDL_SetRenderDrawColor(m_app->GetRenderer(), 0, 0, 0, 0);
            SDL_RenderClear(m_app->GetRenderer());

            // Render the logo to the temporary texture
            SDL_Rect srcRect = {0, 0, m_logoTexture->GetWidth(), m_logoTexture->GetHeight()};
            SDL_Rect dstRect = {0, 0, scaledWidth, scaledHeight};
            SDL_RenderCopy(m_app->GetRenderer(), m_logoTexture->GetSDLTexture(), &srcRect, &dstRect);

            // Restore the original render target
            SDL_SetRenderTarget(m_app->GetRenderer(), currentTarget);

            // Render the temporary texture to the screen
            SDL_RenderCopy(m_app->GetRenderer(), tempTexture, nullptr, &destRect);

            // Clean up the temporary texture
            SDL_DestroyTexture(tempTexture);
        } else {
            // Fallback to direct rendering if temporary texture creation fails
            SDL_RenderCopy(m_app->GetRenderer(), m_logoTexture->GetSDLTexture(), nullptr, &destRect);
        }
    }

    // Always show the status message at the bottom of the screen
    if (m_statusTexture) {
        // Always show status at full opacity regardless of fade
        m_statusTexture->SetBlendMode(SDL_BLENDMODE_BLEND);
        m_statusTexture->SetAlpha(255);  // Always fully visible

        // Draw a more visible background for the text
        SDL_SetRenderDrawColor(m_app->GetRenderer(), 0, 0, 0, 200); // More opaque black background
        SDL_Rect rect = {0, m_app->GetScreenHeight() - 80, m_app->GetScreenWidth(), 60}; // Larger background area
        SDL_SetRenderDrawBlendMode(m_app->GetRenderer(), SDL_BLENDMODE_BLEND);
        SDL_RenderFillRect(m_app->GetRenderer(), &rect);

        // Add a border to make the text area more prominent
        SDL_SetRenderDrawColor(m_app->GetRenderer(), 255, 255, 0, 255); // Yellow border
        SDL_RenderDrawRect(m_app->GetRenderer(), &rect);

        // Draw the status text
        int textX = (m_app->GetScreenWidth() - m_statusTexture->GetWidth()) / 2;
        int textY = m_app->GetScreenHeight() - 60;  // 60 pixels from the bottom
        m_statusTexture->Render(textX, textY);

        // Add a loading animation effect
        int animTime = (m_fadeTimer / 500) % 4; // Change every 500ms
        std::string dots = "";
        for (int i = 0; i < animTime; i++) {
            dots += ".";
        }

        // If we have a font, render dynamic dots
        if (m_statusTexture->GetWidth() >= 10) {
            // Draw dynamic dots to the right of the text
            SDL_SetRenderDrawColor(m_app->GetRenderer(), 255, 255, 0, 255);
            for (int i = 0; i < animTime; i++) {
                SDL_Rect dotRect = {textX + m_statusTexture->GetWidth() + 10 + i*10, textY + 10, 5, 5};
                SDL_RenderFillRect(m_app->GetRenderer(), &dotRect);
            }
        } else {
            // If texture loading failed, draw a simple loading indicator
            SDL_SetRenderDrawColor(m_app->GetRenderer(), 255, 255, 255, 255);
            SDL_Rect loadingRect = {m_app->GetScreenWidth()/2 - 100, textY, 200, 30};
            SDL_RenderDrawRect(m_app->GetRenderer(), &loadingRect);

            // Draw progress bar
            int progressWidth = (m_fadeTimer % 2000) / 10; // Cycle in 0-200 range
            if (progressWidth > 200) progressWidth = 400 - progressWidth;
            SDL_Rect progressRect = {m_app->GetScreenWidth()/2 - 100, textY, progressWidth, 30};
            SDL_RenderFillRect(m_app->GetRenderer(), &progressRect);
        }
    }

    // Render copyright text at the bottom of the screen
    if (m_textTextures.size() > 2 && m_textTextures[2]) {
        int textX = (m_app->GetScreenWidth() - m_textTextures[2]->GetWidth()) / 2;
        int textY = m_app->GetScreenHeight() - 30;  // 30 pixels from the bottom
        m_textTextures[2]->Render(textX, textY);
    } else {
        // Create a temporary texture for copyright if it doesn't exist
        TTF_Font* tempFont = TTF_OpenFont("assets/data/font.ttf", 12);
        if (!tempFont) tempFont = TTF_OpenFont("font.ttf", 12);

        if (tempFont) {
            SDL_Color textColor = {200, 200, 200, 255};
            SDL_Surface* textSurface = TTF_RenderText_Solid(tempFont, "Legend of Mir 2 - Copyright © 2023", textColor);
            if (textSurface) {
                SDL_Texture* texture = SDL_CreateTextureFromSurface(m_app->GetRenderer(), textSurface);
                if (texture) {
                    SDL_Rect destRect = {m_app->GetScreenWidth()/2 - textSurface->w/2, m_app->GetScreenHeight() - 30, textSurface->w, textSurface->h};
                    SDL_RenderCopy(m_app->GetRenderer(), texture, NULL, &destRect);
                    SDL_DestroyTexture(texture);
                }
                SDL_FreeSurface(textSurface);
            }
            TTF_CloseFont(tempFont);
        }
    }

    // Menu is no longer rendered as requested
}

void IntroState::HandleEvents(SDL_Event& event)
{
    // Allow skipping the intro with any key or mouse click
    if (event.type == SDL_KEYDOWN || event.type == SDL_MOUSEBUTTONDOWN) {
        // Skip to LoginState immediately
        LOG_INFO("IntroState: User input detected, transitioning to LoginState");
        m_app->ChangeState(std::make_unique<LoginState>(m_app));
    }

    // Menu-related event handling is removed as requested
}

void IntroState::CreateTextTextures()
{
    LOG_INFO("IntroState: Creating text textures");

    // Check if font file exists
    FILE* file = fopen("assets/data/font.ttf", "r");
    if (file) {
        fclose(file);
        LOG_DEBUG("IntroState: Font file exists at assets/data/font.ttf");
    } else {
        LOG_WARNING("IntroState: Font file does not exist at assets/data/font.ttf");

        // Try to find the font in the current directory
        file = fopen("font.ttf", "r");
        if (file) {
            fclose(file);
            LOG_DEBUG("IntroState: Font file exists at ./font.ttf");
        } else {
            LOG_ERROR("IntroState: Font file does not exist at ./font.ttf");
        }
    }

    // Load font for copyright text
    TTF_Font* smallFont = TTF_OpenFont("assets/data/font.ttf", 12);
    if (!smallFont) {
        LOG_WARNING("IntroState: Failed to load small font from assets/data/font.ttf, trying fallback");
        smallFont = TTF_OpenFont("font.ttf", 12);
    }

    if (smallFont) {
        // Copyright text
        LOG_DEBUG("IntroState: Creating copyright texture");
        std::shared_ptr<Texture> copyrightTexture = std::make_shared<Texture>(m_app->GetRenderer());
        SDL_Color copyrightColor = {200, 200, 200, 255};  // Light gray
        if (copyrightTexture->LoadFromText("Legend of Mir 2 - Copyright © 2023", smallFont, copyrightColor)) {
            m_textTextures.push_back(copyrightTexture);
            LOG_DEBUG("IntroState: Created copyright texture: " +
                     std::to_string(copyrightTexture->GetWidth()) + "x" +
                     std::to_string(copyrightTexture->GetHeight()));
        } else {
            LOG_ERROR("IntroState: Failed to create copyright texture");
        }
        TTF_CloseFont(smallFont);
    } else {
        LOG_ERROR("IntroState: Failed to load small font from any location");
    }

    // Summary output
    LOG_INFO("IntroState: Created " + std::to_string(m_textTextures.size()) + " text textures");

    // Menu option textures are no longer created as requested
}

void IntroState::HandleMenuSelection()
{
    // This method is kept as a stub for compatibility but is no longer used
    LOG_INFO("IntroState: HandleMenuSelection called but is no longer used");

    // Directly transition to LoginState
    m_app->ChangeState(std::make_unique<LoginState>(m_app));
}

