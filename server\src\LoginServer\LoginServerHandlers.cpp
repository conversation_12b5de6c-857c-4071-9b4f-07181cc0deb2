#include "LoginServer.h"
#include "AccountDB.h"
#include "MsgServerManager.h"
#include "../Common/Logger.h"
#include "../Protocol/MessageConverter.h"
#include "../Protocol/PacketTypes.h"
#include <chrono>
#include <cstring>
#include <sstream>

namespace MirServer {

// User message processing
void LoginServer::ProcessUserMessage(std::shared_ptr<UserSession> user, const std::string& msg) {
    if (msg.length() < DEFBLOCKSIZE) return;
    
    // Decode the default message header
    std::string defMsg = msg.substr(0, DEFBLOCKSIZE);
    std::string data = msg.substr(DEFBLOCKSIZE);
    
    TDefaultMessage dm;
    std::memcpy(&dm, defMsg.c_str(), sizeof(TDefaultMessage));
    
    // Update activity
    user->lastActivity = std::chrono::steady_clock::now();
    
    switch (dm.Ident) {
        case CM_PROTOCOL:
            HandleProtocol(user, dm.Recog);
            break;
            
        case CM_IDPASSWORD:
            if (user->sAccount.empty()) {
                HandleLogin(user, data);
            } else {
                KickUser(user, 0);
            }
            break;
            
        case CM_SELECTSERVER:
            if (!user->boSelServer) {
                HandleSelectServer(user, data);
            }
            break;
            
        case CM_ADDNEWUSER:
            if (m_config.boEnableMakingID) {
                HandleNewAccount(user, data);
            }
            break;
            
        case CM_CHANGEPASSWORD:
            if (user->sAccount.empty()) {
                HandleChangePassword(user, data);
            }
            break;
            
        case CM_UPDATEUSER:
            HandleUpdateUserInfo(user, data);
            break;
            
        case CM_GETBACKPASSWORD:
            if (m_config.boEnableGetbackPassword) {
                HandleGetBackPassword(user, data);
            }
            break;
            
        default:
            Logger::Warning("Unknown message ID: " + std::to_string(dm.Ident));
            break;
    }
}

void LoginServer::HandleProtocol(std::shared_ptr<UserSession> user, int32_t version) {
    TDefaultMessage dm;
    
    // Version check - assuming version date is ********
    const int32_t REQUIRED_VERSION = ********;
    
    if (version < REQUIRED_VERSION) {
        dm = MakeDefaultMsg(SM_CERTIFICATION_FAIL, 0, 0, 0, 0);
    } else {
        dm = MakeDefaultMsg(SM_CERTIFICATION_SUCCESS, 0, 0, 0, 0);
        user->nVersionDate = version;
        user->boCertificationOK = true;
    }
    
    SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
}

void LoginServer::HandleLogin(std::shared_ptr<UserSession> user, const std::string& data) {
    // Decode login data
    std::string decoded = Protocol::MessageConverter::DecodeString(data);
    
    // Parse login ID and password (format: "id/password")
    size_t pos = decoded.find('/');
    if (pos == std::string::npos) {
        TDefaultMessage dm = MakeDefaultMsg(SM_PASSWD_FAIL, -1, 0, 0, 0);
        SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
        return;
    }
    
    std::string loginID = decoded.substr(0, pos);
    std::string password = decoded.substr(pos + 1);
    
    // Validate login
    int32_t nCode = 0;
    bool boNeedUpdate = false;
    TUserEntry userEntry;
    TAccountDBRecord dbRecord;
    
    // Check account in database
    if (m_accountDB->Open()) {
        int32_t index = m_accountDB->Index(loginID);
        if (index >= 0 && m_accountDB->Get(index, dbRecord)) {
            // Check error count and unlock time
            auto now = std::chrono::steady_clock::now().time_since_epoch();
            uint32_t nowTick = static_cast<uint32_t>(std::chrono::duration_cast<std::chrono::milliseconds>(now).count());
            
            // Auto unlock account
            if (m_config.boUnLockAccount && dbRecord.nErrorCount >= 5 && 
                (nowTick - dbRecord.dwActionTick) >= m_config.dwUnLockAccountTime * 60 * 1000) {
                dbRecord.nErrorCount = 0;
                dbRecord.dwActionTick = nowTick - 70000;
            }
            
            if (dbRecord.nErrorCount < 5 || (nowTick - dbRecord.dwActionTick) > 60000) {
                if (std::string(dbRecord.UserEntry.sPassword) == password) {
                    dbRecord.nErrorCount = 0;
                    nCode = 1;  // Success
                    
                    // Check if need update
                    if (strlen(dbRecord.UserEntry.sUserName) == 0 || 
                        strlen(dbRecord.UserEntryAdd.sQuiz2) == 0) {
                        userEntry = dbRecord.UserEntry;
                        boNeedUpdate = true;
                    }
                } else {
                    dbRecord.nErrorCount++;
                    dbRecord.dwActionTick = nowTick;
                    nCode = -1;  // Wrong password
                }
                m_accountDB->Update(index, dbRecord);
            } else {
                nCode = -2;  // Account locked
                dbRecord.dwActionTick = nowTick;
                m_accountDB->Update(index, dbRecord);
            }
        }
        m_accountDB->Close();
    }
    
    // Check if already logged in
    if (nCode == 1 && IsLogin(loginID)) {
        SessionKick(loginID);
        nCode = -3;  // Already logged in
    }
    
    // Send response
    if (boNeedUpdate) {
        TDefaultMessage dm = MakeDefaultMsg(SM_NEEDUPDATE_ACCOUNT, 0, 0, 0, 0);
        std::string msg = EncodeDefaultMessage(dm) + 
                         Protocol::MessageConverter::EncodeBuffer(&userEntry, sizeof(TUserEntry));
        SendGateMsg(user->gate.lock(), user->sSockIndex, msg);
    } else if (nCode == 1) {
        // Login success
        user->sAccount = loginID;
        user->nSessionID = GetNewSessionID();
        user->boSelServer = false;
        
        // Add session
        SessionAdd(loginID, user->sUserIPaddr, user->nSessionID, false);
        
        // Send server list
        TDefaultMessage dm = MakeDefaultMsg(SM_PASSOK_SELECTSERVER, 0, 0, 0, 
                                                     static_cast<int32_t>(m_config.serverNameList.size()));
        std::string msg = EncodeDefaultMessage(dm) + GetServerListInfo();
        SendGateMsg(user->gate.lock(), user->sSockIndex, msg);
        
        m_stats.totalLogins++;
        Logger::Info("User login: " + loginID + " from " + user->sUserIPaddr);
    } else {
        // Login failed
        TDefaultMessage dm = MakeDefaultMsg(SM_PASSWD_FAIL, nCode, 0, 0, 0);
        SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
        
        m_stats.failedLogins++;
    }
}

void LoginServer::HandleSelectServer(std::shared_ptr<UserSession> user, const std::string& data) {
    if (user->sAccount.empty() || user->nSessionID == 0) {
        return;
    }
    
    std::string serverName = Protocol::MessageConverter::DecodeString(data);
    
    // Find server in route table
    std::string selGateIP;
    int32_t selGatePort = 0;
    GetSelGateInfo(serverName, user->sUserIPaddr, selGateIP, selGatePort);
    
    if (!selGateIP.empty() && selGatePort > 0) {
        // Check if server is not full
        if (m_msgServerManager->IsNotUserFull(serverName)) {
            // Update session
            user->boSelServer = true;
            SessionUpdate(user->nSessionID, serverName, user->boPayCost);
            
            // Notify GameServer about new session
            int32_t payMode = 5;  // Default free mode
            if (user->boPayCost) {
                // Calculate pay mode based on user's payment status
                // This would be based on account cost configuration
                payMode = 1;  // Simplified for now
            }
            
            // Send OPENSESSION message to GameServer
            // Format: account/sessionID/payCost/payMode/userIP
            std::string sessionMsg = user->sAccount + "/" + 
                                   std::to_string(user->nSessionID) + "/" +
                                   std::to_string(user->boPayCost ? 1 : 0) + "/" +
                                   std::to_string(payMode) + "/" +
                                   user->sUserIPaddr;
            
            m_msgServerManager->SendServerMsg(100, serverName, sessionMsg);  // SS_OPENSESSION = 100
            
            // Send success response with gate info
            TDefaultMessage dm = MakeDefaultMsg(SM_SELECTSERVER_OK, user->nSessionID, 0, 0, 0);
            std::string msg = EncodeDefaultMessage(dm) + 
                             Protocol::MessageConverter::EncodeString(selGateIP + "/" + 
                                                                      std::to_string(selGatePort) + "/" +
                                                                      std::to_string(user->nSessionID));
            SendGateMsg(user->gate.lock(), user->sSockIndex, msg);
            
            Logger::Info("User " + user->sAccount + " selected server: " + serverName);
        } else {
            // Server is full
            user->boSelServer = false;
            SessionDel(user->nSessionID);
            
            TDefaultMessage dm = MakeDefaultMsg(SM_STARTFAIL, 0, 0, 0, 0);
            SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
            
            Logger::Info("Server full: " + serverName + " for user: " + user->sAccount);
        }
    } else {
        // Server not found or unavailable
        TDefaultMessage dm = MakeDefaultMsg(SM_SELECTSERVER_FAIL, 0, 0, 0, 0);
        SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
    }
}

void LoginServer::HandleNewAccount(std::shared_ptr<UserSession> user, const std::string& data) {
    // Decode user entry data
    size_t entrySize = GetEncodedSize(sizeof(TUserEntry) * 4 / 3);
    if (data.length() < entrySize) {
        TDefaultMessage dm = MakeDefaultMsg(SM_NEWID_FAIL, -1, 0, 0, 0);
        SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
        return;
    }
    
    std::string userEntryMsg = data.substr(0, entrySize);
    std::string userAddEntryMsg = data.substr(entrySize);
    
    TUserEntry userEntry;
    TUserEntryAdd userAddEntry;
    
    Protocol::MessageConverter::DecodeBuffer(userEntryMsg, &userEntry, sizeof(TUserEntry));
    Protocol::MessageConverter::DecodeBuffer(userAddEntryMsg, &userAddEntry, sizeof(TUserEntryAdd));
    
    // Validate account name
    if (!m_accountDB->CheckAccountName(userEntry.sAccount)) {
        TDefaultMessage dm = MakeDefaultMsg(SM_NEWID_FAIL, -1, 0, 0, 0);
        SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
        return;
    }
    
    // Create new account
    int32_t nCode = -1;
    if (m_accountDB->Open()) {
        int32_t index = m_accountDB->Index(userEntry.sAccount);
        if (index < 0) {  // Account doesn't exist
            TAccountDBRecord dbRecord;
            std::memset(&dbRecord, 0, sizeof(TAccountDBRecord));
            dbRecord.UserEntry = userEntry;
            dbRecord.UserEntryAdd = userAddEntry;
            
            if (m_accountDB->Add(dbRecord)) {
                nCode = 1;  // Success
                m_stats.newAccounts++;
                WriteLogMsg("new", userEntry, userAddEntry);
            }
        } else {
            nCode = 0;  // Account already exists
        }
        m_accountDB->Close();
    }
    
    // Send response
    TDefaultMessage dm;
    if (nCode == 1) {
        dm = MakeDefaultMsg(SM_NEWID_SUCCESS, 0, 0, 0, 0);
        Logger::Info("New account created: " + std::string(userEntry.sAccount));
    } else {
        dm = MakeDefaultMsg(SM_NEWID_FAIL, nCode, 0, 0, 0);
    }
    SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
}

void LoginServer::HandleChangePassword(std::shared_ptr<UserSession> user, const std::string& data) {
    std::string decoded = Protocol::MessageConverter::DecodeString(data);
    
    // Parse format: "loginid\toldpass\tnewpass"
    std::vector<std::string> parts;
    size_t start = 0;
    size_t pos = 0;
    while ((pos = decoded.find('\t', start)) != std::string::npos) {
        parts.push_back(decoded.substr(start, pos - start));
        start = pos + 1;
    }
    parts.push_back(decoded.substr(start));
    
    if (parts.size() != 3) {
        TDefaultMessage dm = MakeDefaultMsg(SM_CHGPASSWD_FAIL, -1, 0, 0, 0);
        SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
        return;
    }
    
    std::string loginID = parts[0];
    std::string oldPassword = parts[1];
    std::string newPassword = parts[2];
    
    int32_t nCode = 0;
    TAccountDBRecord dbRecord;
    
    if (m_accountDB->Open() && newPassword.length() >= 3) {
        int32_t index = m_accountDB->Index(loginID);
        if (index >= 0 && m_accountDB->Get(index, dbRecord)) {
            auto now = std::chrono::steady_clock::now().time_since_epoch();
            uint32_t nowTick = static_cast<uint32_t>(std::chrono::duration_cast<std::chrono::milliseconds>(now).count());
            
            if (dbRecord.nErrorCount < 5 || (nowTick - dbRecord.dwActionTick) > 180000) {
                if (std::string(dbRecord.UserEntry.sPassword) == oldPassword) {
                    dbRecord.nErrorCount = 0;
                    strncpy(dbRecord.UserEntry.sPassword, newPassword.c_str(), sizeof(dbRecord.UserEntry.sPassword) - 1);
                    nCode = 1;  // Success
                    m_stats.passwordChanges++;
                } else {
                    dbRecord.nErrorCount++;
                    dbRecord.dwActionTick = nowTick;
                    nCode = -1;  // Wrong old password
                }
                m_accountDB->Update(index, dbRecord);
            } else {
                nCode = -2;  // Account locked
            }
        }
        m_accountDB->Close();
    }
    
    // Send response
    TDefaultMessage dm;
    if (nCode == 1) {
        dm = MakeDefaultMsg(SM_CHGPASSWD_SUCCESS, 0, 0, 0, 0);
        WriteLogMsg("chg", dbRecord.UserEntry, dbRecord.UserEntryAdd);
        Logger::Info("Password changed for: " + loginID);
    } else {
        dm = MakeDefaultMsg(SM_CHGPASSWD_FAIL, nCode, 0, 0, 0);
    }
    SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
}

void LoginServer::HandleUpdateUserInfo(std::shared_ptr<UserSession> user, const std::string& data) {
    // Decode user entry data
    size_t entrySize = GetEncodedSize(sizeof(TUserEntry) * 4 / 3);
    if (data.length() < entrySize) {
        TDefaultMessage dm = MakeDefaultMsg(SM_UPDATEID_FAIL, -1, 0, 0, 0);
        SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
        return;
    }
    
    std::string userEntryMsg = data.substr(0, entrySize);
    std::string userAddEntryMsg = data.substr(entrySize);
    
    TUserEntry userEntry;
    TUserEntryAdd userAddEntry;
    
    Protocol::MessageConverter::DecodeBuffer(userEntryMsg, &userEntry, sizeof(TUserEntry));
    Protocol::MessageConverter::DecodeBuffer(userAddEntryMsg, &userAddEntry, sizeof(TUserEntryAdd));
    
    int32_t nCode = -1;
    
    // Verify account matches
    if (user->sAccount == userEntry.sAccount && m_accountDB->CheckAccountName(userEntry.sAccount)) {
        if (m_accountDB->Open()) {
            TAccountDBRecord dbRecord;
            int32_t index = m_accountDB->Index(userEntry.sAccount);
            
            if (index >= 0 && m_accountDB->Get(index, dbRecord)) {
                // Update user information
                dbRecord.UserEntry = userEntry;
                dbRecord.UserEntryAdd = userAddEntry;
                
                if (m_accountDB->Update(index, dbRecord)) {
                    nCode = 1;  // Success
                    WriteLogMsg("upg", userEntry, userAddEntry);
                }
            } else {
                nCode = 0;  // Account not found
            }
            m_accountDB->Close();
        }
    }
    
    // Send response
    TDefaultMessage dm;
    if (nCode == 1) {
        dm = MakeDefaultMsg(SM_UPDATEID_SUCCESS, 0, 0, 0, 0);
        Logger::Info("User info updated: " + std::string(userEntry.sAccount));
    } else {
        dm = MakeDefaultMsg(SM_UPDATEID_FAIL, nCode, 0, 0, 0);
    }
    SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
}

void LoginServer::HandleGetBackPassword(std::shared_ptr<UserSession> user, const std::string& data) {
    std::string decoded = Protocol::MessageConverter::DecodeString(data);
    
    // Parse format: "account\tquest1\tanswer1\tquest2\tanswer2\tbirthday"
    std::vector<std::string> parts;
    size_t start = 0;
    size_t pos = 0;
    
    while ((pos = decoded.find('\t', start)) != std::string::npos) {
        parts.push_back(decoded.substr(start, pos - start));
        start = pos + 1;
    }
    parts.push_back(decoded.substr(start));
    
    if (parts.size() != 6) {
        TDefaultMessage dm = MakeDefaultMsg(SM_GETBACKPASSWD_FAIL, -1, 0, 0, 0);
        SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
        return;
    }
    
    std::string account = parts[0];
    std::string quest1 = parts[1];
    std::string answer1 = parts[2];
    std::string quest2 = parts[3];
    std::string answer2 = parts[4];
    std::string birthday = parts[5];
    
    int32_t nCode = 0;
    std::string password;
    TAccountDBRecord dbRecord;
    
    if (!account.empty() && m_accountDB->Open()) {
        int32_t index = m_accountDB->Index(account);
        if (index >= 0 && m_accountDB->Get(index, dbRecord)) {
            auto now = std::chrono::steady_clock::now().time_since_epoch();
            uint32_t nowTick = static_cast<uint32_t>(std::chrono::duration_cast<std::chrono::milliseconds>(now).count());
            
            if (dbRecord.nErrorCount < 5 || (nowTick - dbRecord.dwActionTick) > 180000) {
                nCode = -1;  // Default: wrong info
                
                // Check first set of security questions
                if (std::string(dbRecord.UserEntry.sQuiz) == quest1) {
                    nCode = -3;  // Wrong answer
                    if (std::string(dbRecord.UserEntry.sAnswer) == answer1) {
                        if (std::string(dbRecord.UserEntryAdd.sBirthDay) == birthday) {
                            nCode = 1;  // Success
                        }
                    }
                }
                
                // If first set failed, try second set
                if (nCode != 1) {
                    if (std::string(dbRecord.UserEntryAdd.sQuiz2) == quest2) {
                        nCode = -3;  // Wrong answer
                        if (std::string(dbRecord.UserEntryAdd.sAnswer2) == answer2) {
                            if (std::string(dbRecord.UserEntryAdd.sBirthDay) == birthday) {
                                nCode = 1;  // Success
                            }
                        }
                    }
                }
                
                if (nCode == 1) {
                    password = dbRecord.UserEntry.sPassword;
                } else {
                    // Increase error count
                    dbRecord.nErrorCount++;
                    dbRecord.dwActionTick = nowTick;
                    m_accountDB->Update(index, dbRecord);
                }
            } else {
                nCode = -2;  // Account locked
                if (nowTick < dbRecord.dwActionTick) {
                    dbRecord.dwActionTick = nowTick;
                    m_accountDB->Update(index, dbRecord);
                }
            }
        }
        m_accountDB->Close();
    }
    
    // Send response
    TDefaultMessage dm;
    if (nCode == 1) {
        dm = MakeDefaultMsg(SM_GETBACKPASSWD_SUCCESS, 0, 0, 0, 0);
        std::string msg = EncodeDefaultMessage(dm) + 
                         Protocol::MessageConverter::EncodeString(password);
        SendGateMsg(user->gate.lock(), user->sSockIndex, msg);
        Logger::Info("Password recovered for account: " + account);
    } else {
        dm = MakeDefaultMsg(SM_GETBACKPASSWD_FAIL, nCode, 0, 0, 0);
        SendGateMsg(user->gate.lock(), user->sSockIndex, EncodeDefaultMessage(dm));
    }
}

// Session management
int32_t LoginServer::GetNewSessionID() {
    int32_t id = m_sessionIDCounter.fetch_add(1);
    if (id >= 0x7FFFFFFF) {
        m_sessionIDCounter = 2;
        id = 1;
    }
    return id;
}

void LoginServer::SessionAdd(const std::string& account, const std::string& ipaddr, 
                            int32_t sessionID, bool payCost) {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    auto session = std::make_shared<UserSession>("");
    session->sAccount = account;
    session->sUserIPaddr = ipaddr;
    session->nSessionID = sessionID;
    session->boPayCost = payCost;
    session->lastActivity = std::chrono::steady_clock::now();
    
    m_sessionList[sessionID] = session;
    m_stats.onlineUsers++;
}

void LoginServer::SessionDel(int32_t sessionID) {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    auto it = m_sessionList.find(sessionID);
    if (it != m_sessionList.end()) {
        m_sessionList.erase(it);
        m_stats.onlineUsers--;
    }
}

void LoginServer::SessionKick(const std::string& account) {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    for (auto it = m_sessionList.begin(); it != m_sessionList.end(); ) {
        if (it->second->sAccount == account) {
            it = m_sessionList.erase(it);
            m_stats.onlineUsers--;
        } else {
            ++it;
        }
    }
}

void LoginServer::SessionUpdate(int32_t sessionID, const std::string& serverName, bool payCost) {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    auto it = m_sessionList.find(sessionID);
    if (it != m_sessionList.end()) {
        it->second->boSelServer = true;
        it->second->lastActivity = std::chrono::steady_clock::now();
    }
}

bool LoginServer::IsLogin(const std::string& account) {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    for (const auto& pair : m_sessionList) {
        if (pair.second->sAccount == account) {
            return true;
        }
    }
    return false;
}

bool LoginServer::IsLogin(int32_t sessionID) {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    return m_sessionList.find(sessionID) != m_sessionList.end();
}

// Gate communication
void LoginServer::SendGateMsg(std::shared_ptr<GateConnection> gate, 
                             const std::string& sockIndex, const std::string& msg) {
    if (!gate) return;
    
    // Format: %dsockindex/msg$
    std::string packet = "%d" + sockIndex + "/" + msg + "$";
    
    gate->connection->Send(packet.data(), packet.size());
}

void LoginServer::SendGateKickMsg(std::shared_ptr<GateConnection> gate, const std::string& sockIndex) {
    if (!gate) return;
    
    // Format: %xsockindex$
    std::string packet = "%x" + sockIndex + "$";
    
    gate->connection->Send(packet.data(), packet.size());
}

void LoginServer::SendGateAddBlockList(std::shared_ptr<GateConnection> gate, const std::string& sockIndex) {
    if (!gate) return;
    
    // Format: %bsockindex$
    std::string packet = "%b" + sockIndex + "$";
    
    gate->connection->Send(packet.data(), packet.size());
}

void LoginServer::SendGateAddTempBlockList(std::shared_ptr<GateConnection> gate, const std::string& sockIndex) {
    if (!gate) return;
    
    // Format: %tsockindex$
    std::string packet = "%t" + sockIndex + "$";
    
    gate->connection->Send(packet.data(), packet.size());
}

void LoginServer::SendKeepAlivePacket(std::shared_ptr<GateConnection> gate) {
    if (!gate) return;
    
    // Format: %k$
    std::string packet = "%k$";
    
    gate->connection->Send(packet.data(), packet.size());
}

// Utility functions
std::string LoginServer::GetServerListInfo() {
    std::string result;
    
    for (const auto& serverName : m_config.serverNameList) {
        // Get server status from MsgServerManager
        int32_t status = m_msgServerManager->ServerStatus(serverName);
        result += serverName + "/" + std::to_string(status) + "/";
    }
    
    return Protocol::MessageConverter::EncodeString(result);
}

void LoginServer::GetSelGateInfo(const std::string& serverName, const std::string& ipaddr,
                                std::string& selGateIP, int32_t& selGatePort) {
    selGateIP = "";
    selGatePort = 0;
    
    // Find server in route table
    for (const auto& route : m_config.routes) {
        if (route.sServerName == serverName) {
            // Select a gate based on round-robin
            if (!route.gates.empty()) {
                int32_t idx = route.nSelIdx % static_cast<int32_t>(route.gates.size());
                const auto& gate = route.gates[idx];
                
                if (gate.boEnable) {
                    selGateIP = gate.sIPaddr;
                    selGatePort = gate.nPort;
                    
                    // Update round-robin index
                    const_cast<ServerRoute&>(route).nSelIdx++;
                }
            }
            break;
        }
    }
}

bool LoginServer::KickUser(std::shared_ptr<UserSession> user, int32_t kickType) {
    auto gate = user->gate.lock();
    if (!gate) return false;
    
    switch (kickType) {
        case 0:  // Normal kick
            SendGateKickMsg(gate, user->sSockIndex);
            break;
        case 1:  // Temp block
            SendGateAddTempBlockList(gate, user->sSockIndex);
            break;
        case 2:  // Permanent block
            SendGateAddBlockList(gate, user->sSockIndex);
            break;
    }
    
    gate->RemoveUser(user->sSockIndex);
    
    if (m_config.boShowDetailMsg) {
        Logger::Debug("Kicked user: " + user->sSockIndex + " type: " + std::to_string(kickType));
    }
    
    return true;
}

void LoginServer::CloseUser(const std::string& account, int32_t sessionID) {
    SessionDel(sessionID);
}

// Logging functions
void LoginServer::WriteLogMsg(const std::string& type, const TUserEntry& userEntry, 
                             const TUserEntryAdd& userAddEntry) {
    // This would write to a log file
    // For now, just log to console
    std::ostringstream msg;
    msg << "Account log [" << type << "]: " << userEntry.sAccount << " from " << userAddEntry.sQuiz2;
    Logger::Info(msg.str());
}

void LoginServer::SaveContLogMsg(const std::string& logMsg) {
    // This would save continuous log messages
    // For now, just log to console
    Logger::Debug("ContLog: " + logMsg);
}

} // namespace MirServer 


