#pragma once

#include "Dialog.h"
#include "MessageDialog.h"
#include <memory>
#include <vector>
#include <string>
#include <functional>

/**
 * @class DialogManager
 * @brief Manages dialogs
 *
 * This class is responsible for creating and managing dialogs.
 */
class DialogManager {
private:
    std::vector<std::shared_ptr<Dialog>> m_dialogs;  ///< Active dialogs
    SDL_Renderer* m_renderer;                        ///< SDL renderer
    std::shared_ptr<WILManager> m_wilManager;        ///< WIL manager
    std::string m_resourceFile;                      ///< Resource file

public:
    /**
     * @brief Constructor
     * @param renderer SDL renderer
     */
    DialogManager(SDL_Renderer* renderer);

    /**
     * @brief Destructor
     */
    ~DialogManager();

    /**
     * @brief Update all dialogs
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    void Update(int deltaTime);

    /**
     * @brief Render all dialogs
     */
    void Render();

    /**
     * @brief Handle SDL events
     * @param event SDL event
     * @return true if handled, false otherwise
     */
    bool HandleEvent(const SDL_Event& event);

    /**
     * @brief Show a message dialog
     * @param title Dialog title
     * @param message Dialog message
     * @param buttons Dialog buttons
     * @param callback Result callback function
     * @return Created dialog
     */
    std::shared_ptr<Dialog> ShowMessageDialog(const std::string& title, const std::string& message, DialogButtons buttons = DialogButtons::OK, std::function<void(DialogResult)> callback = nullptr);

    /**
     * @brief Show a message dialog with a specific message type
     * @param title Dialog title
     * @param message Dialog message
     * @param messageType Message type
     * @param buttons Dialog buttons
     * @param callback Result callback function
     * @return Created dialog
     */
    std::shared_ptr<MessageDialog> ShowMessageDialog(const std::string& title, const std::string& message, MessageType messageType, DialogButtons buttons = DialogButtons::OK, std::function<void(DialogResult)> callback = nullptr);

    /**
     * @brief Show an input dialog
     * @param title Dialog title
     * @param message Dialog message
     * @param defaultText Default input text
     * @param callback Result callback function
     * @return Created dialog
     */
    std::shared_ptr<Dialog> ShowInputDialog(const std::string& title, const std::string& message, const std::string& defaultText = "", std::function<void(const std::string&)> callback = nullptr);

    /**
     * @brief Close all dialogs
     */
    void CloseAllDialogs();

    /**
     * @brief Get the number of active dialogs
     * @return Number of active dialogs
     */
    size_t GetDialogCount() const { return m_dialogs.size(); }

    /**
     * @brief Check if any dialogs are active
     * @return true if any dialogs are active, false otherwise
     */
    bool HasActiveDialogs() const { return !m_dialogs.empty(); }

    /**
     * @brief Set the WIL manager
     * @param wilManager WIL manager
     */
    void SetWILManager(std::shared_ptr<WILManager> wilManager) { m_wilManager = wilManager; }

    /**
     * @brief Set the resource file
     * @param resourceFile Resource file
     */
    void SetResourceFile(const std::string& resourceFile) { m_resourceFile = resourceFile; }

    /**
     * @brief Get the WIL manager
     * @return WIL manager
     */
    std::shared_ptr<WILManager> GetWILManager() const { return m_wilManager; }

    /**
     * @brief Get the resource file
     * @return Resource file
     */
    const std::string& GetResourceFile() const { return m_resourceFile; }
};
