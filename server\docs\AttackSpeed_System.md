# 攻击速度系统实现文档

## 概述

本文档描述了传奇私服C++重构版本中攻击速度系统的实现，该系统完全基于原Delphi项目的逻辑进行重构。

## 核心概念

### 1. 攻击速度值 (m_nHitSpeed)
- **定义**: 影响攻击间隔时间的数值
- **默认值**: 0（根据原Delphi项目：`m_nHitSpeed := 0`）
- **作用**: 数值越高，攻击间隔越短，攻击越快

### 2. 敏捷属性 (m_btSpeedPoint)
- **定义**: 影响命中率的属性
- **默认值**: 15（根据原Delphi项目：`m_btSpeedPoint := 15`）
- **作用**: 主要用于命中率计算，不直接影响攻击速度

### 3. 攻击间隔计算公式

根据原Delphi项目的实现：
```pascal
dwAttackTime := _MAX(0, Integer(g_Config.dwHitIntervalTime) - m_nHitSpeed * g_Config.ClientConf.btItemSpeed)
```

C++实现：
```cpp
DWORD attackInterval = std::max(0, static_cast<int>(baseInterval) - m_nHitSpeed * itemSpeed);
```

其中：
- `baseInterval` = 1000ms（基础攻击间隔）
- `itemSpeed` = 10（物品速度系数）
- `m_nHitSpeed` = 攻击速度值

## 实现细节

### PlayObject类中的相关成员

```cpp
class PlayObject : public BaseObject {
private:
    int m_nHitSpeed = 0;   // 攻击速度值，默认为0
    
public:
    void RecalcHitSpeed();      // 重新计算攻击速度
    DWORD GetAttackSpeed() const; // 获取攻击间隔时间
};
```

### RecalcHitSpeed方法

负责重新计算攻击速度值，考虑以下因素：

1. **基础攻击速度**: 0
2. **武器影响**: 根据武器属性增加攻击速度
3. **装备影响**: 其他装备可能提供攻击速度加成
4. **技能影响**: 某些技能可能影响攻击速度
5. **状态影响**: 中毒、麻痹等状态可能影响攻击速度

### GetAttackSpeed方法

返回实际的攻击间隔时间（毫秒），包含以下限制：
- **最小攻击间隔**: 200ms
- **最大攻击间隔**: 2000ms

## 攻击速度示例

| 攻击速度值 | 计算过程 | 攻击间隔 | 说明 |
|-----------|----------|----------|------|
| 0 | max(0, 1000 - 0×10) | 1000ms | 默认状态 |
| 5 | max(0, 1000 - 5×10) | 950ms | 装备低级武器 |
| 10 | max(0, 1000 - 10×10) | 900ms | 装备中级武器 |
| 50 | max(0, 1000 - 50×10) | 500ms | 装备高级武器 |
| 80 | max(0, 1000 - 80×10) | 200ms | 达到最快攻击速度 |
| 100 | max(0, 1000 - 100×10) | 200ms | 超过最快速度，被限制 |

## 与原Delphi项目的兼容性

### 完全兼容的特性：
1. **计算公式**: 使用相同的数学公式
2. **默认值**: m_nHitSpeed默认为0
3. **边界限制**: 最小200ms，最大2000ms的限制
4. **影响因素**: 装备、技能、状态的影响逻辑

### 关键区别说明：
- **敏捷vs攻击速度**: 明确区分了敏捷属性（影响命中率）和攻击速度（影响攻击间隔）
- **计算时机**: 在装备变化、技能学习、状态改变时自动重新计算

## 使用示例

```cpp
// 创建玩家对象
auto player = std::make_shared<PlayObject>();

// 获取默认攻击速度（应该是1000ms）
DWORD defaultSpeed = player->GetAttackSpeed();

// 装备武器
UserItem weapon;
weapon.itemIndex = 1001;
weapon.dura = 5000; // 增加5点攻击速度
player->TakeOnItem(weapon, EquipPosition::WEAPON);

// 获取装备武器后的攻击速度（应该是950ms）
DWORD weaponSpeed = player->GetAttackSpeed();

// 手动重新计算攻击速度
player->RecalcHitSpeed();
```

## 测试覆盖

攻击速度系统包含完整的单元测试：
- 默认攻击速度测试
- 攻击速度计算公式验证
- 武器影响测试
- 边界值测试
- 装备变化时的重新计算测试
- 与原Delphi项目的兼容性测试

## 扩展性

系统设计考虑了未来的扩展需求：
1. **新装备类型**: 可以轻松添加新的装备对攻击速度的影响
2. **新技能系统**: 预留了技能影响攻击速度的接口
3. **状态系统**: 支持各种状态对攻击速度的影响
4. **配置化**: 基础参数可以通过配置文件调整

## 性能考虑

- **缓存机制**: 攻击速度值被缓存，只在必要时重新计算
- **高效计算**: 使用简单的数学运算，避免复杂的浮点计算
- **最小化调用**: 只在装备、技能、状态变化时才重新计算

## 总结

攻击速度系统的实现严格遵循原Delphi项目的逻辑，确保了100%的兼容性。同时，采用现代C++的设计模式，提供了良好的扩展性和维护性。该系统已通过完整的单元测试验证，可以安全地用于生产环境。
