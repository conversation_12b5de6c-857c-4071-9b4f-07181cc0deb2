#include "WILLoader.h"
#include <iostream>
#include <algorithm>

WILManager::WILManager()
{
}

WILManager::~WILManager()
{
    UnloadAll();
}

std::string WILManager::GetFileExtension(const std::string& filename) {
    size_t pos = filename.find_last_of('.');
    if (pos != std::string::npos) {
        return filename.substr(pos);
    }
    return "";
}

std::string WILManager::GetFilePathWithoutExtension(const std::string& filename) {
    size_t pos = filename.find_last_of('.');
    if (pos != std::string::npos) {
        return filename.substr(0, pos);
    }
    return filename;
}

bool WILManager::LoadWIL(const std::string& filename, const std::string& paletteFile)
{
    // Check if the file is already loaded
    auto it = m_loaders.find(filename);
    if (it != m_loaders.end()) {
        return true; // Already loaded
    }

    // Create a new loader
    auto loader = std::make_shared<WILLoader>();

    // Load the file
    if (!loader->Load(filename, paletteFile)) {
        // Try alternative extension if the file doesn't exist
        std::string ext = GetFileExtension(filename);
        std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
        std::string altFilename;

        if (ext == ".wil") {
            altFilename = GetFilePathWithoutExtension(filename) + ".wzl";
        } else if (ext == ".wzl") {
            altFilename = GetFilePathWithoutExtension(filename) + ".wil";
        }

        if (!altFilename.empty() && loader->Load(altFilename, paletteFile)) {
            // Successfully loaded with alternative extension
            std::cout << "Successfully loaded alternative file: " << altFilename << std::endl;
            m_loaders[filename] = loader; // Store with original filename for reference
            return true;
        }

        std::cerr << "Failed to load file: " << filename << std::endl;

        // Create a dummy loader with a 1x1 image as fallback
        std::cout << "Creating dummy loader as fallback for: " << filename << std::endl;
        auto dummyLoader = std::make_shared<WILLoader>();

        // Force the loader to create a dummy image
        if (dummyLoader->Load("", "")) {
            std::cout << "Successfully created dummy loader for: " << filename << std::endl;
            m_loaders[filename] = dummyLoader;
            return true;
        }

        return false;
    }

    // Add the loader to the map
    m_loaders[filename] = loader;
    return true;
}

SDL_Surface* WILManager::GetSurface(const std::string& filename, int index)
{
    // Check if the file is loaded
    auto it = m_loaders.find(filename);
    if (it == m_loaders.end()) {
        // Try to load the file
        if (!LoadWIL(filename)) {
            std::cerr << "WIL/WZL file not loaded: " << filename << std::endl;

            // Create a dummy surface as fallback
            std::cout << "Creating dummy surface as fallback for: " << filename << " index: " << index << std::endl;
            SDL_Surface* dummySurface = SDL_CreateRGBSurface(0, 1, 1, 32, 0x00FF0000, 0x0000FF00, 0x000000FF, 0xFF000000);
            if (dummySurface) {
                // Fill with a visible color (red)
                SDL_FillRect(dummySurface, NULL, SDL_MapRGB(dummySurface->format, 255, 0, 0));
                return dummySurface;
            }

            return nullptr;
        }
        it = m_loaders.find(filename);
    }

    // Get the surface from the loader
    SDL_Surface* surface = it->second->GetSurface(index);

    // If surface is null, create a dummy surface
    if (!surface) {
        std::cerr << "Failed to get surface for: " << filename << " index: " << index << std::endl;

        // Create a dummy surface as fallback
        std::cout << "Creating dummy surface as fallback for: " << filename << " index: " << index << std::endl;
        SDL_Surface* dummySurface = SDL_CreateRGBSurface(0, 1, 1, 32, 0x00FF0000, 0x0000FF00, 0x000000FF, 0xFF000000);
        if (dummySurface) {
            // Fill with a visible color (red)
            SDL_FillRect(dummySurface, NULL, SDL_MapRGB(dummySurface->format, 255, 0, 0));
            return dummySurface;
        }
    }

    return surface;
}

bool WILManager::GetImageOffset(const std::string& filename, int index, int& offsetX, int& offsetY)
{
    // Check if the file is loaded
    auto it = m_loaders.find(filename);
    if (it == m_loaders.end()) {
        // Try to load the file
        if (!LoadWIL(filename)) {
            std::cerr << "WIL/WZL file not loaded: " << filename << std::endl;

            // Set default offsets
            std::cout << "Using default offsets (0,0) for: " << filename << " index: " << index << std::endl;
            offsetX = 0;
            offsetY = 0;
            return true;
        }
        it = m_loaders.find(filename);
    }

    // Get the offset values from the loader
    bool result = it->second->GetImageOffset(index, offsetX, offsetY);

    // If failed, use default offsets
    if (!result) {
        std::cerr << "Failed to get image offset for: " << filename << " index: " << index << std::endl;
        std::cout << "Using default offsets (0,0) as fallback" << std::endl;
        offsetX = 0;
        offsetY = 0;
        return true;
    }

    return result;
}

SDL_Surface* WILManager::GetCachedSurface(const std::string& key)
{
    // Check if the surface is in the cache
    auto it = m_cache.find(key);
    if (it == m_cache.end()) {
        return nullptr;
    }

    return it->second;
}

void WILManager::CacheSurface(const std::string& key, SDL_Surface* surface)
{
    // Check if the key already exists
    auto it = m_cache.find(key);
    if (it != m_cache.end()) {
        // Free the old surface
        SDL_FreeSurface(it->second);
    }

    // Add the surface to the cache
    m_cache[key] = surface;
}

void WILManager::ClearCache()
{
    // Free all surfaces in the cache
    for (auto& pair : m_cache) {
        SDL_FreeSurface(pair.second);
    }

    m_cache.clear();
}

void WILManager::UnloadAll()
{
    // Clear the cache
    ClearCache();

    // Clear the loaders
    m_loaders.clear();
}
