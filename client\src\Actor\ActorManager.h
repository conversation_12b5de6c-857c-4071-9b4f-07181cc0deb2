#pragma once

#include "Actor.h"
#include "Player.h"
#include "Monster.h"
#include "../Graphics/WILLoader.h"
#include <SDL2/SDL_ttf.h>
#include <memory>
#include <unordered_map>
#include <vector>

/**
 * @class ActorManager
 * @brief Manages all actors in the game
 *
 * This class is responsible for creating, updating, and rendering all actors in the game.
 * It also provides methods for finding actors by ID or position.
 */
class ActorManager {
private:
    std::unordered_map<int, std::shared_ptr<Actor>> m_actors;  ///< Map of actors by ID
    std::shared_ptr<Player> m_localPlayer;                     ///< Pointer to the local player

    std::shared_ptr<WILManager> m_wilManager;                  ///< WIL manager for actor sprites
    SDL_Renderer* m_renderer;                                  ///< SDL renderer
    TTF_Font* m_chatFont;                                      ///< Font for chat bubbles

    int m_nextActorId;                                         ///< Next actor ID to assign

    /**
     * @brief Load actor sprites
     * @param actor Actor to load sprites for
     * @param spritesetName Spriteset name
     * @return true if successful, false otherwise
     */
    bool LoadActorSprites(std::shared_ptr<Actor> actor, const std::string& spritesetName);

public:
    /**
     * @brief Constructor
     * @param wilManager WIL manager for actor sprites
     * @param renderer SDL renderer
     */
    ActorManager(std::shared_ptr<WILManager> wilManager, SDL_Renderer* renderer);

    /**
     * @brief Destructor
     */
    ~ActorManager();

    /**
     * @brief Update all actors
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    void Update(int deltaTime);

    /**
     * @brief Render all actors
     */
    void Render();

    /**
     * @brief Create a player
     * @param name Player name
     * @param playerClass Player class
     * @param isLocalPlayer Whether this is the local player
     * @return Pointer to the created player
     */
    std::shared_ptr<Player> CreatePlayer(const std::string& name, PlayerClass playerClass, bool isLocalPlayer = false);

    /**
     * @brief Create a monster
     * @param name Monster name
     * @param type Monster type
     * @param level Monster level
     * @return Pointer to the created monster
     */
    std::shared_ptr<Monster> CreateMonster(const std::string& name, MonsterType type, int level);

    /**
     * @brief Get an actor by ID
     * @param id Actor ID
     * @return Pointer to the actor or nullptr if not found
     */
    std::shared_ptr<Actor> GetActor(int id);

    /**
     * @brief Get the local player
     * @return Pointer to the local player or nullptr if not found
     */
    std::shared_ptr<Player> GetLocalPlayer();

    /**
     * @brief Get actors at a position
     * @param x X coordinate
     * @param y Y coordinate
     * @return Vector of actors at the position
     */
    std::vector<std::shared_ptr<Actor>> GetActorsAt(int x, int y);

    /**
     * @brief Get actors in a range
     * @param x X coordinate
     * @param y Y coordinate
     * @param range Range in tiles
     * @return Vector of actors in the range
     */
    std::vector<std::shared_ptr<Actor>> GetActorsInRange(int x, int y, int range);

    /**
     * @brief Remove an actor
     * @param id Actor ID
     * @return true if successful, false otherwise
     */
    bool RemoveActor(int id);

    /**
     * @brief Clear all actors
     */
    void ClearActors();

    /**
     * @brief Get the number of actors
     * @return Number of actors
     */
    size_t GetActorCount() const { return m_actors.size(); }

    /**
     * @brief Get all actors
     * @return Vector of all actors
     */
    std::vector<std::shared_ptr<Actor>> GetActors() const;

    /**
     * @brief Make an actor say a message
     * @param id Actor ID
     * @param message Message to say
     * @param duration Duration in milliseconds (default: 5000)
     * @return true if successful, false otherwise
     */
    bool ActorSay(int id, const std::string& message, int duration = 5000);

    /**
     * @brief Set the chat font for all actors
     * @param font Font to use for chat bubbles
     */
    void SetChatFont(TTF_Font* font);

    /**
     * @brief Clear all non-player actors
     */
    void ClearNonPlayerActors();

    /**
     * @brief Spawn a player at a position
     * @param id Player ID
     * @param name Player name
     * @param playerClass Player class
     * @param x X position
     * @param y Y position
     * @param isLocalPlayer Whether this is the local player
     * @return Pointer to the created player
     */
    std::shared_ptr<Player> SpawnPlayer(int id, const std::string& name, PlayerClass playerClass, int x, int y, bool isLocalPlayer = false);

    /**
     * @brief Spawn a monster at a position
     * @param id Monster ID
     * @param name Monster name
     * @param type Monster type
     * @param level Monster level
     * @param x X position
     * @param y Y position
     * @return Pointer to the created monster
     */
    std::shared_ptr<Monster> SpawnMonster(int id, const std::string& name, MonsterType type, int level, int x, int y);

    /**
     * @brief Spawn an NPC at a position
     * @param id NPC ID
     * @param name NPC name
     * @param appearance NPC appearance
     * @param x X position
     * @param y Y position
     * @return Pointer to the created NPC
     */
    std::shared_ptr<Actor> SpawnNPC(int id, const std::string& name, int appearance, int x, int y);

    /**
     * @brief Spawn an item at a position
     * @param id Item ID
     * @param itemType Item type
     * @param itemLook Item appearance
     * @param x X position
     * @param y Y position
     * @return Pointer to the created item
     */
    std::shared_ptr<Actor> SpawnItem(int id, int itemType, int itemLook, int x, int y);

    /**
     * @brief Despawn an actor
     * @param id Actor ID
     * @return true if successful, false otherwise
     */
    bool DespawnActor(int id);

    /**
     * @brief Move an actor
     * @param id Actor ID
     * @param x New X position
     * @param y New Y position
     * @param direction New direction
     * @param moveType Movement type
     * @return true if successful, false otherwise
     */
    bool MoveActor(int id, int x, int y, int direction, int moveType);

    /**
     * @brief Make an actor attack
     * @param id Actor ID
     * @param targetId Target actor ID
     * @param attackType Attack type
     * @return true if successful, false otherwise
     */
    bool ActorAttack(int id, int targetId, int attackType);

    /**
     * @brief Make an actor cast a spell
     * @param id Actor ID
     * @param targetId Target actor ID
     * @param skillId Skill ID
     * @param skillLevel Skill level
     * @return true if successful, false otherwise
     */
    bool ActorCast(int id, int targetId, int skillId, int skillLevel);

    /**
     * @brief Apply an effect to an actor
     * @param id Actor ID
     * @param effectId Effect ID
     * @return true if successful, false otherwise
     */
    bool ApplyEffectToActor(int id, int effectId);


};

