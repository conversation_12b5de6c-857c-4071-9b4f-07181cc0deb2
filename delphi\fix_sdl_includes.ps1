# PowerShell script to fix SDL includes
$sourceDir = "src"

# Function to process a file
function Process-File {
    param (
        [string]$filePath
    )
    
    $content = Get-Content -Path $filePath -Raw
    
    # Replace <SDL.h> with <SDL2/SDL.h>
    $newContent = $content -replace '#include\s+<SDL\.h>', '#include <SDL2/SDL.h>'
    
    # Replace <SDL_*.h> with <SDL2/SDL_*.h>
    $newContent = $newContent -replace '#include\s+<SDL_([^>]+)>', '#include <SDL2/SDL_$1>'
    
    # Only write to the file if changes were made
    if ($content -ne $newContent) {
        Write-Host "Updating: $filePath"
        Set-Content -Path $filePath -Value $newContent
    }
}

# Get all C++ source and header files
$files = Get-ChildItem -Path $sourceDir -Recurse -Include *.cpp, *.h

# Process each file
foreach ($file in $files) {
    Process-File -filePath $file.FullName
}

Write-Host "SDL include paths have been updated." 