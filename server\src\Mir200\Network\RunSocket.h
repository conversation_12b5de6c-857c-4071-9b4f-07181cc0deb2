#pragma once

// Mir200 RunSocket - Main game socket system
// Based on delphi/EM2Engine/RunSock.pas - Following original project structure
// Phase 1 Implementation - Basic placeholder for M2Server integration

#include "Common/M2Share.h"
#include <memory>
#include <string>
#include <mutex>

// RunSocket class - Main game network socket
class RunSocket {
private:
    // Socket state
    bool m_initialized;
    bool m_running;
    
    // Network configuration
    std::string m_listen_addr;
    int m_listen_port;
    int m_max_connections;
    
    // Thread safety
    std::mutex m_socket_mutex;
    
public:
    RunSocket();
    ~RunSocket();
    
    // Core lifecycle
    bool Initialize();
    void Finalize();
    bool Start();
    void Stop();
    
    // Message processing
    void ProcessMessages();
    
    // Emergency operations
    void EmergencyStop();
    
    // State management
    bool IsInitialized() const { return m_initialized; }
    bool IsRunning() const { return m_running; }
    
    // Configuration
    void SetListenAddress(const std::string& addr) { m_listen_addr = addr; }
    void SetListenPort(int port) { m_listen_port = port; }
    void SetMaxConnections(int max_conn) { m_max_connections = max_conn; }
};
