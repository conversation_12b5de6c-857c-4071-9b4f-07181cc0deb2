; LoginServer Configuration File
; Legend of MIR Server - Login Server Settings

[Server]
; Database server connection
DBServer=127.0.0.1
DBSPort=16300

; ID database directory
IdDir=./DB/

; Gate server settings (for accepting connections from login gates)
GateAddr=0.0.0.0
GatePort=5500

; Server listening settings (for game servers)
ServerAddr=0.0.0.0
ServerPort=5600

; Monitor server settings
MonAddr=0.0.0.0
MonPort=3000

[Features]
; Enable new account creation
EnableMakingID=1

; Enable password recovery
EnableGetbackPassword=1

; Auto clear inactive accounts
AutoClearID=1
AutoClearTime=1000

; Auto unlock locked accounts
UnLockAccount=1
UnLockAccountTime=10

; Dynamic IP mode
DynamicIPMode=0

; Show detailed messages in log
ShowDetailMsg=1

[Logging]
; Log directory
LogDir=./Logs/

; Count log directory
CountLogDir=./CountLog/

; Character log directory
ChrLogDir=./ChrLog/

; Web log directory
WebLogDir=./Share/

[Security]
; Feed ID list file
FeedIDList=./FeedIDList.txt

; Feed IP list file
FeedIPList=./FeedIPList.txt

; Test server mode
TestServer=1

[Advanced]
; Process gate tick interval (ms)
ProcessGateTick=10

; Process gate time (ms)
ProcessGateTime=50

; Maximum route count
MaxRouteCount=60

; Session timeout (seconds)
SessionTimeout=300

; Keep alive interval (seconds)
KeepAliveInterval=10 