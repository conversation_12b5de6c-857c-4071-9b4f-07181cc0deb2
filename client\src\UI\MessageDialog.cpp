#include "MessageDialog.h"
#include "UIConstants.h"
#include "Label.h"
#include "Button.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>

MessageDialog::MessageDialog(int x, int y, int width, int height, const std::string& title, const std::string& message,
                           MessageType messageType, DialogButtons buttons, const std::string& name)
    : Dialog(x, y, width, height, title, message, buttons, name)
    , m_messageType(messageType)
    , m_iconTexture(nullptr)
    , m_iconImageIndex(-1)
{
    // Set dialog size to normal by default (matches original Delphi implementation)
    SetDialogSize(DialogSize::NORMAL);

    // Set icon image index based on message type
    switch (messageType) {
        case MessageType::INFORMATION:
            m_iconImageIndex = UIConstants::ICON_INFORMATION;
            break;
        case MessageType::WARNING:
            m_iconImageIndex = UIConstants::ICON_WARNING;
            break;
        case MessageType::ERROR:
            m_iconImageIndex = UIConstants::ICON_ERROR;
            break;
        case MessageType::QUESTION:
            m_iconImageIndex = UIConstants::ICON_QUESTION;
            break;
    }
}

MessageDialog::~MessageDialog()
{
}

void MessageDialog::Initialize(SDL_Renderer* renderer)
{
    // Call base class initialize
    Dialog::Initialize(renderer);

    // Load icon texture if WIL manager and resource file are set
    if (m_iconImageIndex >= 0 && GetWILManager() && !GetResourceFile().empty()) {
        // Load icon texture from WIL resource
        SDL_Surface* surface = GetWILManager()->GetSurface(GetResourceFile(), m_iconImageIndex);
        if (surface) {
            m_iconTexture = std::make_shared<Texture>(renderer);
            m_iconTexture->LoadFromSurface(surface);
        }
    }
}

void MessageDialog::Render(SDL_Renderer* renderer)
{
    // Call base class render
    Dialog::Render(renderer);

    // Render icon if available
    if (m_iconTexture) {
        // Calculate icon position (left side of message)
        // In the original Delphi implementation, the icon would be positioned
        // near the message text, which is at msglx, msgly
        int iconX = GetX() + GetMessageLabel()->GetX() - 30; // 30px to the left of message
        int iconY = GetY() + GetMessageLabel()->GetY();      // Same Y as message

        m_iconTexture->Render(iconX, iconY);
    }
}

void MessageDialog::SetMessageType(MessageType messageType)
{
    m_messageType = messageType;

    // Set icon image index based on message type
    switch (messageType) {
        case MessageType::INFORMATION:
            SetIconImageIndex(UIConstants::ICON_INFORMATION);
            break;
        case MessageType::WARNING:
            SetIconImageIndex(UIConstants::ICON_WARNING);
            break;
        case MessageType::ERROR:
            SetIconImageIndex(UIConstants::ICON_ERROR);
            break;
        case MessageType::QUESTION:
            SetIconImageIndex(UIConstants::ICON_QUESTION);
            break;
    }
}

void MessageDialog::SetIconImageIndex(int iconImageIndex)
{
    m_iconImageIndex = iconImageIndex;
    m_iconTexture.reset();
}

void MessageDialog::CreateControls()
{
    // Call base class create controls
    Dialog::CreateControls();

    // In the original Delphi implementation, the message is positioned at msglx, msgly
    // which are set based on the dialog size. We're already setting these in the Dialog class,
    // but we need to adjust for the icon if present.

    // Adjust message label position to make room for icon if needed
    if (GetMessageLabel() && m_iconImageIndex >= 0) {
        // Get the current message position
        int messageX = GetMessageLabel()->GetX();
        int messageY = GetMessageLabel()->GetY();

        // Add space for icon (about 30px to the right)
        messageX += 30;

        // Update message label position
        GetMessageLabel()->SetPosition(messageX, messageY);

        // Adjust message width to account for icon
        int messageWidth = GetWidth() - messageX - 20; // 20px right margin
        GetMessageLabel()->SetSize(messageWidth, GetMessageLabel()->GetHeight());
    }
}

