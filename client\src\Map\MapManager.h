#pragma once

#include "MapCell.h"
#include "../Graphics/WILLoader.h"
#include "../Graphics/Texture.h"
#include <vector>
#include <string>
#include <memory>
#include <unordered_map>

/**
 * @class MapManager
 * @brief Manages game maps
 *
 * This class is responsible for loading, managing, and rendering game maps.
 * It handles map files, tile sets, and provides methods for map interaction.
 */
class MapManager {
private:
    std::vector<std::vector<MapCell>> m_cells;  ///< 2D grid of map cells
    int m_width;                                ///< Map width in cells
    int m_height;                               ///< Map height in cells
    std::string m_mapName;                      ///< Map name

    // Tile resources
    std::shared_ptr<WILManager> m_wilManager;   ///< WIL manager for tile images
    std::unordered_map<std::string, std::shared_ptr<Texture>> m_tileTextures;  ///< Cached tile textures

    // Camera
    int m_cameraX;                              ///< Camera X position in pixels
    int m_cameraY;                              ///< Camera Y position in pixels
    int m_viewportWidth;                        ///< Viewport width in pixels
    int m_viewportHeight;                       ///< Viewport height in pixels

    // Tile dimensions
    int m_tileWidth;                            ///< Tile width in pixels
    int m_tileHeight;                           ///< Tile height in pixels

    // Rendering layers
    bool m_renderBackground;                    ///< Whether to render background layer
    bool m_renderMiddle;                        ///< Whether to render middle layer
    bool m_renderObjects;                       ///< Whether to render object layer

    /**
     * @brief Load tile textures
     * @param tilesetName Tileset name
     * @return true if successful, false otherwise
     */
    bool LoadTileTextures(const std::string& tilesetName);

    /**
     * @brief Get texture for a tile
     * @param index Tile index
     * @param layer Layer (0=background, 1=middle, 2=object)
     * @return Texture pointer or nullptr if not found
     */
    std::shared_ptr<Texture> GetTileTexture(uint16_t index, int layer);

    /**
     * @brief Initialize light map based on map cells
     *
     * This method initializes the light map based on the light values in map cells.
     * It is called after loading a map.
     */
    void InitializeLightMap();

public:
    /**
     * @brief Constructor
     * @param wilManager WIL manager for tile images
     * @param renderer SDL renderer
     */
    MapManager(std::shared_ptr<WILManager> wilManager, SDL_Renderer* renderer);

    /**
     * @brief Destructor
     */
    ~MapManager();

    /**
     * @brief Load a map from file
     * @param filename Map file name
     * @param tilesetName Tileset name
     * @return true if successful, false otherwise
     */
    bool LoadMap(const std::string& filename, const std::string& tilesetName);

    /**
     * @brief Create an empty map
     * @param width Map width in cells
     * @param height Map height in cells
     * @param tilesetName Tileset name
     * @return true if successful, false otherwise
     */
    bool CreateEmptyMap(int width, int height, const std::string& tilesetName);

    /**
     * @brief Save the map to a file
     * @param filename Map file name
     * @return true if successful, false otherwise
     */
    bool SaveMap(const std::string& filename);

    /**
     * @brief Set the camera position
     * @param x X position in pixels
     * @param y Y position in pixels
     */
    void SetCamera(int x, int y);

    /**
     * @brief Set the viewport size
     * @param width Viewport width in pixels
     * @param height Viewport height in pixels
     */
    void SetViewport(int width, int height);

    /**
     * @brief Set which layers to render
     * @param background Whether to render background layer
     * @param middle Whether to render middle layer
     * @param objects Whether to render object layer
     */
    void SetRenderLayers(bool background, bool middle, bool objects);

    /**
     * @brief Render the map
     * @param renderer SDL renderer
     */
    void Render(SDL_Renderer* renderer);

    /**
     * @brief Update map animations
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    void UpdateAnimations(int deltaTime);

    /**
     * @brief Toggle door at position
     * @param x X coordinate
     * @param y Y coordinate
     * @return true if door is now open, false otherwise
     */
    bool ToggleDoor(int x, int y);

    /**
     * @brief Get a map cell
     * @param x X coordinate
     * @param y Y coordinate
     * @return Reference to the map cell
     */
    MapCell& GetCell(int x, int y);

    /**
     * @brief Check if coordinates are valid
     * @param x X coordinate
     * @param y Y coordinate
     * @return true if valid, false otherwise
     */
    bool IsValidCoordinate(int x, int y) const;

    /**
     * @brief Check if a position is walkable
     * @param x X coordinate
     * @param y Y coordinate
     * @return true if walkable, false otherwise
     */
    bool IsWalkable(int x, int y) const;

    /**
     * @brief Convert screen coordinates to map coordinates
     * @param screenX Screen X coordinate
     * @param screenY Screen Y coordinate
     * @param mapX Output map X coordinate
     * @param mapY Output map Y coordinate
     * @return true if successful, false otherwise
     */
    bool ScreenToMap(int screenX, int screenY, int& mapX, int& mapY) const;

    /**
     * @brief Convert map coordinates to screen coordinates
     * @param mapX Map X coordinate
     * @param mapY Map Y coordinate
     * @param screenX Output screen X coordinate
     * @param screenY Output screen Y coordinate
     */
    void MapToScreen(int mapX, int mapY, int& screenX, int& screenY) const;

    /**
     * @brief Get the map width
     * @return Map width in cells
     */
    int GetWidth() const { return m_width; }

    /**
     * @brief Get the map height
     * @return Map height in cells
     */
    int GetHeight() const { return m_height; }

    /**
     * @brief Get the map name
     * @return Map name
     */
    const std::string& GetMapName() const { return m_mapName; }

    /**
     * @brief Set the map name
     * @param name Map name
     */
    void SetMapName(const std::string& name) { m_mapName = name; }

    /**
     * @brief Get cells within a certain range of a position
     * @param centerX Center X coordinate
     * @param centerY Center Y coordinate
     * @param range Range in cells
     * @return Vector of map cells with their coordinates
     */
    std::vector<std::tuple<int, int, MapCell*>> GetCellsInRange(int centerX, int centerY, int range);

    /**
     * @brief Set the state of an area
     * @param x X coordinate
     * @param y Y coordinate
     * @param state Area state
     * @return true if successful, false otherwise
     */
    bool SetAreaState(int x, int y, int state);

    /**
     * @brief Set the state of a door
     * @param x X coordinate
     * @param y Y coordinate
     * @param state Door state (0 = closed, 1 = open)
     * @return true if successful, false otherwise
     */
    bool SetDoorState(int x, int y, int state);

    /**
     * @brief Set the state of a door with index and offset
     * @param x X coordinate
     * @param y Y coordinate
     * @param doorIndex Door index
     * @param doorOffset Door offset
     * @param isOpen Whether the door is open
     * @return true if successful, false otherwise
     */
    bool SetDoorState(int x, int y, int doorIndex, int doorOffset, bool isOpen);
};
