#pragma once

#include "Packet.h"
#include "MessageConverter.h"
#include <string>
#include <queue>
#include <mutex>
#include <thread>
#include <functional>
#include <unordered_map>

// Check if SDL_NET is disabled
#ifndef SDL_NET_DISABLED
    #include <SDL2/SDL_net.h>
    #define NETWORK_ENABLED 1
#else
    #define NETWORK_ENABLED 0
    // Define stub types for SDL_net when it's not available
    typedef void* TCPsocket;
    typedef void* SDLNet_SocketSet;
#endif

/**
 * @class NetworkManager
 * @brief Manages network communication
 *
 * This class is responsible for managing network communication between the client and server.
 * It handles connecting to the server, sending and receiving packets, and processing network events.
 */
class NetworkManager {
private:
    TCPsocket m_socket;                                 ///< TCP socket
    SDLNet_SocketSet m_socketSet;                       ///< Socket set for checking activity

    std::string m_serverAddress;                        ///< Server address
    uint16_t m_serverPort;                              ///< Server port

    bool m_connected;                                   ///< Connection status
    bool m_running;                                     ///< Thread running status

    std::thread m_receiveThread;                        ///< Thread for receiving packets
    std::mutex m_sendMutex;                             ///< Mutex for sending packets
    std::mutex m_receiveMutex;                          ///< Mutex for receiving packets

    std::queue<Packet> m_sendQueue;                     ///< Queue of packets to send
    std::queue<Packet> m_receiveQueue;                  ///< Queue of received packets

    // Packet handlers
    using PacketHandler = std::function<void(const Packet&)>;
    std::unordered_map<PacketType, PacketHandler> m_packetHandlers;  ///< Map of packet handlers

    /**
     * @brief Thread function for receiving packets
     */
    void ReceiveThread();

    /**
     * @brief Send a packet
     * @param packet Packet to send
     * @return true if successful, false otherwise
     */
    bool SendPacket(const Packet& packet);

    /**
     * @brief Receive a packet
     * @param packet Output packet
     * @return true if successful, false otherwise
     */
    bool ReceivePacket(Packet& packet);

public:
    /**
     * @brief Constructor
     */
    NetworkManager();

    /**
     * @brief Destructor
     */
    ~NetworkManager();

    /**
     * @brief Initialize the network manager
     * @return true if successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Shutdown the network manager
     */
    void Shutdown();

    /**
     * @brief Connect to the server
     * @param address Server address
     * @param port Server port
     * @return true if successful, false otherwise
     */
    bool Connect(const std::string& address, uint16_t port);

    /**
     * @brief Disconnect from the server
     */
    void Disconnect();

    /**
     * @brief Update the network manager
     */
    void Update();

    /**
     * @brief Queue a packet to be sent
     * @param packet Packet to send
     */
    void QueuePacket(const Packet& packet);

    /**
     * @brief Queue a TDefaultMessage to be sent
     * @param msg Message to send
     */
    void QueueMessage(const TDefaultMessage& msg);

    /**
     * @brief Register a packet handler
     * @param type Packet type
     * @param handler Packet handler function
     */
    void RegisterPacketHandler(PacketType type, PacketHandler handler);

    /**
     * @brief Unregister a packet handler
     * @param type Packet type
     */
    void UnregisterPacketHandler(PacketType type);

    /**
     * @brief Check if connected to the server
     * @return true if connected, false otherwise
     */
    bool IsConnected() const { return m_connected; }

    /**
     * @brief Get the server address
     * @return Server address
     */
    const std::string& GetServerAddress() const { return m_serverAddress; }

    /**
     * @brief Get the server port
     * @return Server port
     */
    uint16_t GetServerPort() const { return m_serverPort; }

    // Utility methods for creating packets

    /**
     * @brief Create a login request packet
     * @param username Username
     * @param password Password
     * @return Login request packet
     */
    static Packet CreateLoginRequest(const std::string& username, const std::string& password);

    /**
     * @brief Create a character list request packet
     * @return Character list request packet
     */
    static Packet CreateCharacterListRequest();

    /**
     * @brief Create a character select request packet
     * @param characterName Character name
     * @return Character select request packet
     */
    static Packet CreateCharacterSelectRequest(const std::string& characterName);

    /**
     * @brief Create a character create request packet
     * @param characterName Character name
     * @param characterClass Character class
     * @param gender Character gender
     * @param hair Character hair style
     * @return Character create request packet
     */
    static Packet CreateCharacterCreateRequest(const std::string& characterName, int characterClass, int gender, int hair);

    /**
     * @brief Create a character delete request packet
     * @param characterName Character name
     * @return Character delete request packet
     */
    static Packet CreateCharacterDeleteRequest(const std::string& characterName);

    /**
     * @brief Create a player move packet
     * @param direction Direction
     * @param run Whether to run
     * @return Player move packet
     */
    static Packet CreatePlayerMovePacket(int direction, bool run);

    /**
     * @brief Create a player attack packet
     * @param attackType Attack type
     * @return Player attack packet
     */
    static Packet CreatePlayerAttackPacket(int attackType);

    /**
     * @brief Create a player cast packet
     * @param skillId Skill ID
     * @param targetX Target X coordinate
     * @param targetY Target Y coordinate
     * @return Player cast packet
     */
    static Packet CreatePlayerCastPacket(int skillId, int targetX, int targetY);

    /**
     * @brief Create a chat message packet
     * @param message Message
     * @return Chat message packet
     */
    static Packet CreateChatMessagePacket(const std::string& message);

    /**
     * @brief Create a whisper message packet
     * @param targetName Target name
     * @param message Message
     * @return Whisper message packet
     */
    static Packet CreateWhisperMessagePacket(const std::string& targetName, const std::string& message);

    /**
     * @brief Create a guild message packet
     * @param message Message
     * @return Guild message packet
     */
    static Packet CreateGuildMessagePacket(const std::string& message);

    /**
     * @brief Create a group message packet
     * @param message Message
     * @return Group message packet
     */
    static Packet CreateGroupMessagePacket(const std::string& message);

    /**
     * @brief Send a chat message
     * @param message Message to send
     */
    void SendChatMessage(const std::string& message);

    /**
     * @brief Send a whisper message
     * @param targetName Target name
     * @param message Message to send
     */
    void SendWhisperMessage(const std::string& targetName, const std::string& message);

    /**
     * @brief Send a guild message
     * @param message Message to send
     */
    void SendGuildMessage(const std::string& message);

    /**
     * @brief Send a group message
     * @param message Message to send
     */
    void SendGroupMessage(const std::string& message);
};

