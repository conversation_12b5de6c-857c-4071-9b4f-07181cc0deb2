#include "ListView.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>

// ListViewItem implementation

ListViewItem::ListViewItem(const std::vector<std::string>& columns, void* userData)
    : m_columns(columns)
    , m_userData(userData)
{
}

ListViewItem::~ListViewItem()
{
}

const std::string& ListViewItem::GetColumn(size_t index) const
{
    static const std::string emptyString;
    if (index < m_columns.size()) {
        return m_columns[index];
    }
    return emptyString;
}

void ListViewItem::SetColumn(size_t index, const std::string& value)
{
    if (index >= m_columns.size()) {
        m_columns.resize(index + 1);
    }
    m_columns[index] = value;
}

// ListView implementation

ListView::ListView(int x, int y, int width, int height, const std::string& name)
    : UIControl(x, y, width, height, name)
    , m_font(nullptr)
    , m_textColor({0, 0, 0, 255})
    , m_backgroundColor({255, 255, 255, 255})
    , m_borderColor({0, 0, 0, 255})
    , m_headerBackgroundColor({200, 200, 200, 255})
    , m_headerTextColor({0, 0, 0, 255})
    , m_selectionColor({0, 120, 215, 255})
    , m_selectedIndex(-1)
    , m_itemHeight(20)
    , m_headerHeight(25)
    , m_scrollOffset(0)
    , m_visibleItems(0)
{
}

ListView::~ListView()
{
}

void ListView::Update(int deltaTime)
{
    // Calculate the number of visible items
    m_visibleItems = (m_height - m_headerHeight) / m_itemHeight;
    
    // Ensure scroll offset is valid
    if (m_scrollOffset < 0) {
        m_scrollOffset = 0;
    }
    
    if (m_items.size() > m_visibleItems) {
        int maxOffset = static_cast<int>(m_items.size()) - m_visibleItems;
        if (m_scrollOffset > maxOffset) {
            m_scrollOffset = maxOffset;
        }
    } else {
        m_scrollOffset = 0;
    }
}

void ListView::Render(SDL_Renderer* renderer)
{
    // Skip if not visible
    if (!m_visible) {
        return;
    }
    
    // Render background
    SDL_Rect rect = {m_x, m_y, m_width, m_height};
    SDL_SetRenderDrawColor(renderer, m_backgroundColor.r, m_backgroundColor.g, m_backgroundColor.b, m_backgroundColor.a);
    SDL_RenderFillRect(renderer, &rect);
    
    // Render border
    SDL_SetRenderDrawColor(renderer, m_borderColor.r, m_borderColor.g, m_borderColor.b, m_borderColor.a);
    SDL_RenderDrawRect(renderer, &rect);
    
    // Render header
    SDL_Rect headerRect = {m_x, m_y, m_width, m_headerHeight};
    SDL_SetRenderDrawColor(renderer, m_headerBackgroundColor.r, m_headerBackgroundColor.g, m_headerBackgroundColor.b, m_headerBackgroundColor.a);
    SDL_RenderFillRect(renderer, &headerRect);
    
    // Render header text
    if (m_font) {
        int x = m_x + 5;
        for (size_t i = 0; i < m_columnHeaders.size(); i++) {
            SDL_Surface* surface = TTF_RenderText_Blended(m_font, m_columnHeaders[i].c_str(), m_headerTextColor);
            if (surface) {
                SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
                if (texture) {
                    SDL_Rect textRect = {x, m_y + (m_headerHeight - surface->h) / 2, surface->w, surface->h};
                    SDL_RenderCopy(renderer, texture, nullptr, &textRect);
                    SDL_DestroyTexture(texture);
                }
                SDL_FreeSurface(surface);
            }
            
            x += m_columnWidths[i];
        }
    }
    
    // Render items
    int y = m_y + m_headerHeight;
    for (int i = m_scrollOffset; i < m_scrollOffset + m_visibleItems && i < static_cast<int>(m_items.size()); i++) {
        // Render item background
        SDL_Rect itemRect = {m_x, y, m_width, m_itemHeight};
        
        if (i == m_selectedIndex) {
            // Render selection background
            SDL_SetRenderDrawColor(renderer, m_selectionColor.r, m_selectionColor.g, m_selectionColor.b, m_selectionColor.a);
            SDL_RenderFillRect(renderer, &itemRect);
        }
        
        // Render item text
        if (m_font) {
            int x = m_x + 5;
            for (size_t j = 0; j < m_columnHeaders.size(); j++) {
                SDL_Color textColor = (i == m_selectedIndex) ? SDL_Color{255, 255, 255, 255} : m_textColor;
                SDL_Surface* surface = TTF_RenderText_Blended(m_font, m_items[i].GetColumn(j).c_str(), textColor);
                if (surface) {
                    SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
                    if (texture) {
                        SDL_Rect textRect = {x, y + (m_itemHeight - surface->h) / 2, surface->w, surface->h};
                        SDL_RenderCopy(renderer, texture, nullptr, &textRect);
                        SDL_DestroyTexture(texture);
                    }
                    SDL_FreeSurface(surface);
                }
                
                x += m_columnWidths[j];
            }
        }
        
        y += m_itemHeight;
    }
    
    // Render column dividers
    int x = m_x;
    for (size_t i = 0; i < m_columnWidths.size(); i++) {
        x += m_columnWidths[i];
        SDL_SetRenderDrawColor(renderer, m_borderColor.r, m_borderColor.g, m_borderColor.b, m_borderColor.a);
        SDL_RenderDrawLine(renderer, x, m_y, x, m_y + m_height);
    }
    
    // Render header divider
    SDL_SetRenderDrawColor(renderer, m_borderColor.r, m_borderColor.g, m_borderColor.b, m_borderColor.a);
    SDL_RenderDrawLine(renderer, m_x, m_y + m_headerHeight, m_x + m_width, m_y + m_headerHeight);
}

bool ListView::HandleEvent(const SDL_Event& event)
{
    // Skip if not visible or enabled
    if (!m_visible || !m_enabled) {
        return false;
    }
    
    // Handle mouse events
    if (event.type == SDL_MOUSEBUTTONDOWN) {
        if (event.button.button == SDL_BUTTON_LEFT) {
            // Check if the mouse is inside the control
            int mouseX = event.button.x;
            int mouseY = event.button.y;
            
            if (mouseX >= m_x && mouseX < m_x + m_width && mouseY >= m_y + m_headerHeight && mouseY < m_y + m_height) {
                // Calculate the item index
                int itemIndex = m_scrollOffset + (mouseY - (m_y + m_headerHeight)) / m_itemHeight;
                
                if (itemIndex >= 0 && itemIndex < static_cast<int>(m_items.size())) {
                    // Select the item
                    SetSelectedIndex(itemIndex);
                    
                    // Check for double click
                    static Uint32 lastClickTime = 0;
                    static int lastClickIndex = -1;
                    
                    Uint32 currentTime = SDL_GetTicks();
                    if (currentTime - lastClickTime < 500 && itemIndex == lastClickIndex) {
                        // Double click
                        if (m_onItemDoubleClicked) {
                            m_onItemDoubleClicked(itemIndex);
                        }
                    }
                    
                    lastClickTime = currentTime;
                    lastClickIndex = itemIndex;
                }
                
                return true;
            }
        }
    } else if (event.type == SDL_MOUSEWHEEL) {
        // Check if the mouse is inside the control
        int mouseX, mouseY;
        SDL_GetMouseState(&mouseX, &mouseY);
        
        if (mouseX >= m_x && mouseX < m_x + m_width && mouseY >= m_y && mouseY < m_y + m_height) {
            // Scroll the list
            m_scrollOffset -= event.wheel.y;
            
            // Ensure scroll offset is valid
            if (m_scrollOffset < 0) {
                m_scrollOffset = 0;
            }
            
            if (m_items.size() > m_visibleItems) {
                int maxOffset = static_cast<int>(m_items.size()) - m_visibleItems;
                if (m_scrollOffset > maxOffset) {
                    m_scrollOffset = maxOffset;
                }
            } else {
                m_scrollOffset = 0;
            }
            
            return true;
        }
    }
    
    return false;
}

void ListView::AddColumn(const std::string& header, int width)
{
    m_columnHeaders.push_back(header);
    m_columnWidths.push_back(width);
}

int ListView::AddItem(const ListViewItem& item)
{
    m_items.push_back(item);
    return static_cast<int>(m_items.size()) - 1;
}

void ListView::RemoveItem(int index)
{
    if (index >= 0 && index < static_cast<int>(m_items.size())) {
        m_items.erase(m_items.begin() + index);
        
        // Update selected index
        if (m_selectedIndex == index) {
            m_selectedIndex = -1;
        } else if (m_selectedIndex > index) {
            m_selectedIndex--;
        }
    }
}

void ListView::Clear()
{
    m_items.clear();
    m_selectedIndex = -1;
    m_scrollOffset = 0;
}

const ListViewItem& ListView::GetItem(int index) const
{
    static const ListViewItem emptyItem;
    if (index >= 0 && index < static_cast<int>(m_items.size())) {
        return m_items[index];
    }
    return emptyItem;
}

void ListView::SetSelectedIndex(int index)
{
    if (index >= -1 && index < static_cast<int>(m_items.size()) && index != m_selectedIndex) {
        m_selectedIndex = index;
        
        // Ensure the selected item is visible
        if (m_selectedIndex >= 0) {
            if (m_selectedIndex < m_scrollOffset) {
                m_scrollOffset = m_selectedIndex;
            } else if (m_selectedIndex >= m_scrollOffset + m_visibleItems) {
                m_scrollOffset = m_selectedIndex - m_visibleItems + 1;
            }
        }
        
        // Call the selection changed callback
        if (m_onSelectionChanged) {
            m_onSelectionChanged(m_selectedIndex);
        }
    }
}

const ListViewItem& ListView::GetSelectedItem() const
{
    return GetItem(m_selectedIndex);
}

