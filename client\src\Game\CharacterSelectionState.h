#pragma once

#include "../GameState.h"
#include "../Graphics/Texture.h"
#include "../UI/Button.h"
#include "../UI/Label.h"
#include "../UI/ListView.h"
#include "../Network/NetworkManager.h"
#include "../Data/CharacterInfo.h"
#include <memory>
#include <vector>
#include <string>

/**
 * @class CharacterSelectionState
 * @brief Game state for the character selection screen
 * 
 * This class represents the character selection screen of the game, where the player can
 * select a character to play or create a new one.
 */
class CharacterSelectionState : public GameState {
private:
    std::shared_ptr<Texture> m_backgroundTexture;  ///< Background texture
    
    // UI controls
    std::shared_ptr<Label> m_titleLabel;           ///< Title label
    std::shared_ptr<ListView> m_characterListView; ///< Character list view
    std::shared_ptr<Button> m_selectButton;        ///< Select button
    std::shared_ptr<Button> m_createButton;        ///< Create button
    std::shared_ptr<Button> m_deleteButton;        ///< Delete button
    std::shared_ptr<Button> m_backButton;          ///< Back button
    std::shared_ptr<Label> m_statusLabel;          ///< Status label
    
    // Character preview
    std::shared_ptr<Texture> m_characterPreviewTexture;  ///< Character preview texture
    
    // Network manager
    std::shared_ptr<NetworkManager> m_networkManager;  ///< Network manager
    
    // Character list
    std::vector<CharacterInfo> m_characters;       ///< Character list
    int m_selectedCharacterIndex;                  ///< Selected character index
    
    // Selection state
    bool m_selecting;                              ///< Whether we're currently selecting a character
    
    /**
     * @brief Create UI controls
     */
    void CreateControls();
    
    /**
     * @brief Handle select button click
     */
    void OnSelectButtonClick();
    
    /**
     * @brief Handle create button click
     */
    void OnCreateButtonClick();
    
    /**
     * @brief Handle delete button click
     */
    void OnDeleteButtonClick();
    
    /**
     * @brief Handle back button click
     */
    void OnBackButtonClick();
    
    /**
     * @brief Handle character list response
     * @param packet Character list response packet
     */
    void OnCharacterListResponse(const Packet& packet);
    
    /**
     * @brief Handle character select response
     * @param packet Character select response packet
     */
    void OnCharacterSelectResponse(const Packet& packet);
    
    /**
     * @brief Handle character create response
     * @param packet Character create response packet
     */
    void OnCharacterCreateResponse(const Packet& packet);
    
    /**
     * @brief Handle character delete response
     * @param packet Character delete response packet
     */
    void OnCharacterDeleteResponse(const Packet& packet);
    
public:
    /**
     * @brief Constructor
     * @param app Pointer to the application
     * @param networkManager Network manager
     */
    CharacterSelectionState(Application* app, std::shared_ptr<NetworkManager> networkManager);
    
    /**
     * @brief Destructor
     */
    virtual ~CharacterSelectionState();
    
    /**
     * @brief Called when entering the state
     */
    virtual void Enter() override;
    
    /**
     * @brief Called when exiting the state
     */
    virtual void Exit() override;
    
    /**
     * @brief Update the state
     * @param deltaTime Time elapsed since last frame in seconds
     */
    virtual void Update(float deltaTime) override;
    
    /**
     * @brief Render the state
     */
    virtual void Render() override;
    
    /**
     * @brief Handle SDL events
     * @param event SDL event to handle
     */
    virtual void HandleEvents(SDL_Event& event) override;
};
