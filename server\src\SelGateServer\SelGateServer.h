#pragma once

#include "../Common/Types.h"
#include "../Protocol/PacketTypes.h"
#include "../Protocol/NetworkManager.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <string>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <queue>

namespace MirServer {

// 使用Network命名空间中的类
using Network::ClientConnection;
using Network::NetworkManager;

// RunGate服务器信息
struct RunGateInfo {
    std::string serverName;           // 服务器名称
    std::string serverIP;             // 服务器IP地址
    int serverPort;                   // 服务器端口
    std::string gateIP;               // 网关IP地址
    int gatePort;                     // 网关端口
    int maxUsers;                     // 最大用户数
    int currentUsers;                 // 当前用户数
    bool isOnline;                    // 是否在线
    bool isEnabled;                   // 是否启用
    std::chrono::steady_clock::time_point lastCheckTime;  // 最后检查时间
    std::chrono::steady_clock::time_point lastHeartbeat;  // 最后心跳时间
    int checkFailCount;               // 检查失败计数
    
    RunGateInfo() : serverPort(0), gatePort(0), maxUsers(1000), currentUsers(0),
                    isOnline(false), isEnabled(true), checkFailCount(0) {
        auto now = std::chrono::steady_clock::now();
        lastCheckTime = lastHeartbeat = now;
    }
};

// 客户端会话信息（对应Delphi TUserSession）
struct ClientSession {
    std::shared_ptr<ClientConnection> socket;
    std::string remoteIP;             // 远程IP地址
    int socketHandle;                 // Socket句柄
    std::chrono::steady_clock::time_point connectTime;    // 连接时间
    std::chrono::steady_clock::time_point lastActiveTime; // 最后活动时间
    bool isAssigned;                  // 是否已分配到RunGate
    int assignedGateIndex;            // 分配的RunGate索引
    std::string receivedData;         // 接收的数据缓冲区
    
    ClientSession() : socketHandle(-1), isAssigned(false), assignedGateIndex(-1) {
        auto now = std::chrono::steady_clock::now();
        connectTime = lastActiveTime = now;
    }
};

// IP地址信息结构
struct IPAddressInfo {
    int ipAddress;                    // IP地址（网络字节序）
    std::chrono::steady_clock::time_point firstConnectTime;  // 首次连接时间
    std::chrono::steady_clock::time_point lastConnectTime;   // 最后连接时间
    int connectCount;                 // 连接计数
    int socketHandle;                 // Socket句柄
    bool isBlocked;                   // 是否被阻挡
    
    IPAddressInfo() : ipAddress(0), connectCount(0), socketHandle(-1), isBlocked(false) {
        auto now = std::chrono::steady_clock::now();
        firstConnectTime = lastConnectTime = now;
    }
};

// 阻止IP方法枚举
enum BlockIPMethod {
    DISCONNECT = 0,                   // 断开连接
    TEMP_BLOCK = 1,                   // 临时阻止
    PERMANENT_BLOCK = 2               // 永久阻止
};

// 服务器选择策略
enum ServerSelectStrategy {
    ROUND_ROBIN = 0,                  // 轮询
    LEAST_CONNECTIONS = 1,            // 最少连接
    LOAD_BALANCE = 2,                 // 负载均衡
    RANDOM = 3                        // 随机选择
};

class SelGateServer {
public:
    static constexpr int MAX_SESSIONS = 10000;           // 最大会话数
    static constexpr int MAX_RUNGATE_SERVERS = 50;       // 最大RunGate服务器数
    static constexpr int HEARTBEAT_INTERVAL = 30;        // 心跳间隔（秒）
    static constexpr int CHECK_TIMEOUT = 60;             // 检查超时（秒）
    static constexpr int MAX_CHECK_FAILS = 3;            // 最大检查失败次数
    
    SelGateServer();
    ~SelGateServer();
    
    // 主要接口
    bool Initialize(const std::string& configFile);
    bool Start();
    void Stop();
    bool IsRunning() const { return m_isRunning; }
    
    // 配置管理
    void LoadConfig();
    void SaveConfig();
    void LoadRunGateList();
    void SaveRunGateList();
    
    // 服务器管理
    bool AddRunGateServer(const RunGateInfo& serverInfo);
    bool RemoveRunGateServer(const std::string& serverName);
    bool UpdateRunGateServer(const std::string& serverName, const RunGateInfo& serverInfo);
    RunGateInfo* GetRunGateServer(const std::string& serverName);
    std::vector<RunGateInfo> GetAllRunGateServers() const;
    
    // 连接管理
    void OnClientConnect(std::shared_ptr<ClientConnection> connection);
    void OnClientDisconnect(std::shared_ptr<ClientConnection> connection);
    void OnClientMessage(std::shared_ptr<ClientConnection> connection, const std::vector<BYTE>& data);
    
    // 服务器选择
    int SelectBestRunGate();
    bool AssignClientToRunGate(ClientSession* session, int runGateIndex);
    void SendRunGateInfo(std::shared_ptr<ClientConnection> connection, const RunGateInfo& gateInfo);
    
    // IP管理
    bool IsBlockedIP(const std::string& ipAddress);
    bool IsConnectionLimited(const std::string& ipAddress);
    int AddBlockIP(const std::string& ipAddress);
    int AddTempBlockIP(const std::string& ipAddress);
    void CloseConnectionsByIP(const std::string& ipAddress);
    
    // 统计信息
    int GetSessionCount() const { return m_sessionCount; }
    int GetActiveRunGateCount() const;
    int GetTotalConnections() const;
    std::string GetServerStatus() const;
    
private:
    // 配置参数
    std::string m_configFile;
    std::string m_serverName;
    std::string m_listenIP;
    int m_listenPort;
    int m_maxConnOfIP;
    int m_sessionTimeout;
    int m_heartbeatInterval;
    int m_checkTimeout;
    BlockIPMethod m_blockMethod;
    ServerSelectStrategy m_selectStrategy;
    bool m_enableLoadBalance;
    bool m_enableIPFilter;
    
    // 网络组件
    std::unique_ptr<NetworkManager> m_networkManager;
    
    // RunGate服务器列表
    std::vector<RunGateInfo> m_runGateServers;
    mutable std::mutex m_serverMutex;
    int m_currentRoundRobinIndex;
    
    // 客户端会话管理
    std::array<ClientSession, MAX_SESSIONS> m_sessions;
    std::atomic<int> m_sessionCount;
    std::mutex m_sessionMutex;
    std::unordered_map<int, int> m_socketToSessionMap;  // Socket句柄到会话索引的映射
    
    // IP管理
    std::vector<IPAddressInfo> m_blockIPList;
    std::vector<IPAddressInfo> m_tempBlockIPList;
    std::unordered_map<std::string, std::vector<IPAddressInfo>> m_currentIPList;
    std::mutex m_ipMutex;
    
    // 运行状态
    std::atomic<bool> m_isRunning;
    std::atomic<bool> m_isReady;
    
    // 工作线程
    std::thread m_heartbeatThread;
    std::thread m_checkThread;
    std::thread m_processThread;
    
    // 内部方法
    void HeartbeatThread();
    void CheckThread();
    void ProcessThread();
    
    // 服务器检查
    void CheckRunGateServers();
    bool CheckRunGateStatus(RunGateInfo& serverInfo);
    void UpdateRunGateUserCount(int serverIndex, int userCount);
    
    // 会话管理
    int FindAvailableSession();
    ClientSession* GetSession(int sessionIndex);
    ClientSession* GetSessionBySocket(int socketHandle);
    void RemoveSession(int sessionIndex);
    void CleanupExpiredSessions();
    
    // 服务器选择算法
    int SelectByRoundRobin();
    int SelectByLeastConnections();
    int SelectByLoadBalance();
    int SelectByRandom();
    
    // 协议处理
    void ProcessClientRequest(ClientSession* session, const std::vector<BYTE>& data);
    void SendServerList(std::shared_ptr<ClientConnection> connection);
    void SendRedirectMessage(std::shared_ptr<ClientConnection> connection, const RunGateInfo& gateInfo);
    
    // 工具方法
    std::string IPToString(int ipAddress);
    int StringToIP(const std::string& ipString);
    void AddMainLogMsg(const std::string& msg, int level = 0);
    
    // 配置文件辅助方法
    std::string GetConfigString(const std::string& section, const std::string& key, const std::string& defaultValue);
    int GetConfigInt(const std::string& section, const std::string& key, int defaultValue);
    bool GetConfigBool(const std::string& section, const std::string& key, bool defaultValue);
    void LoadBlockIPFile();
    void SaveBlockIPFile();
};

} // namespace MirServer 