# MirClient 缺失网络协议对比分析

## 一、重构版本已实现的协议

### 连接协议
- ✅ CONNECT (1)
- ✅ DISCONNECT (2)
- ✅ KEEP_ALIVE (3)

### 登录相关协议
- ✅ LOGIN_REQUEST (101) → CM_IDPASSWORD (2001)
- ✅ LOGIN_RESPONSE (102) → SM_CERTIFICATION_SUCCESS/FAIL (500-501)
- ✅ CHANGE_PASSWORD (103) → CM_CHANGEPASSWORD (2003)
- ✅ PASSWORD_CHANGED (104) → SM_CHGPASSWD_SUCCESS/FAIL (506-507)
- ✅ GET_BACK_PASSWORD (105) → CM_GETBACKPASSWORD
- ✅ PASSWORD_RETRIEVED (106) → SM_GETBACKPASSWD_SUCCESS/FAIL

### 服务器选择协议
- ✅ SERVER_LIST_REQUEST (110) → CM_SELECTSERVER (104)
- ✅ SERVER_LIST (111) → SM_SELECTSERVER_OK (530)
- ✅ SERVER_CONNECT_REQUEST (112)
- ✅ SERVER_CONNECT_RESPONSE (113)

### 角色相关协议
- ✅ CHARACTER_LIST_REQUEST (120) → CM_QUERYCHR (100)
- ✅ CHARACTER_LIST (121) → SM_QUERYCHR (520)
- ✅ CHARACTER_CREATE_REQUEST (122) → CM_NEWCHR (101)
- ✅ CHARACTER_CREATE_RESPONSE (123) → SM_NEWCHR_SUCCESS/FAIL (521-522)
- ✅ CHARACTER_DELETE_REQUEST (124) → CM_DELCHR (102)
- ✅ CHARACTER_DELETE_RESPONSE (125) → SM_DELCHR_SUCCESS/FAIL (523-524)
- ✅ CHARACTER_SELECT_REQUEST (126) → CM_SELCHR (103)
- ✅ CHARACTER_SELECT_RESPONSE (127) → SM_STARTPLAY/STARTFAIL (525-526)

## 二、重构版本缺失的重要协议

### 1. 游戏核心动作协议
❌ **缺失协议**:
- CM_RUSHKUNG (3007) / SM_RUSHKUNG (7) - 野蛮冲撞
- CM_RUSH (3006) / SM_RUSH (6) - 冲锋
- CM_BACKSTEP (3009) / SM_BACKSTEP (9) - 后退步法
- CM_HORSERUN (3035) / SM_HORSERUN (5010) - 骑马跑
- CM_CRSHIT (3036) / SM_CRSHIT (25) - 连击
- CM_TWINHIT (3038) / SM_TWINHIT (26) - 双击
- CM_RIDE (3031) - 骑马
- CM_DIGUP (3020) / SM_DIGUP (20) - 挖取
- CM_DIGDOWN (3021) / SM_DIGDOWN (21) - 挖掘
- CM_FLYAXE (3022) / SM_FLYAXE (22) - 飞斧
- CM_LIGHTING (3023) / SM_LIGHTING (23) - 雷电

### 2. 物品和背包管理协议
❌ **缺失协议**:
- CM_QUERYUSERNAME (80) - 查询用户名称
- CM_DROPGOLD (1016) - 扔金币
- CM_USERREPAIRITEM (1023) - 修理物品
- CM_MERCHANTQUERYREPAIRCOST (1024) - 查询修理价格
- CM_USERSTORAGEITEM (1031) - 用户存储物品
- CM_USERTAKEBACKSTORAGEITEM (1032) - 从仓库取回物品
- CM_USERMAKEDRUGITEM (1034) - 制造药品物品
- SM_SENDREPAIRCOST (2080) - 发送修理费用
- SM_USERREPAIRITEM_OK/FAIL (2081-2082) - 修理物品结果
- SM_STORAGE_OK/FULL/FAIL (2083-2085) - 仓库操作结果
- SM_TAKEBACKSTORAGEITEM_OK/FAIL/FULLBAG (2087-2089) - 取回物品结果
- SM_MAKEDRUG_SUCCESS/FAIL (2092-2093) - 制药结果

### 3. 交易系统协议
❌ **缺失协议**:
- CM_DEALTRY (1025) - 交易开始
- CM_DEALADDITEM (1026) - 交易添加物品
- CM_DEALDELITEM (1027) - 交易删除物品
- CM_DEALCANCEL (1028) - 交易取消
- CM_DEALCHGGOLD (1029) - 交易改变金币
- CM_DEALEND (1030) - 交易结束
- SM_DEALTRY_FAIL (2108) - 交易开始失败
- SM_DEALMENU (2109) - 交易菜单
- SM_DEALCANCEL (2110) - 交易取消
- SM_DEALADDITEM_OK/FAIL (2111-2112) - 添加物品结果
- SM_DEALDELITEM_OK/FAIL (2113-2114) - 删除物品结果
- SM_DEALREMOTEADDITEM/DELITEM (682-683) - 远程交易操作
- SM_DEALCHGGOLD_OK/FAIL (2117-2118) - 改变金币结果
- SM_DEALREMOTECHGGOLD (2119) - 远程改变金币
- SM_DEALSUCCESS (2120) - 交易成功

### 4. 魔法键位和快捷键协议
❌ **缺失协议**:
- CM_MAGICKEYCHANGE (1008) - 改变魔法快捷键

### 5. 小地图协议
❌ **缺失协议**:
- CM_WANTMINIMAP (1033) - 请求小地图
- SM_READMINIMAP_OK/FAIL (710-711) - 读取小地图结果

### 6. 高级战斗协议
❌ **缺失协议**:
- SM_SPACEMOVE_HIDE/HIDE2 (1041-1042) - 隐身移动
- SM_SPACEMOVE_SHOW/SHOW2 (1043-1044) - 显示移动
- SM_MOVEFAIL (1045) - 移动失败

### 7. 登录确认协议
❌ **缺失协议**:
- CM_LOGINNOTICEOK (1018) - 登录游戏公告确认按钮

### 8. 系统和配置协议
❌ **缺失协议**:
- SM_SERVERCONFIG (5007) - 服务器配置
- SM_GAMEGOLDNAME (5008) - 游戏金币名称
- SM_PASSWORD (5009) - 密码相关

### 9. 特殊效果协议
❌ **缺失协议**:
- SM_RIDEHORSE (1300) - 骑马
- SM_MONSTERSAY (1501) - 怪物说话
- SM_LAMPCHANGEDURA (241) - 灯具耐久变化
- SM_ALIVE (263) - 复活
- SM_INSTANCEHEALGUAGE (314) - 实例治疗量表
- SM_BREAKWEAPON (315) - 武器破碎
- SM_HIDE (1224) - 隐身
- SM_GHOST (1225) - 幽灵状态

### 10. 其他缺失的状态协议
❌ **缺失协议**:
- SM_NOWDEATH (34) - 当前死亡
- SM_41 (36) - 未知状态
- SM_FEATURECHANGED (41) - 特征改变
- SM_USERNAME (42) - 用户名
- SM_DAYCHANGING (46) - 昼夜变化
- SM_MYSTATUS (766) - 我的状态

## 三、实现优先级建议

### 最高优先级（游戏基本运行必需）
1. **仓库系统协议** - 玩家存储物品的基础功能
2. **交易系统协议** - 玩家间交易的完整实现
3. **小地图协议** - 基础导航功能
4. **修理系统协议** - 装备维护功能

### 高优先级（核心游戏体验）
1. **高级战斗协议** - 各种技能和战斗动作
2. **魔法键位协议** - 快捷键系统
3. **制药系统协议** - 物品制造功能
4. **昼夜变化协议** - 游戏环境系统

### 中优先级（增强功能）
1. **骑马系统协议** - 移动增强
2. **特殊效果协议** - 视觉效果
3. **怪物AI协议** - 怪物行为

### 低优先级（辅助功能）
1. **系统配置协议** - 服务器配置
2. **登录公告协议** - 用户体验
3. **其他状态协议** - 细节功能

## 四、协议实现建议

### 1. 扩展PacketType枚举
```cpp
enum class PacketType : uint16_t {
    // ... 现有协议 ...
    
    // 仓库系统
    STORAGE_ITEM = 1031,            // CM_USERSTORAGEITEM
    STORAGE_RESULT = 2083,          // SM_STORAGE_OK/FULL/FAIL
    TAKEBACK_STORAGE_ITEM = 1032,   // CM_USERTAKEBACKSTORAGEITEM
    TAKEBACK_RESULT = 2087,         // SM_TAKEBACKSTORAGEITEM_OK/FAIL/FULLBAG
    
    // 交易系统
    DEAL_TRY = 1025,                // CM_DEALTRY
    DEAL_ADD_ITEM = 1026,           // CM_DEALADDITEM
    DEAL_DELETE_ITEM = 1027,        // CM_DEALDELITEM
    DEAL_CANCEL = 1028,             // CM_DEALCANCEL
    DEAL_CHANGE_GOLD = 1029,        // CM_DEALCHGGOLD
    DEAL_END = 1030,                // CM_DEALEND
    
    // 小地图
    WANT_MINIMAP = 1033,            // CM_WANTMINIMAP
    MINIMAP_RESULT = 710,           // SM_READMINIMAP_OK/FAIL
    
    // 修理系统
    REPAIR_ITEM = 1023,             // CM_USERREPAIRITEM
    QUERY_REPAIR_COST = 1024,       // CM_MERCHANTQUERYREPAIRCOST
    REPAIR_RESULT = 2081,           // SM_USERREPAIRITEM_OK/FAIL
    
    // 高级战斗
    RUSH_KUNG = 3007,               // CM_RUSHKUNG
    RUSH = 3006,                    // CM_RUSH
    BACK_STEP = 3009,               // CM_BACKSTEP
    HORSE_RUN = 3035,               // CM_HORSERUN
    CRS_HIT = 3036,                 // CM_CRSHIT
    TWIN_HIT = 3038,                // CM_TWINHIT
    
    // 魔法键位
    MAGIC_KEY_CHANGE = 1008,        // CM_MAGICKEYCHANGE
    
    // 制药系统
    MAKE_DRUG_ITEM = 1034,          // CM_USERMAKEDRUGITEM
    MAKE_DRUG_RESULT = 2092,        // SM_MAKEDRUG_SUCCESS/FAIL
};
```

### 2. 实现数据结构
```cpp
// 交易数据结构
struct DealData {
    uint32_t playerId;
    std::array<Item, 10> items;
    uint32_t gold;
    bool locked;
};

// 仓库数据结构
struct StorageData {
    std::vector<Item> items;
    uint32_t maxSlots;
};

// 修理数据结构
struct RepairData {
    uint32_t itemIndex;
    uint32_t cost;
    uint16_t durability;
};
```

### 3. 实现协议处理函数
```cpp
class NetworkManager {
    // 交易系统
    void HandleDealTry(const Packet& packet);
    void HandleDealAddItem(const Packet& packet);
    void HandleDealEnd(const Packet& packet);
    
    // 仓库系统
    void HandleStorageItem(const Packet& packet);
    void HandleTakebackItem(const Packet& packet);
    
    // 修理系统
    void HandleRepairItem(const Packet& packet);
    void HandleQueryRepairCost(const Packet& packet);
    
    // 小地图
    void HandleWantMinimap(const Packet& packet);
};
```

## 五、总结

重构版本已经实现了基础的游戏协议，但缺失了很多重要的功能协议，特别是：

1. **仓库系统** - 完全缺失
2. **交易系统** - 完全缺失  
3. **修理系统** - 完全缺失
4. **小地图系统** - 完全缺失
5. **高级战斗动作** - 大部分缺失
6. **制药系统** - 完全缺失

建议优先实现仓库和交易系统的协议，因为这些是玩家基本游戏体验的核心功能。 