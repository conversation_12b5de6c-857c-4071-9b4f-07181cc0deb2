#pragma once

#include "Dialog.h"
#include "Button.h"
#include "Label.h"
#include "TextInput.h"
#include "DialogManager.h"
#include "MessageDialog.h"
#include "../Graphics/WILLoader.h"
#include <functional>
#include <memory>
#include <string>

/**
 * @class ChangePasswordDialog
 * @brief Dialog for changing password
 *
 * This class represents a dialog for changing password, similar to the
 * DChgPw window in the original Delphi project.
 */
class ChangePasswordDialog : public Dialog {
public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param title Title
     * @param wilManager WIL manager
     */
    ChangePasswordDialog(int x, int y, int width, int height, const std::string& title, std::shared_ptr<WILManager> wilManager);

    /**
     * @brief Destructor
     */
    virtual ~ChangePasswordDialog();

    /**
     * @brief Initialize the dialog
     * @param renderer SDL renderer
     */
    virtual void Initialize(SDL_Renderer* renderer);

    /**
     * @brief Set the callback for when the OK button is clicked
     * @param callback Callback function
     */
    void SetOnOkCallback(std::function<void(const std::string&, const std::string&, const std::string&)> callback);

    /**
     * @brief Set the callback for when the Cancel button is clicked
     * @param callback Callback function
     */
    void SetOnCancelCallback(std::function<void()> callback);

    /**
     * @brief Get the account name
     * @return Account name
     */
    std::string GetAccountName() const;

    /**
     * @brief Get the current password
     * @return Current password
     */
    std::string GetCurrentPassword() const;

    /**
     * @brief Get the new password
     * @return New password
     */
    std::string GetNewPassword() const;

    /**
     * @brief Get the confirm password
     * @return Confirm password
     */
    std::string GetConfirmPassword() const;

private:
    std::shared_ptr<WILManager> m_wilManager;  ///< WIL manager
    std::shared_ptr<DialogManager> m_dialogManager; ///< Dialog manager

    // UI controls
    std::shared_ptr<Label> m_titleLabel;           ///< Title label
    std::shared_ptr<Label> m_accountLabel;         ///< Account label
    std::shared_ptr<Label> m_currentPasswordLabel; ///< Current password label
    std::shared_ptr<Label> m_newPasswordLabel;     ///< New password label
    std::shared_ptr<Label> m_confirmPasswordLabel; ///< Confirm password label

    std::shared_ptr<TextInput> m_accountInput;         ///< Account input
    std::shared_ptr<TextInput> m_currentPasswordInput; ///< Current password input
    std::shared_ptr<TextInput> m_newPasswordInput;     ///< New password input
    std::shared_ptr<TextInput> m_confirmPasswordInput; ///< Confirm password input

    std::shared_ptr<Button> m_okButton;        ///< OK button
    std::shared_ptr<Button> m_cancelButton;    ///< Cancel button

    // Callbacks
    std::function<void(const std::string&, const std::string&, const std::string&)> m_onOkCallback;
    std::function<void()> m_onCancelCallback;

    /**
     * @brief Handle OK button click
     */
    void OnOkButtonClick();

    /**
     * @brief Handle Cancel button click
     */
    void OnCancelButtonClick();
};
