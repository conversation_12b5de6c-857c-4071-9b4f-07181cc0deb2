cmake_minimum_required(VERSION 3.10)
project(SDL_Test)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# 查找SDL2
set(SDL2_PATH "C:/msys64/mingw64" CACHE PATH "SDL2安装路径")
set(SDL2_INCLUDE_DIRS "${SDL2_PATH}/include/SDL2")
set(SDL2_LIBRARIES "${SDL2_PATH}/lib/libSDL2.dll.a;${SDL2_PATH}/lib/libSDL2main.a")

# 包含目录
include_directories(${SDL2_INCLUDE_DIRS})

# 创建可执行文件
add_executable(test_sdl WIN32 test_sdl.cpp)

# 链接库
target_link_libraries(test_sdl
    ${SDL2_LIBRARIES}
    -lmingw32
) 