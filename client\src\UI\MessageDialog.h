#pragma once

#include "Dialog.h"
#include "../Graphics/WILManager.h"
#include "../Graphics/Texture.h"
#include <memory>
#include <string>
#include <functional>

/**
 * @enum MessageType
 * @brief Types of message dialogs
 */
enum class MessageType {
    INFORMATION,
    WARNING,
    ERROR,
    QUESTION
};

/**
 * @class MessageDialog
 * @brief Message dialog UI control
 *
 * This class represents a message dialog UI control, which is a modal window that can
 * display a message and buttons for user interaction.
 */
class MessageDialog : public Dialog {
private:
    MessageType m_messageType;                     ///< Message type
    std::shared_ptr<Texture> m_iconTexture;        ///< Icon texture
    int m_iconImageIndex;                          ///< Icon image index

    /**
     * @brief Create UI controls
     */
    void CreateControls() override;

public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param title Dialog title
     * @param message Dialog message
     * @param messageType Message type
     * @param buttons Dialog buttons
     * @param name Control name
     */
    MessageDialog(int x, int y, int width, int height, const std::string& title, const std::string& message = "", 
                 MessageType messageType = MessageType::INFORMATION, DialogButtons buttons = DialogButtons::OK, 
                 const std::string& name = "");

    /**
     * @brief Destructor
     */
    virtual ~MessageDialog();

    /**
     * @brief Initialize the dialog
     * @param renderer SDL renderer
     */
    void Initialize(SDL_Renderer* renderer) override;

    /**
     * @brief Render the dialog
     * @param renderer SDL renderer
     */
    void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Set the message type
     * @param messageType Message type
     */
    void SetMessageType(MessageType messageType);

    /**
     * @brief Get the message type
     * @return Message type
     */
    MessageType GetMessageType() const { return m_messageType; }

    /**
     * @brief Set the icon image index
     * @param iconImageIndex Icon image index
     */
    void SetIconImageIndex(int iconImageIndex);

    /**
     * @brief Get the icon image index
     * @return Icon image index
     */
    int GetIconImageIndex() const { return m_iconImageIndex; }
};
