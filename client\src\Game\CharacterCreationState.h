#pragma once

#include "../GameState.h"
#include "../Graphics/Texture.h"
#include "../UI/Button.h"
#include "../UI/Label.h"
#include "../UI/TextInput.h"
#include "../UI/RadioButton.h"
#include "../Network/NetworkManager.h"
#include "../Data/CharacterInfo.h"
#include <memory>
#include <vector>
#include <string>

/**
 * @class CharacterCreationState
 * @brief Game state for the character creation screen
 * 
 * This class represents the character creation screen of the game, where the player can
 * create a new character.
 */
class CharacterCreationState : public GameState {
private:
    std::shared_ptr<Texture> m_backgroundTexture;  ///< Background texture
    
    // UI controls
    std::shared_ptr<Label> m_titleLabel;           ///< Title label
    std::shared_ptr<Label> m_nameLabel;            ///< Name label
    std::shared_ptr<TextInput> m_nameInput;        ///< Name input
    std::shared_ptr<Label> m_classLabel;           ///< Class label
    std::vector<std::shared_ptr<RadioButton>> m_classRadioButtons;  ///< Class radio buttons
    std::shared_ptr<Button> m_createButton;        ///< Create button
    std::shared_ptr<Button> m_backButton;          ///< Back button
    std::shared_ptr<Label> m_statusLabel;          ///< Status label
    
    // Character preview
    std::shared_ptr<Texture> m_characterPreviewTexture;  ///< Character preview texture
    
    // Network manager
    std::shared_ptr<NetworkManager> m_networkManager;  ///< Network manager
    
    // Selected class
    PlayerClass m_selectedClass;                   ///< Selected class
    
    // Creation state
    bool m_creating;                               ///< Whether we're currently creating a character
    
    /**
     * @brief Create UI controls
     */
    void CreateControls();
    
    /**
     * @brief Handle create button click
     */
    void OnCreateButtonClick();
    
    /**
     * @brief Handle back button click
     */
    void OnBackButtonClick();
    
    /**
     * @brief Handle class selection
     * @param playerClass Selected player class
     */
    void OnClassSelected(PlayerClass playerClass);
    
    /**
     * @brief Handle character create response
     * @param packet Character create response packet
     */
    void OnCharacterCreateResponse(const Packet& packet);
    
public:
    /**
     * @brief Constructor
     * @param app Pointer to the application
     * @param networkManager Network manager
     */
    CharacterCreationState(Application* app, std::shared_ptr<NetworkManager> networkManager);
    
    /**
     * @brief Destructor
     */
    virtual ~CharacterCreationState();
    
    /**
     * @brief Called when entering the state
     */
    virtual void Enter() override;
    
    /**
     * @brief Called when exiting the state
     */
    virtual void Exit() override;
    
    /**
     * @brief Update the state
     * @param deltaTime Time elapsed since last frame in seconds
     */
    virtual void Update(float deltaTime) override;
    
    /**
     * @brief Render the state
     */
    virtual void Render() override;
    
    /**
     * @brief Handle SDL events
     * @param event SDL event to handle
     */
    virtual void HandleEvents(SDL_Event& event) override;
};
