#pragma once

#include "Dialog.h"
#include "Button.h"
#include "Label.h"
#include "TextInput.h"
#include "DialogManager.h"
#include "MessageDialog.h"
#include "../Graphics/WILLoader.h"
#include <functional>
#include <memory>
#include <string>

/**
 * @class NewAccountDialog
 * @brief Dialog for creating a new account
 *
 * This class represents a dialog for creating a new account, similar to the
 * DNewAccount window in the original Delphi project.
 */
class NewAccountDialog : public Dialog {
public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param title Title
     * @param wilManager WIL manager
     */
    NewAccountDialog(int x, int y, int width, int height, const std::string& title, std::shared_ptr<WILManager> wilManager);

        /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param title Title
     * @param wilManager WIL manager
     */
    NewAccountDialog(int x, int y, const std::string& title, std::shared_ptr<WILManager> wilManager);

    /**
     * @brief Destructor
     */
    virtual ~NewAccountDialog();

    /**
     * @brief Initialize the dialog
     * @param renderer SDL renderer
     */
    virtual void Initialize(SDL_Renderer* renderer);

    /**
     * @brief Set the callback for when the OK button is clicked
     * @param callback Callback function
     */
    void SetOnOkCallback(std::function<void(const std::string&, const std::string&, const std::string&, const std::string&, const std::string&, const std::string&, const std::string&, const std::string&)> callback);

    /**
     * @brief Set the callback for when the Cancel button is clicked
     * @param callback Callback function
     */
    void SetOnCancelCallback(std::function<void()> callback);

    /**
     * @brief Get the account name
     * @return Account name
     */
    std::string GetAccountName() const;

    /**
     * @brief Get the password
     * @return Password
     */
    std::string GetPassword() const;

    /**
     * @brief Get the confirm password
     * @return Confirm password
     */
    std::string GetConfirmPassword() const;

    /**
     * @brief Get the user name
     * @return User name
     */
    std::string GetUserName() const;

    /**
     * @brief Get the security question 1
     * @return Security question 1
     */
    std::string GetSecurityQuestion1() const;

    /**
     * @brief Get the security answer 1
     * @return Security answer 1
     */
    std::string GetSecurityAnswer1() const;

    /**
     * @brief Get the security question 2
     * @return Security question 2
     */
    std::string GetSecurityQuestion2() const;

    /**
     * @brief Get the security answer 2
     * @return Security answer 2
     */
    std::string GetSecurityAnswer2() const;

    /**
     * @brief Get the email
     * @return Email
     */
    std::string GetEmail() const;

    /**
     * @brief Get the phone number
     * @return Phone number
     */
    std::string GetPhoneNumber() const;

    /**
     * @brief Get the mobile phone number
     * @return Mobile phone number
     */
    std::string GetMobilePhoneNumber() const;

    /**
     * @brief Get the birth date
     * @return Birth date
     */
    std::string GetBirthDate() const;

private:
    std::shared_ptr<WILManager> m_wilManager;  ///< WIL manager
    std::shared_ptr<DialogManager> m_dialogManager; ///< Dialog manager

    // UI controls
    std::shared_ptr<Label> m_titleLabel;       ///< Title label
    std::shared_ptr<Label> m_accountLabel;     ///< Account label
    std::shared_ptr<Label> m_passwordLabel;    ///< Password label
    std::shared_ptr<Label> m_confirmLabel;     ///< Confirm password label
    std::shared_ptr<Label> m_userNameLabel;    ///< User name label
    std::shared_ptr<Label> m_question1Label;   ///< Security question 1 label
    std::shared_ptr<Label> m_answer1Label;     ///< Security answer 1 label
    std::shared_ptr<Label> m_question2Label;   ///< Security question 2 label
    std::shared_ptr<Label> m_answer2Label;     ///< Security answer 2 label
    std::shared_ptr<Label> m_emailLabel;       ///< Email label
    std::shared_ptr<Label> m_phoneLabel;       ///< Phone label
    std::shared_ptr<Label> m_mobileLabel;      ///< Mobile phone label
    std::shared_ptr<Label> m_birthDateLabel;   ///< Birth date label

    std::shared_ptr<TextInput> m_accountInput;     ///< Account input
    std::shared_ptr<TextInput> m_passwordInput;    ///< Password input
    std::shared_ptr<TextInput> m_confirmInput;     ///< Confirm password input
    std::shared_ptr<TextInput> m_userNameInput;    ///< User name input
    std::shared_ptr<TextInput> m_question1Input;   ///< Security question 1 input
    std::shared_ptr<TextInput> m_answer1Input;     ///< Security answer 1 input
    std::shared_ptr<TextInput> m_question2Input;   ///< Security question 2 input
    std::shared_ptr<TextInput> m_answer2Input;     ///< Security answer 2 input
    std::shared_ptr<TextInput> m_emailInput;       ///< Email input
    std::shared_ptr<TextInput> m_phoneInput;       ///< Phone input
    std::shared_ptr<TextInput> m_mobileInput;      ///< Mobile phone input
    std::shared_ptr<TextInput> m_birthDateInput;   ///< Birth date input

    std::shared_ptr<Button> m_okButton;        ///< OK button
    std::shared_ptr<Button> m_cancelButton;    ///< Cancel button

    // Callbacks
    std::function<void(const std::string&, const std::string&, const std::string&, const std::string&, const std::string&, const std::string&, const std::string&, const std::string&)> m_onOkCallback;
    std::function<void()> m_onCancelCallback;

    /**
     * @brief Handle OK button click
     */
    void OnOkButtonClick();

    /**
     * @brief Handle Cancel button click
     */
    void OnCancelButtonClick();
};
