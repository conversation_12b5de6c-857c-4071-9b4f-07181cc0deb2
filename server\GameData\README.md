# DBServer 游戏数据库使用说明

## 概述

DBServer现在支持统一的数据库接口，默认使用SQLite实现物品和魔法数据的存储。系统设计为可扩展架构，后续可以轻松添加MySQL、PostgreSQL等其他数据库支持。

## 目录结构

```
GameData/
├── GameData.db          # SQLite数据库文件
├── Import/              # 数据导入目录
│   ├── StdItems.csv    # 物品数据CSV文件
│   └── Magic.csv       # 魔法数据CSV文件
└── README.md           # 本文档
```

## 配置说明

在 `config/DBServer.ini` 中添加以下配置：

```ini
[DB]
; 游戏数据库文件路径
GameDB=./GameData/GameData.db

[GameData]
; 是否自动导入游戏数据
AutoImport=1
; 游戏数据导入目录
ImportDir=./GameData/Import/
; 物品数据文件
ItemsFile=StdItems.csv
; 魔法数据文件
MagicsFile=Magic.csv
```

## 数据格式说明

### 物品数据 (StdItems.csv)

| 字段 | 类型 | 说明 |
|------|------|------|
| Idx | INTEGER | 物品索引（主键） |
| Name | TEXT | 物品名称 |
| StdMode | INTEGER | 物品类型 |
| Shape | INTEGER | 形状 |
| Weight | INTEGER | 重量 |
| AniCount | INTEGER | 动画数量 |
| Source | INTEGER | 来源 |
| Reserved | INTEGER | 保留字段 |
| Looks | INTEGER | 外观 |
| DuraMax | INTEGER | 最大耐久 |
| Ac/Ac2 | INTEGER | 防御力范围 |
| Mac/Mac2 | INTEGER | 魔法防御范围 |
| Dc/Dc2 | INTEGER | 物理攻击范围 |
| Mc/Mc2 | INTEGER | 魔法攻击范围 |
| Sc/Sc2 | INTEGER | 道术攻击范围 |
| Need | INTEGER | 需求类型 |
| NeedLevel | INTEGER | 需要等级 |
| Price | INTEGER | 价格 |

### 魔法数据 (Magic.csv)

| 字段 | 类型 | 说明 |
|------|------|------|
| MagId | INTEGER | 魔法ID（主键） |
| MagName | TEXT | 魔法名称 |
| EffectType | INTEGER | 效果类型 |
| Effect | INTEGER | 效果 |
| Spell | INTEGER | 消耗MP |
| Power | INTEGER | 威力 |
| MaxPower | INTEGER | 最大威力 |
| Job | INTEGER | 职业（0:战士 1:法师 2:道士） |
| NeedL1/L2/L3 | INTEGER | 各等级需求 |
| L1Train/L2Train/L3Train | INTEGER | 各等级训练值 |
| Delay | INTEGER | 施法延迟 |
| DefSpell | INTEGER | 防御施法 |
| DefPower | INTEGER | 防御威力 |
| DefMaxPower | INTEGER | 防御最大威力 |
| Descr | TEXT | 技能描述 |

## 物品类型 (StdMode)

- 0: 药品
- 5: 武器
- 10: 盔甲
- 15: 头盔
- 19: 项链
- 22: 戒指
- 24: 手镯
- 25: 护身符
- 52: 靴子
- 54: 腰带

## API使用示例

### 获取游戏数据管理器

```cpp
// 在DBServer中获取游戏数据管理器
GameDataManager* dataManager = dbServer->GetGameDataManager();
```

### 查询物品

```cpp
// 通过ID查找物品
const StdItem* item = dataManager->FindItem(1);
if (item) {
    LOG_INFO("Item found: " + item->name);
}

// 通过名称查找物品
const StdItem* item = dataManager->FindItemByName("木剑");
```

### 查询魔法

```cpp
// 通过ID查找魔法
const Magic* magic = dataManager->FindMagic(1);
if (magic) {
    LOG_INFO("Magic found: " + magic->magicName);
}

// 通过名称查找魔法
const Magic* magic = dataManager->FindMagicByName("火球术");
```

### 使用DAO直接操作数据库

```cpp
// 获取ItemDAO
ItemDAO* itemDAO = dataManager->GetItemDAO();

// 添加新物品
StdItem newItem;
newItem.idx = 1000;
newItem.name = "新武器";
newItem.stdMode = 5;
// ... 设置其他属性
if (itemDAO->Create(newItem)) {
    LOG_INFO("Item created successfully");
}

// 查询所有武器
auto weapons = itemDAO->ReadByType(5);
for (const auto& weapon : weapons) {
    LOG_INFO("Weapon: " + weapon.name);
}
```

## 数据导入

系统启动时会自动检查 `Import` 目录下的CSV文件并导入数据（如果配置了 `AutoImport=1`）。

也可以手动导入数据：

```cpp
DataImporter importer(dataManager);
if (importer.ImportItems("./GameData/Import/StdItems.csv")) {
    LOG_INFO("Items imported successfully");
}
```

## 扩展其他数据库

要添加新的数据库支持（如MySQL），只需：

1. 实现 `IDatabase` 接口
2. 在 `DatabaseFactory::Create` 中添加创建逻辑
3. 修改配置文件支持新的数据库类型

示例：
```cpp
class MySQLDatabase : public IDatabase {
    // 实现所有虚函数
};

// 在DatabaseFactory中
if (type == DatabaseType::MySQL) {
    return std::make_unique<MySQLDatabase>();
}
```

## 注意事项

1. 数据库文件会自动创建，无需手动建表
2. 首次运行时数据库为空，需要导入数据
3. 建议定期备份 `GameData.db` 文件
4. CSV文件使用UTF-8编码
5. 物品ID和魔法ID必须唯一

## 故障排除

1. **数据库连接失败**
   - 检查文件路径是否正确
   - 确保有写入权限
   - 检查磁盘空间

2. **数据导入失败**
   - 检查CSV格式是否正确
   - 确保没有重复的ID
   - 查看日志文件获取详细错误信息

3. **查询性能问题**
   - 数据已经加载到内存，查询应该很快
   - 如果数据量很大，考虑优化索引 