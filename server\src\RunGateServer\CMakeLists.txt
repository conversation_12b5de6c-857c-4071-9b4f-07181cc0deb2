# RunGateServer CMakeLists.txt
cmake_minimum_required(VERSION 3.12)

# 设置项目名称
set(PROJECT_NAME RunGateServer)
project(${PROJECT_NAME})

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
if(WIN32)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3")
    add_definitions(-DNOMINMAX -DWIN32_LEAN_AND_MEAN)
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")
endif()

# 查找依赖库
find_package(Threads REQUIRED)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/../Common
    ${CMAKE_CURRENT_SOURCE_DIR}/../Protocol
    ${CMAKE_CURRENT_SOURCE_DIR}/../../../third_party
)

# 源文件
set(SOURCES
    main.cpp
    RunGateServer.cpp
)

# 头文件
set(HEADERS
    RunGateServer.h
)

# 依赖的其他模块源文件
set(COMMON_SOURCES
    ../Common/Types.cpp
    ../Common/Config.cpp
    ../Common/Utils.cpp
    ../Common/Logger.cpp
    ../Common/IniFile.cpp
)

set(PROTOCOL_SOURCES
    ../Protocol/PacketTypes.cpp
    ../Protocol/MessageConverter.cpp
    ../Protocol/NetworkManager.cpp
)

# 创建可执行文件
add_executable(${PROJECT_NAME}
    ${SOURCES}
    ${HEADERS}
    ${COMMON_SOURCES}
    ${PROTOCOL_SOURCES}
)

# 链接库
target_link_libraries(${PROJECT_NAME}
    Threads::Threads
)

# Windows特定链接库
if(WIN32)
    target_link_libraries(${PROJECT_NAME}
        ws2_32
        wsock32
        iphlpapi
    )
endif()

# Linux特定链接库
if(UNIX AND NOT APPLE)
    target_link_libraries(${PROJECT_NAME}
        pthread
        dl
    )
endif()

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/bin
    RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/bin
)

# 添加编译定义
target_compile_definitions(${PROJECT_NAME} PRIVATE
    $<$<CONFIG:Debug>:DEBUG>
    $<$<CONFIG:Release>:NDEBUG>
)

# 设置调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(${PROJECT_NAME} PRIVATE
        $<$<CXX_COMPILER_ID:MSVC>:/Zi>
        $<$<CXX_COMPILER_ID:GNU>:-g>
        $<$<CXX_COMPILER_ID:Clang>:-g>
    )
endif()

# 安装规则
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

# 创建配置目录
file(MAKE_DIRECTORY ${CMAKE_BINARY_DIR}/bin/config)

# 创建示例配置文件
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/config/RunGateServer.ini.example
    ${CMAKE_BINARY_DIR}/bin/config/RunGateServer.ini.example
    COPYONLY
)

# 打印构建信息
message(STATUS "Configuring ${PROJECT_NAME}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Output directory: ${CMAKE_BINARY_DIR}/bin") 