#include "MonitorServer.h"
#include "MsgServerManager.h"
#include "../Common/Logger.h"
#include <chrono>
#include <sstream>

namespace MirServer {

MonitorServer::MonitorServer(MsgServerManager* msgManager) 
    : m_msgServerManager(msgManager) {
    m_network = std::make_unique<Network::NetworkManager>();
}

MonitorServer::~MonitorServer() {
    Stop();
}

bool MonitorServer::Initialize(const std::string& monAddr, int32_t monPort) {
    if (!m_network->Initialize()) {
        return false;
    }
    
    if (!m_network->StartServer(monPort)) {
        Logger::Error("Failed to start MonitorServer on port " + std::to_string(monPort));
        return false;
    }
    
    Logger::Info("MonitorServer listening on " + monAddr + ":" + std::to_string(monPort));
    return true;
}

bool MonitorServer::Start() {
    if (m_running) {
        return true;
    }
    
    m_running = true;
    m_broadcastThread = std::thread(&MonitorServer::BroadcastLoop, this);
    
    return true;
}

void MonitorServer::Stop() {
    if (!m_running) {
        return;
    }
    
    m_running = false;
    
    if (m_broadcastThread.joinable()) {
        m_broadcastThread.join();
    }
    
    if (m_network) {
        m_network->StopServer();
    }
}

void MonitorServer::BroadcastLoop() {
    while (m_running) {
        SendStatusToAllClients();
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

void MonitorServer::SendStatusToAllClients() {
    std::string statusMsg = GetServerStatusString();
    
    // Send to all connected monitoring clients
    // TODO: When NetworkManager supports getting all connections,
    // iterate through them and send the status message
    // For now, this is a placeholder
}

std::string MonitorServer::GetServerStatusString() {
    // Format the status string similar to Delphi version
    std::ostringstream oss;
    
    // This would get server information from MsgServerManager
    // Format: count;serverName/index/onlineCount/status;...
    
    int32_t serverCount = 0;  // Would get from MsgServerManager
    int32_t totalOnline = m_msgServerManager->GetOnlineHumCount();
    
    oss << serverCount << ";";
    
    // Add each server's status
    // This would iterate through servers in MsgServerManager
    // For now, just return a placeholder
    
    oss << "TestServer/0/" << totalOnline << "/运行;";
    
    return oss.str();
}

} // namespace MirServer 