#pragma once

#include <SDL2/SDL.h>
#include <memory>
#include <string>
#include <vector>
#include <array>
#include "../Graphics/Texture.h"

// Constants for light system
constexpr int MAXLIGHT = 5;
constexpr int LMX = 30;
constexpr int LMY = 26;

/**
 * @struct LightEffect
 * @brief Represents a light effect texture
 */
struct LightEffect {
    int width;                  ///< Width of the light effect
    int height;                 ///< Height of the light effect
    std::shared_ptr<Texture> texture;  ///< Texture for the light effect
};

/**
 * @struct LightMapInfo
 * @brief Information about a light in the light map
 */
struct LightMapInfo {
    int shiftX;                 ///< X shift of the light
    int shiftY;                 ///< Y shift of the light
    int light;                  ///< Light level (0-5)
    int bright;                 ///< Brightness level

    LightMapInfo() : shiftX(0), shiftY(0), light(-1), bright(0) {}
};

/**
 * @class LightManager
 * @brief Manages light effects and fog rendering
 *
 * This class is responsible for loading, managing, and rendering light effects
 * and fog. It provides methods for adding lights to the scene and applying
 * the light map to the final render.
 */
class LightManager {
private:
    SDL_Renderer* m_renderer;                       ///< SDL renderer
    std::array<LightEffect, MAXLIGHT+1> m_lights;   ///< Light effects
    std::array<std::array<LightMapInfo, LMY+1>, LMX+1> m_lightMap;  ///< Light map

    std::shared_ptr<Texture> m_fogTexture;          ///< Fog texture
    int m_fogWidth;                                 ///< Fog width
    int m_fogHeight;                                ///< Fog height

    bool m_viewFog;                                 ///< Whether to view fog
    bool m_forceNotViewFog;                         ///< Force not to view fog
    int m_playerX;                                  ///< Player X position
    int m_playerY;                                  ///< Player Y position

    // Light masks for different light levels
    static const std::array<std::array<int8_t, 19>, 19> LIGHT_MASK_0;
    static const std::array<std::array<int8_t, 19>, 19> LIGHT_MASK_1;
    static const std::array<std::array<int8_t, 19>, 19> LIGHT_MASK_2;
    static const std::array<std::array<int8_t, 19>, 19> LIGHT_MASK_3;
    static const std::array<std::array<int8_t, 19>, 19> LIGHT_MASK_4;
    static const std::array<std::array<int8_t, 19>, 19> LIGHT_MASK_5;

    /**
     * @brief Update brightness at a position
     * @param x X coordinate in light map
     * @param y Y coordinate in light map
     * @param light Light level (0-5)
     */
    void UpdateBright(int x, int y, int light);

    /**
     * @brief Check if a light would overlap too much with existing lights
     * @param x X coordinate in light map
     * @param y Y coordinate in light map
     * @param light Light level (0-5)
     * @return true if the light would overlap too much, false otherwise
     */
    bool CheckOverLight(int x, int y, int light);

    /**
     * @brief Draw a light effect
     * @param x X coordinate on screen
     * @param y Y coordinate on screen
     * @param bright Brightness level
     */
    void DrawLightEffect(int x, int y, int bright);

public:
    /**
     * @brief Constructor
     * @param renderer SDL renderer
     */
    LightManager(SDL_Renderer* renderer);

    /**
     * @brief Destructor
     */
    ~LightManager();

    /**
     * @brief Initialize the light manager
     * @return true if successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Load light effect data
     * @return true if successful, false otherwise
     */
    bool LoadLightEffects();

    /**
     * @brief Clear the light map
     */
    void ClearLightMap();

    /**
     * @brief Add a light to the light map
     * @param x X coordinate in map coordinates
     * @param y Y coordinate in map coordinates
     * @param shiftX X shift
     * @param shiftY Y shift
     * @param light Light level (0-5)
     * @param nocheck Whether to skip overlap check
     */
    void AddLight(int x, int y, int shiftX, int shiftY, int light, bool nocheck);

    /**
     * @brief Apply the light map to the scene
     * @param playerX Player X coordinate
     * @param playerY Player Y coordinate
     * @param playerShiftX Player X shift
     * @param playerShiftY Player Y shift
     * @param mapLeft Map left coordinate
     * @param mapTop Map top coordinate
     */
    void ApplyLightMap(int playerX, int playerY, int playerShiftX, int playerShiftY, int mapLeft, int mapTop);

    /**
     * @brief Render the fog
     * @param target Target texture to render to
     */
    void RenderFog(SDL_Texture* target);

    /**
     * @brief Set whether to view fog
     * @param viewFog Whether to view fog
     */
    void SetViewFog(bool viewFog) { m_viewFog = viewFog; }

    /**
     * @brief Set whether to force not to view fog
     * @param forceNotViewFog Whether to force not to view fog
     */
    void SetForceNotViewFog(bool forceNotViewFog) { m_forceNotViewFog = forceNotViewFog; }

    /**
     * @brief Check if fog is visible
     * @return true if fog is visible, false otherwise
     */
    bool IsViewFog() const { return m_viewFog && !m_forceNotViewFog; }

    /**
     * @brief Set the day state
     * @param state Day state (0 = night, 1 = dawn, 2 = day, 3 = dusk)
     */
    void SetDayState(int state);
};

