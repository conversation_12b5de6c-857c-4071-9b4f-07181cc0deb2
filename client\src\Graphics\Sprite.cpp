#include "Sprite.h"

// Sprite implementation

Sprite::Sprite(std::shared_ptr<Texture> texture)
    : m_texture(texture)
    , m_x(0)
    , m_y(0)
    , m_offsetX(0)
    , m_offsetY(0)
    , m_angle(0.0)
    , m_flip(SDL_FLIP_NONE)
    , m_visible(true)
{
    // Set default clip to the entire texture
    if (texture) {
        m_clip = {0, 0, texture->GetWidth(), texture->GetHeight()};
    } else {
        m_clip = {0, 0, 0, 0};
    }
    
    // Set default rotation center
    m_center = {0, 0};
}

Sprite::~Sprite()
{
}

void Sprite::SetClip(int x, int y, int w, int h)
{
    m_clip.x = x;
    m_clip.y = y;
    m_clip.w = w;
    m_clip.h = h;
}

void Sprite::SetPosition(int x, int y)
{
    m_x = x;
    m_y = y;
}

void Sprite::SetOffset(int x, int y)
{
    m_offsetX = x;
    m_offsetY = y;
}

void Sprite::SetAngle(double angle)
{
    m_angle = angle;
}

void Sprite::SetCenter(int x, int y)
{
    m_center.x = x;
    m_center.y = y;
}

void Sprite::SetFlip(SDL_RendererFlip flip)
{
    m_flip = flip;
}

void Sprite::SetVisible(bool visible)
{
    m_visible = visible;
}

void Sprite::Render()
{
    if (!m_visible || !m_texture) {
        return;
    }
    
    // Render the sprite with all the specified options
    m_texture->RenderEx(m_x + m_offsetX, m_y + m_offsetY, &m_clip, m_angle, &m_center, m_flip);
}

// AnimatedSprite implementation

AnimatedSprite::AnimatedSprite(std::shared_ptr<Texture> texture)
    : Sprite(texture)
    , m_currentFrame(0)
    , m_frameCount(0)
    , m_frameDelay(100)
    , m_frameTimer(0)
    , m_looping(true)
    , m_playing(false)
{
}

AnimatedSprite::~AnimatedSprite()
{
}

void AnimatedSprite::AddFrame(int x, int y, int w, int h)
{
    SDL_Rect frame = {x, y, w, h};
    m_frames.push_back(frame);
    m_frameCount = static_cast<int>(m_frames.size());
    
    // If this is the first frame, set it as the current clip
    if (m_frameCount == 1) {
        SetClip(x, y, w, h);
    }
}

void AnimatedSprite::SetFrameDelay(int delay)
{
    m_frameDelay = delay;
}

void AnimatedSprite::SetLooping(bool looping)
{
    m_looping = looping;
}

void AnimatedSprite::Play()
{
    m_playing = true;
}

void AnimatedSprite::Stop()
{
    m_playing = false;
}

void AnimatedSprite::Reset()
{
    m_currentFrame = 0;
    m_frameTimer = 0;
    
    // Set the clip to the first frame
    if (m_frameCount > 0) {
        SetClip(
            m_frames[0].x,
            m_frames[0].y,
            m_frames[0].w,
            m_frames[0].h
        );
    }
}

void AnimatedSprite::SetFrame(int frame)
{
    if (frame >= 0 && frame < m_frameCount) {
        m_currentFrame = frame;
        
        // Set the clip to the specified frame
        SetClip(
            m_frames[m_currentFrame].x,
            m_frames[m_currentFrame].y,
            m_frames[m_currentFrame].w,
            m_frames[m_currentFrame].h
        );
    }
}

void AnimatedSprite::Update(int deltaTime)
{
    if (!m_playing || m_frameCount <= 1) {
        return;
    }
    
    // Update the frame timer
    m_frameTimer += deltaTime;
    
    // Check if it's time to advance to the next frame
    if (m_frameTimer >= m_frameDelay) {
        m_frameTimer = 0;
        
        // Advance to the next frame
        m_currentFrame++;
        
        // Check if we've reached the end of the animation
        if (m_currentFrame >= m_frameCount) {
            if (m_looping) {
                // Loop back to the beginning
                m_currentFrame = 0;
            } else {
                // Stop at the last frame
                m_currentFrame = m_frameCount - 1;
                m_playing = false;
            }
        }
        
        // Set the clip to the current frame
        SetClip(
            m_frames[m_currentFrame].x,
            m_frames[m_currentFrame].y,
            m_frames[m_currentFrame].w,
            m_frames[m_currentFrame].h
        );
    }
}

void AnimatedSprite::Render()
{
    // Use the base class's render method
    Sprite::Render();
}
