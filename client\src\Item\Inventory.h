#pragma once

#include "Item.h"
#include <vector>
#include <memory>
#include <unordered_map>
#include <functional>

/**
 * @class Inventory
 * @brief Manages a collection of items
 */
class Inventory {
public:
    static const int MAX_INVENTORY_SIZE = 50;  ///< Maximum number of items in the inventory
    static const int MAX_EQUIPMENT_SLOTS = 13; ///< Maximum number of equipment slots

    /**
     * @enum EquipmentSlot
     * @brief Equipment slots
     */
    enum class EquipmentSlot {
        WEAPON = 0,
        ARMOR = 1,
        HELMET = 2,
        NECKLACE = 3,
        LEFT_HAND = 4,  // Shield or dual wield
        RIGHT_HAND = 5, // Ring
        LEFT_BRACELET = 6,
        RIGHT_BRACELET = 7,
        SHOES = 8,
        BELT = 9,
        STONE = 10,
        TORCH = 11,
        BOOK = 12
    };

private:
    std::vector<std::shared_ptr<Item>> m_items;                  ///< Items in the inventory
    std::unordered_map<int, std::shared_ptr<Item>> m_equipment;  ///< Equipped items
    int m_gold;                                                  ///< Amount of gold
    int m_weight;                                                ///< Total weight of items
    int m_maxWeight;                                             ///< Maximum weight capacity

public:
    /**
     * @brief Constructor
     */
    Inventory();

    /**
     * @brief Destructor
     */
    ~Inventory();

    /**
     * @brief Get the items in the inventory
     * @return Items in the inventory
     */
    const std::vector<std::shared_ptr<Item>>& GetItems() const { return m_items; }

    /**
     * @brief Get the equipped items
     * @return Equipped items
     */
    const std::unordered_map<int, std::shared_ptr<Item>>& GetEquipment() const { return m_equipment; }

    /**
     * @brief Get the amount of gold
     * @return Amount of gold
     */
    int GetGold() const { return m_gold; }

    /**
     * @brief Set the amount of gold
     * @param gold Amount of gold
     */
    void SetGold(int gold) { m_gold = gold; }

    /**
     * @brief Get the total weight of items
     * @return Total weight of items
     */
    int GetWeight() const { return m_weight; }

    /**
     * @brief Set the total weight of items
     * @param weight Total weight of items
     */
    void SetWeight(int weight) { m_weight = weight; }

    /**
     * @brief Get the maximum weight capacity
     * @return Maximum weight capacity
     */
    int GetMaxWeight() const { return m_maxWeight; }

    /**
     * @brief Set the maximum weight capacity
     * @param maxWeight Maximum weight capacity
     */
    void SetMaxWeight(int maxWeight) { m_maxWeight = maxWeight; }

    /**
     * @brief Add an item to the inventory
     * @param item Item to add
     * @param slot Slot to add the item to (-1 for auto-assign)
     * @return true if successful, false otherwise
     */
    bool AddItem(std::shared_ptr<Item> item, int slot = -1);

    /**
     * @brief Remove an item from the inventory
     * @param slot Slot of the item to remove
     * @return Removed item, or nullptr if the slot is empty
     */
    std::shared_ptr<Item> RemoveItem(int slot);

    /**
     * @brief Get an item from the inventory
     * @param slot Slot of the item to get
     * @return Item at the specified slot, or nullptr if the slot is empty
     */
    std::shared_ptr<Item> GetItem(int slot) const;

    /**
     * @brief Update an item in the inventory
     * @param slot Slot of the item to update
     * @param item New item data
     * @return true if successful, false otherwise
     */
    bool UpdateItem(int slot, std::shared_ptr<Item> item);

    /**
     * @brief Clear the inventory
     */
    void Clear();

    /**
     * @brief Equip an item from inventory slot
     * @param slot Inventory slot of the item to equip
     * @param equipSlot Equipment slot to equip the item to
     * @return true if successful, false otherwise
     */
    bool EquipItem(int slot, EquipmentSlot equipSlot);

    /**
     * @brief Equip an item directly
     * @param item Item to equip
     * @param equipSlot Equipment slot to equip the item to
     * @return true if successful, false otherwise
     */
    bool EquipItem(std::shared_ptr<Item> item, EquipmentSlot equipSlot);

    /**
     * @brief Unequip an item
     * @param equipSlot Equipment slot to unequip
     * @return true if successful, false otherwise
     */
    bool UnequipItem(EquipmentSlot equipSlot);

    /**
     * @brief Get an equipped item
     * @param equipSlot Equipment slot to get the item from
     * @return Item at the specified equipment slot, or nullptr if the slot is empty
     */
    std::shared_ptr<Item> GetEquippedItem(EquipmentSlot equipSlot) const;

    /**
     * @brief Find items in the inventory
     * @param predicate Function that takes an Item and returns true if it matches the criteria
     * @return Vector of matching items
     */
    std::vector<std::shared_ptr<Item>> FindItems(std::function<bool(const Item&)> predicate) const;

    /**
     * @brief Find the first empty slot in the inventory
     * @return First empty slot, or -1 if the inventory is full
     */
    int FindEmptySlot() const;

    /**
     * @brief Check if the inventory is full
     * @return true if the inventory is full, false otherwise
     */
    bool IsFull() const;

    /**
     * @brief Calculate the total weight of items in the inventory
     * @return Total weight of items
     */
    int CalculateWeight() const;

    /**
     * @brief Check if an item can be equipped in a specific slot
     * @param item Item to check
     * @param equipSlot Equipment slot to check
     * @return true if the item can be equipped in the specified slot, false otherwise
     */
    bool CanEquip(const Item& item, EquipmentSlot equipSlot) const;

    /**
     * @brief Convert an equipment slot to an item type
     * @param equipSlot Equipment slot
     * @return Corresponding item type
     */
    ItemType EquipmentSlotToItemType(EquipmentSlot equipSlot) const;

private:

    /**
     * @brief Convert an item type to an equipment slot
     * @param type Item type
     * @return Corresponding equipment slot
     */
    EquipmentSlot ItemTypeToEquipmentSlot(ItemType type) const;
};
