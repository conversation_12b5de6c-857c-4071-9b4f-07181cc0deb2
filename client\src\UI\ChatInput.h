#pragma once

#include "UIControl.h"
#include <string>
#include <vector>
#include <functional>
#include <SDL2/SDL.h>
#include <SDL2/SDL_ttf.h>
#include <memory>
#include "../Graphics/Texture.h"

/**
 * @class ChatInput
 * @brief UI control for chat input
 */
class ChatInput : public UIControl {
private:
    std::string m_text;                         ///< Current input text
    std::string m_placeholder;                  ///< Placeholder text
    int m_cursorPosition;                       ///< Current cursor position
    int m_selectionStart;                       ///< Selection start position
    int m_selectionLength;                      ///< Selection length
    bool m_focused;                             ///< Whether the input is focused
    bool m_passwordMode;                        ///< Whether to display text as password
    char m_passwordChar;                        ///< Character to use for password mode
    TTF_Font* m_font;                           ///< Font for rendering text
    std::shared_ptr<Texture> m_backgroundTexture;  ///< Background texture
    std::vector<std::string> m_history;         ///< Command history
    int m_historyPosition;                      ///< Current position in history
    int m_maxHistorySize;                       ///< Maximum history size
    std::function<void(const std::string&)> m_onSubmit;  ///< Callback for when Enter is pressed

    /**
     * @brief Create a texture for the input text
     * @return The created texture
     */
    std::shared_ptr<Texture> CreateTextTexture();

    /**
     * @brief Insert text at the current cursor position
     * @param text The text to insert
     */
    void InsertText(const std::string& text);

    /**
     * @brief Delete selected text
     */
    void DeleteSelection();

    /**
     * @brief Move cursor left
     * @param select Whether to extend selection
     */
    void MoveCursorLeft(bool select);

    /**
     * @brief Move cursor right
     * @param select Whether to extend selection
     */
    void MoveCursorRight(bool select);

    /**
     * @brief Move cursor to start
     * @param select Whether to extend selection
     */
    void MoveCursorToStart(bool select);

    /**
     * @brief Move cursor to end
     * @param select Whether to extend selection
     */
    void MoveCursorToEnd(bool select);

    /**
     * @brief Add current text to history
     */
    void AddToHistory();

    /**
     * @brief Navigate history up
     */
    void HistoryUp();

    /**
     * @brief Navigate history down
     */
    void HistoryDown();

public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param font Font for rendering text
     * @param maxHistorySize Maximum history size
     * @param name Name of the control
     */
    ChatInput(int x, int y, int width, int height, TTF_Font* font, int maxHistorySize = 50, const std::string& name = "ChatInput");

    /**
     * @brief Destructor
     */
    virtual ~ChatInput();

    /**
     * @brief Get the current text
     * @return The current text
     */
    const std::string& GetText() const;

    /**
     * @brief Set the text
     * @param text The text to set
     */
    void SetText(const std::string& text);

    /**
     * @brief Set the placeholder text
     * @param placeholder The placeholder text
     */
    void SetPlaceholder(const std::string& placeholder);

    /**
     * @brief Set whether to display text as password
     * @param passwordMode Whether to display text as password
     * @param passwordChar Character to use for password mode
     */
    void SetPasswordMode(bool passwordMode, char passwordChar = '*');

    /**
     * @brief Set the background texture
     * @param texture The background texture
     */
    void SetBackgroundTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Set the callback for when Enter is pressed
     * @param callback The callback function
     */
    void SetOnSubmit(std::function<void(const std::string&)> callback);

    /**
     * @brief Clear the text
     */
    void Clear();

    /**
     * @brief Focus the input
     */
    void Focus();

    /**
     * @brief Unfocus the input
     */
    void Unfocus();

    /**
     * @brief Check if the input is focused
     * @return true if focused, false otherwise
     */
    bool IsFocused() const;

    /**
     * @brief Update the control
     * @param deltaTime Time elapsed since last frame
     */
    virtual void Update(int deltaTime) override;

    /**
     * @brief Render the control
     * @param renderer The renderer to use
     */
    virtual void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Handle key down events
     * @param event The key event
     * @return true if the event was handled, false otherwise
     */
    virtual bool OnKeyDown(const SDL_KeyboardEvent& event);

    /**
     * @brief Handle text input events
     * @param event The text input event
     * @return true if the event was handled, false otherwise
     */
    virtual bool OnTextInput(const SDL_TextInputEvent& event);

    /**
     * @brief Handle mouse down events
     * @param event The mouse button event
     * @return true if the event was handled, false otherwise
     */
    virtual bool OnMouseDown(const SDL_MouseButtonEvent& event);

    /**
     * @brief Handle mouse up events
     * @param event The mouse button event
     * @return true if the event was handled, false otherwise
     */
    virtual bool OnMouseUp(const SDL_MouseButtonEvent& event);

    /**
     * @brief Handle mouse move events
     * @param event The mouse motion event
     * @return true if the event was handled, false otherwise
     */
    virtual bool OnMouseMove(const SDL_MouseMotionEvent& event);
};

