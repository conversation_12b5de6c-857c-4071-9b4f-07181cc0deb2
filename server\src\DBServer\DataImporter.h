// DataImporter.h - 游戏数据导入工具
#pragma once

#include "GameDataDAO.h"
#include <string>
#include <vector>

namespace DBServer {

// CSV数据导入器
class DataImporter {
public:
    DataImporter(GameDataManager* gameDataManager);
    ~DataImporter() = default;
    
    // 导入物品数据
    bool ImportItems(const std::string& csvFile);
    bool ImportItemsFromDirectory(const std::string& directory);
    
    // 导入魔法数据
    bool ImportMagics(const std::string& csvFile);
    bool ImportMagicsFromDirectory(const std::string& directory);
    
    // 导入所有数据
    bool ImportAllFromDirectory(const std::string& directory);
    
    // 导出数据（用于备份）
    bool ExportItems(const std::string& csvFile);
    bool ExportMagics(const std::string& csvFile);
    bool ExportAllToDirectory(const std::string& directory);
    
    // 获取错误信息
    std::string GetLastError() const { return m_lastError; }
    
    // 获取导入统计
    struct ImportStats {
        int itemsImported;
        int itemsUpdated;
        int itemsFailed;
        int magicsImported;
        int magicsUpdated;
        int magicsFailed;
    };
    const ImportStats& GetStats() const { return m_stats; }
    
private:
    GameDataManager* m_gameDataManager;
    std::string m_lastError;
    ImportStats m_stats;
    
    // CSV解析辅助函数
    std::vector<std::string> ParseCSVLine(const std::string& line);
    bool ParseItemFromCSV(const std::vector<std::string>& fields, MirServer::StdItem& item);
    bool ParseMagicFromCSV(const std::vector<std::string>& fields, MirServer::Magic& magic);
    
    // 数据验证
    bool ValidateItem(const MirServer::StdItem& item);
    bool ValidateMagic(const MirServer::Magic& magic);
};

// 数据格式转换器
class DataConverter {
public:
    // 从传奇原始格式转换
    static bool ConvertLegacyItemFormat(const std::string& inputFile, const std::string& outputFile);
    static bool ConvertLegacyMagicFormat(const std::string& inputFile, const std::string& outputFile);
    
    // 从JSON格式转换
    static bool ConvertItemsFromJSON(const std::string& jsonFile, std::vector<MirServer::StdItem>& items);
    static bool ConvertMagicsFromJSON(const std::string& jsonFile, std::vector<MirServer::Magic>& magics);
    
    // 转换为JSON格式
    static bool ConvertItemsToJSON(const std::vector<MirServer::StdItem>& items, const std::string& jsonFile);
    static bool ConvertMagicsToJSON(const std::vector<MirServer::Magic>& magics, const std::string& jsonFile);
};

} // namespace DBServer 