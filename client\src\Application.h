#pragma once

#include <SDL2/SDL.h>
#include <memory>
#include <string>
#include <vector>
#include "Utils/Logger.h"
#include "Utils/ExceptionHandler.h"

// Forward declarations
class GameState;
class Renderer;

/**
 * @class Application
 * @brief Main application class that manages the game loop and states
 *
 * This class is responsible for initializing SDL, creating the window and renderer,
 * managing game states, and running the main game loop.
 */
class Application {
private:
    SDL_Window* m_window;        ///< SDL window handle
    SDL_Renderer* m_renderer;    ///< SDL renderer handle
    bool m_running;              ///< Flag indicating if the game is running

    int m_screenWidth;           ///< Window width
    int m_screenHeight;          ///< Window height
    std::string m_title;         ///< Window title

    // Game states
    std::unique_ptr<GameState> m_currentState;  ///< Current active game state

    // Frame timing
    Uint32 m_lastFrameTime;      ///< Time of the last frame
    float m_deltaTime;           ///< Time elapsed since last frame

    // Private methods
    bool InitSDL();              ///< Initialize SDL libraries
    void CleanupSDL();           ///< Cleanup SDL resources

public:
    /**
     * @brief Constructor
     * @param title Window title
     * @param width Window width
     * @param height Window height
     */
    Application(const std::string& title = "MirClient", int width = 800, int height = 600);

    /**
     * @brief Destructor
     */
    ~Application();

    /**
     * @brief Initialize the application
     * @return true if initialization was successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Run the main game loop
     */
    void Run();

    /**
     * @brief Shutdown the application
     */
    void Shutdown();

    /**
     * @brief Change the current game state
     * @param state New game state
     */
    void ChangeState(std::unique_ptr<GameState> state);

    /**
     * @brief Get the SDL renderer
     * @return SDL renderer handle
     */
    SDL_Renderer* GetRenderer() const { return m_renderer; }

    /**
     * @brief Get the screen width
     * @return Screen width in pixels
     */
    int GetScreenWidth() const { return m_screenWidth; }

    /**
     * @brief Get the screen height
     * @return Screen height in pixels
     */
    int GetScreenHeight() const { return m_screenHeight; }

    /**
     * @brief Get the delta time
     * @return Time elapsed since last frame in seconds
     */
    float GetDeltaTime() const { return m_deltaTime; }

    /**
     * @brief Quit the application
     */
    void Quit() { m_running = false; }
};
