#include "TextInput.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>

TextInput::TextInput(int x, int y, int width, int height, const std::string& text, const std::string& name)
    : UIControl(x, y, width, height, name)
    , m_text(text)
    , m_displayText(text)
    , m_font(nullptr)
    , m_textColor({0, 0, 0, 255})
    , m_backgroundColor({255, 255, 255, 255})
    , m_borderColor({0, 0, 0, 255})
    , m_alignment(TextAlignment::LEFT)
    , m_maxLength(100)
    , m_passwordMode(false)
    , m_focused(false)
    , m_cursorPosition(text.length())
    , m_cursorBlinkTime(0)
    , m_cursorVisible(true)
    , m_wilManager(nullptr)
    , m_resourceFile("")
    , m_backgroundImageIndex(-1)  // No background image by default
    , m_compositionText("")
    , m_compositionCursor(0)
    , m_fontPath("assets/data/font.ttf") // 默认字体路径
    , m_autoFontSize(true)
{
    UpdateAutoFont();
}

TextInput::TextInput(int x, int y, const std::string& text, const std::string& name)
    : UIControl(x, y, name)  // Use the UIControl constructor that sets width and height to 0
    , m_text(text)
    , m_displayText(text)
    , m_font(nullptr)
    , m_textColor({0, 0, 0, 255})
    , m_backgroundColor({255, 255, 255, 255})
    , m_borderColor({0, 0, 0, 255})
    , m_alignment(TextAlignment::LEFT)
    , m_maxLength(100)
    , m_passwordMode(false)
    , m_focused(false)
    , m_cursorPosition(text.length())
    , m_cursorBlinkTime(0)
    , m_cursorVisible(true)
    , m_wilManager(nullptr)
    , m_resourceFile("")
    , m_backgroundImageIndex(-1)  // No background image by default
    , m_compositionText("")
    , m_compositionCursor(0)
    , m_fontPath("assets/data/font.ttf") // 默认字体路径
    , m_autoFontSize(true)
{
    // TextInput controls need a default size even without an image
    SetSize(150, 25);
    UpdateAutoFont();
}

TextInput::~TextInput()
{
}

void TextInput::Update(int deltaTime)
{
    // Update cursor blink
    if (m_focused) {
        m_cursorBlinkTime += deltaTime;
        if (m_cursorBlinkTime >= 500) { // Blink every 500ms
            m_cursorBlinkTime = 0;
            m_cursorVisible = !m_cursorVisible;
        }
    } else {
        // When not focused, cursor should be invisible
        m_cursorVisible = false;
    }

}

void TextInput::Render(SDL_Renderer* renderer)
{
    // Skip if not visible
    if (!m_visible) {
        return;
    }

    // Get absolute coordinates
    int absoluteX = GetAbsoluteX();
    int absoluteY = GetAbsoluteY();

    // Check if we should render a background image
    if (!m_resourceFile.empty() && m_wilManager && m_backgroundImageIndex >= 0 && m_backgroundImageIndex != NO_IMAGE) {
        // Get the surface from the WIL manager
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, m_backgroundImageIndex);

        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);

            if (texture) {
                // Get texture dimensions
                int texWidth, texHeight;
                SDL_QueryTexture(texture, nullptr, nullptr, &texWidth, &texHeight);

                // Create rendering rectangle
                SDL_Rect destRect = {absoluteX, absoluteY, m_width, m_height};

                // If width or height is 0, use the texture dimensions
                if (m_width == 0) {
                    destRect.w = texWidth;
                    // Update the actual width property for hit testing
                    m_width = texWidth;
                }
                if (m_height == 0) {
                    destRect.h = texHeight;
                    // Update the actual height property for hit testing
                    m_height = texHeight;
                }

                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    } else {
        // Render a simple background and border
        // If width or height is 0, use default dimensions
        if (m_width == 0) {
            m_width = 100; // Default width for text input
        }
        if (m_height == 0) {
            m_height = 20; // Default height for text input
        }

        SDL_Rect rect = {absoluteX, absoluteY, m_width, m_height};

        // Render background
        SDL_SetRenderDrawColor(renderer, m_backgroundColor.r, m_backgroundColor.g, m_backgroundColor.b, m_backgroundColor.a);
        SDL_RenderFillRect(renderer, &rect);

        // Render border
        SDL_SetRenderDrawColor(renderer, m_borderColor.r, m_borderColor.g, m_borderColor.b, m_borderColor.a);
        SDL_RenderDrawRect(renderer, &rect);
    }

    // Calculate text Y position for vertical centering
    int textY = absoluteY + (m_height - (m_font ? TTF_FontHeight(m_font) : 20)) / 2;

    // Render text
    if (m_font) {
        SDL_Color textColor = m_textColor;
        std::string textToRender = m_displayText;
        if (textToRender.empty()) textToRender = " ";

        if (!m_compositionText.empty()) {
            // 渲染 m_displayText + m_compositionText
            std::string beforeCursor = m_displayText.substr(0, m_cursorPosition);
            std::string afterCursor = m_displayText.substr(m_cursorPosition);
            std::string fullText = beforeCursor + m_compositionText + afterCursor;
            SDL_Surface* textSurface = TTF_RenderText_Blended(m_font, fullText.c_str(), textColor);
            if (textSurface) {
                SDL_Texture* textTexture = SDL_CreateTextureFromSurface(renderer, textSurface);
                if (textTexture) {
                    int textX = absoluteX + 5;
                    SDL_Rect textRect = {textX, textY, textSurface->w, textSurface->h};
                    SDL_RenderCopy(renderer, textTexture, nullptr, &textRect);
                    SDL_DestroyTexture(textTexture);
                }
                SDL_FreeSurface(textSurface);
            }
        } else {
            // 只渲染 m_displayText
            SDL_Surface* textSurface = TTF_RenderText_Blended(m_font, textToRender.c_str(), textColor);
            if (textSurface) {
                SDL_Texture* textTexture = SDL_CreateTextureFromSurface(renderer, textSurface);
                if (textTexture) {
                    int textX = absoluteX + 5;
                    SDL_Rect textRect = {textX, textY, textSurface->w, textSurface->h};
                    SDL_RenderCopy(renderer, textTexture, nullptr, &textRect);
                    SDL_DestroyTexture(textTexture);
                }
                SDL_FreeSurface(textSurface);
            }
        }
    }

    // Render cursor if focused
    if (m_focused && m_cursorVisible) {
        // Calculate cursor position
        int cursorX = absoluteX + 5; // Start at left padding
        if (!m_compositionText.empty()) {
            // Show cursor in composition text
            std::string textBeforeCursor = m_compositionText.substr(0, m_compositionCursor);
            if (m_font) {
                int w, h;
                TTF_SizeText(m_font, textBeforeCursor.c_str(), &w, &h);
                cursorX += w;
            }
        } else if (!m_displayText.empty() && m_cursorPosition > 0) {
            // Show cursor in normal text
            std::string textBeforeCursor = m_displayText.substr(0, m_cursorPosition);
            if (m_font) {
                int w, h;
                TTF_SizeText(m_font, textBeforeCursor.c_str(), &w, &h);
                cursorX += w;
            } else {
                cursorX += m_cursorPosition * 8; // Fallback to approximate width
            }
        }

        // Draw cursor
        SDL_SetRenderDrawColor(renderer, 255, 255, 255, 255); // White cursor
        SDL_RenderDrawLine(renderer, cursorX, absoluteY + 2, cursorX, absoluteY + m_height - 2);
    }

    // Render children
    UIControl::Render(renderer);
}

bool TextInput::HandleEvent(const SDL_Event& event)
{
    if (!m_visible || !m_enabled) {
        return false;
    }

    // Handle mouse events
    if (event.type == SDL_MOUSEBUTTONDOWN || event.type == SDL_MOUSEBUTTONUP) {
        return HandleMouseButton(event.button.button, event.type == SDL_MOUSEBUTTONDOWN, event.button.x, event.button.y);
    }
    // 键盘、文本输入、IME等
    if (event.type == SDL_KEYDOWN || event.type == SDL_KEYUP) {
        return HandleKey(event.key.keysym.sym, event.type == SDL_KEYDOWN);
    }

    // Handle text input events
    if (event.type == SDL_TEXTINPUT) {
        if (m_focused) {
            // Add text
            if (m_text.length() < m_maxLength) {
                m_text.insert(m_cursorPosition, event.text.text);
                m_cursorPosition += strlen(event.text.text);
                UpdateDisplayText();
                m_cursorBlinkTime = 0;
                m_cursorVisible = true;
                return true;
            }
        }
        return m_focused;
    }

    // Handle IME composition
    if (event.type == SDL_TEXTEDITING) {
        if (m_focused) {
            // Store the composition text temporarily
            m_compositionText = event.edit.text;
            m_compositionCursor = event.edit.start;
            return true;
        }
        return m_focused;
    }

    return false;
}

bool TextInput::HandleMouseButton(Uint8 button, bool pressed, int x, int y) {
    if (!m_visible || !m_enabled) return false;
    if (button == SDL_BUTTON_LEFT && pressed) {
        if (IsPointInside(x, y)) {
            Focus();
            // 计算光标位置
            if (m_font) {
                int relativeX = x - GetAbsoluteX() - 5;
                int bestPos = 0;
                int bestDist = INT_MAX;
                for (size_t i = 0; i <= m_text.length(); i++) {
                    std::string textBeforeCursor = m_text.substr(0, i);
                    int w, h;
                    TTF_SizeText(m_font, textBeforeCursor.c_str(), &w, &h);
                    int dist = abs(w - relativeX);
                    if (dist < bestDist) {
                        bestDist = dist;
                        bestPos = i;
                    }
                }
                m_cursorPosition = bestPos;
            } else {
                m_cursorPosition = m_text.length();
            }
            return true;
        } else {
            Unfocus();
            return false;
        }
    }
    return false;
}

bool TextInput::HandleKey(SDL_Keycode key, bool pressed) {
    if (!m_visible || !m_enabled || !m_focused || !pressed) return false;
    switch (key) {
        case SDLK_BACKSPACE:
            if (!m_compositionText.empty()) {
                if (m_compositionCursor > 0) {
                    m_compositionText.erase(m_compositionCursor - 1, 1);
                    m_compositionCursor--;
                }
            } else if (m_cursorPosition > 0) {
                m_text.erase(m_cursorPosition - 1, 1);
                m_cursorPosition--;
                UpdateDisplayText();
            }
            return true;
        case SDLK_DELETE:
            if (!m_compositionText.empty()) {
                if (m_compositionCursor < m_compositionText.length()) {
                    m_compositionText.erase(m_compositionCursor, 1);
                }
            } else if (m_cursorPosition < m_text.length()) {
                m_text.erase(m_cursorPosition, 1);
                UpdateDisplayText();
            }
            return true;
        case SDLK_LEFT:
            if (!m_compositionText.empty()) {
                if (m_compositionCursor > 0) {
                    m_compositionCursor--;
                }
            } else if (m_cursorPosition > 0) {
                m_cursorPosition--;
                m_cursorBlinkTime = 0;
                m_cursorVisible = true;
            }
            return true;
        case SDLK_RIGHT:
            if (!m_compositionText.empty()) {
                if (m_compositionCursor < m_compositionText.length()) {
                    m_compositionCursor++;
                }
            } else if (m_cursorPosition < m_text.length()) {
                m_cursorPosition++;
                m_cursorBlinkTime = 0;
                m_cursorVisible = true;
            }
            return true;
        case SDLK_HOME:
            if (!m_compositionText.empty()) {
                m_compositionCursor = 0;
            } else {
                m_cursorPosition = 0;
                m_cursorBlinkTime = 0;
                m_cursorVisible = true;
            }
            return true;
        case SDLK_END:
            if (!m_compositionText.empty()) {
                m_compositionCursor = m_compositionText.length();
            } else {
                m_cursorPosition = m_text.length();
                m_cursorBlinkTime = 0;
                m_cursorVisible = true;
            }
            return true;
        case SDLK_RETURN:
        case SDLK_KP_ENTER:
            if (!m_compositionText.empty()) {
                m_text.insert(m_cursorPosition, m_compositionText);
                m_cursorPosition += m_compositionText.length();
                m_compositionText.clear();
                UpdateDisplayText();
            } else {
                Unfocus();
            }
            return true;
        case SDLK_ESCAPE:
            if (!m_compositionText.empty()) {
                m_compositionText.clear();
            } else {
                Unfocus();
            }
            return true;
        case SDLK_TAB:
            // 让父控件处理Tab
            return false;
    }
    return false;
}

void TextInput::SetText(const std::string& text)
{
    m_text = text;
    if (m_passwordMode) {
        m_displayText = std::string(m_text.length(), '*');
    } else {
        m_displayText = m_text;
    }
    m_cursorPosition = m_text.length();
}

void TextInput::SetFocused(bool focused)
{
    if (focused != m_focused) {
        if (focused) {
            Focus();
        } else {
            Unfocus();
        }
    }
}

void TextInput::SetPasswordMode(bool passwordMode)
{
    m_passwordMode = passwordMode;
    if (m_passwordMode) {
        m_displayText = std::string(m_text.length(), '*');
    } else {
        m_displayText = m_text;
    }
}

void TextInput::UpdateDisplayText()
{
    if (m_passwordMode) {
        m_displayText = std::string(m_text.length(), '*');
    } else {
        m_displayText = m_text;
    }
    // Reset cursor blink when text changes
    m_cursorBlinkTime = 0;
    m_cursorVisible = true;
}

void TextInput::SetFont(TTF_Font* font)
{
    if (m_font && m_font != font) {
        TTF_CloseFont(m_font);
    }
    m_font = font;
    m_autoFontSize = false;
}

void TextInput::SetFontPath(const std::string& path)
{
    m_fontPath = path;
    m_autoFontSize = true;
    UpdateAutoFont();
}

void TextInput::SetAutoFontSize(bool autoSize)
{
    m_autoFontSize = autoSize;
    UpdateAutoFont();
}

void TextInput::UpdateAutoFont()
{
    if (!m_autoFontSize || m_fontPath.empty()) return;
    int fontSize = std::max(8, m_height - 8); // 控件高度-8像素，最小8号
    if (m_font) {
        TTF_CloseFont(m_font);
        m_font = nullptr;
    }
    m_font = TTF_OpenFont(m_fontPath.c_str(), fontSize);
    if (!m_font) {
        std::cerr << "TextInput: Failed to load font: " << m_fontPath << " size: " << fontSize << " - " << TTF_GetError() << std::endl;
    }
}

void TextInput::SetSize(int w, int h)
{
    UIControl::SetSize(w, h);
    UpdateAutoFont();
}

