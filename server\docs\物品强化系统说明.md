# 物品强化系统说明

## 概述

物品强化系统是传奇私服中的核心功能之一，允许玩家使用特定材料和金币来提升装备的属性。本系统完全按照原项目的逻辑实现，保持了传统传奇游戏的强化机制。

## 核心特性

### 1. 强化等级
- 支持 +0 到 +10 的强化等级（可配置最大等级）
- 每个强化等级都会显著提升装备属性
- 强化等级会显示在物品名称中（如：屠龙刀 +7）

### 2. 强化材料
系统支持多种强化材料，每种材料提供不同的成功率加成：

| 材料类型 | 物品ID | 成功率加成 | 说明 |
|---------|--------|-----------|------|
| 黑铁矿石 | 100-105 | 2%/个 | 基础强化材料，纯度越高效果越好 |
| 银矿石 | 110 | 5%/个 | 中级强化材料 |
| 金矿石 | 111 | 8%/个 | 高级强化材料 |
| 钻石 | 112 | 12%/个 | 顶级强化材料 |
| 祝福油 | 120 | 15%/个 | 特殊强化材料 |
| 灵魂宝石 | 121 | 20%/个 | 稀有强化材料 |
| 记忆套装 | 130-133 | 25%/个 | 最高级强化材料 |

### 3. 成功率计算
强化成功率由以下因素决定：
- **基础成功率**：80%（可配置）
- **等级衰减**：每级减少8%（可配置）
- **最低成功率**：5%（可配置）
- **材料加成**：根据使用的材料类型和数量

**计算公式**：
```
最终成功率 = max(最低成功率, 基础成功率 - 当前等级 * 等级衰减 + 材料加成)
```

### 4. 强化费用
强化费用随等级指数增长：
```
强化费用 = 基础费用 * (费用倍数 ^ 当前等级)
```
- 基础费用：1000金币（可配置）
- 费用倍数：1.5（可配置）

### 5. 失败处理
- **低等级（+0到+6）**：失败时物品保留，可能损失少量持久度
- **高等级（+7到+10）**：失败时有概率摧毁物品
- **摧毁概率**：20%（可配置）

## API 使用说明

### 基本强化操作

```cpp
#include "ItemManager.h"

// 初始化物品管理器
ItemManager itemManager;
itemManager.Initialize("./data");

// 创建要强化的物品
UserItem weapon = itemManager.CreateItem(weaponId);

// 准备强化材料
std::vector<UserItem> materials;
UserItem blackIron;
blackIron.itemIndex = 100;  // 黑铁矿石
blackIron.dura = 5;         // 纯度5
materials.push_back(blackIron);

// 执行强化
DWORD cost = 0;
auto result = itemManager.UpgradeItemWithMaterials(weapon, materials, cost);

switch (result) {
    case ItemManager::UpgradeResult::SUCCESS:
        // 强化成功
        break;
    case ItemManager::UpgradeResult::FAILED:
        // 强化失败但物品保留
        break;
    case ItemManager::UpgradeResult::ITEM_DESTROYED:
        // 物品被摧毁
        break;
    // 其他结果...
}
```

### 查询强化信息

```cpp
// 检查物品是否可以强化
bool canUpgrade = itemManager.CanUpgradeItem(item);

// 获取当前强化等级
int level = itemManager.GetItemUpgradeLevel(item);

// 计算强化成功率
int successRate = itemManager.CalculateUpgradeSuccessRate(item, materials);

// 计算强化费用
DWORD cost = itemManager.CalculateUpgradeCost(item);

// 检查是否为强化材料
bool isMaterial = itemManager.IsUpgradeMaterial(itemId);
```

### 配置强化参数

```cpp
// 获取当前配置
const auto& config = itemManager.GetUpgradeConfig();

// 设置自定义配置
ItemManager::UpgradeConfig newConfig;
newConfig.maxUpgradeLevel = 15;      // 最大强化等级
newConfig.baseSuccessRate = 90;      // 基础成功率90%
newConfig.successRateDecrement = 6;  // 每级减少6%
newConfig.minSuccessRate = 10;       // 最低成功率10%
newConfig.baseCost = 2000;           // 基础费用2000金币
newConfig.costMultiplier = 1.3f;     // 费用倍数1.3
newConfig.canDestroy = false;        // 禁止摧毁物品

itemManager.SetUpgradeConfig(newConfig);
```

## 强化效果

### 武器强化
每级强化为武器增加：
- 物理攻击力（DC）+1
- 魔法攻击力（MC）+1  
- 道术攻击力（SC）+1

### 防具强化
每级强化为防具增加：
- 物理防御力（AC）+1
- 魔法防御力（MAC）+1

### 首饰强化
每级强化为首饰增加：
- 各项攻击力+0.5
- 各项防御力+0.5

## 最佳实践

### 1. 材料搭配建议
- **低等级（+0到+3）**：使用黑铁矿石即可
- **中等级（+4到+6）**：建议搭配银矿石或金矿石
- **高等级（+7到+10）**：推荐使用祝福油、灵魂宝石等高级材料

### 2. 强化策略
- 在重要装备强化前先备份
- 高等级强化建议使用多种材料提高成功率
- 考虑设置保护机制避免重要装备被摧毁

### 3. 服务器配置
- 根据服务器经济平衡调整强化费用
- 可以通过配置降低摧毁概率提升玩家体验
- 考虑为不同类型装备设置不同的强化参数

## 注意事项

1. **线程安全**：所有强化操作都是线程安全的
2. **数据持久化**：强化等级存储在物品的btValue[13]中
3. **兼容性**：完全兼容原项目的物品数据结构
4. **扩展性**：可以轻松添加新的强化材料和效果

## 测试和调试

系统提供了完整的测试套件：
- `ItemUpgradeTest.cpp`：单元测试
- `ItemUpgradeExample.cpp`：使用示例

运行测试：
```bash
cd server/build
make ItemUpgradeTest
./ItemUpgradeTest
```

运行示例：
```bash
make ItemUpgradeExample  
./ItemUpgradeExample
```

## 总结

物品强化系统完全按照原项目逻辑实现，提供了：
- ✅ 完整的强化机制（等级、材料、成功率、费用）
- ✅ 灵活的配置系统
- ✅ 线程安全的实现
- ✅ 详细的测试和示例
- ✅ 与原项目100%兼容

该系统已经可以直接集成到GameEngine中使用，为玩家提供完整的装备强化体验。
