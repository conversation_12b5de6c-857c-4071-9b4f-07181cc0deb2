{"version": "2.0.0", "tasks": [{"label": "Build with <PERSON><PERSON>", "type": "shell", "command": "${workspaceFolder}/build_debug.bat", "group": "build", "problemMatcher": "$gcc"}, {"type": "cppbuild", "label": "C/C++: g++.exe build active file", "command": "C:/msys64/mingw64/bin/g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "C:/msys64/mingw64/bin"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Task generated by <PERSON>bugger."}]}