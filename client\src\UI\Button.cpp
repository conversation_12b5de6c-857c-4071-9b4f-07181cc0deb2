#include "Button.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>

Button::Button(int x, int y, int width, int height, const std::string& text, const std::string& name)
    : UIControl(x, y, width, height, name)
    , m_text(text)
    , m_font(nullptr)
{
    // Set default text color
    m_textColor = {255, 255, 255, 255};  // White

    // Set default resource indices
    SetNormalImageIndex(0);    // First image is normal state
    SetHoverImageIndex(1);     // Second image is hover state
    SetPressedImageIndex(2);   // Third image is pressed state
    SetDisabledImageIndex(3);  // Fourth image is disabled state
}

Button::Button(int x, int y, const std::string& text, const std::string& name)
    : UIControl(x, y, name)  // Use the UIControl constructor that sets width and height to 0
    , m_text(text)
    , m_font(nullptr)
{
    // Set default text color
    m_textColor = {255, 255, 255, 255};  // White

    // Set default resource indices
    SetNormalImageIndex(0);    // First image is normal state
    SetHoverImageIndex(1);     // Second image is hover state
    SetPressedImageIndex(2);   // Third image is pressed state
    SetDisabledImageIndex(3);  // Fourth image is disabled state
}

Button::~Button()
{
}

void Button::Render(SDL_Renderer* renderer)
{
    // Skip if not visible
    if (!m_visible) {
        return;
    }

    // First try to use the resource indices from UIControl
    if (!m_resourceFile.empty() && m_wilManager) {
        // Let the base class handle rendering using resource indices
        UIControl::Render(renderer);
    } else {
        // Fall back to using textures
        std::shared_ptr<Texture> texture = nullptr;

        if (!m_enabled) {
            texture = m_disabledTexture;
        } else if (m_mouseDown) {
            texture = m_pressedTexture;
        } else if (m_mouseOver) {
            texture = m_hoverTexture;
        } else {
            texture = m_normalTexture;
        }

        // Render the button texture
        if (texture) {
            texture->Render(m_x, m_y);
        } else {
            // When no texture is set, render as transparent (no rendering)
            // This allows parent elements to show through
        }
    }

    // Render the text
    if (m_textTexture) {
        int textX = m_x + (m_width - m_textTexture->GetWidth()) / 2;
        int textY = m_y + (m_height - m_textTexture->GetHeight()) / 2;
        m_textTexture->Render(textX, textY);
    }

    // Render children (only if we didn't call UIControl::Render already)
    if (m_resourceFile.empty() || !m_wilManager) {
        for (auto& child : m_children) {
            if (child->IsVisible()) {
                child->Render(renderer);
            }
        }
    }
}

void Button::SetNormalTexture(std::shared_ptr<Texture> texture)
{
    m_normalTexture = texture;
}

void Button::SetHoverTexture(std::shared_ptr<Texture> texture)
{
    m_hoverTexture = texture;
}

void Button::SetPressedTexture(std::shared_ptr<Texture> texture)
{
    m_pressedTexture = texture;
}

void Button::SetDisabledTexture(std::shared_ptr<Texture> texture)
{
    m_disabledTexture = texture;
}

void Button::SetText(const std::string& text)
{
    m_text = text;
    CreateTextTexture();
}

void Button::SetTextColor(const SDL_Color& color)
{
    m_textColor = color;
    CreateTextTexture();
}

void Button::SetFont(TTF_Font* font)
{
    m_font = font;
    CreateTextTexture();
}

void Button::SetResourceIndices(const std::string& resourceFile, int normalIndex, int hoverIndex, int pressedIndex, int disabledIndex)
{
    // Set resource file
    SetResourceFile(resourceFile);

    // Set image indices
    SetNormalImageIndex(normalIndex);
    SetHoverImageIndex(hoverIndex);
    SetPressedImageIndex(pressedIndex);
    SetDisabledImageIndex(disabledIndex);
}

void Button::SetResourceType(ResourceManager::ResourceType resourceType, int normalIndex, int hoverIndex, int pressedIndex, int disabledIndex)
{
    // Get resource manager
    ResourceManager* resourceManager = ResourceManager::GetInstance();
    if (!resourceManager) {
        std::cerr << "ResourceManager is not initialized" << std::endl;
        return;
    }

    // Get resource path
    std::string resourcePath = resourceManager->GetResourcePath(resourceType);
    if (resourcePath.empty()) {
        std::cerr << "Resource path not found for type: " << ResourceManager::ResourceTypeToString(resourceType) << std::endl;
        return;
    }

    // Set resource indices
    SetResourceIndices(resourcePath, normalIndex, hoverIndex, pressedIndex, disabledIndex);
}

void Button::CreateTextTexture()
{
    // Skip if no text or font
    if (m_text.empty() || !m_font) {
        m_textTexture.reset();
        return;
    }

    // Create text texture
    m_textTexture = std::make_shared<Texture>(nullptr);  // TODO: Pass renderer
    if (!m_textTexture->LoadFromText(m_text, m_font, m_textColor)) {
        std::cerr << "Failed to create text texture: " << TTF_GetError() << std::endl;
        m_textTexture.reset();
    }
}

void Button::SetOnClick(std::function<void()> callback)
{
    // Create a wrapper that calls the callback
    UIControl::SetOnClick([callback](UIControl* control) {
        if (callback) {
            callback();
        }
    });
}

