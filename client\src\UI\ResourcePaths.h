#pragma once
#include <string>

/**
 * @file ResourcePaths.h
 * @brief 定义资源文件路径常量
 *
 * 这个文件定义了所有资源文件的路径常量，确保与原始Delphi项目保持一致。
 */

namespace ResourcePaths {
    // 主要资源文件 - 与原始Delphi项目保持一致
    const std::string MAIN = "Data/Prguse.wil";
    const std::string MAIN2 = "Data/Prguse2.wil";
    const std::string MAIN3 = "Data/Prguse3.wil";
    const std::string CHR_SELECT = "Data/ChrSel.wil";
    const std::string MINIMAP = "Data/mmap.wil";

    // 地图资源
    const std::string TILES = "Data/Tiles.wil";
    const std::string SMALL_TILES = "Data/SmTiles.wil";
    const std::string OBJECTS = "Data/Objects.wil";
    const std::string OBJECTS_TEMPLATE = "Data/Objects%d.wil"; // 用于格式化对象文件名

    // 角色资源
    const std::string HUM_EFFECT = "Data/HumEffect.wil";
    const std::string HUMAN = "Data/Hum.wil";
    const std::string HAIR = "Data/Hair.wil";
    const std::string WEAPON = "Data/Weapon.wil";

    // 物品资源
    const std::string ITEMS = "Data/Items.wil";
    const std::string STATE_ITEMS = "Data/StateItem.wil";
    const std::string DROP_ITEMS = "Data/DnItems.wil";

    // 魔法资源
    const std::string MAGIC_ICON = "Data/MagIcon.wil";
    const std::string MAGIC = "Data/Magic.wil";
    const std::string MAGIC2 = "Data/Magic2.wil";
    const std::string MAGIC3 = "Data/Magic3.wil";
    const std::string MAGIC4 = "Data/Magic4.wil";

    // 怪物资源
    const std::string MONSTER_TEMPLATE = "Data/Mon%d.wil"; // 用于格式化怪物文件名

    // 其他资源
    const std::string NPC = "Data/Npc.wil";
    const std::string EVENT_EFFECT = "Data/Event.wil";
    const std::string INTERFACE = "Data/Interface.wil";
    const std::string DRAGON = "Data/Dragon.wil";
    const std::string EFFECT = "Data/Effect.wil";

    // 地图目录
    const std::string MAP_DIR = "Map/";

    // 调色板文件
    const std::string DEFAULT_PALETTE = "Data/Prguse.pal";

    // 光照效果文件
    const std::string LIGHT_FILES[6] = {
        "Data/lig0a.dat",
        "Data/lig0b.dat",
        "Data/lig0c.dat",
        "Data/lig0d.dat",
        "Data/lig0e.dat",
        "Data/lig0f.dat"
    };
}
