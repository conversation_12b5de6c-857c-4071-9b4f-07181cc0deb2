# RunGateServer Configuration File
# 基于原ERunGate的配置格式

[Server]
# 服务器配置
ServerAddr=127.0.0.1
ServerPort=5000
GateAddr=0.0.0.0
GatePort=7200

# 连接限制
MaxConnOfIPaddr=50
MaxClientPacketSize=8000
NomClientPacketSize=200
MaxClientMsgCount=20
ClientSendBlockSize=16
ClientCheckTimeOut=50
MaxOverNomSizeCount=2
CheckServerTimeOutTime=180000

# 阻止方法 (0=断开连接, 1=阻止IP, 2=加入黑名单)
BlockMethod=1

# 攻击检测
AttackTick=300
AttackCount=5

# 聊天限制
SayMsgMaxLen=100
SayMsgTime=1000
ReplaceWord=*

[Setup]
# 速度检测设置
HitSpeed=1
SpellSpeed=1
RunSpeed=1
WalkSpeed=1
TurnSpeed=1

# 速度时间间隔(毫秒)
HitTime=500
SpellTime=500
RunTime=300
WalkTime=300
TurnTime=200

# 速度检测计数
HitCount=3
SpellCount=3
RunCount=3
WalkCount=3
TurnCount=3

# 速度控制模式 (0=延迟模式, 1=过滤模式, 2=断开连接)
SpeedControlMode=1

# 速度提示
HintSpeed=1
HintSpeedMsg=系统提示: 请按照游戏节奏,不要使用非法外挂!

# 消息颜色 (0=红色, 1=蓝色, 2=绿色)
MsgColor=0

[Network]
# 网络相关设置
EnableNagle=0
EnableKeepAlive=1
KeepAliveInterval=30000
SendBufferSize=65536
ReceiveBufferSize=65536
ClientTimeout=60000

[Log]
# 日志设置
LogLevel=3
ShowByte=0
MaxLogLines=1000 