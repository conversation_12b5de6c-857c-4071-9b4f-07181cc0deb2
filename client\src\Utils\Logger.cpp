#include "Logger.h"
#include <iostream>
#include <filesystem>
#include <thread>
#include <atomic>
#include <ctime>

// Platform-specific includes
#ifdef _WIN32
#include <windows.h>
#elif defined(__APPLE__)
#include <sys/utsname.h>
#include <unistd.h>
#include <mach-o/dyld.h>  // For _NSGetExecutablePath
#include <sys/types.h>
#include <pwd.h>          // For getpwuid
#else
#include <sys/utsname.h>
#include <unistd.h>
#include <sys/types.h>
#include <pwd.h>          // For getpwuid
#include <limits.h>       // For PATH_MAX
#endif

// Global debug log function implementation
void WriteDebugLog(const std::string& message) {
    static std::mutex debugLogMutex;
    std::lock_guard<std::mutex> lock(debugLogMutex);

    try {
        // Create logs directory if it doesn't exist
        std::filesystem::path logsDir = "logs";
        if (!std::filesystem::exists(logsDir)) {
            std::filesystem::create_directories(logsDir);
        }

        // Use a dedicated debug log file in the logs directory
        std::ofstream logFile("logs/debug_log.txt", std::ios::app);
        if (logFile.is_open()) {
            // Add timestamp with milliseconds
            auto now = std::chrono::system_clock::now();
            auto time = std::chrono::system_clock::to_time_t(now);
            auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                now.time_since_epoch()) % 1000;

            // Use platform-specific time functions or C++11 alternatives
            char buffer[24];
#ifdef _WIN32
            struct tm timeinfo;
            localtime_s(&timeinfo, &time);
            strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", &timeinfo);
#else
            struct tm* timeinfo;
            timeinfo = localtime(&time);
            strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", timeinfo);
#endif

            // Format: YYYY-MM-DD HH:MM:SS.mmm - [DEBUG] Message
            logFile << buffer << "." << std::setfill('0') << std::setw(3) << ms.count()
                   << " - [DEBUG] " << message << std::endl;

            // Flush to ensure writing
            logFile.flush();
            logFile.close();
        } else {
            // If we can't open the file, try writing to a temp location
#ifdef _WIN32
            char tempPath[MAX_PATH];
            GetTempPathA(MAX_PATH, tempPath);
            std::string tempFile = std::string(tempPath) + "mir_debug_log.txt";
#else
            std::string tempFile = "/tmp/mir_debug_log.txt";
#endif

            std::ofstream tempLogFile(tempFile, std::ios::app);
            if (tempLogFile.is_open()) {
                tempLogFile << "Failed to open main debug log. Message: " << message << std::endl;
                tempLogFile.close();
            }

#ifdef _DEBUG
            std::cerr << "Failed to open debug log file: " << message << std::endl;
#endif
        }
    } catch (const std::exception& e) {
#ifdef _DEBUG
        std::cerr << "Error in WriteDebugLog: " << e.what() << " - Original message: " << message << std::endl;
#endif
        // Try one more time with a simpler approach
        try {
            std::ofstream emergencyLog("emergency_log.txt", std::ios::app);
            if (emergencyLog.is_open()) {
                emergencyLog << "Error in WriteDebugLog: " << e.what() << " - Message: " << message << std::endl;
                emergencyLog.close();
            }
        } catch (...) {
            // Nothing more we can do
        }
    }
}

// System information function
std::string GetSystemInfo() {
    std::stringstream ss;

#ifdef _WIN32
    // Windows system info
    OSVERSIONINFOEX osvi;
    ZeroMemory(&osvi, sizeof(OSVERSIONINFOEX));
    osvi.dwOSVersionInfoSize = sizeof(OSVERSIONINFOEX);

    #pragma warning(disable:4996)
    GetVersionEx((OSVERSIONINFO*)&osvi);
    #pragma warning(default:4996)

    ss << "Windows " << osvi.dwMajorVersion << "." << osvi.dwMinorVersion
       << " (Build " << osvi.dwBuildNumber << ")";

    // Get system memory info
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    GlobalMemoryStatusEx(&memInfo);
    DWORDLONG totalPhysMem = memInfo.ullTotalPhys;

    ss << ", RAM: " << (totalPhysMem / (1024 * 1024)) << " MB";
#elif defined(__APPLE__) || defined(__linux__)
    // Unix/Linux/macOS system info
    struct utsname systemInfo;
    uname(&systemInfo);

    ss << systemInfo.sysname << " " << systemInfo.release
       << " (" << systemInfo.version << ", " << systemInfo.machine << ")";

    // Get memory info - simplified for cross-platform compatibility
    // This is a simplified approach - more accurate methods exist for specific platforms
    long pages = sysconf(_SC_PHYS_PAGES);
    long page_size = sysconf(_SC_PAGE_SIZE);
    if (pages > 0 && page_size > 0) {
        long long totalPhysMem = (long long)pages * page_size;
        ss << ", RAM: " << (totalPhysMem / (1024 * 1024)) << " MB";
    }
#else
    ss << "Unknown OS";
#endif

    return ss.str();
}

// Global Logger instance reference
static bool g_loggerInitialized = false;

// Safe logging functions implementation
void SafeLogDebug(const std::string& message) {
    // Only write to debug log when Logger is not initialized
    if (!g_loggerInitialized && !Logger::GetInstance().IsInitialized()) {
        WriteDebugLog(message);
    }

    if (g_loggerInitialized || Logger::GetInstance().IsInitialized()) {
        g_loggerInitialized = true;
        LOG_DEBUG(message);
    }
}

void SafeLogInfo(const std::string& message) {
    // Only write to debug log when Logger is not initialized
    if (!g_loggerInitialized && !Logger::GetInstance().IsInitialized()) {
        WriteDebugLog(message);
    }

    if (g_loggerInitialized || Logger::GetInstance().IsInitialized()) {
        g_loggerInitialized = true;
        LOG_INFO(message);
    }
}

void SafeLogWarning(const std::string& message) {
    // Only write to debug log when Logger is not initialized
    if (!g_loggerInitialized && !Logger::GetInstance().IsInitialized()) {
        WriteDebugLog(message);
    }

    if (g_loggerInitialized || Logger::GetInstance().IsInitialized()) {
        g_loggerInitialized = true;
        LOG_WARNING(message);
    }
}

void SafeLogError(const std::string& message) {
    // Always write errors to debug log to ensure capturing all errors
    WriteDebugLog("ERROR: " + message);

    if (g_loggerInitialized || Logger::GetInstance().IsInitialized()) {
        g_loggerInitialized = true;
        LOG_ERROR(message);
    }
}

void SafeLogFatal(const std::string& message) {
    // Always write fatal errors to debug log
    WriteDebugLog("FATAL: " + message);

    if (g_loggerInitialized || Logger::GetInstance().IsInitialized()) {
        g_loggerInitialized = true;
        LOG_FATAL(message);
    }
}

Logger::Logger()
    : m_logLevel(LogLevel::LOG_LEVEL_INFO)
    , m_initialized(false)
    , m_rotationMode(LogRotationMode::NONE)
    , m_maxSizeKB(10 * 1024)  // 10 MB by default
    , m_maxFilesToKeep(5)
    , m_logFilePath("")
    , m_lastRotationDate("")
{
}

Logger::~Logger()
{
    Close();
}

Logger& Logger::GetInstance()
{
    static Logger instance;
    return instance;
}

bool Logger::IsInitialized() const
{
    return m_initialized;
}

void Logger::WriteDebugLog(const std::string& message)
{
    ::WriteDebugLog(message);
}

bool Logger::SafeInitialize(const std::string& logFilePath, LogLevel level, int timeoutMs, std::function<void(int)> progressCallback)
{
    // 简单转发到带轮转参数的方法，使用默认的无轮转模式
    return SafeInitialize(logFilePath, level, LogRotationMode::NONE, 0, 0, timeoutMs, progressCallback);
}

bool Logger::SafeInitialize(const std::string& logFilePath, LogLevel level, LogRotationMode rotationMode,
                           size_t maxSizeKB, int maxFilesToKeep, int timeoutMs,
                           std::function<void(int)> progressCallback)
{
    // 先检查是否已经初始化
    if (m_initialized) {
        WriteDebugLog("Logger已经初始化");
        return true;
    }

    // 尝试直接初始化
    WriteDebugLog("尝试直接初始化Logger(带轮转): " + logFilePath);
    if (Initialize(logFilePath, level, rotationMode, maxSizeKB, maxFilesToKeep)) {
        WriteDebugLog("带轮转的Logger直接初始化成功");
        g_loggerInitialized = true;
        return true;
    }

    // 如果直接初始化失败，且指定了超时时间，则尝试使用线程初始化
    if (timeoutMs > 0) {
        WriteDebugLog("直接初始化失败，尝试使用线程与超时初始化: " + std::to_string(timeoutMs) + "ms");

        // 使用原子变量跟踪线程是否已经完成
        std::atomic<bool> threadDone(false);
        std::atomic<bool> initSuccess(false);

        // 创建一个智能指针来管理线程，避免资源泄漏
        auto initThread = std::make_shared<std::thread>(
            [this, logFilePath, level, rotationMode, maxSizeKB, maxFilesToKeep, &threadDone, &initSuccess]() {
                try {
                    initSuccess = this->Initialize(logFilePath, level, rotationMode, maxSizeKB, maxFilesToKeep);
                    ::WriteDebugLog(initSuccess ? "线程初始化带轮转Logger成功" : "线程初始化带轮转Logger失败");
                } catch (const std::exception& e) {
                    ::WriteDebugLog("线程初始化带轮转Logger异常: " + std::string(e.what()));
                    initSuccess = false;
                } catch (...) {
                    ::WriteDebugLog("线程初始化带轮转Logger未知异常");
                    initSuccess = false;
                }

                // 标记线程已完成
                threadDone = true;
            }
        );

        // 等待指定的超时时间
        int elapsed = 0;
        int checkInterval = 100; // 每100ms检查一次

        while (elapsed < timeoutMs && !threadDone) {
            Sleep(checkInterval);
            elapsed += checkInterval;

            // 进度回调
            if (progressCallback) {
                progressCallback(elapsed);
            } else {
                // 默认的进度报告，每秒输出一次
                if (elapsed % 1000 == 0 || elapsed == checkInterval) {
                    WriteDebugLog("等待带轮转Logger初始化: " + std::to_string(elapsed) + "ms / " + std::to_string(timeoutMs) + "ms");
                }
            }
        }

        // 检查是否超时
        if (!threadDone) {
            WriteDebugLog("带轮转Logger初始化超时，继续执行程序");

            // 创建一个清理线程来处理超时的初始化线程
            std::thread cleanupThread([initThread]() {
                // 为初始化线程提供有限的额外时间完成
                Sleep(2000);  // 再给2秒

                // 如果线程还在运行，我们没有太多选择只能分离它
                // 而不是使用terminate，这样更安全
                if (initThread->joinable()) {
                    ::WriteDebugLog("初始化线程仍在运行，分离线程");
                    initThread->detach();
                } else {
                    ::WriteDebugLog("初始化线程已完成，无需额外清理");
                    initThread->join();
                }
            });

            // 分离清理线程，让它在后台运行
            cleanupThread.detach();
            return false;
        } else {
            // 线程已完成，可以安全join
            initThread->join();
            g_loggerInitialized = initSuccess;
            return initSuccess;
        }
    }

    return false;
}

bool Logger::Initialize(const std::string& logFilePath, LogLevel level, LogRotationMode rotationMode, size_t maxSizeKB, int maxFilesToKeep)
{
    // 添加Debug输出
    WriteDebugLog("Logger::Initialize - 带轮转参数的初始化: " + logFilePath +
                 ", mode=" + std::to_string(static_cast<int>(rotationMode)) +
                 ", maxSize=" + std::to_string(maxSizeKB) + "KB" +
                 ", maxFiles=" + std::to_string(maxFilesToKeep));

    // 保存轮转设置 - 不需要锁，因为下面的Initialize会获取锁
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_rotationMode = rotationMode;
        m_maxSizeKB = maxSizeKB;
        m_maxFilesToKeep = maxFilesToKeep;
        m_logFilePath = logFilePath;

        if (m_rotationMode == LogRotationMode::DAILY) {
            m_lastRotationDate = GetCurrentDate();
        }
    }

    // 继续常规初始化 - 在释放锁后调用，避免死锁
    return Initialize(logFilePath, level);
}

bool Logger::Initialize(const std::string& logFilePath, LogLevel level)
{
    // Add debug output before locking mutex
    WriteDebugLog("Logger::Initialize - Starting initialization for: " + logFilePath);

    std::lock_guard<std::mutex> lock(m_mutex);

    // Close existing log file if open
    if (m_logFile.is_open()) {
        m_logFile.close();
    }

    try {
        // Get the executable path
        std::filesystem::path executableDir;

#ifdef _WIN32
        // Windows-specific executable path retrieval
        char exePath[MAX_PATH];
        GetModuleFileNameA(NULL, exePath, MAX_PATH);
        WriteDebugLog("Logger::Initialize - Executable path: " + std::string(exePath));
        std::filesystem::path executablePath(exePath);
        executableDir = executablePath.parent_path();
#elif defined(__APPLE__)
        // macOS-specific executable path retrieval
        char exePath[PATH_MAX];
        uint32_t size = sizeof(exePath);
        if (_NSGetExecutablePath(exePath, &size) == 0) {
            WriteDebugLog("Logger::Initialize - Executable path: " + std::string(exePath));
            std::filesystem::path executablePath(exePath);
            executableDir = executablePath.parent_path();
        } else {
            // Fallback to current working directory
            executableDir = std::filesystem::current_path();
            WriteDebugLog("Logger::Initialize - Using current directory: " + executableDir.string());
        }
#else
        // Linux-specific executable path retrieval
        char exePath[PATH_MAX];
        ssize_t count = readlink("/proc/self/exe", exePath, PATH_MAX);
        if (count != -1) {
            exePath[count] = '\0';
            WriteDebugLog("Logger::Initialize - Executable path: " + std::string(exePath));
            std::filesystem::path executablePath(exePath);
            executableDir = executablePath.parent_path();
        } else {
            // Fallback to current working directory
            executableDir = std::filesystem::current_path();
            WriteDebugLog("Logger::Initialize - Using current directory: " + executableDir.string());
        }
#endif

        // Create absolute path for log file
        std::filesystem::path absoluteLogPath = executableDir / logFilePath;
        std::filesystem::path dir = absoluteLogPath.parent_path();

        // Add debug information for paths
        WriteDebugLog("Logger::Initialize - Log directory: " + dir.string());

#ifdef _DEBUG
        std::cout << "Executable directory: " << executableDir.string() << std::endl;
        std::cout << "Absolute log path: " << absoluteLogPath.string() << std::endl;
#endif

        // First check if directory exists, create if it doesn't
        if (!dir.empty()) {
            try {
                WriteDebugLog("Logger::Initialize - Checking if directory exists: " + dir.string());

                if (!std::filesystem::exists(dir)) {
                    WriteDebugLog("Logger::Initialize - Directory does not exist, creating: " + dir.string());

#ifdef _DEBUG
                    std::cout << "Creating directory: " << dir.string() << std::endl;
#endif
                    std::filesystem::create_directories(dir);

                    // Verify directory was created
                    if (std::filesystem::exists(dir)) {
                        WriteDebugLog("Logger::Initialize - Directory created successfully");
                    } else {
#ifdef _DEBUG
                        std::cerr << "Failed to create log directory: " << dir.string() << std::endl;
#endif
                        WriteDebugLog("Logger::Initialize - Failed to create directory: " + dir.string());
                        return false;
                    }
                } else {
                    WriteDebugLog("Logger::Initialize - Directory already exists");
                }

                // Ensure directory has write permissions
                std::ofstream testFile((dir / "test_write.tmp").string());
                if (testFile.is_open()) {
                    testFile << "Test write access" << std::endl;
                    testFile.close();
                    // Delete test file
                    std::filesystem::remove(dir / "test_write.tmp");

                    WriteDebugLog("Logger::Initialize - Directory has write permissions");
                } else {
                    WriteDebugLog("Logger::Initialize - Directory does not have write permissions");
#ifdef _DEBUG
                    std::cerr << "Directory does not have write permissions: " << dir.string() << std::endl;
#endif
                    return false;
                }
            } catch (const std::exception& e) {
#ifdef _DEBUG
                std::cerr << "Failed to create or check log directory: " << e.what() << std::endl;
#endif
                WriteDebugLog("Logger::Initialize - Exception creating/checking directory: " + std::string(e.what()));
                return false;
            }
        }

        // Open log file with error handling
        WriteDebugLog("Logger::Initialize - Opening log file: " + absoluteLogPath.string());

#ifdef _DEBUG
        std::cout << "Opening log file: " << absoluteLogPath.string() << std::endl;
#endif
        // Try to open with limited retries
        int retries = 0;
        bool fileOpened = false;
        while (retries < 3 && !fileOpened) {
            m_logFile.open(absoluteLogPath.string(), std::ios::out | std::ios::app);
            fileOpened = m_logFile.is_open();
            if (!fileOpened) {
                retries++;
                // Small sleep to avoid hammering the filesystem
#ifdef _WIN32
                Sleep(100);
#else
                usleep(100000); // 100ms in microseconds
#endif
            }
        }

        if (!m_logFile.is_open()) {
#ifdef _DEBUG
            std::cerr << "Failed to open log file: " << absoluteLogPath.string() << std::endl;
#endif
            WriteDebugLog("Logger::Initialize - Failed to open log file after " + std::to_string(retries) + " attempts");
            return false;
        }
    } catch (const std::exception& e) {
#ifdef _DEBUG
        std::cerr << "Exception in Initialize: " << e.what() << std::endl;
#endif
        WriteDebugLog("Logger::Initialize - Exception: " + std::string(e.what()));
        return false;
    }

    m_logLevel = level;
    m_initialized = true;

    WriteDebugLog("Logger::Initialize - Logger successfully initialized");

    // Log initialization - we'll use a direct write instead of Log() to avoid potential recursion
    m_logFile << GetTimestamp() << " [INFO] Logger initialized" << std::endl;

    // Add additional information about system
    m_logFile << GetTimestamp() << " [INFO] Application version: 1.0.0" << std::endl;

    // Get OS information in a cross-platform way
#ifdef _WIN32
    m_logFile << GetTimestamp() << " [INFO] OS: Windows" << std::endl;

    // Get computer name (Windows)
    char computerName[MAX_COMPUTERNAME_LENGTH + 1];
    DWORD size = sizeof(computerName) / sizeof(computerName[0]);
    if (GetComputerNameA(computerName, &size)) {
        m_logFile << GetTimestamp() << " [INFO] Computer name: " << computerName << std::endl;
    }

    // Get user name (Windows)
    char userName[256];
    DWORD userNameSize = sizeof(userName) / sizeof(userName[0]);
    if (GetUserNameA(userName, &userNameSize)) {
        m_logFile << GetTimestamp() << " [INFO] User name: " << userName << std::endl;
    }
#elif defined(__APPLE__)
    m_logFile << GetTimestamp() << " [INFO] OS: macOS" << std::endl;

    // Get hostname (macOS/Unix)
    char hostname[256];
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        m_logFile << GetTimestamp() << " [INFO] Computer name: " << hostname << std::endl;
    }

    // Get username (macOS/Unix)
    char username[256];
    if (getlogin_r(username, sizeof(username)) == 0) {
        m_logFile << GetTimestamp() << " [INFO] User name: " << username << std::endl;
    }
#else
    m_logFile << GetTimestamp() << " [INFO] OS: Linux/Unix" << std::endl;

    // Get hostname (Linux/Unix)
    char hostname[256];
    if (gethostname(hostname, sizeof(hostname)) == 0) {
        m_logFile << GetTimestamp() << " [INFO] Computer name: " << hostname << std::endl;
    }

    // Get username (Linux/Unix)
    const char* username = getenv("USER");
    if (username) {
        m_logFile << GetTimestamp() << " [INFO] User name: " << username << std::endl;
    }
#endif

    // Record system info
    m_logFile << GetTimestamp() << " [INFO] System info: " << GetSystemInfo() << std::endl;

    // Flush to ensure writing
    m_logFile.flush();

    // Mark global variable
    g_loggerInitialized = true;

    return true;
}

void Logger::SetLogLevel(LogLevel level)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    m_logLevel = level;
}

void Logger::Debug(const std::string& message)
{
    Log(LogLevel::LOG_LEVEL_DEBUG, message);
}

void Logger::Info(const std::string& message)
{
    Log(LogLevel::LOG_LEVEL_INFO, message);
}

void Logger::Warning(const std::string& message)
{
    Log(LogLevel::LOG_LEVEL_WARNING, message);
}

void Logger::Error(const std::string& message)
{
    Log(LogLevel::LOG_LEVEL_ERROR, message);
}

void Logger::Fatal(const std::string& message)
{
    Log(LogLevel::LOG_LEVEL_FATAL, message);
}

void Logger::LogException(const std::string& exception, const std::string& file, int line)
{
    std::stringstream ss;
    ss << "Exception in " << file << " at line " << line << ": " << exception;
    Log(LogLevel::LOG_LEVEL_ERROR, ss.str());
}

void Logger::Close()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_logFile.is_open()) {
        m_logFile.close();
    }
    m_initialized = false;
}

void Logger::Log(LogLevel level, const std::string& message)
{
    // Check if the log level is high enough
    if (level < m_logLevel) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // Check if logger is initialized
    if (!m_initialized || !m_logFile.is_open()) {
#ifdef _DEBUG
        std::cerr << "Logger not initialized or log file not open" << std::endl;
#endif

        // Try to initialize the logger with default settings
        if (!m_initialized) {
            try {
                // Get the executable path
                char exePath[MAX_PATH];
                GetModuleFileNameA(NULL, exePath, MAX_PATH);
                std::filesystem::path executablePath(exePath);
                std::filesystem::path executableDir = executablePath.parent_path();
                std::filesystem::path logDir = executableDir / "logs";

                // Create logs directory
                std::filesystem::create_directories(logDir);

                // Open fallback log file
                std::string fallbackLogPath = (logDir / "fallback.log").string();
                m_logFile.open(fallbackLogPath, std::ios::out | std::ios::app);

                if (m_logFile.is_open()) {
                    m_initialized = true;
#ifdef _DEBUG
                    std::cerr << "Fallback logger initialized" << std::endl;
#endif
                }
            } catch (const std::exception& e) {
#ifdef _DEBUG
                std::cerr << "Failed to initialize fallback logger: " << e.what() << std::endl;
#endif
                return;
            }
        }

        if (!m_initialized) {
            return;
        }
    }

    // 检查是否需要轮转日志文件
    CheckRotation();

    // Format the log message
    std::string timestamp = GetTimestamp();
    std::string levelStr = LogLevelToString(level);

    try {
        // Write to log file
        m_logFile << timestamp << " [" << levelStr << "] " << message << std::endl;

        // Only flush for ERROR and FATAL messages to improve performance
        if (level >= LogLevel::LOG_LEVEL_ERROR) {
            m_logFile.flush();
        }

#ifdef _DEBUG
        // Also output to console for debugging in debug builds
        std::cout << timestamp << " [" << levelStr << "] " << message << std::endl;
#endif
    } catch (const std::exception& e) {
#ifdef _DEBUG
        std::cerr << "Error writing to log file: " << e.what() << std::endl;
#endif
    }
}

std::string Logger::GetTimestamp() const
{
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

#ifdef _WIN32
    // Windows-specific thread-safe localtime
    struct tm timeinfo;
    localtime_s(&timeinfo, &time);
#else
    // POSIX-compliant systems use localtime_r
    struct tm timeinfo;
    localtime_r(&time, &timeinfo);
#endif

    char buffer[24];
    strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", &timeinfo);

    std::stringstream ss;
    ss << buffer;
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();

    return ss.str();
}

std::string Logger::LogLevelToString(LogLevel level) const
{
    switch (level) {
        case LogLevel::LOG_LEVEL_DEBUG:
            return "DEBUG";
        case LogLevel::LOG_LEVEL_INFO:
            return "INFO";
        case LogLevel::LOG_LEVEL_WARNING:
            return "WARNING";
        case LogLevel::LOG_LEVEL_ERROR:
            return "ERROR";
        case LogLevel::LOG_LEVEL_FATAL:
            return "FATAL";
        default:
            return "UNKNOWN";
    }
}

std::string Logger::GetCurrentDate() const
{
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);

#ifdef _WIN32
    // Windows-specific thread-safe localtime
    struct tm timeinfo;
    localtime_s(&timeinfo, &time);
#else
    // POSIX-compliant systems use localtime_r
    struct tm timeinfo;
    localtime_r(&time, &timeinfo);
#endif

    char buffer[11]; // YYYY-MM-DD + null terminator
    strftime(buffer, sizeof(buffer), "%Y-%m-%d", &timeinfo);

    return std::string(buffer);
}

void Logger::CheckRotation()
{
    // 注意：此方法在Log方法中被调用，已经持有m_mutex锁
    // 因此不需要在此方法中再次获取锁

    if (m_rotationMode == LogRotationMode::NONE) {
        return;
    }

    bool shouldRotate = false;

    // 检查是否需要按大小轮转
    if (m_rotationMode == LogRotationMode::SIZE_BASED && m_logFile.is_open()) {
        // 获取当前文件大小
        auto currentPos = m_logFile.tellp();
        if (currentPos > 0 && static_cast<size_t>(currentPos) >= m_maxSizeKB * 1024) {
            shouldRotate = true;
        }
    }

    // 检查是否需要按日期轮转
    if (m_rotationMode == LogRotationMode::DAILY) {
        std::string currentDate = GetCurrentDate();
        if (!m_lastRotationDate.empty() && m_lastRotationDate != currentDate) {
            shouldRotate = true;
            m_lastRotationDate = currentDate;
        }
    }

    // 执行轮转
    if (shouldRotate) {
        RotateLogFiles();
    }
}

void Logger::RotateLogFiles()
{
    // 注意：此方法在CheckRotation方法中被调用，已经持有m_mutex锁
    // 因此不需要在此方法中再次获取锁

    // 如果文件未打开，不需要轮转
    if (!m_logFile.is_open()) {
        return;
    }

    try {
        // 关闭当前日志文件
        m_logFile.close();

        std::filesystem::path logPath(m_logFilePath);
        std::string baseFilename = logPath.stem().string();
        std::string extension = logPath.extension().string();
        std::string directory = logPath.parent_path().string();

        // 删除最老的日志文件
        std::string oldestLogFile = directory + "/" + baseFilename + "." + std::to_string(m_maxFilesToKeep) + extension;
        if (std::filesystem::exists(oldestLogFile)) {
            std::filesystem::remove(oldestLogFile);
        }

        // 轮转现有日志文件
        for (int i = m_maxFilesToKeep - 1; i >= 1; --i) {
            std::string oldFile = directory + "/" + baseFilename + "." + std::to_string(i) + extension;
            std::string newFile = directory + "/" + baseFilename + "." + std::to_string(i + 1) + extension;

            if (std::filesystem::exists(oldFile)) {
                std::filesystem::rename(oldFile, newFile);
            }
        }

        // 将当前日志文件重命名为 .1
        std::string backupFile = directory + "/" + baseFilename + ".1" + extension;
        std::filesystem::rename(m_logFilePath, backupFile);

        // 重新打开一个新的日志文件
        m_logFile.open(m_logFilePath, std::ios::out | std::ios::app);

        // 记录轮转信息
        if (m_logFile.is_open()) {
            m_logFile << GetTimestamp() << " [INFO] Log file rotated" << std::endl;
        }
    } catch (const std::exception& e) {
        // 记录轮转错误到调试日志
        WriteDebugLog("日志轮转失败: " + std::string(e.what()));

        // 尝试重新打开原始日志文件
        if (!m_logFile.is_open()) {
            m_logFile.open(m_logFilePath, std::ios::out | std::ios::app);
        }
    }
}
