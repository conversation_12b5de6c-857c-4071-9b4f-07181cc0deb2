#ifndef MSG_SERVER_MANAGER_H
#define MSG_SERVER_MANAGER_H

#include <string>
#include <memory>
#include <vector>
#include <map>
#include <mutex>
#include <chrono>
#include "../Protocol/NetworkManager.h"
#include "../Protocol/PacketTypes.h"

namespace MirServer {

// Message server info
struct MsgServerInfo {
    std::shared_ptr<Network::ClientConnection> connection;
    std::string sServerName;
    int32_t nServerIndex = 0;
    int32_t nOnlineCount = 0;
    std::chrono::steady_clock::time_point dwKeepAliveTick;
    std::string sIPaddr;
    std::string sReceiveMsg;
};

// Server limit info
struct LimitServerUserInfo {
    std::string sServerName;
    std::string sName;
    int32_t nLimitCountMin = 0;
    int32_t nLimitCountMax = 1000;
};

class MsgServerManager {
public:
    MsgServerManager();
    ~MsgServerManager();
    
    bool Initialize(const std::string& serverAddr, int32_t serverPort);
    bool Start();
    void Stop();
    
    // Server management
    void LoadServerAddr(const std::string& filename);
    void LoadUserLimit(const std::string& filename);
    bool CheckReadyServers();
    void SendServerMsg(uint16_t wIdent, const std::string& sServerName, const std::string& sMsg);
    void SendServerMsgA(uint16_t wIdent, const std::string& sMsg);
    bool IsNotUserFull(const std::string& sServerName);
    int32_t ServerStatus(const std::string& sServerName);
    int32_t GetOnlineHumCount();
    
    // Connection handlers
    void OnServerConnected(std::shared_ptr<Network::ClientConnection> connection);
    void OnServerDisconnected(std::shared_ptr<Network::ClientConnection> connection);
    void OnServerMessage(std::shared_ptr<Network::ClientConnection> connection, const std::string& msg);
    
private:
    std::unique_ptr<Network::NetworkManager> m_network;
    std::mutex m_serverListMutex;
    std::vector<std::shared_ptr<MsgServerInfo>> m_serverList;
    std::vector<std::string> m_serverAddrList;
    std::map<std::string, LimitServerUserInfo> m_userLimits;
    
    void ProcessServerMessage(std::shared_ptr<MsgServerInfo> server, const std::string& msg);
    void SortServerList(int32_t index);
    void RefServerLimit(const std::string& sServerName);
    std::string LimitName(const std::string& sServerName);
};

} // namespace MirServer

#endif // MSG_SERVER_MANAGER_H 