#pragma once

#include "UIControl.h"
#include "Button.h"
#include "Label.h"
#include "../Graphics/Texture.h"
#include <memory>
#include <string>
#include <vector>
#include <functional>

/**
 * @enum DialogResult
 * @brief Result of a dialog
 */
enum class DialogResult {
    NONE,       ///< No result
    OK,         ///< OK button clicked
    CANCEL,     ///< Cancel button clicked
    YES,        ///< Yes button clicked
    NO,         ///< No button clicked
    RETRY,      ///< Retry button clicked
    ABORT       ///< Abort button clicked
};

/**
 * @enum DialogButtons
 * @brief Buttons to show in a dialog
 */
enum class DialogButtons {
    OK,             ///< OK button
    OK_CANCEL,      ///< OK and Cancel buttons
    YES_NO,         ///< Yes and No buttons
    YES_NO_CANCEL,  ///< Yes, No, and Cancel buttons
    RETRY_CANCEL,   ///< Retry and Cancel buttons
    ABORT_RETRY_IGNORE  ///< Abort, Retry, and Ignore buttons
};

/**
 * @enum DialogSize
 * @brief Size of the dialog
 *
 * This matches the original Delphi project's DialogSize parameter
 */
enum class DialogSize {
    SMALL,      ///< Small dialog (DialogSize = 0 in original)
    NORMAL,     ///< Normal dialog (DialogSize = 1 in original)
    LARGE       ///< Large dialog (DialogSize = 2 in original)
};

/**
 * @class Dialog
 * @brief Dialog UI control
 *
 * This class represents a dialog UI control, which is a modal window that can
 * display a message and buttons for user interaction.
 */
class Dialog : public UIContainer {
protected:
    std::shared_ptr<Texture> m_backgroundTexture;  ///< Background texture
    std::string m_title;                           ///< Dialog title
    std::string m_message;                         ///< Dialog message
    DialogButtons m_buttons;                       ///< Dialog buttons
    DialogResult m_result;                         ///< Dialog result
    DialogSize m_dialogSize;                       ///< Dialog size

    std::shared_ptr<WILManager> m_wilManager;      ///< WIL manager
    std::string m_resourceFile;                    ///< Resource file
    int m_backgroundImageIndex;                    ///< Background image index

    int m_messageX;                                ///< Message X position (relative to dialog)
    int m_messageY;                                ///< Message Y position (relative to dialog)

    std::shared_ptr<Label> m_titleLabel;           ///< Title label
    std::shared_ptr<Label> m_messageLabel;         ///< Message label
    std::vector<std::shared_ptr<Button>> m_buttonControls;  ///< Button controls

    bool m_dragging;                               ///< Whether the dialog is being dragged
    int m_dragOffsetX;                             ///< X offset for dragging
    int m_dragOffsetY;                             ///< Y offset for dragging

    std::function<void(DialogResult)> m_onResult;  ///< Result callback

    /**
     * @brief Create the dialog controls
     */
    virtual void CreateControls();

    /**
     * @brief Handle button click
     * @param button Button that was clicked
     */
    void HandleButtonClick(UIControl* button);

    /**
     * @brief Create a button click handler
     * @param button Button to handle clicks for
     * @return Click handler function
     */
    std::function<void()> CreateButtonClickHandler(std::shared_ptr<Button> button);

public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param title Dialog title
     * @param message Dialog message
     * @param buttons Dialog buttons
     * @param name Control name
     */
    Dialog(int x, int y, int width, int height, const std::string& title, const std::string& message = "", DialogButtons buttons = DialogButtons::OK, const std::string& name = "");

    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param title Dialog title
     * @param message Dialog message
     * @param buttons Dialog buttons
     * @param name Control name
     * */
    Dialog(int x, int y, const std::string& title, const std::string& message = "", DialogButtons buttons = DialogButtons::OK, const std::string& name = "");
    /**
     * @brief Set the WIL manager
     * @param wilManager WIL manager
     */
    void SetWILManager(std::shared_ptr<WILManager> wilManager);

    /**
     * @brief Set the resource file
     * @param resourceFile Resource file
     */
    void SetResourceFile(const std::string& resourceFile);

    /**
     * @brief Set the background image index
     * @param index Background image index
     */
    void SetBackgroundImageIndex(int index);

    /**
     * @brief Set the dialog size
     * @param size Dialog size
     */
    void SetDialogSize(DialogSize size);

    /**
     * @brief Destructor
     */
    virtual ~Dialog();

    /**
     * @brief Initialize the dialog
     * @param renderer SDL renderer
     */
    virtual void Initialize(SDL_Renderer* renderer);

    /**
     * @brief Update the dialog
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    virtual void Update(int deltaTime) override;

    /**
     * @brief Render the dialog
     * @param renderer SDL renderer
     */
    virtual void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Handle mouse motion event
     * @param x Mouse X coordinate
     * @param y Mouse Y coordinate
     * @return true if handled, false otherwise
     */
    virtual bool HandleMouseMotion(int x, int y) override;

    /**
     * @brief Handle mouse button event
     * @param button Mouse button
     * @param pressed Whether the button was pressed or released
     * @param x Mouse X coordinate
     * @param y Mouse Y coordinate
     * @return true if handled, false otherwise
     */
    virtual bool HandleMouseButton(Uint8 button, bool pressed, int x, int y) override;

    /**
     * @brief Handle key event
     * @param key Key code
     * @param pressed Whether the key was pressed or released
     * @return true if handled, false otherwise
     */
    virtual bool HandleKey(SDL_Keycode key, bool pressed) override;

    /**
     * @brief Set the background texture
     * @param texture Background texture
     */
    void SetBackgroundTexture(std::shared_ptr<Texture> texture);

    /**
     * @brief Set the title
     * @param title Dialog title
     */
    void SetTitle(const std::string& title);

    /**
     * @brief Set the message
     * @param message Dialog message
     */
    void SetMessage(const std::string& message);

    /**
     * @brief Set the buttons
     * @param buttons Dialog buttons
     */
    void SetButtons(DialogButtons buttons);

    /**
     * @brief Set the result callback
     * @param callback Result callback function
     */
    void SetOnResult(std::function<void(DialogResult)> callback);

    /**
     * @brief Show the dialog
     */
    void Show();

    /**
     * @brief Hide the dialog
     */
    void Hide();

    /**
     * @brief Close the dialog with a result
     * @param result Dialog result
     */
    void Close(DialogResult result);

    /**
     * @brief Get the title
     * @return Dialog title
     */
    const std::string& GetTitle() const { return m_title; }

    /**
     * @brief Get the message
     * @return Dialog message
     */
    const std::string& GetMessage() const { return m_message; }

    /**
     * @brief Get the buttons
     * @return Dialog buttons
     */
    DialogButtons GetButtons() const { return m_buttons; }

    /**
     * @brief Get the result
     * @return Dialog result
     */
    DialogResult GetResult() const { return m_result; }

    /**
     * @brief Get the WIL manager
     * @return WIL manager
     */
    std::shared_ptr<WILManager> GetWILManager() const { return m_wilManager; }

    /**
     * @brief Get the resource file
     * @return Resource file
     */
    const std::string& GetResourceFile() const { return m_resourceFile; }

    /**
     * @brief Get the background image index
     * @return Background image index
     */
    int GetBackgroundImageIndex() const { return m_backgroundImageIndex; }

    /**
     * @brief Get the dialog size
     * @return Dialog size
     */
    DialogSize GetDialogSize() const { return m_dialogSize; }

    /**
     * @brief Get the title label
     * @return Title label
     */
    std::shared_ptr<Label> GetTitleLabel() const { return m_titleLabel; }

    /**
     * @brief Get the message label
     * @return Message label
     */
    std::shared_ptr<Label> GetMessageLabel() const { return m_messageLabel; }
};
