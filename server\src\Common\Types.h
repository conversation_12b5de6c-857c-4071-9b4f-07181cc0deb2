﻿#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <array>
#include <memory>
#include <cmath>

#ifdef _WIN32
    #ifndef NOMINMAX
        #define NOMINMAX  // 防止Windows.h定义min/max宏
    #endif
    #include <winsock2.h>
    #include <windows.h>
    #define SOCKET_TYPE SOCKET
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #define SOCKET_TYPE int
    #define INVALID_SOCKET -1
    #define SOCKET_ERROR -1
#endif

namespace MirServer {

// 基础类型定义（对应delphi的基础类型）
using BYTE = uint8_t;
using WORD = uint16_t;
using DWORD = uint32_t;
using QWORD = uint64_t;
using BOOLEAN = bool;

// 游戏常量（来自M2Share.pas）
constexpr int MAXLEVEL = 65535;
constexpr int MAXCHANGELEVEL = 1000;
constexpr int MAXMAGIC = 20;          // 魔法数量上限
constexpr int MAXHUMPOWER = 65535;    // 人物能力上限
constexpr int DEFHIT = 5;             // 默认命中
constexpr int DEFSPEED = 15;          // 默认速度

// 战斗系统常量
constexpr int MAX_STATUS_ATTRIBUTE = 20;  // 最大状态属性数量
constexpr int U_MAXUSEITEM = 13;          // 最大装备数量

// 装备位置常量
constexpr int U_WEAPON = 0;      // 武器
constexpr int U_DRESS = 1;       // 衣服
constexpr int U_HELMET = 2;      // 头盔
constexpr int U_NECKLACE = 3;    // 项链
constexpr int U_BRACELETR = 4;   // 右手镯
constexpr int U_BRACELETL = 5;   // 左手镯
constexpr int U_RINGL = 6;       // 左戒指
constexpr int U_RINGR = 7;       // 右戒指
constexpr int U_BOOTS = 8;       // 鞋子
constexpr int U_BELT = 9;        // 腰带
constexpr int U_CHARM = 10;      // 护身符
constexpr int U_TORCH = 11;      // 火把

// 种族常量
constexpr int RC_PLAYOBJECT = 0;   // 玩家对象
constexpr int RC_PLAYMOSTER = 1;   // 玩家怪物
constexpr int RC_ANIMAL = 50;      // 动物
constexpr int RC_MONSTER = 80;     // 怪物

// 生命属性常量
constexpr int LA_UNDEAD = 1;       // 不死系

// 毒药类型常量
constexpr int POISON_GREEN = 0;         // 绿毒
constexpr int POISON_DAMAGEARMOR = 1;   // 腐蚀毒药
constexpr int POISON_STONE = 5;         // 麻痹

// 职业类型（对应delphi的职业定义）
enum class JobType : BYTE {
    NONE = 255,     // 无职业限制
    WARRIOR = 0,    // 战士
    WIZARD = 1,     // 法师
    TAOIST = 2      // 道士
};

// 性别类型
enum class GenderType : BYTE {
    MALE = 0,       // 男性
    FEMALE = 1      // 女性
};

// 方向类型
enum class DirectionType : BYTE {
    UP = 0,         // 上
    UP_RIGHT = 1,   // 右上
    RIGHT = 2,      // 右
    DOWN_RIGHT = 3, // 右下
    DOWN = 4,       // 下
    DOWN_LEFT = 5,  // 左下
    LEFT = 6,       // 左
    UP_LEFT = 7     // 左上
};

// 对象类型 (MirServer internal ObjectType)
enum class ObjectType : BYTE {
    Player = 0,     // 玩家
    HUMAN = 0,      // 玩家（别名）
    Monster = 1,    // 怪物
    MONSTER = 1,    // 怪物（别名）
    NPC = 2,        // NPC
    Item = 3,       // 物品
    ITEM = 3,       // 物品（别名）
    Event = 4       // 事件
};

// 攻击模式
enum class AttackMode : BYTE {
    PEACE = 0,      // 和平模式
    DEAR = 1,       // 夫妻模式
    MASTER = 2,     // 师徒模式
    GROUP = 3,      // 组队模式
    GUILD = 4,      // 行会模式
    REDWHITE = 5,   // 善恶模式
    ALL = 6         // 全体模式
};

// 属性范围结构
struct TAbilityRange {
    WORD min = 0;
    WORD max = 0;

    TAbilityRange() = default;
    TAbilityRange(WORD minVal, WORD maxVal) : min(minVal), max(maxVal) {}
};

// 基础属性结构（对应delphi的TAbility）
struct TAbility {
    WORD Level = 1;           // 等级
    TAbilityRange AC{0, 0};   // 防御力
    TAbilityRange MAC{0, 0};  // 魔法防御力
    TAbilityRange DC{0, 0};   // 攻击力
    TAbilityRange MC{0, 0};   // 魔法攻击力
    TAbilityRange SC{0, 0};   // 道术攻击力
    WORD HP = 0;              // 生命值
    WORD MP = 0;              // 魔法值
    WORD MaxHP = 0;           // 最大生命值
    WORD MaxMP = 0;           // 最大魔法值
    DWORD Exp = 0;            // 经验值
    DWORD MaxExp = 0;         // 升级所需经验值
    WORD Weight = 0;          // 负重
    WORD MaxWeight = 0;       // 最大负重
    WORD WearWeight = 0;      // 装备重量
    WORD MaxWearWeight = 0;   // 最大装备重量
    WORD HandWeight = 0;      // 手持重量
    WORD MaxHandWeight = 0;   // 最大手持重量
};

// 附加属性结构（对应delphi的TAddAbility）
struct TAddAbility {
    BYTE btWeaponStrong = 0;  // 武器强度
    BYTE btUndeadDamage = 0;  // 对不死系伤害
};

// 用户物品结构（对应delphi的TUserItem）
// This is the original TUserItem, which might be distinct from the later UserItem struct
struct TUserItem {
    WORD wIndex = 0;          // 物品索引
    WORD Dura = 0;            // 持久度
    WORD DuraMax = 0;         // 最大持久度
    DWORD MakeIndex = 0;      // 制造索引
    BYTE btValue[14] = {0};   // 附加属性值

    TUserItem() = default;
};

// 兼容性别名
using Ability = TAbility;

// 物品附加属性值结构（用于解析btValue）
struct ItemValue {
    BYTE dc = 0;              // 攻击力
    BYTE dcMax = 0;           // 攻击力上限
    BYTE mc = 0;              // 魔法力
    BYTE mcMax = 0;           // 魔法力上限
    BYTE sc = 0;              // 道术力
    BYTE scMax = 0;           // 道术力上限
    BYTE ac = 0;              // 防御力
    BYTE acMax = 0;           // 防御力上限
    BYTE mac = 0;             // 魔法防御
    BYTE macMax = 0;          // 魔法防御上限
    BYTE accuracy = 0;        // 准确
    BYTE agility = 0;         // 敏捷
    BYTE hp = 0;              // 生命值
    BYTE mp = 0;              // 魔法值
    BYTE luck = 0;            // 幸运

    void FromBytes(const BYTE btValue[14]) {
        dc = btValue[0]; dcMax = btValue[1]; mc = btValue[2]; mcMax = btValue[3];
        sc = btValue[4]; scMax = btValue[5]; ac = btValue[6]; acMax = btValue[7];
        mac = btValue[8]; macMax = btValue[9]; accuracy = btValue[10]; agility = btValue[11];
        hp = btValue[12]; mp = btValue[13]; // Assuming luck is not in these 14 bytes or handled differently
    }

    void ToBytes(BYTE btValue[14]) const {
        btValue[0] = dc; btValue[1] = dcMax; btValue[2] = mc; btValue[3] = mcMax;
        btValue[4] = sc; btValue[5] = scMax; btValue[6] = ac; btValue[7] = acMax;
        btValue[8] = mac; btValue[9] = macMax; btValue[10] = accuracy; btValue[11] = agility;
        btValue[12] = hp; btValue[13] = mp;
    }
};

// 物品结构 (This is a more detailed UserItem, distinct from TUserItem)
struct UserItem {
    WORD makeIndex = 0;       // 物品制造索引
    WORD itemIndex = 0;       // 物品索引
    WORD dura = 0;            // 持久度
    WORD duraMax = 0;         // 最大持久度
    std::string itemName;     // 物品名称
    BYTE btValue[14] = {0};   // 附加属性值 (raw bytes)
    bool identified = true;   // 是否已鉴定

    ItemValue value; // Parsed values

    UserItem() {
        value.FromBytes(btValue); // Initialize parsed values from raw bytes
    }

    void UpdateBytesFromValue() { // Sync raw bytes from parsed values
        value.ToBytes(btValue);
    }
    void UpdateValueFromBytes() { // Sync parsed values from raw bytes
        value.FromBytes(btValue);
    }
};

// 装备位置枚举
enum class EquipPosition : BYTE {
    WEAPON = 0,
    DRESS = 1,
    HELMET = 2,
    NECKLACE = 3,
    BRACELET_RIGHT = 4,
    BRACELET_LEFT = 5,
    RING_LEFT = 6,
    RING_RIGHT = 7,
    BOOTS = 8,
    BELT = 9,
    CHARM = 10,
    TORCH = 11,
    MAX_EQUIP // 哨兵值，表示装备槽数量
};

// 用户魔法结构
struct UserMagic {
    WORD magicId = 0;
    BYTE level = 0;
    BYTE key = 0;
    DWORD curTrain = 0;
    DWORD maxTrain = 0;
    DWORD trainLevel = 0;
    DWORD nextUseTime = 0;
};

// 坐标点结构
struct Point {
    int x = 0;
    int y = 0;

    Point() = default;
    Point(int x_, int y_) : x(x_), y(y_) {}

    bool operator==(const Point& other) const {
        return x == other.x && y == other.y;
    }
    bool operator!=(const Point& other) const {
        return !(*this == other);
    }
};

// 玩家基础数据结构
struct HumDataInfo {
    std::string charName;
    std::string account;
    GenderType gender = GenderType::MALE;
    JobType job = JobType::WARRIOR;
    BYTE country = 0;
    BYTE hair = 0;
    BYTE level = 1;
    DirectionType direction = DirectionType::DOWN;
    Point currentPos{0, 0};
    std::string mapName;
    Point homePos{0, 0};
    std::string homeMap;
    std::string dearName;
    std::string masterName;
    bool isDead = false;
    DWORD pkPoint = 0;
    DWORD reputation = 0;
    DWORD creditPoint = 0;
    WORD goldMax = 5000;
    DWORD gold = 0;
    DWORD gameGold = 0;
    DWORD gamePoint = 0;
    DWORD paymentPoint = 0;
    WORD bonusPoint = 0;
    Ability abil;
    std::array<UserItem, static_cast<size_t>(EquipPosition::MAX_EQUIP)> useItems;
    std::vector<UserItem> bagItems;
    std::vector<UserItem> storageItems;
    std::vector<UserMagic> magics;
    std::string bankPassword;
    DWORD bankGold = 0;
    std::string guildName;
    BYTE guildRank = 0;
};

// 会话信息结构
struct SessionInfo {
    SOCKET_TYPE socket = INVALID_SOCKET;
    std::string remoteIP;
    WORD remotePort = 0;
    DWORD connectTime = 0;
    DWORD lastActiveTime = 0;
    bool isValid = false;
    std::string account;
    std::string accountName;
    std::string charName;
    std::string ipAddress;
    BYTE softVersionDate = 0;
};

// 服务器配置结构
struct ServerConfig {
    std::string serverName = "MirServer";
    WORD gatePorts[4] = {7000, 7001, 7002, 7003};
    WORD dbServerPort = 5100;
    WORD loginServerPort = 5500;
    std::string dbServerIP = "127.0.0.1";
    std::string loginServerIP = "127.0.0.1";
    int maxUserCount = 1000;
    int maxConnections = 2000;
    bool testMode = false;
    bool voiceMode = false;
    bool startInTray = false;
};

// 工具函数声明
DWORD GetCurrentTime();
int GenerateRandom(int min, int max);
Point GetDirectionOffset(DirectionType direction);
DirectionType GetDirectionFromPoints(const Point& from, const Point& to);
double GetDistance(const Point& p1, const Point& p2);
bool IsInRange(const Point& center, const Point& target, int range);
void InitializeDefaultAbility(Ability& ability, JobType job, BYTE level = 1);
DWORD GetLevelUpExp(BYTE level);
bool IsValidCharacterName(const std::string& name);
bool IsValidAccountName(const std::string& account);

// MirServer ObjectState (distinct from Mir200::BaseObject::ObjectState)
enum class ObjectState : uint8_t {
    ALIVE = 0,
    DEAD = 1,
    INVISIBLE = 2
    // Add other general states if needed from original Delphi M2Share or common defs
};

//矩形
struct Rect {
    int left, top, right, bottom;
    
    Rect() : left(0), top(0), right(0), bottom(0) {}
    Rect(int l, int t, int r, int b) : left(l), top(t), right(r), bottom(b) {}

    int Width() const { return right - left; }
    int Height() const { return bottom - top; }
    bool Contains(const Point& p) const {
        return p.x >= left && p.x < right && p.y >= top && p.y < bottom;
    }
};

} // namespace MirServer