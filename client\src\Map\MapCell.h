#pragma once

#include <cstdint>

/**
 * @enum TileType
 * @brief Types of map tiles
 */
enum class TileType {
    EMPTY,      ///< Empty tile (no background)
    GROUND,     ///< Ground tile (walkable)
    OBSTACLE,   ///< Obstacle tile (not walkable)
    WATER,      ///< Water tile (special movement)
    DOOR        ///< Door tile (can be opened)
};

/**
 * @class MapCell
 * @brief Represents a single cell in the game map
 *
 * This class stores information about a single cell in the game map,
 * including its background, middle, and object layers, as well as
 * its walkable status and other properties.
 */
class MapCell {
private:
    uint16_t m_backgroundImageIndex;  ///< Background image index
    uint16_t m_middleImageIndex;      ///< Middle image index
    uint16_t m_objectImageIndex;      ///< Object image index

    bool m_walkable;                  ///< Whether the cell is walkable
    bool m_transparent;               ///< Whether the cell is transparent (for line of sight)

    TileType m_tileType;              ///< Type of tile

    uint8_t m_light;                  ///< Light level (0-255)

    // Door-related fields
    uint8_t m_doorIndex;              ///< Door index (0 for no door)
    uint8_t m_doorOffset;             ///< Door offset (for animation)
    bool m_doorOpen;                  ///< Whether the door is open

    // Animation-related fields
    uint8_t m_aniFrame;               ///< Current animation frame
    uint8_t m_aniTick;                ///< Animation tick counter
    uint8_t m_aniTickMax;             ///< Maximum animation tick (speed)

public:
    /**
     * @brief Constructor
     */
    MapCell();

    /**
     * @brief Destructor
     */
    ~MapCell();

    /**
     * @brief Set the background image index
     * @param index Image index
     */
    void SetBackgroundImageIndex(uint16_t index);

    /**
     * @brief Set the middle image index
     * @param index Image index
     */
    void SetMiddleImageIndex(uint16_t index);

    /**
     * @brief Set the object image index
     * @param index Image index
     */
    void SetObjectImageIndex(uint16_t index);

    /**
     * @brief Set whether the cell is walkable
     * @param walkable Walkable flag
     */
    void SetWalkable(bool walkable);

    /**
     * @brief Set whether the cell is transparent
     * @param transparent Transparent flag
     */
    void SetTransparent(bool transparent);

    /**
     * @brief Set the tile type
     * @param type Tile type
     */
    void SetTileType(TileType type);

    /**
     * @brief Set the light level
     * @param light Light level (0-255)
     */
    void SetLight(uint8_t light);

    /**
     * @brief Get the background image index
     * @return Background image index
     */
    uint16_t GetBackgroundImageIndex() const { return m_backgroundImageIndex; }

    /**
     * @brief Get the middle image index
     * @return Middle image index
     */
    uint16_t GetMiddleImageIndex() const { return m_middleImageIndex; }

    /**
     * @brief Get the object image index
     * @return Object image index
     */
    uint16_t GetObjectImageIndex() const { return m_objectImageIndex; }

    /**
     * @brief Check if the cell is walkable
     * @return true if walkable, false otherwise
     */
    bool IsWalkable() const { return m_walkable; }

    /**
     * @brief Check if the cell is transparent
     * @return true if transparent, false otherwise
     */
    bool IsTransparent() const { return m_transparent; }

    /**
     * @brief Get the tile type
     * @return Tile type
     */
    TileType GetTileType() const { return m_tileType; }

    /**
     * @brief Get the light level
     * @return Light level (0-255)
     */
    uint8_t GetLight() const { return m_light; }

    /**
     * @brief Set the door index
     * @param index Door index
     */
    void SetDoorIndex(uint8_t index);

    /**
     * @brief Set the door offset
     * @param offset Door offset
     */
    void SetDoorOffset(uint8_t offset);

    /**
     * @brief Set whether the door is open
     * @param open Door open flag
     */
    void SetDoorOpen(bool open);

    /**
     * @brief Set the animation frame
     * @param frame Animation frame
     */
    void SetAniFrame(uint8_t frame);

    /**
     * @brief Set the animation tick
     * @param tick Animation tick
     */
    void SetAniTick(uint8_t tick);

    /**
     * @brief Set the maximum animation tick
     * @param tickMax Maximum animation tick
     */
    void SetAniTickMax(uint8_t tickMax);

    /**
     * @brief Get the door index
     * @return Door index
     */
    uint8_t GetDoorIndex() const { return m_doorIndex; }

    /**
     * @brief Get the door offset
     * @return Door offset
     */
    uint8_t GetDoorOffset() const { return m_doorOffset; }

    /**
     * @brief Check if the door is open
     * @return true if open, false otherwise
     */
    bool IsDoorOpen() const { return m_doorOpen; }

    /**
     * @brief Get the animation frame
     * @return Animation frame
     */
    uint8_t GetAniFrame() const { return m_aniFrame; }

    /**
     * @brief Get the animation tick
     * @return Animation tick
     */
    uint8_t GetAniTick() const { return m_aniTick; }

    /**
     * @brief Get the maximum animation tick
     * @return Maximum animation tick
     */
    uint8_t GetAniTickMax() const { return m_aniTickMax; }

    /**
     * @brief Update animation
     * @param deltaTime Time elapsed since last frame in milliseconds
     * @return true if animation frame changed, false otherwise
     */
    bool UpdateAnimation(int deltaTime);

    /**
     * @brief Toggle door state
     * @return true if door is now open, false otherwise
     */
    bool ToggleDoor();

    /**
     * @brief Open the door
     */
    void OpenDoor();

    /**
     * @brief Close the door
     */
    void CloseDoor();
};
