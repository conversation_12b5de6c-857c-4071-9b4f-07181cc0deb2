#pragma once

// Mir200 UserEngine - User management system
// Based on delphi/EM2Engine/UsrEngn.pas - Following original project structure
// Phase 1 Implementation - Basic placeholder for M2Server integration

#include "Common/M2Share.h"
#include <memory>
#include <vector>
#include <unordered_map>
#include <mutex>

// Forward declarations
class PlayObject;
class Environment;
enum class ServerState : BYTE;

// UserEngine class - Main user management system
class UserEngine {
private:
    // User management
    std::unordered_map<std::string, std::shared_ptr<PlayObject>> m_online_users;
    std::mutex m_users_mutex;
    
    // Statistics
    int m_online_user_count;
    int m_total_user_count;
    int m_max_user_count;
    
    // State
    bool m_initialized;
    bool m_running;
    
public:
    UserEngine();
    ~UserEngine();
    
    // Core lifecycle
    bool Initialize();
    void Finalize();
    bool Start();
    void Stop();
    
    // User management
    bool PlayerLogin(std::shared_ptr<PlayObject> player);
    bool PlayerLogout(const std::string& char_name);
    std::shared_ptr<PlayObject> FindPlayer(const std::string& char_name);
    
    // Processing
    void ProcessUsers();
    void CleanupExpiredSessions();
    
    // Statistics
    int GetOnlineUserCount() const { return m_online_user_count; }
    int GetTotalUserCount() const { return m_total_user_count; }
    int GetMaxUserCount() const { return m_max_user_count; }
    
    // Data management
    void SaveAllUsers();
    
    // State management
    bool IsInitialized() const { return m_initialized; }
    bool IsRunning() const { return m_running; }
    
    // Server state notifications
    void OnServerStateChanged(ServerState new_state);
};
