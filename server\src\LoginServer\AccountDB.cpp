#include "AccountDB.h"
#include "../Common/Logger.h"
#include <filesystem>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <cstring>

namespace MirServer {

AccountDB::AccountDB() {
}

AccountDB::~AccountDB() {
    if (m_isOpen) {
        Close();
    }
}

bool AccountDB::Initialize(const std::string& dbPath) {
    m_dbPath = dbPath + "ID.DB";
    m_idxPath = dbPath + "ID.idx";
    
    // Create directory if it doesn't exist
    std::filesystem::path dir(dbPath);
    if (!std::filesystem::exists(dir)) {
        try {
            std::filesystem::create_directories(dir);
        } catch (const std::exception& e) {
            Logger::Error("Failed to create database directory: " + std::string(e.what()));
            return false;
        }
    }
    
    return true;
}

bool AccountDB::Open() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_isOpen) {
        return true;
    }
    
    // Load existing database or create new one
    if (!LoadDatabase()) {
        Logger::Info("Creating new account database");
        m_records.clear();
        m_accountIndex.clear();
        m_nextIndex = 0;
    }
    
    m_isOpen = true;
    return true;
}

void AccountDB::Close() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_isOpen) {
        return;
    }
    
    SaveDatabase();
    m_isOpen = false;
}

int32_t AccountDB::Index(const std::string& account) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_accountIndex.find(account);
    if (it != m_accountIndex.end()) {
        return it->second;
    }
    return -1;
}

bool AccountDB::Get(int32_t index, TAccountDBRecord& record) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_records.find(index);
    if (it != m_records.end()) {
        record = it->second;
        return true;
    }
    return false;
}

bool AccountDB::Add(const TAccountDBRecord& record) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_isOpen) {
        return false;
    }
    
    // Check if account already exists
    if (m_accountIndex.find(record.UserEntry.sAccount) != m_accountIndex.end()) {
        return false;
    }
    
    // Add new record
    TAccountDBRecord newRecord = record;
    UpdateHeader(newRecord);
    
    m_records[m_nextIndex] = newRecord;
    m_accountIndex[record.UserEntry.sAccount] = m_nextIndex;
    m_nextIndex++;
    
    // Save to disk
    return SaveDatabase();
}

bool AccountDB::Update(int32_t index, const TAccountDBRecord& record) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_isOpen) {
        return false;
    }
    
    auto it = m_records.find(index);
    if (it == m_records.end()) {
        return false;
    }
    
    // Update record
    TAccountDBRecord updatedRecord = record;
    UpdateHeader(updatedRecord);
    it->second = updatedRecord;
    
    // Save to disk
    return SaveDatabase();
}

bool AccountDB::Delete(int32_t index) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_isOpen) {
        return false;
    }
    
    auto it = m_records.find(index);
    if (it == m_records.end()) {
        return false;
    }
    
    // Remove from index
    m_accountIndex.erase(it->second.UserEntry.sAccount);
    
    // Mark as deleted
    it->second.Header.boDeleted = true;
    
    // Save to disk
    return SaveDatabase();
}

bool AccountDB::CheckAccountName(const std::string& account) {
    if (account.empty() || account.length() > 12) {
        return false;
    }
    
    for (char c : account) {
        if (!((c >= '0' && c <= '9') || 
              (c >= 'A' && c <= 'Z') || 
              (c >= 'a' && c <= 'z') ||
              c == '_')) {
            return false;
        }
    }
    
    return true;
}

void AccountDB::CreateBackup() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (!m_isOpen) {
        return;
    }
    
    std::string backupFile = GetBackupFileName();
    try {
        std::filesystem::copy_file(m_dbPath, backupFile, 
            std::filesystem::copy_options::overwrite_existing);
        Logger::Info("Created backup: " + backupFile);
    } catch (const std::exception& e) {
        Logger::Error("Failed to create backup: " + std::string(e.what()));
    }
}

bool AccountDB::LoadDatabase() {
    std::ifstream file(m_dbPath, std::ios::binary);
    if (!file) {
        return false;
    }
    
    m_records.clear();
    m_accountIndex.clear();
    m_nextIndex = 0;
    
    TAccountDBRecord record;
    while (file.read(reinterpret_cast<char*>(&record), sizeof(TAccountDBRecord))) {
        if (!record.Header.boDeleted) {
            m_records[m_nextIndex] = record;
            m_accountIndex[record.UserEntry.sAccount] = m_nextIndex;
            m_nextIndex++;
        }
    }
    
    file.close();
    Logger::Info("Loaded " + std::to_string(m_records.size()) + " accounts from database");
    return true;
}

bool AccountDB::SaveDatabase() {
    std::ofstream file(m_dbPath, std::ios::binary);
    if (!file) {
        Logger::Error("Failed to open database file for writing");
        return false;
    }
    
    for (const auto& pair : m_records) {
        if (!pair.second.Header.boDeleted) {
            file.write(reinterpret_cast<const char*>(&pair.second), sizeof(TAccountDBRecord));
        }
    }
    
    file.close();
    
    // Save index
    return SaveIndex();
}

bool AccountDB::LoadIndex() {
    std::ifstream file(m_idxPath, std::ios::binary);
    if (!file) {
        return BuildIndex();
    }
    
    // Index file format: account_name(13 bytes) + index(4 bytes)
    char account[13];
    int32_t index;
    
    m_accountIndex.clear();
    while (file.read(account, 13) && file.read(reinterpret_cast<char*>(&index), sizeof(int32_t))) {
        m_accountIndex[account] = index;
    }
    
    file.close();
    return true;
}

bool AccountDB::SaveIndex() {
    std::ofstream file(m_idxPath, std::ios::binary);
    if (!file) {
        return false;
    }
    
    for (const auto& pair : m_accountIndex) {
        char account[13] = {0};
        size_t len = std::min(pair.first.length(), size_t(12));
        std::memcpy(account, pair.first.c_str(), len);
        file.write(account, 13);
        file.write(reinterpret_cast<const char*>(&pair.second), sizeof(int32_t));
    }
    
    file.close();
    return true;
}

bool AccountDB::BuildIndex() {
    m_accountIndex.clear();
    
    for (const auto& pair : m_records) {
        if (!pair.second.Header.boDeleted) {
            m_accountIndex[pair.second.UserEntry.sAccount] = pair.first;
        }
    }
    
    return SaveIndex();
}

std::string AccountDB::GetBackupFileName() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::stringstream ss;
    ss << m_dbPath << ".bak_";
    ss << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");
    
    return ss.str();
}

void AccountDB::UpdateHeader(TAccountDBRecord& record) {
    auto now = std::chrono::system_clock::now();
    auto duration = now.time_since_epoch();
    auto millis = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
    
    record.Header.UpdateDateTime = static_cast<double>(millis);
    record.Header.UpdateDate = static_cast<uint16_t>(std::time(nullptr) / 86400);  // Days since epoch
}

} // namespace MirServer 