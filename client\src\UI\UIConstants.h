#pragma once

/**
 * @file UIConstants.h
 * @brief 定义UI元素的图像索引常量
 *
 * 这个文件定义了所有UI元素的图像索引常量，确保与原始Delphi项目保持一致。
 */

namespace UIConstants {
    // login state
    constexpr int LOGIN_NEWACCOUNT_BG_INDEX = 63;         //
    // 主界面元素
    constexpr int MAIN_FRAME_INDEX = 0;        // 主框架图像索引
    constexpr int HEALTH_BAR_FRAME_INDEX = 1;  // 生命条框架图像索引
    constexpr int MANA_BAR_FRAME_INDEX = 2;    // 魔法条框架图像索引
    constexpr int EXP_BAR_FRAME_INDEX = 3;     // 经验条框架图像索引
    constexpr int BOTTOM_PANEL_INDEX = 1;      // 底部面板图像索引 (原始中的DBottom.Image)
    constexpr int BELT_ITEM_FRAME_INDEX = 0;   // 腰带物品框架图像索引
    constexpr int GOLD_FRAME_INDEX = 29;       // 金币框架图像索引 (原始中的DGold.Image)

    // 按钮
    constexpr int STATE_BTN_INDEX = 8;         // 状态按钮图像索引 (原始中的DMyState.Image)
    constexpr int BAG_BTN_INDEX = 9;           // 背包按钮图像索引 (原始中的DMyBag.Image)
    constexpr int SKILL_BTN_INDEX = 10;        // 技能按钮图像索引 (原始中的DMyMagic.Image)
    constexpr int OPTION_BTN_INDEX = 11;       // 选项按钮图像索引 (原始中的DOption.Image)
    constexpr int MINIMAP_BTN_INDEX = 130;     // 小地图按钮图像索引 (原始中的DBotMiniMap.Image)
    constexpr int TRADE_BTN_INDEX = 132;       // 交易按钮图像索引 (原始中的DBotTrade.Image)
    constexpr int GUILD_BTN_INDEX = 134;       // 公会按钮图像索引 (原始中的DBotGuild.Image)
    constexpr int GROUP_BTN_INDEX = 128;       // 组队按钮图像索引 (原始中的DBotGroup.Image)
    constexpr int EXIT_BTN_INDEX = 138;        // 退出按钮图像索引 (原始中的DBotExit.Image)
    constexpr int LOGOUT_BTN_INDEX = 136;      // 登出按钮图像索引 (原始中的DBotLogout.Image)

    // 技能界面
    constexpr int SKILL_UI_BG_INDEX = 200;     // 技能界面背景图像索引
    constexpr int SKILL_UI_CLOSE_BTN_NORMAL = 201;    // 技能界面关闭按钮普通状态
    constexpr int SKILL_UI_CLOSE_BTN_HOVER = 202;     // 技能界面关闭按钮悬停状态
    constexpr int SKILL_UI_CLOSE_BTN_PRESSED = 203;   // 技能界面关闭按钮按下状态
    constexpr int SKILL_UI_CLOSE_BTN_DISABLED = 204;  // 技能界面关闭按钮禁用状态

    // 热键配置界面
    constexpr int HOTKEY_UI_BG_INDEX = 250;    // 热键配置界面背景图像索引
    constexpr int HOTKEY_UI_CLOSE_BTN_NORMAL = 251;   // 热键配置界面关闭按钮普通状态
    constexpr int HOTKEY_UI_CLOSE_BTN_HOVER = 252;    // 热键配置界面关闭按钮悬停状态
    constexpr int HOTKEY_UI_CLOSE_BTN_PRESSED = 253;  // 热键配置界面关闭按钮按下状态
    constexpr int HOTKEY_UI_CLOSE_BTN_DISABLED = 254; // 热键配置界面关闭按钮禁用状态

    // 物品栏界面
    constexpr int INVENTORY_UI_BG_INDEX = 300;        // 物品栏界面背景图像索引
    constexpr int INVENTORY_UI_CLOSE_BTN_NORMAL = 301;    // 物品栏界面关闭按钮普通状态
    constexpr int INVENTORY_UI_CLOSE_BTN_HOVER = 302;     // 物品栏界面关闭按钮悬停状态
    constexpr int INVENTORY_UI_CLOSE_BTN_PRESSED = 303;   // 物品栏界面关闭按钮按下状态
    constexpr int INVENTORY_UI_CLOSE_BTN_DISABLED = 304;  // 物品栏界面关闭按钮禁用状态
    constexpr int INVENTORY_ITEM_CELL_INDEX = 305;    // 物品栏单元格图像索引

    // 装备界面
    constexpr int EQUIPMENT_PANEL_BG_INDEX = 350;          // 装备界面背景图像索引
    constexpr int EQUIPMENT_PANEL_CLOSE_BTN_NORMAL = 351;  // 装备界面关闭按钮普通状态
    constexpr int EQUIPMENT_PANEL_CLOSE_BTN_HOVER = 352;   // 装备界面关闭按钮悬停状态
    constexpr int EQUIPMENT_PANEL_CLOSE_BTN_PRESSED = 353; // 装备界面关闭按钮按下状态
    constexpr int EQUIPMENT_PANEL_CLOSE_BTN_DISABLED = 354;// 装备界面关闭按钮禁用状态
    constexpr int EQUIPMENT_SLOT_BG_INDEX = 355;        // 装备槽背景图像索引

    // For backward compatibility
    constexpr int EQUIPMENT_UI_BG_INDEX = EQUIPMENT_PANEL_BG_INDEX;
    constexpr int EQUIPMENT_UI_CLOSE_BTN_NORMAL = EQUIPMENT_PANEL_CLOSE_BTN_NORMAL;
    constexpr int EQUIPMENT_UI_CLOSE_BTN_HOVER = EQUIPMENT_PANEL_CLOSE_BTN_HOVER;
    constexpr int EQUIPMENT_UI_CLOSE_BTN_PRESSED = EQUIPMENT_PANEL_CLOSE_BTN_PRESSED;
    constexpr int EQUIPMENT_UI_CLOSE_BTN_DISABLED = EQUIPMENT_PANEL_CLOSE_BTN_DISABLED;

    // 聊天界面
    constexpr int CHAT_UI_BG_INDEX = 350;      // 聊天界面背景图像索引
    constexpr int CHAT_UI_INPUT_BG_INDEX = 351;    // 聊天输入框背景图像索引
    constexpr int CHAT_UI_SEND_BTN_NORMAL = 352;   // 聊天发送按钮普通状态
    constexpr int CHAT_UI_SEND_BTN_HOVER = 353;    // 聊天发送按钮悬停状态
    constexpr int CHAT_UI_SEND_BTN_PRESSED = 354;  // 聊天发送按钮按下状态
    constexpr int CHAT_UI_SEND_BTN_DISABLED = 355; // 聊天发送按钮禁用状态

    // 状态界面
    constexpr int STATUS_UI_BG_INDEX = 380;          // 状态界面背景图像索引
    constexpr int STATUS_UI_CLOSE_BTN_NORMAL = 381;  // 状态界面关闭按钮普通状态
    constexpr int STATUS_UI_CLOSE_BTN_HOVER = 382;   // 状态界面关闭按钮悬停状态
    constexpr int STATUS_UI_CLOSE_BTN_PRESSED = 383; // 状态界面关闭按钮按下状态
    constexpr int STATUS_UI_CLOSE_BTN_DISABLED = 384;// 状态界面关闭按钮禁用状态
    constexpr int MALE_CHARACTER_BASE = 385;         // 男性角色基础图像索引
    constexpr int FEMALE_CHARACTER_BASE = 386;       // 女性角色基础图像索引
    constexpr int TAB_NORMAL_INDEX = 387;            // 标签页普通状态
    constexpr int TAB_SELECTED_INDEX = 388;          // 标签页选中状态

    // 技能面板 (SkillPanel)
    constexpr int SKILL_PANEL_BG_INDEX = 390;          // 技能面板背景图像索引
    constexpr int SKILL_PANEL_CLOSE_BTN_NORMAL = 391;  // 技能面板关闭按钮普通状态
    constexpr int SKILL_PANEL_CLOSE_BTN_HOVER = 392;   // 技能面板关闭按钮悬停状态
    constexpr int SKILL_PANEL_CLOSE_BTN_PRESSED = 393; // 技能面板关闭按钮按下状态
    constexpr int SKILL_PANEL_CLOSE_BTN_DISABLED = 394;// 技能面板关闭按钮禁用状态

    // 对话框 - 基于原始Delphi项目中的DMsgDlg实现
    // 对话框背景图像索引 (原始项目中使用不同大小)
    constexpr int DIALOG_BG_NORMAL_INDEX = 360;     // 普通大小对话框背景 (原始中的DMsgDlg.Image, DialogSize=1)
    constexpr int DIALOG_BG_SMALL_INDEX = 381;      // 小对话框背景 (原始中的DMsgDlg.Image, DialogSize=0)
    constexpr int DIALOG_BG_LARGE_INDEX = 380;      // 大对话框背景 (原始中的DMsgDlg.Image, DialogSize=2)
    constexpr int DIALOG_BG_INDEX = DIALOG_BG_NORMAL_INDEX; // 默认使用普通大小

    // 对话框按钮图像索引
    constexpr int DIALOG_OK_BTN_NORMAL = 361;       // 确定按钮 (原始中的DMsgDlgOk.Image)
    constexpr int DIALOG_YES_BTN_NORMAL = 363;      // 是按钮 (原始中的DMsgDlgYes.Image)
    constexpr int DIALOG_CANCEL_BTN_NORMAL = 365;   // 取消按钮 (原始中的DMsgDlgCancel.Image)
    constexpr int DIALOG_NO_BTN_NORMAL = 367;       // 否按钮 (原始中的DMsgDlgNo.Image)

    // 为了向后兼容保留的旧索引
    constexpr int DIALOG_CLOSE_BTN_NORMAL = 401;    // 对话框关闭按钮普通状态
    constexpr int DIALOG_CLOSE_BTN_HOVER = 402;     // 对话框关闭按钮悬停状态
    constexpr int DIALOG_CLOSE_BTN_PRESSED = 403;   // 对话框关闭按钮按下状态
    constexpr int DIALOG_CLOSE_BTN_DISABLED = 404;  // 对话框关闭按钮禁用状态
    constexpr int DIALOG_OK_BTN_HOVER = 406;        // 对话框确定按钮悬停状态
    constexpr int DIALOG_OK_BTN_PRESSED = 407;      // 对话框确定按钮按下状态
    constexpr int DIALOG_OK_BTN_DISABLED = 408;     // 对话框确定按钮禁用状态
    constexpr int DIALOG_CANCEL_BTN_HOVER = 410;    // 对话框取消按钮悬停状态
    constexpr int DIALOG_CANCEL_BTN_PRESSED = 411;  // 对话框取消按钮按下状态
    constexpr int DIALOG_CANCEL_BTN_DISABLED = 412; // 对话框取消按钮禁用状态

    // 文本输入框
    constexpr int TEXT_INPUT_BG_INDEX = 420;        // 文本输入框背景图像索引

    // 消息对话框图标
    constexpr int ICON_INFORMATION = 430;           // 信息图标
    constexpr int ICON_WARNING = 431;               // 警告图标
    constexpr int ICON_ERROR = 432;                 // 错误图标
    constexpr int ICON_QUESTION = 433;              // 问题图标
}
