// LoginServer.cpp
#include "LoginServer.h"
#include "AccountDB.h"
#include "MsgServerManager.h"
#include "MonitorServer.h"
#include "../Common/Logger.h"
#include "../Protocol/MessageConverter.h"
#include <filesystem>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cstring>

namespace MirServer {

// GateConnection implementation
GateConnection::GateConnection(std::shared_ptr<Network::ClientConnection> conn) 
    : connection(conn) {
    lastKeepAlive = std::chrono::steady_clock::now();
}

void GateConnection::AddUser(const std::string& sockIndex, std::shared_ptr<UserSession> user) {
    userList[sockIndex] = user;
}

void GateConnection::RemoveUser(const std::string& sockIndex) {
    userList.erase(sockIndex);
}

std::shared_ptr<UserSession> GateConnection::GetUser(const std::string& sockIndex) {
    auto it = userList.find(sockIndex);
    if (it != userList.end()) {
        return it->second;
    }
    return nullptr;
}

// LoginServerConfig implementation
bool LoginServerConfig::LoadConfig(const std::string& filename) {
    std::ifstream file(filename);
    if (!file) {
        Logger::Error("Failed to open config file: " + filename);
        return false;
    }
    
    std::string line;
    while (std::getline(file, line)) {
        // Remove comments
        size_t commentPos = line.find(';');
        if (commentPos != std::string::npos) {
            line = line.substr(0, commentPos);
        }
        
        // Trim whitespace
        line.erase(0, line.find_first_not_of(" \t"));
        line.erase(line.find_last_not_of(" \t") + 1);
        
        if (line.empty()) continue;
        
        // Parse key=value
        size_t equalPos = line.find('=');
        if (equalPos != std::string::npos) {
            std::string key = line.substr(0, equalPos);
            std::string value = line.substr(equalPos + 1);
            
            // Trim key and value
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);
            
            // Set configuration values
            if (key == "DBServer") sDBServer = value;
            else if (key == "DBSPort") nDBSPort = std::stoi(value);
            else if (key == "IdDir") sIdDir = value;
            else if (key == "GateAddr") sGateAddr = value;
            else if (key == "GatePort") nGatePort = std::stoi(value);
            else if (key == "ServerAddr") sServerAddr = value;
            else if (key == "ServerPort") nServerPort = std::stoi(value);
            else if (key == "MonAddr") sMonAddr = value;
            else if (key == "MonPort") nMonPort = std::stoi(value);
            else if (key == "EnableMakingID") boEnableMakingID = (value == "1" || value == "true");
            else if (key == "EnableGetbackPassword") boEnableGetbackPassword = (value == "1" || value == "true");
            else if (key == "UnLockAccount") boUnLockAccount = (value == "1" || value == "true");
            else if (key == "UnLockAccountTime") dwUnLockAccountTime = std::stoul(value);
            else if (key == "ShowDetailMsg") boShowDetailMsg = (value == "1" || value == "true");
        }
    }
    
    return true;
}

bool LoginServerConfig::LoadRouteTable(const std::string& filename) {
    std::ifstream file(filename);
    if (!file) {
        Logger::Warning("Route table file not found: " + filename);
        return false;
    }
    
    routes.clear();
    serverNameList.clear();
    
    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == ';') continue;
        
        std::istringstream iss(line);
        ServerRoute route;
        
        iss >> route.sServerName >> route.sTitle >> route.sRemoteAddr >> route.sPublicAddr;
        
        // Read gate information
        std::string gateStr;
        while (iss >> gateStr) {
            ServerRoute::GateInfo gate;
            
            // Check if gate is disabled (starts with *)
            if (gateStr[0] == '*') {
                gate.boEnable = false;
                gateStr = gateStr.substr(1);
            } else {
                gate.boEnable = true;
            }
            
            // Parse IP:Port
            size_t colonPos = gateStr.find(':');
            if (colonPos != std::string::npos) {
                gate.sIPaddr = gateStr.substr(0, colonPos);
                gate.nPort = std::stoi(gateStr.substr(colonPos + 1));
                route.gates.push_back(gate);
            }
        }
        
        if (!route.sServerName.empty()) {
            routes.push_back(route);
            serverNameList.push_back(route.sServerName);
        }
    }
    
    Logger::Info("Loaded " + std::to_string(routes.size()) + " server routes");
    return true;
}

bool LoginServerConfig::LoadAccountCost(const std::string& filename) {
    // This would load account cost information from file
    // For now, we'll leave it empty as it's optional
    return true;
}

// LoginServer implementation
LoginServer::LoginServer() {
    m_accountDB = std::make_unique<AccountDB>();
    m_msgServerManager = std::make_unique<MsgServerManager>();
}

LoginServer::~LoginServer() {
    Stop();
}

bool LoginServer::Initialize(const std::string& configFile) {
    // Load configuration
    if (!m_config.LoadConfig(configFile)) {
        Logger::Error("Failed to load configuration");
        return false;
    }
    
    // Load route table
    std::string routeFile = "./!addrtable.txt";
    m_config.LoadRouteTable(routeFile);
    
    // Initialize account database
    if (!m_accountDB->Initialize(m_config.sIdDir)) {
        Logger::Error("Failed to initialize account database");
        return false;
    }
    
    // Initialize message server manager
    if (!m_msgServerManager->Initialize(m_config.sServerAddr, m_config.nServerPort)) {
        Logger::Error("Failed to initialize MsgServerManager");
        return false;
    }
    
    // Load server addresses and limits
    m_msgServerManager->LoadServerAddr("./!ServerAddr.txt");
    m_msgServerManager->LoadUserLimit("./!UserLimit.txt");
    
    // Create monitor server
    m_monitorServer = std::make_unique<MonitorServer>(m_msgServerManager.get());
    if (!m_monitorServer->Initialize(m_config.sMonAddr, m_config.nMonPort)) {
        Logger::Error("Failed to initialize MonitorServer");
        return false;
    }
    
    // Create network manager
    m_network = std::make_unique<Network::NetworkManager>();
    
    Logger::Info("LoginServer initialized successfully");
    return true;
}

bool LoginServer::Start() {
    if (m_running) {
        return true;
    }
    
    // Open account database
    if (!m_accountDB->Open()) {
        Logger::Error("Failed to open account database");
        return false;
    }
    
    // Start message server manager
    if (!m_msgServerManager->Start()) {
        Logger::Error("Failed to start MsgServerManager");
        return false;
    }
    
    // Start monitor server
    if (!m_monitorServer->Start()) {
        Logger::Error("Failed to start MonitorServer");
        return false;
    }
    
    // Start network service
    if (!m_network->Initialize()) {
        Logger::Error("Failed to initialize network");
        return false;
    }
    
    if (!m_network->StartServer(m_config.nGatePort)) {
        Logger::Error("Failed to start network server");
        return false;
    }
    
    m_network->SetPacketHandler(std::shared_ptr<Network::PacketHandler>(this, [](Network::PacketHandler*){}));
    
    m_running = true;
    
    // Start processing threads
    m_processThread = std::thread(&LoginServer::ProcessLoop, this);
    m_cleanupThread = std::thread(&LoginServer::CleanupLoop, this);
    
    Logger::Info("LoginServer started on " + m_config.sGateAddr + ":" + std::to_string(m_config.nGatePort));
    return true;
}

void LoginServer::Stop() {
    if (!m_running) {
        return;
    }
    
    m_running = false;
    
    // Stop monitor server
    if (m_monitorServer) {
        m_monitorServer->Stop();
    }
    
    // Stop message server manager
    if (m_msgServerManager) {
        m_msgServerManager->Stop();
    }
    
    // Stop network
    if (m_network) {
        m_network->StopServer();
    }
    
    // Wait for threads
    if (m_processThread.joinable()) {
        m_processThread.join();
    }
    if (m_cleanupThread.joinable()) {
        m_cleanupThread.join();
    }
    
    // Close database
    if (m_accountDB) {
        m_accountDB->Close();
    }
    
    Logger::Info("LoginServer stopped");
}

void LoginServer::Run() {
    // Main loop - just wait until stopped
    while (m_running) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

// Network event handlers
void LoginServer::OnClientConnected(std::shared_ptr<Network::ClientConnection> connection) {
    std::lock_guard<std::mutex> lock(m_gateMutex);
    
    auto gate = std::make_shared<GateConnection>(connection);
    gate->sIPaddr = connection->GetRemoteIP();
    
    m_gateList[connection] = gate;
    
    Logger::Info("Gate connected from: " + gate->sIPaddr);
}

void LoginServer::OnClientDisconnected(std::shared_ptr<Network::ClientConnection> connection) {
    std::lock_guard<std::mutex> lock(m_gateMutex);
    
    auto it = m_gateList.find(connection);
    if (it != m_gateList.end()) {
        auto gate = it->second;
        
        // Remove all users from this gate
        for (auto& userPair : gate->userList) {
            auto user = userPair.second;
            if (!user->boSelServer) {
                SessionDel(user->nSessionID);
            }
        }
        
        m_gateList.erase(it);
        Logger::Info("Gate disconnected: " + gate->sIPaddr);
    }
}

void LoginServer::HandlePacket(std::shared_ptr<Network::ClientConnection> connection,
                               const Protocol::PacketHeader& header, 
                               const uint8_t* data, 
                               size_t dataSize) {
    std::lock_guard<std::mutex> lock(m_gateMutex);
    
    auto it = m_gateList.find(connection);
    if (it != m_gateList.end()) {
        auto gate = it->second;
        
        // Convert packet to string format expected by gate protocol
        std::string msg(reinterpret_cast<const char*>(data), dataSize);
        gate->sReceiveMsg += msg;
        
        // Process accumulated messages
        DecodeGateData(gate);
    }
}

// Wrapper for compatibility
void LoginServer::OnPacketReceived(std::shared_ptr<Network::ClientConnection> connection,
                                  const Network::GamePacket& packet) {
    // Extract header and data from GamePacket
    if (packet.data.size() >= sizeof(Protocol::PacketHeader)) {
        Protocol::PacketHeader header;
        std::memcpy(&header, packet.data.data(), sizeof(Protocol::PacketHeader));
        
        const uint8_t* data = packet.data.data() + sizeof(Protocol::PacketHeader);
        size_t dataSize = packet.data.size() - sizeof(Protocol::PacketHeader);
        
        HandlePacket(connection, header, data, dataSize);
    }
}

// Gate message processing
void LoginServer::ProcessGateMessage(std::shared_ptr<GateConnection> gate, const std::string& msg) {
    if (msg.size() < 2) return;
    
    char msgType = msg[0];
    std::string data = msg.substr(1);
    
    switch (msgType) {
        case '+': {  // Open user
            // Format: +sockindex/userip/gateip
            size_t pos1 = data.find('/');
            if (pos1 != std::string::npos) {
                std::string sockIndex = data.substr(0, pos1);
                size_t pos2 = data.find('/', pos1 + 1);
                if (pos2 != std::string::npos) {
                    std::string userIP = data.substr(pos1 + 1, pos2 - pos1 - 1);
                    std::string gateIP = data.substr(pos2 + 1);
                    
                    // Create new user session
                    auto user = std::make_shared<UserSession>(sockIndex);
                    user->sUserIPaddr = userIP;
                    user->sGateIPaddr = gateIP;
                    user->gate = gate;
                    
                    gate->AddUser(sockIndex, user);
                    
                    if (m_config.boShowDetailMsg) {
                        Logger::Debug("Open user: " + sockIndex + " from " + userIP);
                    }
                }
            }
            break;
        }
        
        case '-': {  // Close user
            // Format: -sockindex
            std::string sockIndex = data;
            auto user = gate->GetUser(sockIndex);
            if (user) {
                if (!user->boSelServer) {
                    SessionDel(user->nSessionID);
                }
                gate->RemoveUser(sockIndex);
                
                if (m_config.boShowDetailMsg) {
                    Logger::Debug("Close user: " + sockIndex);
                }
            }
            break;
        }
        
        case 'D': {  // User data
            // Format: Dsockindex/data
            size_t pos = data.find('/');
            if (pos != std::string::npos) {
                std::string sockIndex = data.substr(0, pos);
                std::string userData = data.substr(pos + 1);
                
                auto user = gate->GetUser(sockIndex);
                if (user) {
                    user->sReceiveMsg += userData;
                    DecodeUserData(user);
                }
            }
            break;
        }
        
        case 'K': {  // Keep alive response
            gate->lastKeepAlive = std::chrono::steady_clock::now();
            break;
        }
    }
}

void LoginServer::DecodeGateData(std::shared_ptr<GateConnection> gate) {
    // Process messages delimited by $
    while (true) {
        size_t pos = gate->sReceiveMsg.find('$');
        if (pos == std::string::npos) break;
        
        std::string msg = gate->sReceiveMsg.substr(0, pos);
        gate->sReceiveMsg = gate->sReceiveMsg.substr(pos + 1);
        
        if (!msg.empty()) {
            ProcessGateMessage(gate, msg);
        }
    }
}

void LoginServer::DecodeUserData(std::shared_ptr<UserSession> user) {
    // Process messages in format #...!
    while (true) {
        size_t startPos = user->sReceiveMsg.find('#');
        if (startPos == std::string::npos) break;
        
        size_t endPos = user->sReceiveMsg.find('!', startPos);
        if (endPos == std::string::npos) break;
        
        std::string msg = user->sReceiveMsg.substr(startPos + 1, endPos - startPos - 1);
        user->sReceiveMsg = user->sReceiveMsg.substr(endPos + 1);
        
        if (msg.length() >= DEFBLOCKSIZE) {
            ProcessUserMessage(user, msg);
        }
    }
}

// Process and cleanup loops
void LoginServer::ProcessLoop() {
    while (m_running) {
        ProcessGates();
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

void LoginServer::CleanupLoop() {
    auto lastCleanup = std::chrono::steady_clock::now();
    auto lastCountLog = std::chrono::steady_clock::now();
    
    while (m_running) {
        auto now = std::chrono::steady_clock::now();
        
        // Cleanup sessions every 5 seconds
        if (std::chrono::duration_cast<std::chrono::seconds>(now - lastCleanup).count() >= 5) {
            CleanupSessions();
            lastCleanup = now;
        }
        
        // Save count log every minute
        if (std::chrono::duration_cast<std::chrono::minutes>(now - lastCountLog).count() >= 1) {
            SaveCountLog();
            lastCountLog = now;
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

void LoginServer::ProcessGates() {
    std::lock_guard<std::mutex> lock(m_gateMutex);
    
    auto now = std::chrono::steady_clock::now();
    
    for (auto& gatePair : m_gateList) {
        auto gate = gatePair.second;
        
        // Send keep alive packet every 10 seconds
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - gate->lastKeepAlive).count();
        if (elapsed >= 10) {
            SendKeepAlivePacket(gate);
        }
    }
}

void LoginServer::CleanupSessions() {
    // Clean up kicked sessions
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    auto now = std::chrono::steady_clock::now();
    
    for (auto it = m_sessionList.begin(); it != m_sessionList.end(); ) {
        auto session = it->second;
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - session->lastActivity).count();
        
        // Remove sessions inactive for more than 5 minutes
        if (elapsed > 300) {
            it = m_sessionList.erase(it);
        } else {
            ++it;
        }
    }
}

void LoginServer::SaveCountLog() {
    // Save server statistics
    // This would write to a log file with current online counts, etc.
}

// Session management functions will be implemented next...

} // namespace MirServer 