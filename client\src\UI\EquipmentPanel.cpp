#include "EquipmentPanel.h"
#include "Button.h"
#include "Label.h"
#include "Font.h"
#include "UIConstants.h"
#include "UILayout.h"
#include "ResourcePaths.h"
#include <iostream>
#include <sstream>

EquipmentPanel::EquipmentPanel(int x, int y, int width, int height, std::shared_ptr<Player> player)
    : UIControl(x, y, width, height, "EquipmentPanel")
    , m_player(player)
    , m_textColor({255, 255, 255, 255})
    , m_textShadowColor({0, 0, 0, 255})
    , m_slotColor({50, 50, 50, 200})
    , m_selectedSlotColor({100, 100, 255, 200})
    , m_borderColor({255, 255, 255, 255})
    , m_slotTypeColor({200, 200, 100, 150})
    , m_hoverColor({150, 150, 255, 150})
    , m_slotSize(UILayout::EquipmentPanel::SLOT_SIZE)
    , m_selectedSlot(-1)
    , m_hoverSlot(-1)
    , m_isDragging(false)
    , m_draggedSlot(Inventory::EquipmentSlot::WEAPON)
    , m_dragX(0)
    , m_dragY(0)
    , m_resourceFile(ResourcePaths::ITEMS)
    , m_showItemInfo(false)
    , m_infoItemSlot(-1)
    , m_font(nullptr)
    , m_onItemUnequipped(nullptr)
    , m_onItemEquipped(nullptr)
    , m_onItemInfoRequested(nullptr)
{
    // Create UI controls
    CreateControls();

    // Hide by default
    SetVisible(false);
}

EquipmentPanel::~EquipmentPanel()
{
}

void EquipmentPanel::CreateControls()
{
    // Set resource file for UI elements
    SetResourceFile(ResourcePaths::INTERFACE);

    // Create background (using normal image index)
    SetNormalImageIndex(UIConstants::EQUIPMENT_PANEL_BG_INDEX);

    // Create title label
    m_titleLabel = std::make_shared<Label>(
        UILayout::EquipmentPanel::TITLE_X_OFFSET,
        UILayout::EquipmentPanel::TITLE_Y_OFFSET,
        100,
        20,
        "Equipment"
    );
    m_titleLabel->SetTextColor(m_textColor);
    AddChild(m_titleLabel);

    // Create close button
    m_closeButton = std::make_shared<Button>(
        m_width + UILayout::EquipmentPanel::CLOSE_BTN_X_OFFSET,
        UILayout::EquipmentPanel::CLOSE_BTN_Y_OFFSET,
        UILayout::EquipmentPanel::CLOSE_BTN_WIDTH,
        UILayout::EquipmentPanel::CLOSE_BTN_HEIGHT,
        "X"
    );
    m_closeButton->SetResourceFile(ResourcePaths::INTERFACE);
    m_closeButton->SetResourceIndices(
        ResourcePaths::INTERFACE,
        UIConstants::EQUIPMENT_PANEL_CLOSE_BTN_NORMAL,
        UIConstants::EQUIPMENT_PANEL_CLOSE_BTN_HOVER,
        UIConstants::EQUIPMENT_PANEL_CLOSE_BTN_PRESSED,
        UIConstants::EQUIPMENT_PANEL_CLOSE_BTN_DISABLED
    );
    m_closeButton->SetOnClick([this]() { OnCloseButtonClick(nullptr); });
    AddChild(m_closeButton);
}

void EquipmentPanel::Update(int deltaTime)
{
    // Update base class
    UIControl::Update(deltaTime);
}

void EquipmentPanel::Render(SDL_Renderer* renderer)
{
    // Skip if not visible
    if (!m_visible) {
        return;
    }

    // Render background and controls
    UIControl::Render(renderer);

    // Render character model with equipment
    RenderCharacterModel(renderer);

    // Render equipment slots
    if (m_player && m_player->GetInventory()) {
        const auto& inventory = m_player->GetInventory();

        // Render each equipment slot
        for (int i = 0; i < Inventory::MAX_EQUIPMENT_SLOTS; i++) {
            Inventory::EquipmentSlot slot = static_cast<Inventory::EquipmentSlot>(i);
            int slotX, slotY;
            GetSlotPosition(slot, slotX, slotY);

            // Render slot background
            SDL_Rect slotRect = {slotX, slotY, m_slotSize, m_slotSize};
            if (i == m_selectedSlot) {
                SDL_SetRenderDrawColor(renderer, m_selectedSlotColor.r, m_selectedSlotColor.g, m_selectedSlotColor.b, m_selectedSlotColor.a);
            } else if (i == m_hoverSlot) {
                SDL_SetRenderDrawColor(renderer, m_hoverColor.r, m_hoverColor.g, m_hoverColor.b, m_hoverColor.a);
            } else {
                SDL_SetRenderDrawColor(renderer, m_slotColor.r, m_slotColor.g, m_slotColor.b, m_slotColor.a);
            }
            SDL_RenderFillRect(renderer, &slotRect);

            // Render slot type indicator (small colored rectangle at the corner)
            SDL_Rect typeRect = {slotX + 2, slotY + 2, 8, 8};
            ItemType slotType = inventory->EquipmentSlotToItemType(slot);
            // 根据装备类型设置不同的颜色
            switch (slotType) {
                case ItemType::WEAPON:
                    SDL_SetRenderDrawColor(renderer, 255, 0, 0, 150); // 红色 - 武器
                    break;
                case ItemType::ARMOR:
                case ItemType::HELMET:
                case ItemType::SHOES:
                    SDL_SetRenderDrawColor(renderer, 0, 0, 255, 150); // 蓝色 - 防具
                    break;
                case ItemType::NECKLACE:
                case ItemType::RING:
                case ItemType::BRACELET:
                    SDL_SetRenderDrawColor(renderer, 255, 255, 0, 150); // 黄色 - 饰品
                    break;
                default:
                    SDL_SetRenderDrawColor(renderer, 0, 255, 0, 150); // 绿色 - 其他
                    break;
            }
            SDL_RenderFillRect(renderer, &typeRect);

            // Render slot border
            SDL_SetRenderDrawColor(renderer, m_borderColor.r, m_borderColor.g, m_borderColor.b, m_borderColor.a);
            SDL_RenderDrawRect(renderer, &slotRect);

            // Render item if there is one in this slot
            auto item = inventory->GetEquippedItem(slot);
            if (item && (slot != m_draggedSlot || !m_isDragging)) {
                RenderItem(renderer, *item, slotX, slotY, m_slotSize);
            }
        }
    }

    // Render dragged item
    if (m_isDragging && m_player && m_player->GetInventory()) {
        auto item = m_player->GetInventory()->GetEquippedItem(m_draggedSlot);
        if (item) {
            RenderItem(renderer, *item, m_dragX - m_slotSize / 2, m_dragY - m_slotSize / 2, m_slotSize);
        }
    }

    // 渲染装备属性信息
    if (m_showItemInfo && m_infoItemSlot >= 0 && m_infoItemSlot < Inventory::MAX_EQUIPMENT_SLOTS) {
        if (m_player && m_player->GetInventory()) {
            Inventory::EquipmentSlot slot = static_cast<Inventory::EquipmentSlot>(m_infoItemSlot);
            auto item = m_player->GetInventory()->GetEquippedItem(slot);
            if (item) {
                // 在鼠标位置附近显示装备信息
                RenderItemInfo(renderer, *item, m_dragX + 20, m_dragY);
            }
        }
    }
}

void EquipmentPanel::GetSlotPosition(Inventory::EquipmentSlot slot, int& x, int& y) const
{
    // 使用UILayout中定义的装备槽位置常量
    // 这些位置与原始Delphi项目保持一致
    switch (slot) {
        case Inventory::EquipmentSlot::WEAPON:
            x = m_x + UILayout::EquipmentPanel::WEAPON_SLOT_X;
            y = m_y + UILayout::EquipmentPanel::WEAPON_SLOT_Y;
            break;
        case Inventory::EquipmentSlot::ARMOR:
            x = m_x + UILayout::EquipmentPanel::ARMOR_SLOT_X;
            y = m_y + UILayout::EquipmentPanel::ARMOR_SLOT_Y;
            break;
        case Inventory::EquipmentSlot::HELMET:
            x = m_x + UILayout::EquipmentPanel::HELMET_SLOT_X;
            y = m_y + UILayout::EquipmentPanel::HELMET_SLOT_Y;
            break;
        case Inventory::EquipmentSlot::NECKLACE:
            x = m_x + UILayout::EquipmentPanel::NECKLACE_SLOT_X;
            y = m_y + UILayout::EquipmentPanel::NECKLACE_SLOT_Y;
            break;
        case Inventory::EquipmentSlot::LEFT_HAND:
            x = m_x + UILayout::EquipmentPanel::LEFT_HAND_SLOT_X;
            y = m_y + UILayout::EquipmentPanel::LEFT_HAND_SLOT_Y;
            break;
        case Inventory::EquipmentSlot::RIGHT_HAND:
            x = m_x + UILayout::EquipmentPanel::RIGHT_HAND_SLOT_X;
            y = m_y + UILayout::EquipmentPanel::RIGHT_HAND_SLOT_Y;
            break;
        case Inventory::EquipmentSlot::LEFT_BRACELET:
            x = m_x + UILayout::EquipmentPanel::LEFT_BRACELET_SLOT_X;
            y = m_y + UILayout::EquipmentPanel::LEFT_BRACELET_SLOT_Y;
            break;
        case Inventory::EquipmentSlot::RIGHT_BRACELET:
            x = m_x + UILayout::EquipmentPanel::RIGHT_BRACELET_SLOT_X;
            y = m_y + UILayout::EquipmentPanel::RIGHT_BRACELET_SLOT_Y;
            break;
        case Inventory::EquipmentSlot::SHOES:
            x = m_x + UILayout::EquipmentPanel::SHOES_SLOT_X;
            y = m_y + UILayout::EquipmentPanel::SHOES_SLOT_Y;
            break;
        case Inventory::EquipmentSlot::BELT:
            x = m_x + UILayout::EquipmentPanel::BELT_SLOT_X;
            y = m_y + UILayout::EquipmentPanel::BELT_SLOT_Y;
            break;
        case Inventory::EquipmentSlot::STONE:
            x = m_x + UILayout::EquipmentPanel::STONE_SLOT_X;
            y = m_y + UILayout::EquipmentPanel::STONE_SLOT_Y;
            break;
        case Inventory::EquipmentSlot::TORCH:
            x = m_x + UILayout::EquipmentPanel::TORCH_SLOT_X;
            y = m_y + UILayout::EquipmentPanel::TORCH_SLOT_Y;
            break;
        case Inventory::EquipmentSlot::BOOK:
            x = m_x + UILayout::EquipmentPanel::BOOK_SLOT_X;
            y = m_y + UILayout::EquipmentPanel::BOOK_SLOT_Y;
            break;
        default:
            x = m_x + UILayout::EquipmentPanel::CHARACTER_MODEL_X_OFFSET;
            y = m_y + UILayout::EquipmentPanel::CHARACTER_MODEL_Y_OFFSET;
            break;
    }
}

void EquipmentPanel::RenderItem(SDL_Renderer* renderer, const Item& item, int x, int y, int size)
{
    // Get the item image from the WIL manager
    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(m_resourceFile, item.GetLook());
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {x, y, size, size};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }
}

void EquipmentPanel::RenderCharacterModel(SDL_Renderer* renderer)
{
    if (!m_player) {
        return;
    }

    // Character model position - 使用UILayout中定义的常量
    int modelX = m_x + UILayout::EquipmentPanel::CHARACTER_MODEL_X_OFFSET;
    int modelY = m_y + UILayout::EquipmentPanel::CHARACTER_MODEL_Y_OFFSET;

    // Render base character model
    int baseImageIndex = m_player->GetSex() == 0 ? UIConstants::MALE_CHARACTER_BASE : UIConstants::FEMALE_CHARACTER_BASE;

    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(ResourcePaths::INTERFACE, baseImageIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {modelX - surface->w / 2, modelY - surface->h / 2, surface->w, surface->h};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }

    // Render equipped items on character model
    if (m_player->GetInventory()) {
        const auto& inventory = m_player->GetInventory();

        // Render each equipped item
        for (int i = 0; i < Inventory::MAX_EQUIPMENT_SLOTS; i++) {
            Inventory::EquipmentSlot slot = static_cast<Inventory::EquipmentSlot>(i);
            auto item = inventory->GetEquippedItem(slot);

            if (item) {
                // Get the item's appearance on the character
                int lookIndex = item->GetLook();

                // Adjust look index based on player sex for certain items
                if (slot == Inventory::EquipmentSlot::ARMOR || slot == Inventory::EquipmentSlot::HELMET) {
                    // This is a placeholder - in the real implementation, you would adjust the look index
                    // based on the player's sex and the item type
                }

                if (m_wilManager) {
                    SDL_Surface* surface = m_wilManager->GetSurface(ResourcePaths::ITEMS, lookIndex);
                    if (surface) {
                        // Create a temporary texture from the surface
                        SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
                        if (texture) {
                            // Render the texture
                            SDL_Rect destRect = {modelX - surface->w / 2, modelY - surface->h / 2, surface->w, surface->h};
                            SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                            // Free the texture
                            SDL_DestroyTexture(texture);
                        }
                    }
                }
            }
        }
    }
}

bool EquipmentPanel::HandleMouseButton(int button, bool pressed, int x, int y)
{
    // Skip if not visible
    if (!m_visible) {
        return false;
    }

    // Check if a child control handled the event
    if (UIControl::HandleMouseButton(button, pressed, x, y)) {
        return true;
    }

    // 获取鼠标位置对应的装备槽
    int slotIndex = GetSlotAt(x, y);

    // 处理左键点击
    if (button == SDL_BUTTON_LEFT) {
        if (pressed) {
            // 如果点击在装备槽上
            if (slotIndex != -1 && m_player && m_player->GetInventory()) {
                Inventory::EquipmentSlot slot = static_cast<Inventory::EquipmentSlot>(slotIndex);

                // 选中该槽
                m_selectedSlot = slotIndex;

                // 如果槽中有物品，开始拖动
                auto item = m_player->GetInventory()->GetEquippedItem(slot);
                if (item) {
                    m_isDragging = true;
                    m_draggedSlot = slot;
                    m_dragX = x;
                    m_dragY = y;

                    // 显示物品信息
                    m_showItemInfo = true;
                    m_infoItemSlot = slotIndex;

                    // 通知物品信息请求
                    if (m_onItemInfoRequested) {
                        m_onItemInfoRequested(item);
                    }
                }

                return true;
            }
        } else {
            // 鼠标按钮释放
            if (m_isDragging) {
                // 处理物品放下
                // 检查是否放在了另一个装备槽上
                int targetSlotIndex = GetSlotAt(x, y);
                if (targetSlotIndex != -1 && targetSlotIndex != m_selectedSlot) {
                    // 不允许直接在装备槽之间移动物品
                    // 这里可以添加特殊逻辑，例如交换左右手的武器等
                } else {
                    // 放在了非装备槽区域，尝试卸下装备
                    if (m_player && m_player->GetInventory()) {
                        // 检查背包是否有空间
                        int emptySlot = m_player->GetInventory()->FindEmptySlot();
                        if (emptySlot != -1) {
                            // 卸下装备到背包
                            m_player->GetInventory()->UnequipItem(m_draggedSlot);

                            // 通知卸下装备
                            if (m_onItemUnequipped) {
                                m_onItemUnequipped(m_draggedSlot);
                            }
                        }
                    }
                }

                // 停止拖动
                m_isDragging = false;
                m_showItemInfo = false;
                return true;
            }
        }
    }
    // 处理右键点击
    else if (button == SDL_BUTTON_RIGHT) {
        if (pressed) {
            // 如果点击在装备槽上
            if (slotIndex != -1 && m_player && m_player->GetInventory()) {
                Inventory::EquipmentSlot slot = static_cast<Inventory::EquipmentSlot>(slotIndex);

                // 如果槽中有物品，显示物品信息
                auto item = m_player->GetInventory()->GetEquippedItem(slot);
                if (item) {
                    m_showItemInfo = true;
                    m_infoItemSlot = slotIndex;

                    // 通知物品信息请求
                    if (m_onItemInfoRequested) {
                        m_onItemInfoRequested(item);
                    }
                }

                return true;
            }
        } else {
            // 右键释放，如果不是在拖动中，隐藏物品信息
            if (!m_isDragging) {
                m_showItemInfo = false;
            }
        }
    }

    return false;
}

bool EquipmentPanel::HandleMouseMotion(int x, int y, int relX, int relY)
{
    // Skip if not visible
    if (!m_visible) {
        return false;
    }

    // Check if a child control handled the event
    if (UIControl::HandleMouseMotion(x, y)) {
        return true;
    }

    // 更新拖动位置
    if (m_isDragging) {
        m_dragX = x;
        m_dragY = y;
        return true;
    }

    // 更新悬停槽
    int oldHoverSlot = m_hoverSlot;
    m_hoverSlot = GetSlotAt(x, y);

    // 如果悬停槽改变，需要更新显示
    if (oldHoverSlot != m_hoverSlot) {
        // 如果鼠标悬停在装备槽上，显示物品信息
        if (m_hoverSlot != -1 && m_player && m_player->GetInventory()) {
            Inventory::EquipmentSlot slot = static_cast<Inventory::EquipmentSlot>(m_hoverSlot);
            auto item = m_player->GetInventory()->GetEquippedItem(slot);
            if (item) {
                m_showItemInfo = true;
                m_infoItemSlot = m_hoverSlot;

                // 通知物品信息请求
                if (m_onItemInfoRequested) {
                    m_onItemInfoRequested(item);
                }
            } else {
                m_showItemInfo = false;
            }
        } else {
            // 如果不是在拖动中，且鼠标不在装备槽上，隐藏物品信息
            if (!m_isDragging) {
                m_showItemInfo = false;
            }
        }

        return true;
    }

    return false;
}

bool EquipmentPanel::HandleKey(SDL_Keycode key, bool pressed)
{
    // Skip if not visible
    if (!m_visible) {
        return false;
    }

    // Check if a child control handled the event
    if (UIControl::HandleKey(key, pressed)) {
        return true;
    }

    // Handle escape key to close the UI
    if (key == SDLK_ESCAPE && pressed) {
        Hide();
        return true;
    }

    return false;
}

void EquipmentPanel::Show()
{
    SetVisible(true);
    RefreshEquipment();
}

void EquipmentPanel::Hide()
{
    SetVisible(false);
    m_isDragging = false;
    m_showItemInfo = false;
    m_selectedSlot = -1;
    m_hoverSlot = -1;
}

void EquipmentPanel::RefreshEquipment()
{
    // Nothing to do here, equipment is rendered directly from the player's inventory
}

void EquipmentPanel::OnCloseButtonClick(UIControl* control)
{
    Hide();
}

void EquipmentPanel::RenderItemInfo(SDL_Renderer* renderer, const Item& item, int x, int y)
{
    // 创建信息面板背景
    const int infoWidth = 200;
    const int lineHeight = 20;

    // 计算信息面板高度（根据显示的信息行数）
    int infoHeight = 10 + lineHeight * 8; // 标题 + 7行属性信息 + 底部边距

    // 确保信息面板不会超出屏幕边界
    if (x + infoWidth > UILayout::SCREEN_WIDTH) {
        x = UILayout::SCREEN_WIDTH - infoWidth - 10;
    }
    if (y + infoHeight > UILayout::SCREEN_HEIGHT) {
        y = UILayout::SCREEN_HEIGHT - infoHeight - 10;
    }

    // 绘制信息面板背景
    SDL_Rect infoRect = {x, y, infoWidth, infoHeight};
    SDL_SetRenderDrawColor(renderer, 0, 0, 0, 220);
    SDL_RenderFillRect(renderer, &infoRect);

    // 绘制信息面板边框
    SDL_SetRenderDrawColor(renderer, m_borderColor.r, m_borderColor.g, m_borderColor.b, m_borderColor.a);
    SDL_RenderDrawRect(renderer, &infoRect);

    // 如果没有字体，无法渲染文本
    if (!m_font) {
        return;
    }

    // 渲染物品名称（标题）
    SDL_Color yellowColor = {255, 255, 0, 255}; // 黄色
    std::string itemName = item.GetName();
    if (item.IsIdentified()) {
        // 如果物品已鉴定，显示完整名称
        m_font->RenderText(renderer, itemName, x + 10, y + 10, yellowColor);
    } else {
        // 如果物品未鉴定，显示"未鉴定的 xxx"
        m_font->RenderText(renderer, "未鉴定的 " + itemName, x + 10, y + 10, yellowColor);
    }

    // 渲染物品属性
    int textY = y + 10 + lineHeight;

    // 物品类型
    std::string typeStr = "类型: ";
    switch (item.GetType()) {
        case ItemType::WEAPON: typeStr += "武器"; break;
        case ItemType::ARMOR: typeStr += "盔甲"; break;
        case ItemType::HELMET: typeStr += "头盔"; break;
        case ItemType::NECKLACE: typeStr += "项链"; break;
        case ItemType::RING: typeStr += "戒指"; break;
        case ItemType::BRACELET: typeStr += "手镯"; break;
        case ItemType::SHOES: typeStr += "鞋子"; break;
        case ItemType::BELT: typeStr += "腰带"; break;
        case ItemType::STONE: typeStr += "宝石"; break;
        case ItemType::TORCH: typeStr += "火把"; break;
        case ItemType::BOOK: typeStr += "书籍"; break;
        default: typeStr += "未知"; break;
    }
    m_font->RenderText(renderer, typeStr, x + 10, textY, m_textColor);
    textY += lineHeight;

    // 耐久度
    if (item.GetMaxDurability() > 0) {
        std::stringstream ss;
        ss << "耐久度: " << item.GetDurability() << "/" << item.GetMaxDurability();
        m_font->RenderText(renderer, ss.str(), x + 10, textY, m_textColor);
        textY += lineHeight;
    }

    // 攻击力
    if (item.GetAttackPower() > 0) {
        std::stringstream ss;
        ss << "攻击力: +" << item.GetAttackPower();
        m_font->RenderText(renderer, ss.str(), x + 10, textY, m_textColor);
        textY += lineHeight;
    }

    // 防御力
    if (item.GetDefense() > 0) {
        std::stringstream ss;
        ss << "防御力: +" << item.GetDefense();
        m_font->RenderText(renderer, ss.str(), x + 10, textY, m_textColor);
        textY += lineHeight;
    }

    // 魔法攻击
    if (item.GetMagicPower() > 0) {
        std::stringstream ss;
        ss << "魔法攻击: +" << item.GetMagicPower();
        m_font->RenderText(renderer, ss.str(), x + 10, textY, m_textColor);
        textY += lineHeight;
    }

    // 魔法防御
    if (item.GetMagicDefense() > 0) {
        std::stringstream ss;
        ss << "魔法防御: +" << item.GetMagicDefense();
        m_font->RenderText(renderer, ss.str(), x + 10, textY, m_textColor);
        textY += lineHeight;
    }

    // 重量
    std::stringstream ss;
    ss << "重量: " << item.GetWeight();
    m_font->RenderText(renderer, ss.str(), x + 10, textY, m_textColor);
}

int EquipmentPanel::GetSlotAt(int x, int y) const
{
    if (!m_visible) {
        return -1;
    }

    // 检查每个装备槽
    for (int i = 0; i < Inventory::MAX_EQUIPMENT_SLOTS; i++) {
        Inventory::EquipmentSlot slot = static_cast<Inventory::EquipmentSlot>(i);
        int slotX, slotY;
        GetSlotPosition(slot, slotX, slotY);

        // 检查点击位置是否在槽内
        if (x >= slotX && x < slotX + m_slotSize && y >= slotY && y < slotY + m_slotSize) {
            return i;
        }
    }

    return -1;
}

bool EquipmentPanel::TryEquipItem(std::shared_ptr<Item> item, Inventory::EquipmentSlot slot)
{
    if (!m_player || !m_player->GetInventory() || !item) {
        return false;
    }

    // 检查物品是否可以装备到指定槽位
    if (!m_player->GetInventory()->CanEquip(*item, slot)) {
        return false;
    }

    // 检查玩家等级是否满足要求
    if (m_player->GetLevel() < item->GetRequiredLevel()) {
        return false;
    }

    // 检查玩家职业是否满足要求
    if (item->GetRequiredClass() != 0 && (m_player->GetJobClass() & item->GetRequiredClass()) == 0) {
        return false;
    }

    // 检查玩家性别是否满足要求
    if (item->GetRequiredGender() != 0 && m_player->GetSex() != item->GetRequiredGender() - 1) {
        return false;
    }

    // 先卸下当前槽位的装备（如果有）
    auto currentItem = m_player->GetInventory()->GetEquippedItem(slot);
    if (currentItem) {
        // 找一个空的背包槽
        int emptySlot = m_player->GetInventory()->FindEmptySlot();
        if (emptySlot == -1) {
            // 背包已满，无法卸下当前装备
            return false;
        }

        // 卸下当前装备
        m_player->GetInventory()->UnequipItem(slot);

        // 通知卸下装备
        if (m_onItemUnequipped) {
            m_onItemUnequipped(slot);
        }
    }

    // 装备新物品
    // 使用Inventory的EquipItem方法
    m_player->GetInventory()->EquipItem(item, slot);

    // 通知装备物品
    if (m_onItemEquipped) {
        m_onItemEquipped(item, slot);
    }

    return true;
}
