#include "SoundManager.h"
#include <iostream>

// Sound implementation

Sound::Sound(Mix_Chunk* chunk)
    : m_chunk(chunk)
    , m_channel(-1)
{
}

Sound::~Sound()
{
    Stop();
    if (m_chunk) {
        Mix_FreeChunk(m_chunk);
        m_chunk = nullptr;
    }
}

int Sound::Play(int loops, int volume)
{
    if (!m_chunk) {
        return -1;
    }
    
    // Set volume
    if (volume >= 0) {
        Mix_VolumeChunk(m_chunk, volume);
    }
    
    // Play sound
    m_channel = Mix_PlayChannel(-1, m_chunk, loops);
    
    return m_channel;
}

void Sound::Stop()
{
    if (m_channel >= 0) {
        Mix_HaltChannel(m_channel);
        m_channel = -1;
    }
}

void Sound::Pause()
{
    if (m_channel >= 0) {
        Mix_Pause(m_channel);
    }
}

void Sound::Resume()
{
    if (m_channel >= 0) {
        Mix_Resume(m_channel);
    }
}

void Sound::SetVolume(int volume)
{
    if (m_chunk) {
        Mix_VolumeChunk(m_chunk, volume);
    }
}

bool Sound::IsPlaying() const
{
    return m_channel >= 0 && Mix_Playing(m_channel);
}

// Music implementation

Music::Music(Mix_Music* music)
    : m_music(music)
{
}

Music::~Music()
{
    Stop();
    if (m_music) {
        Mix_FreeMusic(m_music);
        m_music = nullptr;
    }
}

bool Music::Play(int loops, int fadeInMs)
{
    if (!m_music) {
        return false;
    }
    
    // Play music
    if (fadeInMs > 0) {
        return Mix_FadeInMusic(m_music, loops, fadeInMs) == 0;
    } else {
        return Mix_PlayMusic(m_music, loops) == 0;
    }
}

void Music::Stop(int fadeOutMs)
{
    if (fadeOutMs > 0) {
        Mix_FadeOutMusic(fadeOutMs);
    } else {
        Mix_HaltMusic();
    }
}

void Music::Pause()
{
    Mix_PauseMusic();
}

void Music::Resume()
{
    Mix_ResumeMusic();
}

void Music::SetVolume(int volume)
{
    Mix_VolumeMusic(volume);
}

bool Music::IsPlaying() const
{
    return Mix_PlayingMusic();
}

// SoundManager implementation

SoundManager::SoundManager()
    : m_soundVolume(128)
    , m_musicVolume(128)
    , m_soundEnabled(true)
    , m_musicEnabled(true)
{
}

SoundManager::~SoundManager()
{
    Shutdown();
}

bool SoundManager::Initialize(int frequency, int channels, int chunkSize)
{
    // Initialize SDL_mixer
    if (Mix_OpenAudio(frequency, MIX_DEFAULT_FORMAT, channels, chunkSize) < 0) {
        std::cerr << "Failed to initialize SDL_mixer: " << Mix_GetError() << std::endl;
        return false;
    }
    
    // Allocate channels
    Mix_AllocateChannels(16);
    
    return true;
}

void SoundManager::Shutdown()
{
    // Stop all sounds and music
    StopAllSounds();
    StopMusic();
    
    // Clear sound and music maps
    m_sounds.clear();
    m_music.clear();
    
    // Close SDL_mixer
    Mix_CloseAudio();
}

bool SoundManager::LoadSound(const std::string& name, const std::string& filename)
{
    // Check if sound already exists
    if (m_sounds.find(name) != m_sounds.end()) {
        return true;
    }
    
    // Load sound
    Mix_Chunk* chunk = Mix_LoadWAV(filename.c_str());
    if (!chunk) {
        std::cerr << "Failed to load sound: " << filename << " - " << Mix_GetError() << std::endl;
        return false;
    }
    
    // Create sound
    std::shared_ptr<Sound> sound = std::make_shared<Sound>(chunk);
    
    // Add to map
    m_sounds[name] = sound;
    
    return true;
}

bool SoundManager::LoadMusic(const std::string& name, const std::string& filename)
{
    // Check if music already exists
    if (m_music.find(name) != m_music.end()) {
        return true;
    }
    
    // Load music
    Mix_Music* music = Mix_LoadMUS(filename.c_str());
    if (!music) {
        std::cerr << "Failed to load music: " << filename << " - " << Mix_GetError() << std::endl;
        return false;
    }
    
    // Create music
    std::shared_ptr<Music> musicObj = std::make_shared<Music>(music);
    
    // Add to map
    m_music[name] = musicObj;
    
    return true;
}

bool SoundManager::PlaySound(const std::string& name, int loops, int volume)
{
    if (!m_soundEnabled) {
        return false;
    }
    
    // Get sound
    std::shared_ptr<Sound> sound = GetSound(name);
    if (!sound) {
        return false;
    }
    
    // Set default volume
    if (volume < 0) {
        volume = m_soundVolume;
    }
    
    // Play sound
    return sound->Play(loops, volume) >= 0;
}

bool SoundManager::PlayMusic(const std::string& name, int loops, int fadeInMs)
{
    if (!m_musicEnabled) {
        return false;
    }
    
    // Get music
    std::shared_ptr<Music> music = GetMusic(name);
    if (!music) {
        return false;
    }
    
    // Stop current music
    StopMusic();
    
    // Set volume
    music->SetVolume(m_musicVolume);
    
    // Play music
    if (music->Play(loops, fadeInMs)) {
        m_currentMusic = name;
        return true;
    }
    
    return false;
}

bool SoundManager::StopSound(const std::string& name)
{
    // Get sound
    std::shared_ptr<Sound> sound = GetSound(name);
    if (!sound) {
        return false;
    }
    
    // Stop sound
    sound->Stop();
    
    return true;
}

void SoundManager::StopAllSounds()
{
    Mix_HaltChannel(-1);
}

void SoundManager::StopMusic(int fadeOutMs)
{
    if (fadeOutMs > 0) {
        Mix_FadeOutMusic(fadeOutMs);
    } else {
        Mix_HaltMusic();
    }
    
    m_currentMusic.clear();
}

bool SoundManager::PauseSound(const std::string& name)
{
    // Get sound
    std::shared_ptr<Sound> sound = GetSound(name);
    if (!sound) {
        return false;
    }
    
    // Pause sound
    sound->Pause();
    
    return true;
}

void SoundManager::PauseAllSounds()
{
    Mix_Pause(-1);
}

void SoundManager::PauseMusic()
{
    Mix_PauseMusic();
}

bool SoundManager::ResumeSound(const std::string& name)
{
    // Get sound
    std::shared_ptr<Sound> sound = GetSound(name);
    if (!sound) {
        return false;
    }
    
    // Resume sound
    sound->Resume();
    
    return true;
}

void SoundManager::ResumeAllSounds()
{
    Mix_Resume(-1);
}

void SoundManager::ResumeMusic()
{
    Mix_ResumeMusic();
}

void SoundManager::SetSoundVolume(int volume)
{
    m_soundVolume = volume;
    Mix_Volume(-1, volume);
}

void SoundManager::SetMusicVolume(int volume)
{
    m_musicVolume = volume;
    Mix_VolumeMusic(volume);
}

void SoundManager::EnableSound(bool enabled)
{
    m_soundEnabled = enabled;
    
    if (!enabled) {
        StopAllSounds();
    }
}

void SoundManager::EnableMusic(bool enabled)
{
    m_musicEnabled = enabled;
    
    if (!enabled) {
        StopMusic();
    }
}

std::shared_ptr<Sound> SoundManager::GetSound(const std::string& name)
{
    auto it = m_sounds.find(name);
    if (it != m_sounds.end()) {
        return it->second;
    }
    
    return nullptr;
}

std::shared_ptr<Music> SoundManager::GetMusic(const std::string& name)
{
    auto it = m_music.find(name);
    if (it != m_music.end()) {
        return it->second;
    }
    
    return nullptr;
}
