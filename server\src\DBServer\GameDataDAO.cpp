// GameDataDAO.cpp - 游戏数据访问对象实现
#include "GameDataDAO.h"
#include "../Common/Logger.h"
#include <fstream>
#include <sstream>

namespace DBServer {

// ==================== ItemDAO 实现 ====================

ItemDAO::ItemDAO(std::shared_ptr<MirServer::IDatabase> db) : m_db(db) {}

bool ItemDAO::InitializeTable() {
    const char* createTableSQL = R"(
        CREATE TABLE IF NOT EXISTS StdItems (
            Idx INTEGER PRIMARY KEY,
            Name TEXT NOT NULL,
            StdMode INTEGER NOT NULL,
            Shape INTEGER NOT NULL,
            Weight INTEGER NOT NULL,
            AniCount INTEGER NOT NULL,
            Source INTEGER NOT NULL,
            Reserved INTEGER NOT NULL,
            Looks INTEGER NOT NULL,
            DuraMax INTEGER NOT NULL,
            Ac INTEGER NOT NULL,
            Ac2 INTEGER NOT NULL,
            Mac INTEGER NOT NULL,
            Mac2 INTEGER NOT NULL,
            Dc INTEGER NOT NULL,
            Dc2 INTEGER NOT NULL,
            Mc INTEGER NOT NULL,
            Mc2 INTEGER NOT NULL,
            Sc INTEGER NOT NULL,
            Sc2 INTEGER NOT NULL,
            Need INTEGER NOT NULL,
            NeedLevel INTEGER NOT NULL,
            Price INTEGER NOT NULL
        )
    )";
    
    if (!m_db->Execute(createTableSQL)) {
        m_lastError = "Failed to create StdItems table: " + m_db->GetLastError();
        return false;
    }
    
    // 创建索引
    m_db->Execute("CREATE INDEX IF NOT EXISTS idx_stdmode ON StdItems(StdMode)");
    m_db->Execute("CREATE INDEX IF NOT EXISTS idx_name ON StdItems(Name)");
    
    return true;
}

bool ItemDAO::Create(const MirServer::StdItem& item) {
    const char* sql = R"(
        INSERT INTO StdItems (
            Idx, Name, StdMode, Shape, Weight, AniCount, Source, Reserved,
            Looks, DuraMax, Ac, Ac2, Mac, Mac2, Dc, Dc2, Mc, Mc2, Sc, Sc2,
            Need, NeedLevel, Price
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    )";
    
    std::vector<std::any> params = {
        item.idx, item.name, item.stdMode, item.shape, item.weight,
        item.aniCount, item.source, item.reserved, item.looks, item.duraMax,
        item.ac, item.ac2, item.mac, item.mac2, item.dc, item.dc2,
        item.mc, item.mc2, item.sc, item.sc2,
        item.need, item.needLevel, item.price
    };
    
    if (!m_db->Execute(sql, params)) {
        m_lastError = "Failed to insert item: " + m_db->GetLastError();
        return false;
    }
    
    return true;
}

std::optional<MirServer::StdItem> ItemDAO::Read(int32_t idx) {
    const char* sql = "SELECT * FROM StdItems WHERE Idx = ?";
    std::vector<std::any> params = { idx };
    
    auto results = m_db->Query(sql, params);
    if (results.empty()) {
        return std::nullopt;
    }
    
    return BuildItemFromResult(results[0]);
}

bool ItemDAO::Update(const MirServer::StdItem& item) {
    const char* sql = R"(
        UPDATE StdItems SET
            Name = ?, StdMode = ?, Shape = ?, Weight = ?, AniCount = ?,
            Source = ?, Reserved = ?, Looks = ?, DuraMax = ?,
            Ac = ?, Ac2 = ?, Mac = ?, Mac2 = ?, Dc = ?, Dc2 = ?,
            Mc = ?, Mc2 = ?, Sc = ?, Sc2 = ?,
            Need = ?, NeedLevel = ?, Price = ?
        WHERE Idx = ?
    )";
    
    std::vector<std::any> params = {
        item.name, item.stdMode, item.shape, item.weight, item.aniCount,
        item.source, item.reserved, item.looks, item.duraMax,
        item.ac, item.ac2, item.mac, item.mac2, item.dc, item.dc2,
        item.mc, item.mc2, item.sc, item.sc2,
        item.need, item.needLevel, item.price,
        item.idx
    };
    
    if (!m_db->Execute(sql, params)) {
        m_lastError = "Failed to update item: " + m_db->GetLastError();
        return false;
    }
    
    return m_db->GetAffectedRows() > 0;
}

bool ItemDAO::Delete(int32_t idx) {
    const char* sql = "DELETE FROM StdItems WHERE Idx = ?";
    std::vector<std::any> params = { idx };
    
    if (!m_db->Execute(sql, params)) {
        m_lastError = "Failed to delete item: " + m_db->GetLastError();
        return false;
    }
    
    return m_db->GetAffectedRows() > 0;
}

std::vector<MirServer::StdItem> ItemDAO::ReadAll() {
    const char* sql = "SELECT * FROM StdItems ORDER BY Idx";
    auto results = m_db->Query(sql);
    
    std::vector<MirServer::StdItem> items;
    items.reserve(results.size());
    
    for (const auto& row : results) {
        items.push_back(BuildItemFromResult(row));
    }
    
    return items;
}

std::vector<MirServer::StdItem> ItemDAO::ReadByType(uint8_t stdMode) {
    const char* sql = "SELECT * FROM StdItems WHERE StdMode = ? ORDER BY Idx";
    std::vector<std::any> params = { stdMode };
    auto results = m_db->Query(sql, params);
    
    std::vector<MirServer::StdItem> items;
    items.reserve(results.size());
    
    for (const auto& row : results) {
        items.push_back(BuildItemFromResult(row));
    }
    
    return items;
}

std::vector<MirServer::StdItem> ItemDAO::ReadByName(const std::string& namePart) {
    const char* sql = "SELECT * FROM StdItems WHERE Name LIKE ? ORDER BY Idx";
    std::vector<std::any> params = { "%" + namePart + "%" };
    auto results = m_db->Query(sql, params);
    
    std::vector<MirServer::StdItem> items;
    items.reserve(results.size());
    
    for (const auto& row : results) {
        items.push_back(BuildItemFromResult(row));
    }
    
    return items;
}

bool ItemDAO::CreateBatch(const std::vector<MirServer::StdItem>& items) {
    auto transaction = m_db->BeginTransaction();
    if (!transaction) {
        m_lastError = "Failed to begin transaction";
        return false;
    }
    
    for (const auto& item : items) {
        if (!Create(item)) {
            transaction->Rollback();
            return false;
        }
    }
    
    return transaction->Commit();
}

size_t ItemDAO::Count() {
    const char* sql = "SELECT COUNT(*) as count FROM StdItems";
    auto results = m_db->Query(sql);
    
    if (results.empty()) return 0;
    
    try {
        return std::any_cast<int64_t>(results[0].at("count"));
    } catch (...) {
        return 0;
    }
}

bool ItemDAO::Exists(int32_t idx) {
    const char* sql = "SELECT 1 FROM StdItems WHERE Idx = ? LIMIT 1";
    std::vector<std::any> params = { idx };
    auto results = m_db->Query(sql, params);
    
    return !results.empty();
}

bool ItemDAO::ExistsByName(const std::string& name) {
    const char* sql = "SELECT 1 FROM StdItems WHERE Name = ? LIMIT 1";
    std::vector<std::any> params = { name };
    auto results = m_db->Query(sql, params);
    
    return !results.empty();
}

MirServer::StdItem ItemDAO::BuildItemFromResult(const MirServer::ResultRow& row) {
    MirServer::StdItem item;
    
    try {
        item.idx = static_cast<int32_t>(std::any_cast<int64_t>(row.at("Idx")));
        item.name = std::any_cast<std::string>(row.at("Name"));
        item.stdMode = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("StdMode")));
        item.shape = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("Shape")));
        item.weight = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Weight")));
        item.aniCount = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("AniCount")));
        item.source = static_cast<int16_t>(std::any_cast<int64_t>(row.at("Source")));
        item.reserved = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("Reserved")));
        item.looks = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Looks")));
        item.duraMax = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("DuraMax")));
        item.ac = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Ac")));
        item.ac2 = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Ac2")));
        item.mac = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Mac")));
        item.mac2 = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Mac2")));
        item.dc = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Dc")));
        item.dc2 = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Dc2")));
        item.mc = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Mc")));
        item.mc2 = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Mc2")));
        item.sc = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Sc")));
        item.sc2 = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Sc2")));
        item.need = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("Need")));
        item.needLevel = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("NeedLevel")));
        item.price = static_cast<uint32_t>(std::any_cast<int64_t>(row.at("Price")));
    } catch (const std::exception& e) {
        MirServer::Logger::Error("Error building item from result: " + std::string(e.what()));
    }
    
    return item;
}

// ==================== MagicDAO 实现 ====================

MagicDAO::MagicDAO(std::shared_ptr<MirServer::IDatabase> db) : m_db(db) {}

bool MagicDAO::InitializeTable() {
    const char* createTableSQL = R"(
        CREATE TABLE IF NOT EXISTS Magic (
            MagId INTEGER PRIMARY KEY,
            MagName TEXT NOT NULL,
            EffectType INTEGER NOT NULL,
            Effect INTEGER NOT NULL,
            Spell INTEGER NOT NULL,
            Power INTEGER NOT NULL,
            MaxPower INTEGER NOT NULL,
            Job INTEGER NOT NULL,
            NeedL1 INTEGER NOT NULL,
            NeedL2 INTEGER NOT NULL,
            NeedL3 INTEGER NOT NULL,
            L1Train INTEGER NOT NULL,
            L2Train INTEGER NOT NULL,
            L3Train INTEGER NOT NULL,
            Delay INTEGER NOT NULL,
            DefSpell INTEGER NOT NULL,
            DefPower INTEGER NOT NULL,
            DefMaxPower INTEGER NOT NULL,
            Descr TEXT
        )
    )";
    
    if (!m_db->Execute(createTableSQL)) {
        m_lastError = "Failed to create Magic table: " + m_db->GetLastError();
        return false;
    }
    
    // 创建索引
    m_db->Execute("CREATE INDEX IF NOT EXISTS idx_job ON Magic(Job)");
    m_db->Execute("CREATE INDEX IF NOT EXISTS idx_magname ON Magic(MagName)");
    
    return true;
}

bool MagicDAO::Create(const MirServer::Magic& magic) {
    const char* sql = R"(
        INSERT INTO Magic (
            MagId, MagName, EffectType, Effect, Spell, Power, MaxPower, Job,
            NeedL1, NeedL2, NeedL3, L1Train, L2Train, L3Train,
            Delay, DefSpell, DefPower, DefMaxPower, Descr
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    )";
    
    std::vector<std::any> params = {
        magic.magicId, magic.magicName, magic.effectType, magic.effect,
        magic.spell, magic.power, magic.maxPower, magic.job,
        magic.trainLevel[0], magic.trainLevel[1], magic.trainLevel[2],
        magic.maxTrain[0], magic.maxTrain[1], magic.maxTrain[2],
        magic.delayTime, magic.defSpell, magic.defPower, magic.defMaxPower,
        magic.descr
    };
    
    if (!m_db->Execute(sql, params)) {
        m_lastError = "Failed to insert magic: " + m_db->GetLastError();
        return false;
    }
    
    return true;
}

std::optional<MirServer::Magic> MagicDAO::Read(uint16_t magicId) {
    const char* sql = "SELECT * FROM Magic WHERE MagId = ?";
    std::vector<std::any> params = { magicId };
    
    auto results = m_db->Query(sql, params);
    if (results.empty()) {
        return std::nullopt;
    }
    
    return BuildMagicFromResult(results[0]);
}

bool MagicDAO::Update(const MirServer::Magic& magic) {
    const char* sql = R"(
        UPDATE Magic SET
            MagName = ?, EffectType = ?, Effect = ?, Spell = ?,
            Power = ?, MaxPower = ?, Job = ?,
            NeedL1 = ?, NeedL2 = ?, NeedL3 = ?,
            L1Train = ?, L2Train = ?, L3Train = ?,
            Delay = ?, DefSpell = ?, DefPower = ?, DefMaxPower = ?, Descr = ?
        WHERE MagId = ?
    )";
    
    std::vector<std::any> params = {
        magic.magicName, magic.effectType, magic.effect, magic.spell,
        magic.power, magic.maxPower, magic.job,
        magic.trainLevel[0], magic.trainLevel[1], magic.trainLevel[2],
        magic.maxTrain[0], magic.maxTrain[1], magic.maxTrain[2],
        magic.delayTime, magic.defSpell, magic.defPower, magic.defMaxPower,
        magic.descr, magic.magicId
    };
    
    if (!m_db->Execute(sql, params)) {
        m_lastError = "Failed to update magic: " + m_db->GetLastError();
        return false;
    }
    
    return m_db->GetAffectedRows() > 0;
}

bool MagicDAO::Delete(uint16_t magicId) {
    const char* sql = "DELETE FROM Magic WHERE MagId = ?";
    std::vector<std::any> params = { magicId };
    
    if (!m_db->Execute(sql, params)) {
        m_lastError = "Failed to delete magic: " + m_db->GetLastError();
        return false;
    }
    
    return m_db->GetAffectedRows() > 0;
}

std::vector<MirServer::Magic> MagicDAO::ReadAll() {
    const char* sql = "SELECT * FROM Magic ORDER BY MagId";
    auto results = m_db->Query(sql);
    
    std::vector<MirServer::Magic> magics;
    magics.reserve(results.size());
    
    for (const auto& row : results) {
        magics.push_back(BuildMagicFromResult(row));
    }
    
    return magics;
}

std::vector<MirServer::Magic> MagicDAO::ReadByJob(uint8_t job) {
    const char* sql = "SELECT * FROM Magic WHERE Job = ? OR Job = 99 ORDER BY MagId";
    std::vector<std::any> params = { job };
    auto results = m_db->Query(sql, params);
    
    std::vector<MirServer::Magic> magics;
    magics.reserve(results.size());
    
    for (const auto& row : results) {
        magics.push_back(BuildMagicFromResult(row));
    }
    
    return magics;
}

std::vector<MirServer::Magic> MagicDAO::ReadByName(const std::string& namePart) {
    const char* sql = "SELECT * FROM Magic WHERE MagName LIKE ? ORDER BY MagId";
    std::vector<std::any> params = { "%" + namePart + "%" };
    auto results = m_db->Query(sql, params);
    
    std::vector<MirServer::Magic> magics;
    magics.reserve(results.size());
    
    for (const auto& row : results) {
        magics.push_back(BuildMagicFromResult(row));
    }
    
    return magics;
}

bool MagicDAO::CreateBatch(const std::vector<MirServer::Magic>& magics) {
    auto transaction = m_db->BeginTransaction();
    if (!transaction) {
        m_lastError = "Failed to begin transaction";
        return false;
    }
    
    for (const auto& magic : magics) {
        if (!Create(magic)) {
            transaction->Rollback();
            return false;
        }
    }
    
    return transaction->Commit();
}

size_t MagicDAO::Count() {
    const char* sql = "SELECT COUNT(*) as count FROM Magic";
    auto results = m_db->Query(sql);
    
    if (results.empty()) return 0;
    
    try {
        return std::any_cast<int64_t>(results[0].at("count"));
    } catch (...) {
        return 0;
    }
}

bool MagicDAO::Exists(uint16_t magicId) {
    const char* sql = "SELECT 1 FROM Magic WHERE MagId = ? LIMIT 1";
    std::vector<std::any> params = { magicId };
    auto results = m_db->Query(sql, params);
    
    return !results.empty();
}

bool MagicDAO::ExistsByName(const std::string& name) {
    const char* sql = "SELECT 1 FROM Magic WHERE MagName = ? LIMIT 1";
    std::vector<std::any> params = { name };
    auto results = m_db->Query(sql, params);
    
    return !results.empty();
}

MirServer::Magic MagicDAO::BuildMagicFromResult(const MirServer::ResultRow& row) {
    MirServer::Magic magic;
    
    try {
        magic.magicId = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("MagId")));
        magic.magicName = std::any_cast<std::string>(row.at("MagName"));
        magic.effectType = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("EffectType")));
        magic.effect = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("Effect")));
        magic.spell = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Spell")));
        magic.power = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("Power")));
        magic.maxPower = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("MaxPower")));
        magic.job = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("Job")));
        magic.trainLevel[0] = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("NeedL1")));
        magic.trainLevel[1] = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("NeedL2")));
        magic.trainLevel[2] = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("NeedL3")));
        magic.trainLevel[3] = magic.trainLevel[2]; // 复制第3级需求
        magic.maxTrain[0] = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("L1Train")));
        magic.maxTrain[1] = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("L2Train")));
        magic.maxTrain[2] = static_cast<uint16_t>(std::any_cast<int64_t>(row.at("L3Train")));
        magic.maxTrain[3] = magic.maxTrain[2]; // 复制第3级训练值
        magic.trainLv = 3;
        magic.delayTime = static_cast<uint32_t>(std::any_cast<int64_t>(row.at("Delay")));
        magic.defSpell = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("DefSpell")));
        magic.defPower = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("DefPower")));
        magic.defMaxPower = static_cast<uint8_t>(std::any_cast<int64_t>(row.at("DefMaxPower")));
        magic.descr = std::any_cast<std::string>(row.at("Descr"));
    } catch (const std::exception& e) {
        MirServer::Logger::Error("Error building magic from result: " + std::string(e.what()));
    }
    
    return magic;
}

// ==================== GameDataManager 实现 ====================

GameDataManager::GameDataManager() {}

GameDataManager::~GameDataManager() {
    Shutdown();
}

bool GameDataManager::Initialize(const std::string& dbPath) {
    // 创建数据库连接
    m_database = MirServer::DatabaseFactory::Create(MirServer::DatabaseType::SQLite);
    if (!m_database) {
        MirServer::Logger::Error("Failed to create database instance");
        return false;
    }
    
    // 连接数据库
    if (!m_database->Connect(dbPath)) {
        MirServer::Logger::Error("Failed to connect to database: " + dbPath);
        return false;
    }
    
    // 创建DAO对象
    m_itemDAO = std::make_unique<ItemDAO>(m_database);
    m_magicDAO = std::make_unique<MagicDAO>(m_database);
    
    // 初始化数据表
    if (!m_itemDAO->InitializeTable()) {
        MirServer::Logger::Error("Failed to initialize items table");
        return false;
    }
    
    if (!m_magicDAO->InitializeTable()) {
        MirServer::Logger::Error("Failed to initialize magic table");
        return false;
    }
    
    MirServer::Logger::Info("GameDataManager initialized successfully");
    return true;
}

void GameDataManager::Shutdown() {
    m_items.clear();
    m_magics.clear();
    m_itemIndexMap.clear();
    m_magicIndexMap.clear();
    
    m_itemDAO.reset();
    m_magicDAO.reset();
    
    if (m_database) {
        m_database->Disconnect();
        m_database.reset();
    }
}

bool GameDataManager::LoadAllData() {
    // 加载物品数据
    m_items = m_itemDAO->ReadAll();
    m_itemIndexMap.clear();
    
    for (size_t i = 0; i < m_items.size(); ++i) {
        m_itemIndexMap[m_items[i].idx] = i;
    }
    
    MirServer::Logger::Info("Loaded " + std::to_string(m_items.size()) + " items");
    
    // 加载魔法数据
    m_magics = m_magicDAO->ReadAll();
    m_magicIndexMap.clear();
    
    for (size_t i = 0; i < m_magics.size(); ++i) {
        m_magicIndexMap[m_magics[i].magicId] = i;
    }
    
    MirServer::Logger::Info("Loaded " + std::to_string(m_magics.size()) + " magics");
    
    return true;
}

const MirServer::StdItem* GameDataManager::FindItem(int32_t idx) const {
    auto it = m_itemIndexMap.find(idx);
    if (it != m_itemIndexMap.end()) {
        return &m_items[it->second];
    }
    return nullptr;
}

const MirServer::StdItem* GameDataManager::FindItemByName(const std::string& name) const {
    for (const auto& item : m_items) {
        if (item.name == name) {
            return &item;
        }
    }
    return nullptr;
}

const MirServer::Magic* GameDataManager::FindMagic(uint16_t magicId) const {
    auto it = m_magicIndexMap.find(magicId);
    if (it != m_magicIndexMap.end()) {
        return &m_magics[it->second];
    }
    return nullptr;
}

const MirServer::Magic* GameDataManager::FindMagicByName(const std::string& name) const {
    for (const auto& magic : m_magics) {
        if (magic.magicName == name) {
            return &magic;
        }
    }
    return nullptr;
}

} // namespace DBServer 