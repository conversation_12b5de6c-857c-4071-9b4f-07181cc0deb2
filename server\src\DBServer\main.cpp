#include <iostream>
#include "DBServer.h"
#include "../Common/Logger.h"
#include <csignal>
#include <memory>

#ifdef _WIN32
#include <windows.h>
#include <conio.h>
#else
#include <unistd.h>
#include <termios.h>
#endif

// 全局DBServer实例
std::unique_ptr<DBServer::DBServerMain> g_pDBServer;

// 信号处理函数
void SignalHandler(int signal) {
    std::cout << "\nReceived signal " << signal << ", shutting down..." << std::endl;
    if (g_pDBServer) {
        g_pDBServer->Stop();
    }
}

// 跨平台的kbhit实现
#ifndef _WIN32
int kbhit() {
    struct termios oldt, newt;
    int ch;
    int oldf;
    
    tcgetattr(STDIN_FILENO, &oldt);
    newt = oldt;
    newt.c_lflag &= ~(ICANON | ECHO);
    tcsetattr(STDIN_FILENO, TCSANOW, &newt);
    oldf = fcntl(STDIN_FILENO, F_GETFL, 0);
    fcntl(STDIN_FILENO, F_SETFL, oldf | O_NONBLOCK);
    
    ch = getchar();
    
    tcsetattr(STDIN_FILENO, TCSANOW, &oldt);
    fcntl(STDIN_FILENO, F_SETFL, oldf);
    
    if(ch != EOF) {
        ungetc(ch, stdin);
        return 1;
    }
    
    return 0;
}
#endif

int main(int argc, char* argv[]) {
    // 设置信号处理
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    
    // 初始化日志系统
    MirServer::Logger::SetLogFile("logs/DBServer.log");
    MirServer::Logger::SetLogLevel(MirServer::LogLevel::LOG_INFO);
    MirServer::Logger::Info("=====================================");
    MirServer::Logger::Info("DBServer Starting...");
    MirServer::Logger::Info("=====================================");
    
    // 创建DBServer实例
    g_pDBServer = std::make_unique<DBServer::DBServerMain>();
    
    // 初始化
    if (!g_pDBServer->Initialize()) {
        MirServer::Logger::Error("Failed to initialize DBServer");
        return 1;
    }
    
    std::cout << "DBServer is running. Press 'Q' to quit." << std::endl;
    std::cout << "Commands:" << std::endl;
    std::cout << "  Q - Quit" << std::endl;
    std::cout << "  S - Show statistics" << std::endl;
    std::cout << "  B - Backup database" << std::endl;
    std::cout << std::endl;
    
    // 启动服务器线程
    std::thread serverThread([&]() {
        g_pDBServer->Run();
    });
    
    // 主线程处理控制台输入
    bool running = true;
    while (running) {
        if (kbhit()) {
            char ch = std::tolower(getchar());
            switch (ch) {
                case 'q':
                    running = false;
                    break;
                    
                case 's':
                    // TODO: 显示统计信息
                    std::cout << "\nStatistics:" << std::endl;
                    std::cout << "  Queries: " << "TODO" << std::endl;
                    std::cout << "  Loads: " << "TODO" << std::endl;
                    std::cout << "  Saves: " << "TODO" << std::endl;
                    break;
                    
                case 'b':
                    std::cout << "\nBacking up database..." << std::endl;
                    if (DBServer::DBServerMain::BackupDatabase("./FDB/Hum.DB", "./Backup/")) {
                        std::cout << "Backup completed successfully." << std::endl;
                    } else {
                        std::cout << "Backup failed!" << std::endl;
                    }
                    break;
            }
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // 停止服务器
    std::cout << "\nShutting down DBServer..." << std::endl;
    g_pDBServer->Stop();
    
    // 等待服务器线程结束
    if (serverThread.joinable()) {
        serverThread.join();
    }
    
    MirServer::Logger::Info("DBServer stopped successfully");
    return 0;
} 