#include "Item.h"

Item::Item(int id, ItemType type, int look, const std::string& name, bool identified)
    : m_id(id)
    , m_makeIndex(0)
    , m_type(type)
    , m_look(look)
    , m_name(name)
    , m_identified(identified)
    , m_durability(0)
    , m_maxDurability(0)
    , m_weight(0)
    , m_price(0)
    , m_requiredLevel(0)
    , m_requiredClass(0)
    , m_requiredGender(0)
    , m_attackPower(0)
    , m_magicPower(0)
    , m_defense(0)
    , m_magicDefense(0)
{
    // Initialize values array with 14 elements (as in the original TUserItem)
    m_values.resize(14, 0);
}

Item::~Item()
{
}

uint8_t Item::GetValue(size_t index) const
{
    if (index < m_values.size()) {
        return m_values[index];
    }
    return 0;
}

void Item::SetValue(size_t index, uint8_t value)
{
    if (index >= m_values.size()) {
        m_values.resize(index + 1, 0);
    }
    m_values[index] = value;
}
