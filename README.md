# 传奇 Delphi 重构项目

这是一个传奇游戏客户端和服务器的重构项目，旨在将原始的 Delphi 代码迁移到现代的 C++ 实现。

## 项目结构

```
mir-delphi-refactored/
├── client/         # 客户端重构代码
├── server/         # 服务器重构代码（计划中）
├── common/         # 客户端和服务器共享代码
├── delphi/         # 原始 Delphi 代码（参考）
├── docs/           # 项目文档
├── tools/          # 开发工具
└── build/          # 构建输出目录
```

详细的目录结构说明请参见 [DIRECTORY_STRUCTURE.md](DIRECTORY_STRUCTURE.md)

## 构建说明

### 客户端

```bash
mkdir build
cd build
cmake .. -DBUILD_CLIENT=ON
cmake --build .
```

### 服务器（计划中）

```bash
cmake .. -DBUILD_SERVER=ON
cmake --build .
```

## 开发状态

- **客户端**: 活跃开发中
- **服务器**: 计划中

## 文档

- [功能对比清单](docs/功能对比清单.md)
- [缺失网络协议对比](docs/缺失网络协议对比.md)
- [重构版本缺失功能实现建议](docs/重构版本缺失功能实现建议.md)

# EGameOfMir2
传奇2服务器端源码, delphi版本

之前那个vc版本的源码, 封包的格式好像不太对, 没有/隔开
这个应该是对的

这个也不太对, 物品的结构体, Name之前应该有个byte 表示name len

## C++ Client Build and Debug Environment Setup

The C++ client implementation uses CMake for build configuration and MinGW64 for compilation. Visual Studio Code is configured for development and debugging.

### Prerequisites

1. **MinGW64** - Install MSYS2 from https://www.msys2.org/ and then install MinGW64 toolchain:
   ```
   pacman -S mingw-w64-x86_64-toolchain
   ```

2. **SDL2 Libraries** - Install SDL2 and its extensions:
   ```
   pacman -S mingw-w64-x86_64-SDL2 mingw-w64-x86_64-SDL2_image mingw-w64-x86_64-SDL2_ttf mingw-w64-x86_64-SDL2_mixer
   ```

3. **CMake** - Install CMake:
   ```
   pacman -S mingw-w64-x86_64-cmake
   ```

4. **Visual Studio Code** - Install VS Code and the following extensions:
   - C/C++ Extension (ms-vscode.cpptools)
   - CMake Tools (ms-vscode.cmake-tools)

### Building the Project

#### Using the Command Line

1. Open MSYS2 MinGW64 terminal
2. Navigate to the project directory
3. Run the build script:
   ```
   ./build.sh
   ```

   Or manually:
   ```
   mkdir -p build
   cd build
   cmake -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Debug ..
   cmake --build . -- -j4
   ```

4. The executable will be in the `build/bin` directory

#### Using Visual Studio Code

1. Open the project folder in VS Code
2. Use the provided tasks:
   - Press `Ctrl+Shift+B` to build the project (default build task)
   - Or use the Terminal > Run Task menu to select:
     - "CMake Configure" - to configure the project
     - "CMake Build" - to build the project
     - "CMake Clean" - to clean the build
     - "Run" - to run the executable

### Debugging

#### Using Visual Studio Code

1. Set breakpoints in your code
2. Press F5 to start debugging
3. Use the debug toolbar to control execution (continue, step over, step into, etc.)

### Troubleshooting

#### SDL2 Libraries Not Found

If CMake cannot find SDL2 libraries, you may need to adjust the SDL2 paths in CMakeLists.txt:

```cmake
set(SDL2_PATH "C:/msys64/mingw64" CACHE PATH "Path to SDL2 installation")
```

#### Debugging Not Working

Make sure:
1. You have built the project in Debug mode
2. The path to GDB in launch.json is correct
3. The executable path in launch.json matches your build output

#### Common MinGW Issues

1. **Path Issues**: Ensure MinGW's bin directory is in your PATH environment variable
2. **DLL Not Found**: Copy required DLLs (SDL2.dll, etc.) to your executable directory
3. **Permission Issues**: Make sure build.sh has execute permissions (`chmod +x build.sh`)