#ifndef ACCOUNTDB_H
#define ACCOUNTDB_H

#include <string>
#include <memory>
#include <mutex>
#include <map>
#include <fstream>
#include "../Common/Types.h"
#include "AccountTypes.h"

namespace MirServer {

// Account database record
struct TAccountDBRecord {
    TDBHeader Header;
    TUserEntry UserEntry;
    TUserEntryAdd UserEntryAdd;
    int32_t nErrorCount = 0;
    uint32_t dwActionTick = 0;
};

// Account database manager
class AccountDB {
public:
    AccountDB();
    ~AccountDB();
    
    // Database operations
    bool Initialize(const std::string& dbPath);
    bool Open();
    void Close();
    bool IsOpen() const { return m_isOpen; }
    
    // Record operations
    int32_t Index(const std::string& account);
    bool Get(int32_t index, TAccountDBRecord& record);
    bool Add(const TAccountDBRecord& record);
    bool Update(int32_t index, const TAccountDBRecord& record);
    bool Delete(int32_t index);
    
    // Utility functions
    int32_t GetRecordCount() const { return static_cast<int32_t>(m_records.size()); }
    bool CheckAccountName(const std::string& account);
    void CreateBackup();
    
private:
    std::string m_dbPath;
    std::string m_idxPath;
    bool m_isOpen = false;
    std::mutex m_mutex;
    
    // In-memory data
    std::map<std::string, int32_t> m_accountIndex;  // Account name to record index
    std::map<int32_t, TAccountDBRecord> m_records;  // Record index to record data
    int32_t m_nextIndex = 0;
    
    // File operations
    bool LoadDatabase();
    bool SaveDatabase();
    bool LoadIndex();
    bool SaveIndex();
    bool BuildIndex();
    
    // Helper functions
    std::string GetBackupFileName();
    void UpdateHeader(TAccountDBRecord& record);
};

} // namespace MirServer

#endif // ACCOUNTDB_H 