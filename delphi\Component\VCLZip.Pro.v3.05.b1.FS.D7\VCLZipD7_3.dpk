package VCLZipD7_3;

{$R *.res}
{$R 'VCLZip.dcr'}
{$R 'kpSFXCfg.dcr'}
{$R 'VCLUnZip.dcr'}
{$ALIGN 8}
{$ASSERTIONS OFF}
{$BOOLEVAL OFF}
{$DEBUGINFO OFF}
{$EXTENDEDSYNTAX ON}
{$IMPORTEDDATA ON}
{$IOCHECKS ON}
{$LOCALSYMBOLS OFF}
{$LONGSTRINGS ON}
{$OPENSTRINGS ON}
{$OPTIMIZATION ON}
{$OVERFLOWCHECKS OFF}
{$RANGECHECKS OFF}
{$REFERENCEINFO OFF}
{$SAFEDIVIDE OFF}
{$STACKFRAMES OFF}
{$TYPEDADDRESS OFF}
{$VARSTRINGCHECKS ON}
{$WRITEABLECONST OFF}
{$MINENUMSIZE 1}
{$IMAGEBASE $400000}
{$DESCRIPTION 'VCLZip Pro'}
{$IMPLICITBUILD OFF}

requires
  rtl,
  vcl,
  vclx;

contains
  kpSFXCfg in 'kpSFXCfg.pas',
  <PERSON><PERSON>Un<PERSON><PERSON> in 'VCLUnZip.pas',
  VCLZip in 'VCLZip.pas',
  kpCntn in 'kpCntn.pas',
  KpLib in 'KPLib.pas',
  kpMatch in 'kpMatch.pas',
  kpSStrm in 'kpSStrm.pas',
  kpZcnst in 'Kpzcnst.pas',
  kpZipObj in 'kpZipObj.pas',
  kpDiskIOs in 'kpDiskIOs.pas',
  KpSmall in 'KpSmall.pas',
  kpDiskIOWin9x in 'kpDiskIOWin9x.pas',
  kpSHuge in 'kpSHuge.pas';

end.
