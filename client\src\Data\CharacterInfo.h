#pragma once

#include <string>
#include <cstdint>
#include "PlayerClass.h"

/**
 * @class CharacterInfo
 * @brief Information about a player character
 *
 * This class represents information about a player character, including its name,
 * class, level, and other attributes.
 */
class CharacterInfo {
private:
    std::string m_name;       ///< Character name
    PlayerClass m_class;      ///< Character class
    int m_level;              ///< Character level
    int m_experience;         ///< Character experience
    int m_health;             ///< Character health
    int m_mana;               ///< Character mana
    int m_strength;           ///< Character strength
    int m_dexterity;          ///< Character dexterity
    int m_vitality;           ///< Character vitality
    int m_energy;             ///< Character energy
    int m_lastMapIndex;       ///< Last map index
    int m_lastPositionX;      ///< Last position X
    int m_lastPositionY;      ///< Last position Y

public:
    /**
     * @brief Constructor
     * @param name Character name
     * @param playerClass Character class
     * @param level Character level
     * @param experience Character experience
     * @param health Character health
     * @param mana Character mana
     * @param strength Character strength
     * @param dexterity Character dexterity
     * @param vitality Character vitality
     * @param energy Character energy
     * @param lastMapIndex Last map index
     * @param lastPositionX Last position X
     * @param lastPositionY Last position Y
     */
    CharacterInfo(
        const std::string& name = "",
        PlayerClass playerClass = PlayerClass::WARRIOR,
        int level = 1,
        int experience = 0,
        int health = 100,
        int mana = 100,
        int strength = 10,
        int dexterity = 10,
        int vitality = 10,
        int energy = 10,
        int lastMapIndex = 0,
        int lastPositionX = 0,
        int lastPositionY = 0
    );

    /**
     * @brief Destructor
     */
    ~CharacterInfo();

    /**
     * @brief Get the character name
     * @return Character name
     */
    const std::string& GetName() const { return m_name; }

    /**
     * @brief Set the character name
     * @param name Character name
     */
    void SetName(const std::string& name) { m_name = name; }

    /**
     * @brief Get the character class
     * @return Character class
     */
    PlayerClass GetClass() const { return m_class; }

    /**
     * @brief Set the character class
     * @param playerClass Character class
     */
    void SetClass(PlayerClass playerClass) { m_class = playerClass; }

    /**
     * @brief Get the character level
     * @return Character level
     */
    int GetLevel() const { return m_level; }

    /**
     * @brief Set the character level
     * @param level Character level
     */
    void SetLevel(int level) { m_level = level; }

    /**
     * @brief Get the character experience
     * @return Character experience
     */
    int GetExperience() const { return m_experience; }

    /**
     * @brief Set the character experience
     * @param experience Character experience
     */
    void SetExperience(int experience) { m_experience = experience; }

    /**
     * @brief Get the character health
     * @return Character health
     */
    int GetHealth() const { return m_health; }

    /**
     * @brief Set the character health
     * @param health Character health
     */
    void SetHealth(int health) { m_health = health; }

    /**
     * @brief Get the character mana
     * @return Character mana
     */
    int GetMana() const { return m_mana; }

    /**
     * @brief Set the character mana
     * @param mana Character mana
     */
    void SetMana(int mana) { m_mana = mana; }

    /**
     * @brief Get the character strength
     * @return Character strength
     */
    int GetStrength() const { return m_strength; }

    /**
     * @brief Set the character strength
     * @param strength Character strength
     */
    void SetStrength(int strength) { m_strength = strength; }

    /**
     * @brief Get the character dexterity
     * @return Character dexterity
     */
    int GetDexterity() const { return m_dexterity; }

    /**
     * @brief Set the character dexterity
     * @param dexterity Character dexterity
     */
    void SetDexterity(int dexterity) { m_dexterity = dexterity; }

    /**
     * @brief Get the character vitality
     * @return Character vitality
     */
    int GetVitality() const { return m_vitality; }

    /**
     * @brief Set the character vitality
     * @param vitality Character vitality
     */
    void SetVitality(int vitality) { m_vitality = vitality; }

    /**
     * @brief Get the character energy
     * @return Character energy
     */
    int GetEnergy() const { return m_energy; }

    /**
     * @brief Set the character energy
     * @param energy Character energy
     */
    void SetEnergy(int energy) { m_energy = energy; }

    /**
     * @brief Get the last map index
     * @return Last map index
     */
    int GetLastMapIndex() const { return m_lastMapIndex; }

    /**
     * @brief Set the last map index
     * @param lastMapIndex Last map index
     */
    void SetLastMapIndex(int lastMapIndex) { m_lastMapIndex = lastMapIndex; }

    /**
     * @brief Get the last position X
     * @return Last position X
     */
    int GetLastPositionX() const { return m_lastPositionX; }

    /**
     * @brief Set the last position X
     * @param lastPositionX Last position X
     */
    void SetLastPositionX(int lastPositionX) { m_lastPositionX = lastPositionX; }

    /**
     * @brief Get the last position Y
     * @return Last position Y
     */
    int GetLastPositionY() const { return m_lastPositionY; }

    /**
     * @brief Set the last position Y
     * @param lastPositionY Last position Y
     */
    void SetLastPositionY(int lastPositionY) { m_lastPositionY = lastPositionY; }

    /**
     * @brief Get the character class as a string
     * @return Character class as a string
     */
    std::string GetClassString() const;
};
