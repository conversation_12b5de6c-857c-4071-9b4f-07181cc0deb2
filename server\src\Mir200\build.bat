@echo off
REM Mir200 Build Script
REM Phase 1 Implementation Build

echo ========================================
echo   Mir200 Server Build Script
echo   Phase 1 Implementation
echo ========================================
echo.

REM Check if CMake is available
cmake --version >nul 2>&1
if errorlevel 1 (
    echo Error: CMake is not installed or not in PATH
    echo Please install CMake 3.16 or later
    pause
    exit /b 1
)

REM Create build directory
if not exist "build" (
    echo Creating build directory...
    mkdir build
)

REM Enter build directory
cd build

echo Configuring project with CMake...
cmake .. -G "Visual Studio 16 2019" -A x64
if errorlevel 1 (
    echo Error: CMake configuration failed
    echo Trying with MinGW Makefiles...
    cmake .. -G "MinGW Makefiles"
    if errorlevel 1 (
        echo Error: CMake configuration failed with both generators
        pause
        exit /b 1
    )
)

echo.
echo Building project...
cmake --build . --config Release
if errorlevel 1 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Build completed successfully!
echo ========================================
echo.
echo Executable location: build\bin\Mir200.exe
echo Configuration file: config\M2Server.ini
echo.
echo To run the server:
echo   cd build\bin
echo   Mir200.exe
echo.
echo To run with custom config:
echo   Mir200.exe -c ..\..\config\M2Server.ini
echo.
echo For help:
echo   Mir200.exe --help
echo.

pause
