#include "MenuState.h"
#include "../Application.h"
#include "IntroState.h"
#include "PlayState.h"
#include <SDL2/SDL_ttf.h>
#include <iostream>

MenuState::MenuState(Application* app, GameState* previousState)
    : GameState(app)
    , m_previousState(previousState)
    , m_selectedOption(0)
{
}

MenuState::~MenuState()
{
}

void MenuState::Enter()
{
    // Load background texture
    m_backgroundTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (!m_backgroundTexture->LoadFromFile("assets/data/menu_background.png")) {
        std::cerr << "Failed to load menu background texture" << std::endl;
        
        // Create a semi-transparent black background
        SDL_Surface* surface = SDL_CreateRGBSurface(0, m_app->GetScreenWidth(), m_app->GetScreenHeight(), 32, 0, 0, 0, 0);
        SDL_FillRect(surface, nullptr, SDL_MapRGBA(surface->format, 0, 0, 0, 192));
        m_backgroundTexture->LoadFromSurface(surface);
        SDL_FreeSurface(surface);
    }
    
    // Create text textures
    CreateTextTextures();
}

void MenuState::Exit()
{
    // Clear textures
    m_backgroundTexture.reset();
    m_textTextures.clear();
}

void MenuState::Update(float deltaTime)
{
    // Nothing to update
}

void MenuState::Render()
{
    // Render previous state
    if (m_previousState) {
        m_previousState->Render();
    }
    
    // Render background
    if (m_backgroundTexture) {
        m_backgroundTexture->Render(0, 0);
    }
    
    // Render menu
    int menuY = 100;
    int menuSpacing = 40;
    
    for (size_t i = 0; i < m_textTextures.size(); i++) {
        if (m_textTextures[i]) {
            // Highlight selected option
            if (i == m_selectedOption) {
                m_textTextures[i]->SetColor(255, 255, 0);  // Yellow
            } else {
                m_textTextures[i]->SetColor(255, 255, 255);  // White
            }
            
            int textX = (m_app->GetScreenWidth() - m_textTextures[i]->GetWidth()) / 2;
            int textY = menuY + i * menuSpacing;
            m_textTextures[i]->Render(textX, textY);
        }
    }
}

void MenuState::HandleEvents(SDL_Event& event)
{
    if (event.type == SDL_KEYDOWN) {
        switch (event.key.keysym.sym) {
            case SDLK_UP:
                m_selectedOption = (m_selectedOption - 1 + m_textTextures.size()) % m_textTextures.size();
                break;
            case SDLK_DOWN:
                m_selectedOption = (m_selectedOption + 1) % m_textTextures.size();
                break;
            case SDLK_RETURN:
            case SDLK_SPACE:
                HandleMenuSelection();
                break;
            case SDLK_ESCAPE:
                // Return to previous state
                if (m_previousState) {
                    if (dynamic_cast<PlayState*>(m_previousState)) {
                        dynamic_cast<PlayState*>(m_previousState)->Resume();
                    }
                    m_app->ChangeState(std::unique_ptr<GameState>(m_previousState));
                    m_previousState = nullptr;  // Ownership transferred
                }
                break;
        }
    }
}

void MenuState::CreateTextTextures()
{
    // Load font
    TTF_Font* font = TTF_OpenFont("assets/data/font.ttf", 24);
    if (!font) {
        std::cerr << "Failed to load font: " << TTF_GetError() << std::endl;
        return;
    }
    
    // Create text textures
    SDL_Color textColor = {255, 255, 255, 255};  // White
    
    // Resume Game
    std::shared_ptr<Texture> resumeTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (resumeTexture->LoadFromText("Resume Game", font, textColor)) {
        m_textTextures.push_back(resumeTexture);
    }
    
    // Save Game
    std::shared_ptr<Texture> saveTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (saveTexture->LoadFromText("Save Game", font, textColor)) {
        m_textTextures.push_back(saveTexture);
    }
    
    // Load Game
    std::shared_ptr<Texture> loadTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (loadTexture->LoadFromText("Load Game", font, textColor)) {
        m_textTextures.push_back(loadTexture);
    }
    
    // Options
    std::shared_ptr<Texture> optionsTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (optionsTexture->LoadFromText("Options", font, textColor)) {
        m_textTextures.push_back(optionsTexture);
    }
    
    // Main Menu
    std::shared_ptr<Texture> mainMenuTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (mainMenuTexture->LoadFromText("Main Menu", font, textColor)) {
        m_textTextures.push_back(mainMenuTexture);
    }
    
    // Exit Game
    std::shared_ptr<Texture> exitTexture = std::make_shared<Texture>(m_app->GetRenderer());
    if (exitTexture->LoadFromText("Exit Game", font, textColor)) {
        m_textTextures.push_back(exitTexture);
    }
    
    // Close font
    TTF_CloseFont(font);
}

void MenuState::HandleMenuSelection()
{
    switch (m_selectedOption) {
        case 0:  // Resume Game
            if (m_previousState) {
                if (dynamic_cast<PlayState*>(m_previousState)) {
                    dynamic_cast<PlayState*>(m_previousState)->Resume();
                }
                m_app->ChangeState(std::unique_ptr<GameState>(m_previousState));
                m_previousState = nullptr;  // Ownership transferred
            }
            break;
        case 1:  // Save Game
            // TODO: Implement save game
            break;
        case 2:  // Load Game
            // TODO: Implement load game
            break;
        case 3:  // Options
            // TODO: Implement options
            break;
        case 4:  // Main Menu
            m_app->ChangeState(std::make_unique<IntroState>(m_app));
            break;
        case 5:  // Exit Game
            m_app->Quit();
            break;
    }
}

