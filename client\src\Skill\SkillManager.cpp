#include "SkillManager.h"
#include <fstream>
#include <iostream>
#include <sstream>

// 初始化静态成员变量
SkillManager* SkillManager::s_instance = nullptr;

SkillManager::SkillManager()
{
}

SkillManager* SkillManager::GetInstance()
{
    if (!s_instance) {
        s_instance = new SkillManager();
    }
    return s_instance;
}

void SkillManager::ReleaseInstance()
{
    if (s_instance) {
        delete s_instance;
        s_instance = nullptr;
    }
}

SkillManager::~SkillManager()
{
    m_skills.clear();
}

bool SkillManager::Initialize()
{
    // 添加一些默认技能
    // 火球术 - 基础火系攻击魔法
    AddSkill(std::make_shared<Skill>(
        1, "火球术", EffectType::FIRE, 1,
        10, 20, 30, JobClass::WIZARD, 1000
    ));

    // 治疗术 - 基础治疗魔法
    AddSkill(std::make_shared<Skill>(
        2, "治疗术", EffectType::HEAL, 1,
        15, 25, 40, JobClass::TAOIST, 2000
    ));

    // 雷电术 - 基础雷系攻击魔法
    AddSkill(std::make_shared<Skill>(
        3, "雷电术", EffectType::LIGHTNING, 1,
        12, 25, 35, JobClass::WIZARD, 1500
    ));

    // 刺杀剑术 - 基础物理攻击技能
    AddSkill(std::make_shared<Skill>(
        4, "刺杀剑术", EffectType::PHYSICAL, 1,
        5, 15, 25, JobClass::WARRIOR, 800
    ));

    // 隐身术 - 基础增益魔法
    AddSkill(std::make_shared<Skill>(
        5, "隐身术", EffectType::BUFF, 1,
        20, 0, 0, JobClass::TAOIST, 5000
    ));

    // 毒云术 - 基础减益魔法
    AddSkill(std::make_shared<Skill>(
        6, "毒云术", EffectType::DEBUFF, 1,
        18, 10, 20, JobClass::TAOIST, 3000
    ));

    // 烈火剑法 - 高级物理攻击技能
    AddSkill(std::make_shared<Skill>(
        7, "烈火剑法", EffectType::PHYSICAL, 2,
        15, 30, 50, JobClass::WARRIOR, 2000
    ));

    // 冰咆哮 - 高级冰系攻击魔法
    AddSkill(std::make_shared<Skill>(
        8, "冰咆哮", EffectType::ICE, 2,
        25, 40, 60, JobClass::WIZARD, 3000
    ));

    // 召唤骷髅 - 召唤技能
    AddSkill(std::make_shared<Skill>(
        9, "召唤骷髅", EffectType::SUMMON, 1,
        30, 0, 0, JobClass::TAOIST, 10000
    ));

    // 瞬息移动 - 传送技能
    AddSkill(std::make_shared<Skill>(
        10, "瞬息移动", EffectType::TELEPORT, 1,
        15, 0, 0, JobClass::WIZARD, 5000
    ));

    return true;
}

bool SkillManager::LoadSkills(const std::string& filename)
{
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open skill data file: " << filename << std::endl;
        return false;
    }

    std::string line;
    while (std::getline(file, line)) {
        // 跳过注释行和空行
        if (line.empty() || line[0] == '#') {
            continue;
        }

        std::istringstream iss(line);
        int id, effectTypeInt, effectValue, manaCost, power, maxPower, jobClassInt, cooldown;
        std::string name;

        // 解析技能数据
        if (iss >> id >> std::ws) {
            // 读取名称（可能包含空格）
            std::getline(iss, name, ',');

            if (iss >> effectTypeInt >> effectValue >> manaCost >> power >> maxPower >> jobClassInt >> cooldown) {
                // 创建技能
                EffectType effectType = static_cast<EffectType>(effectTypeInt);
                JobClass jobClass = static_cast<JobClass>(jobClassInt);

                std::shared_ptr<Skill> skill = std::make_shared<Skill>(
                    id, name, effectType, effectValue,
                    manaCost, power, maxPower, jobClass, cooldown
                );

                // 添加到技能列表
                AddSkill(skill);
            }
        }
    }

    file.close();
    return true;
}

void SkillManager::AddSkill(std::shared_ptr<Skill> skill)
{
    if (skill) {
        m_skills[skill->GetId()] = skill;
    }
}

std::shared_ptr<Skill> SkillManager::GetSkill(int skillId) const
{
    auto it = m_skills.find(skillId);
    if (it != m_skills.end()) {
        return it->second;
    }
    return nullptr;
}

std::vector<std::shared_ptr<Skill>> SkillManager::GetAllSkills() const
{
    std::vector<std::shared_ptr<Skill>> skills;
    for (const auto& pair : m_skills) {
        skills.push_back(pair.second);
    }
    return skills;
}

std::vector<std::shared_ptr<Skill>> SkillManager::GetSkillsByJob(JobClass jobClass) const
{
    std::vector<std::shared_ptr<Skill>> skills;
    for (const auto& pair : m_skills) {
        if (pair.second->GetJobClass() == jobClass || pair.second->GetJobClass() == JobClass::ALL) {
            skills.push_back(pair.second);
        }
    }
    return skills;
}

std::vector<std::shared_ptr<Skill>> SkillManager::GetSkillsByEffectType(EffectType effectType) const
{
    std::vector<std::shared_ptr<Skill>> skills;
    for (const auto& pair : m_skills) {
        if (pair.second->GetEffectType() == effectType) {
            skills.push_back(pair.second);
        }
    }
    return skills;
}

bool SkillManager::SaveSkillsToFile(const std::string& filename) const
{
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open file for writing: " << filename << std::endl;
        return false;
    }

    // 写入文件头
    file << "# Skill Data File" << std::endl;
    file << "# Format: ID Name EffectType EffectValue ManaCost Power MaxPower JobClass Cooldown" << std::endl;
    file << "# EffectType: 0=None, 1=Fire, 2=Ice, 3=Lightning, 4=Wind, 5=Holy, 6=Dark, 7=Physical, 8=Heal, 9=Buff, 10=Debuff, 11=Summon, 12=Teleport, 13=Special" << std::endl;
    file << "# JobClass: 0=All, 1=Warrior, 2=Wizard, 3=Taoist, 4=Assassin" << std::endl;
    file << std::endl;

    // 写入技能数据
    for (const auto& pair : m_skills) {
        const auto& skill = pair.second;
        file << skill->GetId() << " " << skill->GetName() << ", "
             << static_cast<int>(skill->GetEffectType()) << " "
             << skill->GetEffectValue() << " "
             << skill->GetManaCost() << " "
             << skill->GetPower() << " "
             << skill->GetMaxPower() << " "
             << static_cast<int>(skill->GetJobClass()) << " "
             << skill->GetCooldown() << std::endl;
    }

    file.close();
    return true;
}

bool SkillManager::CanLearnSkill(int skillId, int playerLevel, JobClass playerClass) const
{
    // 获取技能
    auto skill = GetSkill(skillId);
    if (!skill) {
        return false;
    }

    // 检查职业要求
    if (skill->GetJobClass() != JobClass::ALL && skill->GetJobClass() != playerClass) {
        return false;
    }

    // 检查等级要求
    if (skill->GetLevel() == 0 && playerLevel < skill->GetLevel1Requirement()) {
        return false;
    }
    else if (skill->GetLevel() == 1 && playerLevel < skill->GetLevel2Requirement()) {
        return false;
    }
    else if (skill->GetLevel() == 2 && playerLevel < skill->GetLevel3Requirement()) {
        return false;
    }

    return true;
}

std::shared_ptr<Skill> SkillManager::CreateSkill(int id, const std::string& name, EffectType effectType, int effectValue,
                                               int manaCost, int power, int maxPower, JobClass jobClass, int cooldown)
{
    // 创建技能
    auto skill = std::make_shared<Skill>(id, name, effectType, effectValue, manaCost, power, maxPower, jobClass, cooldown);

    // 设置默认的等级要求和训练点数
    if (jobClass == JobClass::WARRIOR) {
        skill->SetLevel1Requirement(5);
        skill->SetLevel2Requirement(15);
        skill->SetLevel3Requirement(25);
    }
    else if (jobClass == JobClass::WIZARD) {
        skill->SetLevel1Requirement(7);
        skill->SetLevel2Requirement(17);
        skill->SetLevel3Requirement(27);
    }
    else if (jobClass == JobClass::TAOIST) {
        skill->SetLevel1Requirement(6);
        skill->SetLevel2Requirement(16);
        skill->SetLevel3Requirement(26);
    }
    else if (jobClass == JobClass::ASSASSIN) {
        skill->SetLevel1Requirement(8);
        skill->SetLevel2Requirement(18);
        skill->SetLevel3Requirement(28);
    }

    // 设置默认的训练点数
    skill->SetLevel1TrainingPoints(50);
    skill->SetLevel2TrainingPoints(100);
    skill->SetLevel3TrainingPoints(200);

    // 设置默认的攻击范围和施放条件
    if (effectType == EffectType::FIRE || effectType == EffectType::ICE || effectType == EffectType::LIGHTNING) {
        skill->SetAttackRange(AttackRange::AREA);
    }
    else if (effectType == EffectType::BUFF || effectType == EffectType::DEBUFF) {
        skill->SetAttackRange(AttackRange::SCREEN);
    }
    else {
        skill->SetAttackRange(AttackRange::SINGLE);
    }

    if (jobClass == JobClass::TAOIST && (effectType == EffectType::BUFF || effectType == EffectType::DEBUFF)) {
        skill->SetCastCondition(CastCondition::SCROLL);
    }
    else {
        skill->SetCastCondition(CastCondition::MANA);
    }

    return skill;
}
