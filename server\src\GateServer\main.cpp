#include "GateServer.h"
#include "../Common/Logger.h"
#include <iostream>
#include <string>
#include <signal.h>

using namespace MirServer;

// 全局GateServer实例
std::unique_ptr<GateServer> g_gateServer;

// 信号处理函数
void SignalHandler(int signal) {
    std::cout << "\nReceived signal " << signal << ", shutting down GateServer..." << std::endl;
    
    if (g_gateServer) {
        g_gateServer->Stop();
        g_gateServer.reset();
    }
    
    exit(0);
}

int main() {
    std::cout << "=== MirServer GateServer ===" << std::endl;
    std::cout << "Version: 1.0.0" << std::endl;
    std::cout << "Build Date: " << __DATE__ << " " << __TIME__ << std::endl;
    std::cout << "=============================" << std::endl;
    
    // 设置信号处理
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    #ifdef _WIN32
    signal(SIGBREAK, SignalHandler);
    #endif
    
    try {
        // 创建GateServer实例
        g_gateServer = std::make_unique<GateServer>();
        
        // 初始化GateServer
        std::string configFile = "config/GateServer.ini";
        if (!g_gateServer->Initialize(configFile)) {
            std::cerr << "Failed to initialize GateServer" << std::endl;
            return -1;
        }
        
        // 启动GateServer
        if (!g_gateServer->Start()) {
            std::cerr << "Failed to start GateServer" << std::endl;
            return -1;
        }
        
        std::cout << "GateServer is running. Press Ctrl+C to stop." << std::endl;
        
        // 主循环
        while (g_gateServer->IsRunning()) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
            
            // 可以在这里添加状态监控或其他周期性任务
            static int statusCounter = 0;
            if (++statusCounter >= 30) { // 每30秒显示一次状态
                statusCounter = 0;
                std::cout << "GateServer Status - Active Connections: " 
                         << g_gateServer->GetActiveConnections() 
                         << ", Sessions: " << g_gateServer->GetSessionCount() 
                         << std::endl;
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "GateServer exception: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return -1;
    }
    
    std::cout << "GateServer shutdown completed." << std::endl;
    return 0;
} 