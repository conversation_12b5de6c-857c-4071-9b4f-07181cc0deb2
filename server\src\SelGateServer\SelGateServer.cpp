#include "SelGateServer.h"
#include "../Common/Logger.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <random>
#include <iostream>
#include <ctime>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#endif

namespace MirServer {

SelGateServer::SelGateServer() 
    : m_sessionCount(0), m_isRunning(false), m_isReady(false)
    , m_serverName("SelGate"), m_listenIP("0.0.0.0"), m_listenPort(7100)
    , m_maxConnOfIP(10), m_sessionTimeout(300000), m_heartbeatInterval(30)
    , m_checkTimeout(60), m_blockMethod(DISCONNECT), m_selectStrategy(LEAST_CONNECTIONS)
    , m_enableLoadBalance(true), m_enableIPFilter(true), m_currentRoundRobinIndex(0)
{
    // 初始化会话数组
    for (int i = 0; i < MAX_SESSIONS; ++i) {
        m_sessions[i] = ClientSession();
    }
}

SelGateServer::~SelGateServer() {
    Stop();
}

bool SelGateServer::Initialize(const std::string& configFile) {
    m_configFile = configFile;
    
    AddMainLogMsg("Initializing SelGateServer...", 0);
    
    // 加载配置
    LoadConfig();
    LoadRunGateList();
    LoadBlockIPFile();
    
    // 创建网络管理器
    m_networkManager = std::make_unique<NetworkManager>();
    if (!m_networkManager) {
        AddMainLogMsg("Failed to create network manager", 1);
        return false;
    }
    
    AddMainLogMsg("SelGateServer initialized successfully", 0);
    return true;
}

bool SelGateServer::Start() {
    if (m_isRunning) {
        return true;
    }
    
    AddMainLogMsg("Starting SelGateServer on " + m_listenIP + ":" + std::to_string(m_listenPort), 0);
    
    m_isRunning = true;
    m_isReady = true;
    
    // 启动工作线程
    m_heartbeatThread = std::thread(&SelGateServer::HeartbeatThread, this);
    m_checkThread = std::thread(&SelGateServer::CheckThread, this);
    m_processThread = std::thread(&SelGateServer::ProcessThread, this);
    
    AddMainLogMsg("SelGateServer started successfully", 0);
    AddMainLogMsg("Loaded " + std::to_string(m_runGateServers.size()) + " RunGate servers", 3);
    
    return true;
}

void SelGateServer::Stop() {
    if (!m_isRunning) {
        return;
    }
    
    AddMainLogMsg("Stopping SelGateServer...", 0);
    
    m_isRunning = false;
    m_isReady = false;
    
    // 等待线程结束
    if (m_heartbeatThread.joinable()) {
        m_heartbeatThread.join();
    }
    if (m_checkThread.joinable()) {
        m_checkThread.join();
    }
    if (m_processThread.joinable()) {
        m_processThread.join();
    }
    
    // 保存配置
    SaveConfig();
    SaveRunGateList();
    SaveBlockIPFile();
    
    AddMainLogMsg("SelGateServer stopped", 0);
}

void SelGateServer::LoadConfig() {
    AddMainLogMsg("Loading configuration...", 3);
    
    std::ifstream file(m_configFile);
    if (!file.is_open()) {
        AddMainLogMsg("Config file not found, using defaults", 2);
        return;
    }
    
    std::string line, section;
    while (std::getline(file, line)) {
        // 移除首尾空白字符
        line.erase(0, line.find_first_not_of(" \t\r\n"));
        line.erase(line.find_last_not_of(" \t\r\n") + 1);
        
        if (line.empty() || line[0] == ';' || line[0] == '#') {
            continue;
        }
        
        if (line[0] == '[' && line.back() == ']') {
            section = line.substr(1, line.length() - 2);
            continue;
        }
        
        size_t pos = line.find('=');
        if (pos == std::string::npos) {
            continue;
        }
        
        std::string key = line.substr(0, pos);
        std::string value = line.substr(pos + 1);
        
        // 移除key和value的空白字符
        key.erase(0, key.find_first_not_of(" \t"));
        key.erase(key.find_last_not_of(" \t") + 1);
        value.erase(0, value.find_first_not_of(" \t"));
        value.erase(value.find_last_not_of(" \t") + 1);
        
        if (section == "SelGate") {
            if (key == "ServerName") m_serverName = value;
            else if (key == "ListenIP") m_listenIP = value;
            else if (key == "ListenPort") m_listenPort = std::stoi(value);
            else if (key == "MaxConnOfIP") m_maxConnOfIP = std::stoi(value);
            else if (key == "SessionTimeOut") m_sessionTimeout = std::stoi(value);
            else if (key == "HeartbeatInterval") m_heartbeatInterval = std::stoi(value);
            else if (key == "CheckTimeOut") m_checkTimeout = std::stoi(value);
            else if (key == "BlockMethod") m_blockMethod = static_cast<BlockIPMethod>(std::stoi(value));
            else if (key == "SelectStrategy") m_selectStrategy = static_cast<ServerSelectStrategy>(std::stoi(value));
            else if (key == "EnableLoadBalance") m_enableLoadBalance = (value == "1" || value == "true");
            else if (key == "EnableIPFilter") m_enableIPFilter = (value == "1" || value == "true");
        }
    }
    
    file.close();
    AddMainLogMsg("Configuration loaded", 3);
}

void SelGateServer::SaveConfig() {
    std::ofstream file(m_configFile);
    if (!file.is_open()) {
        AddMainLogMsg("Failed to save configuration", 1);
        return;
    }
    
    file << "[SelGate]\n";
    file << "ServerName=" << m_serverName << "\n";
    file << "ListenIP=" << m_listenIP << "\n";
    file << "ListenPort=" << m_listenPort << "\n";
    file << "MaxConnOfIP=" << m_maxConnOfIP << "\n";
    file << "SessionTimeOut=" << m_sessionTimeout << "\n";
    file << "HeartbeatInterval=" << m_heartbeatInterval << "\n";
    file << "CheckTimeOut=" << m_checkTimeout << "\n";
    file << "BlockMethod=" << static_cast<int>(m_blockMethod) << "\n";
    file << "SelectStrategy=" << static_cast<int>(m_selectStrategy) << "\n";
    file << "EnableLoadBalance=" << (m_enableLoadBalance ? "1" : "0") << "\n";
    file << "EnableIPFilter=" << (m_enableIPFilter ? "1" : "0") << "\n";
    
    file.close();
}

void SelGateServer::LoadRunGateList() {
    AddMainLogMsg("Loading RunGate server list...", 3);
    
    std::ifstream file("RunGateList.txt");
    if (!file.is_open()) {
        AddMainLogMsg("RunGateList.txt not found, creating default", 2);
        
        // 创建默认RunGate服务器
        RunGateInfo defaultGate;
        defaultGate.serverName = "RunGate1";
        defaultGate.serverIP = "127.0.0.1";
        defaultGate.serverPort = 5000;
        defaultGate.gateIP = "127.0.0.1";
        defaultGate.gatePort = 7200;
        defaultGate.maxUsers = 1000;
        defaultGate.isEnabled = true;
        
        m_runGateServers.push_back(defaultGate);
        SaveRunGateList();
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_serverMutex);
    m_runGateServers.clear();
    
    std::string line;
    while (std::getline(file, line)) {
        if (line.empty() || line[0] == ';' || line[0] == '#') {
            continue;
        }
        
        // 格式：ServerName|ServerIP|ServerPort|GateIP|GatePort|MaxUsers|Enabled
        std::istringstream iss(line);
        std::string token;
        std::vector<std::string> tokens;
        
        while (std::getline(iss, token, '|')) {
            tokens.push_back(token);
        }
        
        if (tokens.size() >= 7) {
            RunGateInfo gateInfo;
            gateInfo.serverName = tokens[0];
            gateInfo.serverIP = tokens[1];
            gateInfo.serverPort = std::stoi(tokens[2]);
            gateInfo.gateIP = tokens[3];
            gateInfo.gatePort = std::stoi(tokens[4]);
            gateInfo.maxUsers = std::stoi(tokens[5]);
            gateInfo.isEnabled = (tokens[6] == "1" || tokens[6] == "true");
            
            m_runGateServers.push_back(gateInfo);
        }
    }
    
    file.close();
    AddMainLogMsg("Loaded " + std::to_string(m_runGateServers.size()) + " RunGate servers", 3);
}

void SelGateServer::SaveRunGateList() {
    std::ofstream file("RunGateList.txt");
    if (!file.is_open()) {
        AddMainLogMsg("Failed to save RunGate server list", 1);
        return;
    }
    
    file << "; RunGate Server List\n";
    file << "; Format: ServerName|ServerIP|ServerPort|GateIP|GatePort|MaxUsers|Enabled\n";
    
    std::lock_guard<std::mutex> lock(m_serverMutex);
    for (const auto& server : m_runGateServers) {
        file << server.serverName << "|"
             << server.serverIP << "|"
             << server.serverPort << "|"
             << server.gateIP << "|"
             << server.gatePort << "|"
             << server.maxUsers << "|"
             << (server.isEnabled ? "1" : "0") << "\n";
    }
    
    file.close();
}

bool SelGateServer::AddRunGateServer(const RunGateInfo& serverInfo) {
    std::lock_guard<std::mutex> lock(m_serverMutex);
    
    // 检查是否已存在同名服务器
    for (const auto& server : m_runGateServers) {
        if (server.serverName == serverInfo.serverName) {
            return false;
        }
    }
    
    m_runGateServers.push_back(serverInfo);
    AddMainLogMsg("Added RunGate server: " + serverInfo.serverName, 3);
    return true;
}

bool SelGateServer::RemoveRunGateServer(const std::string& serverName) {
    std::lock_guard<std::mutex> lock(m_serverMutex);
    
    auto it = std::find_if(m_runGateServers.begin(), m_runGateServers.end(),
                          [&serverName](const RunGateInfo& server) {
                              return server.serverName == serverName;
                          });
    
    if (it != m_runGateServers.end()) {
        m_runGateServers.erase(it);
        AddMainLogMsg("Removed RunGate server: " + serverName, 3);
        return true;
    }
    
    return false;
}

int SelGateServer::SelectBestRunGate() {
    std::lock_guard<std::mutex> lock(m_serverMutex);
    
    if (m_runGateServers.empty()) {
        return -1;
    }
    
    switch (m_selectStrategy) {
        case ROUND_ROBIN:
            return SelectByRoundRobin();
        case LEAST_CONNECTIONS:
            return SelectByLeastConnections();
        case LOAD_BALANCE:
            return SelectByLoadBalance();
        case RANDOM:
            return SelectByRandom();
        default:
            return SelectByLeastConnections();
    }
}

int SelGateServer::SelectByRoundRobin() {
    int startIndex = m_currentRoundRobinIndex;
    int serverCount = static_cast<int>(m_runGateServers.size());
    
    for (int i = 0; i < serverCount; ++i) {
        int index = (startIndex + i) % serverCount;
        const auto& server = m_runGateServers[index];
        
        if (server.isEnabled && server.isOnline && server.currentUsers < server.maxUsers) {
            m_currentRoundRobinIndex = (index + 1) % serverCount;
            return index;
        }
    }
    
    return -1;
}

int SelGateServer::SelectByLeastConnections() {
    int bestIndex = -1;
    int minConnections = INT_MAX;
    
    for (int i = 0; i < static_cast<int>(m_runGateServers.size()); ++i) {
        const auto& server = m_runGateServers[i];
        
        if (server.isEnabled && server.isOnline && server.currentUsers < server.maxUsers) {
            if (server.currentUsers < minConnections) {
                minConnections = server.currentUsers;
                bestIndex = i;
            }
        }
    }
    
    return bestIndex;
}

int SelGateServer::SelectByLoadBalance() {
    int bestIndex = -1;
    double minLoadRatio = 1.0;
    
    for (int i = 0; i < static_cast<int>(m_runGateServers.size()); ++i) {
        const auto& server = m_runGateServers[i];
        
        if (server.isEnabled && server.isOnline && server.currentUsers < server.maxUsers) {
            double loadRatio = static_cast<double>(server.currentUsers) / server.maxUsers;
            if (loadRatio < minLoadRatio) {
                minLoadRatio = loadRatio;
                bestIndex = i;
            }
        }
    }
    
    return bestIndex;
}

int SelGateServer::SelectByRandom() {
    std::vector<int> availableServers;
    
    for (int i = 0; i < static_cast<int>(m_runGateServers.size()); ++i) {
        const auto& server = m_runGateServers[i];
        
        if (server.isEnabled && server.isOnline && server.currentUsers < server.maxUsers) {
            availableServers.push_back(i);
        }
    }
    
    if (availableServers.empty()) {
        return -1;
    }
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, availableServers.size() - 1);
    
    return availableServers[dis(gen)];
}

void SelGateServer::OnClientConnect(std::shared_ptr<ClientConnection> connection) {
    std::string clientIP = connection->GetRemoteIP();
    
    // 检查IP是否被阻挡
    if (m_enableIPFilter && IsBlockedIP(clientIP)) {
        AddMainLogMsg("Blocked IP connection: " + clientIP, 3);
        connection->Disconnect();
        return;
    }
    
    // 检查连接数限制
    if (m_enableIPFilter && IsConnectionLimited(clientIP)) {
        AddMainLogMsg("Connection limit exceeded for IP: " + clientIP, 3);
        switch (m_blockMethod) {
            case DISCONNECT:
                connection->Disconnect();
                break;
            case TEMP_BLOCK:
                AddTempBlockIP(clientIP);
                connection->Disconnect();
                break;
            case PERMANENT_BLOCK:
                AddBlockIP(clientIP);
                connection->Disconnect();
                break;
        }
        return;
    }
    
    // 查找可用的会话槽
    int sessionIndex = FindAvailableSession();
    if (sessionIndex == -1) {
        AddMainLogMsg("No available session slots", 2);
        connection->Disconnect();
        return;
    }
    
    // 初始化会话
    ClientSession* session = &m_sessions[sessionIndex];
    session->socket = connection;
    session->remoteIP = clientIP;
    session->socketHandle = static_cast<int>(connection->GetId());
    session->connectTime = std::chrono::steady_clock::now();
    session->lastActiveTime = session->connectTime;
    session->isAssigned = false;
    session->assignedGateIndex = -1;
    
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        m_socketToSessionMap[session->socketHandle] = sessionIndex;
        m_sessionCount++;
    }
    
    AddMainLogMsg("Client connected from " + clientIP + " (Session: " + std::to_string(sessionIndex) + ")", 4);
    
    // 发送服务器列表
    SendServerList(connection);
}

void SelGateServer::OnClientDisconnect(std::shared_ptr<ClientConnection> connection) {
    int socketHandle = static_cast<int>(connection->GetId());
    
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    auto it = m_socketToSessionMap.find(socketHandle);
    if (it != m_socketToSessionMap.end()) {
        int sessionIndex = it->second;
        RemoveSession(sessionIndex);
        m_socketToSessionMap.erase(it);
        m_sessionCount--;
        
        AddMainLogMsg("Client disconnected (Session: " + std::to_string(sessionIndex) + ")", 4);
    }
}

void SelGateServer::OnClientMessage(std::shared_ptr<ClientConnection> connection, const std::vector<BYTE>& data) {
    int socketHandle = static_cast<int>(connection->GetId());
    
    ClientSession* session = GetSessionBySocket(socketHandle);
    if (!session) {
        return;
    }
    
    session->lastActiveTime = std::chrono::steady_clock::now();
    ProcessClientRequest(session, data);
}

void SelGateServer::ProcessClientRequest(ClientSession* session, const std::vector<BYTE>& data) {
    // 简单的协议处理，实际应该根据具体协议来实现
    if (data.size() < 4) {
        return;
    }
    
    // 检查是否是服务器选择请求
    if (data[0] == 0xAA && data[1] == 0x55) {
        // 选择最佳RunGate服务器
        int gateIndex = SelectBestRunGate();
        if (gateIndex >= 0) {
            const auto& gateInfo = m_runGateServers[gateIndex];
            SendRedirectMessage(session->socket, gateInfo);
            
            session->isAssigned = true;
            session->assignedGateIndex = gateIndex;
            
            AddMainLogMsg("Assigned client to " + gateInfo.serverName + 
                         " (" + gateInfo.gateIP + ":" + std::to_string(gateInfo.gatePort) + ")", 4);
        } else {
            AddMainLogMsg("No available RunGate servers", 2);
            session->socket->Disconnect();
        }
    }
}

void SelGateServer::SendServerList(std::shared_ptr<ClientConnection> connection) {
    // 构造服务器列表消息
    std::string serverListMsg = "SERVERLIST:";
    
    std::lock_guard<std::mutex> lock(m_serverMutex);
    for (const auto& server : m_runGateServers) {
        if (server.isEnabled && server.isOnline) {
            serverListMsg += server.serverName + "," + 
                           server.gateIP + "," + 
                           std::to_string(server.gatePort) + "," +
                           std::to_string(server.currentUsers) + "," +
                           std::to_string(server.maxUsers) + ";";
        }
    }
    
    connection->Send(serverListMsg.c_str(), serverListMsg.length());
}

void SelGateServer::SendRedirectMessage(std::shared_ptr<ClientConnection> connection, const RunGateInfo& gateInfo) {
    // 构造重定向消息
    std::string redirectMsg = "REDIRECT:" + gateInfo.gateIP + ":" + std::to_string(gateInfo.gatePort);
    
    connection->Send(redirectMsg.c_str(), redirectMsg.length());
}

void SelGateServer::HeartbeatThread() {
    while (m_isRunning) {
        try {
            CheckRunGateServers();
            std::this_thread::sleep_for(std::chrono::seconds(m_heartbeatInterval));
        } catch (const std::exception& e) {
            AddMainLogMsg("HeartbeatThread exception: " + std::string(e.what()), 1);
        }
    }
}

void SelGateServer::CheckThread() {
    while (m_isRunning) {
        try {
            CleanupExpiredSessions();
            std::this_thread::sleep_for(std::chrono::seconds(30));
        } catch (const std::exception& e) {
            AddMainLogMsg("CheckThread exception: " + std::string(e.what()), 1);
        }
    }
}

void SelGateServer::ProcessThread() {
    while (m_isRunning) {
        try {
            // 处理各种后台任务
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        } catch (const std::exception& e) {
            AddMainLogMsg("ProcessThread exception: " + std::string(e.what()), 1);
        }
    }
}

void SelGateServer::CheckRunGateServers() {
    std::lock_guard<std::mutex> lock(m_serverMutex);
    
    for (auto& server : m_runGateServers) {
        if (!server.isEnabled) {
            continue;
        }
        
        bool wasOnline = server.isOnline;
        server.isOnline = CheckRunGateStatus(server);
        
        if (wasOnline && !server.isOnline) {
            AddMainLogMsg("RunGate server " + server.serverName + " is offline", 2);
        } else if (!wasOnline && server.isOnline) {
            AddMainLogMsg("RunGate server " + server.serverName + " is back online", 3);
        }
    }
}

bool SelGateServer::CheckRunGateStatus(RunGateInfo& serverInfo) {
    // 简单的连接测试，实际应该发送心跳包
    // 这里先返回true作为示例
    return true;
}

int SelGateServer::FindAvailableSession() {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    for (int i = 0; i < MAX_SESSIONS; ++i) {
        if (!m_sessions[i].socket) {
            return i;
        }
    }
    
    return -1;
}

ClientSession* SelGateServer::GetSessionBySocket(int socketHandle) {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    auto it = m_socketToSessionMap.find(socketHandle);
    if (it != m_socketToSessionMap.end()) {
        return &m_sessions[it->second];
    }
    
    return nullptr;
}

void SelGateServer::RemoveSession(int sessionIndex) {
    if (sessionIndex >= 0 && sessionIndex < MAX_SESSIONS) {
        m_sessions[sessionIndex] = ClientSession();
    }
}

void SelGateServer::CleanupExpiredSessions() {
    auto now = std::chrono::steady_clock::now();
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    for (int i = 0; i < MAX_SESSIONS; ++i) {
        auto& session = m_sessions[i];
        if (session.socket) {
            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - session.lastActiveTime);
            if (elapsed.count() > m_sessionTimeout) {
                AddMainLogMsg("Session " + std::to_string(i) + " expired", 4);
                session.socket->Disconnect();
                RemoveSession(i);
                m_sessionCount--;
            }
        }
    }
}

bool SelGateServer::IsBlockedIP(const std::string& ipAddress) {
    int ipAddr = StringToIP(ipAddress);
    if (ipAddr == 0) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    for (const auto& addr : m_blockIPList) {
        if (addr.ipAddress == ipAddr) {
            return true;
        }
    }
    
    for (const auto& addr : m_tempBlockIPList) {
        if (addr.ipAddress == ipAddr) {
            return true;
        }
    }
    
    return false;
}

bool SelGateServer::IsConnectionLimited(const std::string& ipAddress) {
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    auto it = m_currentIPList.find(ipAddress);
    if (it != m_currentIPList.end()) {
        return it->second.size() >= static_cast<size_t>(m_maxConnOfIP);
    }
    
    return false;
}

std::string SelGateServer::IPToString(int ipAddress) {
    struct in_addr addr;
    addr.s_addr = ipAddress;
#ifdef _WIN32
    return std::string(inet_ntoa(addr));
#else
    char ip_str[INET_ADDRSTRLEN];
    inet_ntop(AF_INET, &addr, ip_str, INET_ADDRSTRLEN);
    return std::string(ip_str);
#endif
}

int SelGateServer::StringToIP(const std::string& ipString) {
#ifdef _WIN32
    int addr = inet_addr(ipString.c_str());
    return (addr != INADDR_NONE) ? addr : 0;
#else
    struct in_addr addr;
    return inet_aton(ipString.c_str(), &addr) ? addr.s_addr : 0;
#endif
}

void SelGateServer::AddMainLogMsg(const std::string& msg, int level) {
    std::time_t now = std::time(nullptr);
    std::tm* timeinfo = std::localtime(&now);
    char timestamp[64];
    std::strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", timeinfo);
    
    std::string logMsg = "[" + std::string(timestamp) + "] " + msg;
    
    // 输出到控制台
    if (level <= 3) {
        std::cout << logMsg << std::endl;
    }
    
    // 记录到日志系统
    switch(level) {
        case 0: Logger::Info(msg); break;
        case 1: Logger::Warning(msg); break;
        case 2: Logger::Info(msg); break;
        case 3: Logger::Debug(msg); break;
        default: Logger::Debug(msg); break;
    }
}

void SelGateServer::LoadBlockIPFile() {
    std::ifstream file("BlockIPList.txt");
    if (!file.is_open()) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_ipMutex);
    m_blockIPList.clear();
    
    std::string line;
    while (std::getline(file, line)) {
        if (!line.empty()) {
            IPAddressInfo info;
            info.ipAddress = StringToIP(line);
            if (info.ipAddress != 0) {
                info.isBlocked = true;
                m_blockIPList.push_back(info);
            }
        }
    }
    
    file.close();
    AddMainLogMsg("Loaded " + std::to_string(m_blockIPList.size()) + " blocked IPs", 4);
}

void SelGateServer::SaveBlockIPFile() {
    std::ofstream file("BlockIPList.txt");
    if (!file.is_open()) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_ipMutex);
    for (const auto& addr : m_blockIPList) {
        file << IPToString(addr.ipAddress) << "\n";
    }
    
    file.close();
}

int SelGateServer::AddBlockIP(const std::string& ipAddress) {
    int ipAddr = StringToIP(ipAddress);
    if (ipAddr == 0) {
        return 0;
    }
    
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    // 检查是否已存在
    for (const auto& addr : m_blockIPList) {
        if (addr.ipAddress == ipAddr) {
            return m_blockIPList.size();
        }
    }
    
    IPAddressInfo info;
    info.ipAddress = ipAddr;
    info.isBlocked = true;
    m_blockIPList.push_back(info);
    
    return m_blockIPList.size();
}

int SelGateServer::AddTempBlockIP(const std::string& ipAddress) {
    int ipAddr = StringToIP(ipAddress);
    if (ipAddr == 0) {
        return 0;
    }
    
    std::lock_guard<std::mutex> lock(m_ipMutex);
    
    // 检查是否已存在
    for (const auto& addr : m_tempBlockIPList) {
        if (addr.ipAddress == ipAddr) {
            return m_tempBlockIPList.size();
        }
    }
    
    IPAddressInfo info;
    info.ipAddress = ipAddr;
    info.isBlocked = true;
    m_tempBlockIPList.push_back(info);
    
    return m_tempBlockIPList.size();
}

int SelGateServer::GetActiveRunGateCount() const {
    std::lock_guard<std::mutex> lock(m_serverMutex);
    
    int count = 0;
    for (const auto& server : m_runGateServers) {
        if (server.isEnabled && server.isOnline) {
            count++;
        }
    }
    
    return count;
}

std::string SelGateServer::GetServerStatus() const {
    std::stringstream ss;
    ss << "SelGateServer Status:\n";
    ss << "- Active Sessions: " << m_sessionCount << "/" << MAX_SESSIONS << "\n";
    ss << "- Active RunGates: " << GetActiveRunGateCount() << "/" << m_runGateServers.size() << "\n";
    ss << "- Strategy: ";
    
    switch (m_selectStrategy) {
        case ROUND_ROBIN: ss << "Round Robin"; break;
        case LEAST_CONNECTIONS: ss << "Least Connections"; break;
        case LOAD_BALANCE: ss << "Load Balance"; break;
        case RANDOM: ss << "Random"; break;
    }
    
    ss << "\n- Load Balance: " << (m_enableLoadBalance ? "Enabled" : "Disabled");
    ss << "\n- IP Filter: " << (m_enableIPFilter ? "Enabled" : "Disabled");
    
    return ss.str();
}

// 其他方法的基础实现
bool SelGateServer::UpdateRunGateServer(const std::string& serverName, const RunGateInfo& serverInfo) {
    std::lock_guard<std::mutex> lock(m_serverMutex);
    
    for (auto& server : m_runGateServers) {
        if (server.serverName == serverName) {
            server = serverInfo;
            return true;
        }
    }
    
    return false;
}

RunGateInfo* SelGateServer::GetRunGateServer(const std::string& serverName) {
    std::lock_guard<std::mutex> lock(m_serverMutex);
    
    for (auto& server : m_runGateServers) {
        if (server.serverName == serverName) {
            return &server;
        }
    }
    
    return nullptr;
}

std::vector<RunGateInfo> SelGateServer::GetAllRunGateServers() const {
    std::lock_guard<std::mutex> lock(m_serverMutex);
    return m_runGateServers;
}

bool SelGateServer::AssignClientToRunGate(ClientSession* session, int runGateIndex) {
    if (runGateIndex < 0 || runGateIndex >= static_cast<int>(m_runGateServers.size())) {
        return false;
    }
    
    session->isAssigned = true;
    session->assignedGateIndex = runGateIndex;
    
    return true;
}

void SelGateServer::SendRunGateInfo(std::shared_ptr<ClientConnection> connection, const RunGateInfo& gateInfo) {
    SendRedirectMessage(connection, gateInfo);
}

void SelGateServer::CloseConnectionsByIP(const std::string& ipAddress) {
    // 实现关闭指定IP的所有连接
}

void SelGateServer::UpdateRunGateUserCount(int serverIndex, int userCount) {
    if (serverIndex >= 0 && serverIndex < static_cast<int>(m_runGateServers.size())) {
        m_runGateServers[serverIndex].currentUsers = userCount;
    }
}

int SelGateServer::GetTotalConnections() const {
    int total = 0;
    std::lock_guard<std::mutex> lock(m_serverMutex);
    
    for (const auto& server : m_runGateServers) {
        total += server.currentUsers;
    }
    
    return total;
}

} // namespace MirServer 