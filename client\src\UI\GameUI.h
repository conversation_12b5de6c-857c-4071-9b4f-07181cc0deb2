#pragma once

#include "UIControl.h"
#include "../Actor/Player.h"
#include <memory>
#include <array>

/**
 * @class GameUI
 * @brief Main game interface UI
 *
 * This class represents the main game interface UI, which displays player stats,
 * health bar, mana bar, experience bar, etc.
 */
class GameUI : public UIControl {
private:
    std::shared_ptr<Player> m_player;  ///< Player reference

    // UI elements
    int m_healthBarX;                  ///< Health bar X position
    int m_healthBarY;                  ///< Health bar Y position
    int m_healthBarWidth;              ///< Health bar width
    int m_healthBarHeight;             ///< Health bar height

    int m_manaBarX;                    ///< Mana bar X position
    int m_manaBarY;                    ///< Mana bar Y position
    int m_manaBarWidth;                ///< Mana bar width
    int m_manaBarHeight;               ///< Mana bar height

    int m_expBarX;                     ///< Experience bar X position
    int m_expBarY;                     ///< Experience bar Y position
    int m_expBarWidth;                 ///< Experience bar width
    int m_expBarHeight;                ///< Experience bar height

    // Bottom panel elements
    int m_bottomPanelX;                ///< Bottom panel X position
    int m_bottomPanelY;                ///< Bottom panel Y position
    int m_bottomPanelWidth;            ///< Bottom panel width
    int m_bottomPanelHeight;           ///< Bottom panel height

    // Belt items
    static const int MAX_BELT_ITEMS = 6;
    std::array<int, MAX_BELT_ITEMS> m_beltItemX;  ///< Belt item X positions
    std::array<int, MAX_BELT_ITEMS> m_beltItemY;  ///< Belt item Y positions
    int m_beltItemWidth;               ///< Belt item width
    int m_beltItemHeight;              ///< Belt item height

    // Gold display
    int m_goldX;                       ///< Gold display X position
    int m_goldY;                       ///< Gold display Y position
    int m_goldWidth;                   ///< Gold display width
    int m_goldHeight;                  ///< Gold display height
    int m_gold;                        ///< Current gold amount

    // Mini map
    int m_miniMapX;                    ///< Mini map X position
    int m_miniMapY;                    ///< Mini map Y position
    int m_miniMapWidth;                ///< Mini map width
    int m_miniMapHeight;               ///< Mini map height
    bool m_showMiniMap;                ///< Whether to show mini map
    int m_miniMapMode;                 ///< Mini map display mode (0=normal, 1=transparent)
    int m_miniMapIndex;                ///< Mini map resource index

    // Button positions
    int m_stateBtnX;                   ///< State button X position
    int m_stateBtnY;                   ///< State button Y position
    int m_bagBtnX;                     ///< Bag button X position
    int m_bagBtnY;                     ///< Bag button Y position
    int m_skillBtnX;                   ///< Skill button X position
    int m_skillBtnY;                   ///< Skill button Y position
    int m_optionBtnX;                  ///< Option button X position
    int m_optionBtnY;                  ///< Option button Y position
    int m_miniMapBtnX;                 ///< Mini map button X position
    int m_miniMapBtnY;                 ///< Mini map button Y position
    int m_tradeBtnX;                   ///< Trade button X position
    int m_tradeBtnY;                   ///< Trade button Y position
    int m_guildBtnX;                   ///< Guild button X position
    int m_guildBtnY;                   ///< Guild button Y position
    int m_groupBtnX;                   ///< Group button X position
    int m_groupBtnY;                   ///< Group button Y position
    int m_exitBtnX;                    ///< Exit button X position
    int m_exitBtnY;                    ///< Exit button Y position
    int m_logoutBtnX;                  ///< Logout button X position
    int m_logoutBtnY;                  ///< Logout button Y position

    // Resource indices
    int m_mainFrameIndex;              ///< Main frame image index
    int m_healthBarFrameIndex;         ///< Health bar frame image index
    int m_manaBarFrameIndex;           ///< Mana bar frame image index
    int m_expBarFrameIndex;            ///< Experience bar frame image index
    int m_bottomPanelIndex;            ///< Bottom panel image index
    int m_beltItemFrameIndex;          ///< Belt item frame image index
    int m_goldFrameIndex;              ///< Gold frame image index
    int m_stateBtnIndex;               ///< State button image index
    int m_bagBtnIndex;                 ///< Bag button image index
    int m_skillBtnIndex;               ///< Skill button image index
    int m_optionBtnIndex;              ///< Option button image index
    int m_miniMapBtnIndex;             ///< Mini map button image index
    int m_tradeBtnIndex;               ///< Trade button image index
    int m_guildBtnIndex;               ///< Guild button image index
    int m_groupBtnIndex;               ///< Group button image index
    int m_exitBtnIndex;                ///< Exit button image index
    int m_logoutBtnIndex;              ///< Logout button image index

    // Font for text rendering
    TTF_Font* m_font;                  ///< Font for text rendering
    SDL_Color m_textColor;             ///< Text color
    SDL_Color m_textShadowColor;       ///< Text shadow color

    /**
     * @brief Create UI controls
     */
    void CreateControls();

    /**
     * @brief Render health bar
     * @param renderer SDL renderer
     */
    void RenderHealthBar(SDL_Renderer* renderer);

    /**
     * @brief Render mana bar
     * @param renderer SDL renderer
     */
    void RenderManaBar(SDL_Renderer* renderer);

    /**
     * @brief Render experience bar
     * @param renderer SDL renderer
     */
    void RenderExpBar(SDL_Renderer* renderer);

    /**
     * @brief Render player stats
     * @param renderer SDL renderer
     */
    void RenderPlayerStats(SDL_Renderer* renderer);

    /**
     * @brief Render bottom panel
     * @param renderer SDL renderer
     */
    void RenderBottomPanel(SDL_Renderer* renderer);

    /**
     * @brief Render belt items
     * @param renderer SDL renderer
     */
    void RenderBeltItems(SDL_Renderer* renderer);

    /**
     * @brief Render gold display
     * @param renderer SDL renderer
     */
    void RenderGold(SDL_Renderer* renderer);

    /**
     * @brief Render mini map
     * @param renderer SDL renderer
     */
    void RenderMiniMap(SDL_Renderer* renderer);

    /**
     * @brief Render buttons
     * @param renderer SDL renderer
     */
    void RenderButtons(SDL_Renderer* renderer);

    /**
     * @brief Render text with shadow
     * @param renderer SDL renderer
     * @param text Text to render
     * @param x X position
     * @param y Y position
     * @param color Text color
     * @param shadowColor Shadow color
     */
    void RenderTextWithShadow(SDL_Renderer* renderer, const std::string& text, int x, int y,
                             const SDL_Color& color, const SDL_Color& shadowColor);

public:
    /**
     * @brief Constructor
     * @param x X position
     * @param y Y position
     * @param width Width
     * @param height Height
     * @param player Player reference
     * @param name Control name
     */
    GameUI(int x, int y, int width, int height, std::shared_ptr<Player> player, const std::string& name = "GameUI");

    /**
     * @brief Destructor
     */
    virtual ~GameUI();

    /**
     * @brief Update the UI
     * @param deltaTime Time elapsed since last frame in milliseconds
     */
    virtual void Update(int deltaTime) override;

    /**
     * @brief Render the UI
     * @param renderer SDL renderer
     */
    virtual void Render(SDL_Renderer* renderer) override;

    /**
     * @brief Set the player reference
     * @param player Player reference
     */
    void SetPlayer(std::shared_ptr<Player> player);

    /**
     * @brief Get the player reference
     * @return Player reference
     */
    std::shared_ptr<Player> GetPlayer() const { return m_player; }

    /**
     * @brief Set gold amount
     * @param gold Gold amount
     */
    void SetGold(int gold) { m_gold = gold; }

    /**
     * @brief Get gold amount
     * @return Gold amount
     */
    int GetGold() const { return m_gold; }

    /**
     * @brief Toggle mini map visibility
     */
    void ToggleMiniMap() { m_showMiniMap = !m_showMiniMap; }

    /**
     * @brief Set mini map visibility
     * @param show Whether to show mini map
     */
    void SetShowMiniMap(bool show) { m_showMiniMap = show; }

    /**
     * @brief Get mini map visibility
     * @return Whether mini map is visible
     */
    bool IsShowMiniMap() const { return m_showMiniMap; }

    /**
     * @brief Set mini map mode
     * @param mode Mini map mode (0=normal, 1=transparent)
     */
    void SetMiniMapMode(int mode) { m_miniMapMode = mode; }

    /**
     * @brief Get mini map mode
     * @return Mini map mode
     */
    int GetMiniMapMode() const { return m_miniMapMode; }

    /**
     * @brief Set mini map index
     * @param index Mini map resource index
     */
    void SetMiniMapIndex(int index) { m_miniMapIndex = index; }

    /**
     * @brief Get mini map index
     * @return Mini map resource index
     */
    int GetMiniMapIndex() const { return m_miniMapIndex; }

    /**
     * @brief Handle mouse button events
     * @param button Mouse button
     * @param pressed True if pressed, false if released
     * @param x Mouse x position
     * @param y Mouse y position
     * @return True if handled, false otherwise
     */
    virtual bool HandleMouseButton(int button, bool pressed, int x, int y);

    /**
     * @brief Set the callback for when the status button is clicked
     * @param callback Callback function
     */
    void SetOnStatusButtonClick(std::function<void()> callback) { m_onStatusButtonClick = callback; }

    /**
     * @brief Set the callback for when the skill button is clicked
     * @param callback Callback function
     */
    void SetOnSkillButtonClick(std::function<void()> callback) { m_onSkillButtonClick = callback; }

private:
    /**
     * @brief Convert PlayerClass enum to string
     * @param playerClass Player class
     * @return String representation of player class
     */
    std::string PlayerClassToString(PlayerClass playerClass);

    std::function<void()> m_onStatusButtonClick;  ///< Callback for status button click
    std::function<void()> m_onSkillButtonClick;   ///< Callback for skill button click
};
