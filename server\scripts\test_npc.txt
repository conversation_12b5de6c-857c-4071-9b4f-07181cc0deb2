[@main]
你好，<$USERNAME>！\\
我是<$NPCNAME>，欢迎来到传奇世界！\\
你现在是<$LEVEL>级的<$JOB>，拥有<$GOLD>金币。\\
\\
请选择你要进行的操作：\\
<购买物品/@shop> 我要购买物品\\
<接受任务/@quest> 我要接受任务\\
<查看仓库/@storage> 查看我的仓库\\
<离开/@exit> 离开\\

[@shop]
欢迎来到我的商店！\\
这里有各种装备和药品。\\
#IF
CHECKLEVEL > 10
#ACT
OPENMERCHANT
SENDMSG 5 欢迎光临！
#ELSEACT
SENDMSG 5 你的等级不够，需要10级以上才能购买物品！
GOTO main

[@quest]
我这里有一个任务给你。\\
需要你帮我收集一些金创药。\\
#IF
CHECKITEM 金创药 5
#ACT
TAKE 金创药 5
GIVEEXP 1000
GIVEGOLD 500
SENDMSG 5 任务完成！你获得了1000经验和500金币！
GOTO main
#ELSEACT
SENDMSG 5 你需要收集5个金创药才能完成任务！
GOTO main

[@storage]
#IF
CHECKGOLD >= 100
#ACT
TAKEGOLD 100
OPENSTORAGE
SENDMSG 5 仓库已打开，收费100金币！
#ELSEACT
SENDMSG 5 使用仓库需要100金币，你的金币不够！
GOTO main

[@exit]
再见，<$USERNAME>！\\
欢迎下次再来！\\
#ACT
CLOSE

; 这是注释行
// 这也是注释行

[@admin]
; 管理员专用功能
欢迎管理员！请选择功能：\\
<传送功能/@teleport> 传送功能\\
<怪物生成/@monster> 生成怪物\\
<返回/@main> 返回主菜单\\
#IF
CHECKISADMIN
#ACT
SENDMSG 5 管理员权限验证通过！
#ELSEACT
SENDMSG 5 你没有管理员权限！
GOTO main

[@teleport]
请选择传送地点：\\
<比奇城/@teleport_beach> 传送到比奇城\\
<盟重省/@teleport_sabak> 传送到盟重省\\
<返回/@admin> 返回管理菜单\\

[@teleport_beach]
#ACT
MAP 0 330 330
SENDMSG 5 已传送到比奇城！

[@teleport_sabak]
#ACT
MAP 3 300 300
SENDMSG 5 已传送到盟重省！

[@monster]
#IF
CHECKISADMIN
#ACT
MONGEN 鸡 1 3 3
SENDMSG 5 已在你周围生成了鸡！
GOTO admin
#ELSEACT
SENDMSG 5 权限不足！
GOTO main
