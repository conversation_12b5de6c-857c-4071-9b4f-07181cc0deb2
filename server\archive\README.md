# 项目归档目录说明

本目录包含了从server项目中清理出来的测试文件、示例文件、临时文件和文档。

## 目录结构

```
archive/
├── tests/          # 测试文件归档
├── examples/       # 示例文件归档  
├── docs/           # 文档归档
├── temp/           # 临时文件归档
└── README.md       # 本说明文件
```

## 归档内容

### tests/ - 测试文件归档
包含所有的测试文件，包括：
- 单元测试文件 (*Test.cpp, test_*.cpp)
- 简化测试文件 (Simple*.cpp, simple_*.cpp)
- 功能测试文件
- 集成测试文件
- 性能测试文件

主要文件：
- BasicGuildTest.cpp - 基础行会测试
- CombatSystemTest.cpp - 战斗系统测试
- CoreFeaturesTest.cpp - 核心功能测试
- EnvironmentTest.cpp - 环境测试
- GroupSystemTest.cpp - 组队系统测试
- ItemUpgradeTest.cpp - 物品强化测试
- LocalDatabase_Test.cpp - 本地数据库测试
- PKSystemTest.cpp - PK系统测试
- ScriptParserTest.cpp - 脚本解析器测试
- UserEngineTest.cpp - 用户引擎测试
- 以及各种Simple*Test.cpp简化测试文件

### examples/ - 示例文件归档
包含所有的示例和演示文件：
- CastleSystemExample.cpp - 城堡系统示例
- GuildSystemExample.cpp - 行会系统示例
- ItemUpgradeExample.cpp - 物品强化示例
- LocalDatabaseExample.cpp - 本地数据库示例
- UserEngine_Usage_Example.cpp - 用户引擎使用示例
- item_identification_example.cpp - 物品鉴定示例

### docs/ - 文档归档
包含项目开发过程中的各种文档：
- 系统实现文档
- 重构报告
- 功能对比文档
- 实现总结文档
- 使用说明文档

主要文档：
- CastleSystemImplementation.md - 城堡系统实现
- DELPHI_VS_CPP_FEATURE_COMPARISON.md - Delphi vs C++功能对比
- FEATURE_AUDIT_SUMMARY.md - 功能审计总结
- ITEM_IDENTIFICATION_SYSTEM.md - 物品鉴定系统
- PK_SYSTEM_REFACTORING_REPORT.md - PK系统重构报告
- SCRIPT_ENGINE_REFACTORING_SUMMARY.md - 脚本引擎重构总结
- SendRefMsg实现说明.md - SendRefMsg实现说明
- 各种功能实现总结文档

### temp/ - 临时文件归档
包含编译产物、日志文件、脚本文件等临时文件：
- 可执行文件 (*.exe)
- 日志文件 (*.log)
- 编译脚本 (*.sh, *.bat)
- 压缩包文件 (*.zip)
- 构建目录
- 临时测试文件

## 清理说明

本次清理的目标是：
1. 移除项目中过多的测试文件和demo文件
2. 整理散落的文档和说明文件
3. 清理编译产物和临时文件
4. 保持项目结构的整洁性

## 保留的核心文件

以下核心文件和目录保留在原位置：
- src/ - 源代码目录
- tests/CMakeLists.txt - 核心测试配置
- docs/ - 核心文档目录（保留重要文档）
- examples/ - 核心示例目录（保留重要示例）
- README.md - 项目主说明文件
- CMakeLists.txt - 主构建配置

## 恢复说明

如果需要恢复某些文件，可以从对应的归档目录中复制回原位置。
建议在恢复前先确认文件的必要性，避免重新引入过多的测试文件。

## 维护建议

1. 新的测试文件应该放在tests/目录下，并遵循统一的命名规范
2. 示例文件应该放在examples/目录下
3. 临时文件和编译产物应该及时清理
4. 文档应该集中管理，避免散落在各个目录中

---
归档时间: $(date)
归档原因: 清理重构项目中过多的测试cpp和demo文件
