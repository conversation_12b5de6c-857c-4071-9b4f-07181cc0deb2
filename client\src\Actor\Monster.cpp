#include "Monster.h"
#include <cmath>
#include <random>
#include <algorithm>

Monster::Monster(int id, const std::string& name, MonsterType type, int level)
    : Actor(id, name)
    , m_type(type)
    , m_level(level)
    , m_health(100)
    , m_maxHealth(100)
    , m_attackPower(10)
    , m_defense(5)
    , m_experienceValue(10)
    , m_goldValue(5)
    , m_aggroRange(5)
    , m_attackRange(1)
    , m_aggressive(false)
    , m_targetId(-1)
    , m_patrolX(-1)
    , m_patrolY(-1)
    , m_homeX(0)
    , m_homeY(0)
    , m_aiTimer(0)
    , m_aiUpdateInterval(1000)  // Update AI every 1 second
{
    // Set monster-specific properties based on type and level
    switch (m_type) {
        case MonsterType::NORMAL:
            m_maxHealth = 50 + m_level * 10;
            m_attackPower = 5 + m_level * 2;
            m_defense = 2 + m_level;
            m_experienceValue = 5 + m_level * 2;
            m_goldValue = 1 + m_level;
            m_aggroRange = 5;
            m_attackRange = 1;
            m_aggressive = false;
            m_moveSpeed = 80;
            break;
        case MonsterType::ELITE:
            m_maxHealth = 100 + m_level * 20;
            m_attackPower = 10 + m_level * 3;
            m_defense = 5 + m_level * 2;
            m_experienceValue = 15 + m_level * 5;
            m_goldValue = 5 + m_level * 2;
            m_aggroRange = 7;
            m_attackRange = 2;
            m_aggressive = true;
            m_moveSpeed = 100;
            break;
        case MonsterType::BOSS:
            m_maxHealth = 500 + m_level * 50;
            m_attackPower = 20 + m_level * 5;
            m_defense = 10 + m_level * 3;
            m_experienceValue = 50 + m_level * 10;
            m_goldValue = 20 + m_level * 5;
            m_aggroRange = 10;
            m_attackRange = 3;
            m_aggressive = true;
            m_moveSpeed = 120;
            break;
    }
    
    // Initialize current health
    m_health = m_maxHealth;
    
    // Set home position to current position
    m_homeX = m_x;
    m_homeY = m_y;
}

Monster::~Monster()
{
}

void Monster::Update(int deltaTime)
{
    // Update AI
    m_aiTimer += deltaTime;
    if (m_aiTimer >= m_aiUpdateInterval) {
        UpdateAI(deltaTime);
        m_aiTimer = 0;
    }
    
    // Call base class update
    Actor::Update(deltaTime);
}

void Monster::Render()
{
    // Call base class render
    Actor::Render();
    
    // Render monster-specific elements
    // TODO: Render health bar, etc.
}

void Monster::SetTarget(int targetId)
{
    m_targetId = targetId;
}

void Monster::ClearTarget()
{
    m_targetId = -1;
}

void Monster::SetHomePosition(int x, int y)
{
    m_homeX = x;
    m_homeY = y;
}

void Monster::SetPatrolPosition(int x, int y)
{
    m_patrolX = x;
    m_patrolY = y;
}

void Monster::SetAggressive(bool aggressive)
{
    m_aggressive = aggressive;
}

bool Monster::TakeDamage(int damage)
{
    // Calculate actual damage after defense
    int actualDamage = std::max(1, damage - m_defense);
    
    // Reduce health
    m_health = std::max(0, m_health - actualDamage);
    
    // Check if dead
    if (m_health <= 0) {
        SetState(ActorState::DYING);
        return false;
    }
    
    // Set hit state
    SetState(ActorState::HIT);
    return true;
}

std::vector<int> Monster::GenerateDrops()
{
    std::vector<int> drops;
    
    // Check if there are any drop items
    if (m_dropItems.empty()) {
        return drops;
    }
    
    // Random number generator
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<float> dis(0.0f, 1.0f);
    
    // Check each item
    for (size_t i = 0; i < m_dropItems.size(); i++) {
        float roll = dis(gen);
        if (roll < m_dropRates[i]) {
            drops.push_back(m_dropItems[i]);
        }
    }
    
    return drops;
}

void Monster::UpdateAI(int deltaTime)
{
    // Don't update AI if dead
    if (m_state == ActorState::DEAD || m_state == ActorState::DYING) {
        return;
    }
    
    // Check if we have a target
    if (m_targetId != -1) {
        // TODO: Get target position from actor manager
        int targetX = 0;
        int targetY = 0;
        
        // Check if target is in attack range
        if (IsInRange(targetX, targetY, m_attackRange)) {
            // Attack the target
            Attack();
        } else if (IsInRange(targetX, targetY, m_aggroRange)) {
            // Move towards the target
            PathToTarget(targetX, targetY);
        } else {
            // Target is out of range, clear target
            ClearTarget();
        }
    } else {
        // No target, check for nearby targets
        // TODO: Check for nearby actors
        
        // If no targets found, patrol or return home
        if (m_patrolX != -1 && m_patrolY != -1) {
            // Patrol between home and patrol point
            if (m_x == m_homeX && m_y == m_homeY) {
                // At home, move to patrol point
                MoveTo(m_patrolX, m_patrolY);
            } else if (m_x == m_patrolX && m_y == m_patrolY) {
                // At patrol point, move to home
                MoveTo(m_homeX, m_homeY);
            } else {
                // Not at either point, move to home
                MoveTo(m_homeX, m_homeY);
            }
        } else {
            // No patrol point, return home if not already there
            if (m_x != m_homeX || m_y != m_homeY) {
                MoveTo(m_homeX, m_homeY);
            }
        }
    }
}

bool Monster::IsInRange(int targetX, int targetY, int range) const
{
    // Calculate distance
    int dx = targetX - m_x;
    int dy = targetY - m_y;
    int distanceSquared = dx * dx + dy * dy;
    
    // Check if in range
    return distanceSquared <= range * range;
}

bool Monster::PathToTarget(int targetX, int targetY)
{
    // Simple direct path for now
    return MoveTo(targetX, targetY);
    
    // TODO: Implement proper pathfinding
}
