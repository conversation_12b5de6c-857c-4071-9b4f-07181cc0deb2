#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据文件格式修复脚本
Fix data file format script
"""

import os
import re

def fix_tab_separated_file(filename):
    """修复制表符分隔的文件为空格分隔"""
    if not os.path.exists(filename):
        print(f"文件不存在: {filename}")
        return False
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        fixed_lines = []
        for line in lines:
            # 跳过注释行
            if line.strip().startswith(';') or line.strip() == '':
                fixed_lines.append(line)
                continue
            
            # 将制表符替换为空格，并规范化空格
            fixed_line = re.sub(r'\t+', ' ', line)
            fixed_line = re.sub(r' +', ' ', fixed_line)
            fixed_lines.append(fixed_line)
        
        # 备份原文件
        backup_filename = filename + '.backup'
        with open(backup_filename, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        # 写入修复后的文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.writelines(fixed_lines)
        
        print(f"✓ 修复完成: {filename}")
        return True
        
    except Exception as e:
        print(f"✗ 修复失败 {filename}: {e}")
        return False

def main():
    """主函数"""
    print("=== 数据文件格式修复工具 ===")
    
    data_files = [
        'data/StdItems.txt',
        'data/Monster.txt',
        'data/MonsterDrops.txt',
        'data/Shop.txt'
    ]
    
    success_count = 0
    total_count = len(data_files)
    
    for filename in data_files:
        if fix_tab_separated_file(filename):
            success_count += 1
    
    print(f"\n修复完成: {success_count}/{total_count} 个文件")
    
    if success_count == total_count:
        print("✓ 所有文件修复成功！")
    else:
        print("⚠ 部分文件修复失败，请检查错误信息")

if __name__ == '__main__':
    main()
