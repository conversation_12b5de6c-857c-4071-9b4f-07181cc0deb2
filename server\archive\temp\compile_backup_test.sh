#!/bin/bash

# 编译BackupGuildFile功能测试

echo "编译BackupGuildFile功能测试..."

# 设置编译参数
CXX=g++
CXXFLAGS="-std=c++17 -Wall -Wextra -g -O0"
INCLUDES="-I../src -I../src/Common -I../src/BaseObject -I../src/GameEngine"
LIBS="-lgtest -lgtest_main -pthread"

# 源文件列表
SOURCES=(
    "test_backup_guild_file.cpp"
    "../src/BaseObject/PlayObject.cpp"
    "../src/BaseObject/BaseObject.cpp"
    "../src/GameEngine/GuildManager.cpp"
    "../src/Common/Logger.cpp"
    "../src/Common/Types.cpp"
    "../src/Common/Utils.cpp"
)

# 检查源文件是否存在
for src in "${SOURCES[@]}"; do
    if [ ! -f "$src" ]; then
        echo "警告: 源文件 $src 不存在，跳过..."
    fi
done

# 编译
echo "正在编译..."
$CXX $CXXFLAGS $INCLUDES "${SOURCES[@]}" $LIBS -o test_backup_guild_file

if [ $? -eq 0 ]; then
    echo "编译成功！"
    echo "运行测试: ./test_backup_guild_file"
    echo ""
    echo "测试将验证以下功能（原项目兼容性）："
    echo "1. 备份文件创建（只备份数据文件）"
    echo "2. 数据清理功能（清理成员和公告）"
    echo "3. 备份文件内容完整性"
    echo "4. 原项目兼容性验证"
    echo "5. 重新加载功能"
    echo ""
    echo "注意：BackupGuildFile在原项目中用于删除行会时的清理，不发送解散消息"
else
    echo "编译失败！"
    exit 1
fi
