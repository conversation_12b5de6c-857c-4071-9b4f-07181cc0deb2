#include <iostream>

// Forward declaration of the test function
extern void RunExceptionLoggingTests();

/**
 * @brief Main entry point for the test application
 * @param argc Count of command line arguments
 * @param argv Array of command line arguments
 * @return Exit code
 */
int main(int argc, char* argv[]) {
    std::cout << "Starting Exception Logging Test..." << std::endl;
    
    // Run the tests
    RunExceptionLoggingTests();
    
    std::cout << "Exception Logging Test completed." << std::endl;
    return 0;
}
