#include "StatusPanel.h"
#include "Button.h"
#include "Label.h"
#include "UIConstants.h"
#include "UILayout.h"
#include "ResourcePaths.h"
#include <iostream>
#include <sstream>

StatusPanel::StatusPanel(int x, int y, int width, int height, std::shared_ptr<Player> player)
    : UIControl(x, y, width, height, "StatusPanel")
    , m_player(player)
    , m_textColor({255, 255, 255, 255})
    , m_textShadowColor({0, 0, 0, 255})
    , m_currentTab(0)
    , m_resourceFile(ResourcePaths::INTERFACE)
{
    // Create UI controls
    CreateControls();

    // Hide by default
    SetVisible(false);
}

StatusPanel::~StatusPanel()
{
}

void StatusPanel::CreateControls()
{
    // Set resource file for UI elements
    SetResourceFile(ResourcePaths::INTERFACE);

    // Create background (using normal image index)
    SetNormalImageIndex(UIConstants::STATUS_UI_BG_INDEX);

    // Create title label
    m_titleLabel = std::make_shared<Label>(
        UILayout::StatusUI::TITLE_X_OFFSET,
        UILayout::StatusUI::TITLE_Y_OFFSET,
        100,
        20,
        "Character Status"
    );
    m_titleLabel->SetTextColor(m_textColor);
    AddChild(m_titleLabel);

    // Create close button
    m_closeButton = std::make_shared<Button>(
        m_width + UILayout::StatusUI::CLOSE_BTN_X_OFFSET,
        UILayout::StatusUI::CLOSE_BTN_Y_OFFSET,
        UILayout::StatusUI::CLOSE_BTN_WIDTH,
        UILayout::StatusUI::CLOSE_BTN_HEIGHT,
        "X"
    );
    m_closeButton->SetResourceFile(ResourcePaths::INTERFACE);
    m_closeButton->SetResourceIndices(
        ResourcePaths::INTERFACE,
        UIConstants::STATUS_UI_CLOSE_BTN_NORMAL,
        UIConstants::STATUS_UI_CLOSE_BTN_HOVER,
        UIConstants::STATUS_UI_CLOSE_BTN_PRESSED,
        UIConstants::STATUS_UI_CLOSE_BTN_DISABLED
    );
    m_closeButton->SetOnClick([this]() { OnCloseButtonClick(nullptr); });
    AddChild(m_closeButton);

    // Create tab buttons - 使用UILayout中定义的常量
    const int tabCount = UILayout::StatusUI::TAB_COUNT;
    const int tabWidth = UILayout::StatusUI::TAB_WIDTH;
    const int tabHeight = UILayout::StatusUI::TAB_HEIGHT;
    const int tabSpacing = UILayout::StatusUI::TAB_SPACING;
    const int tabStartX = UILayout::StatusUI::TAB_START_X;
    const int tabY = UILayout::StatusUI::TAB_Y;

    const char* tabNames[tabCount] = {"Stats", "Equipment", "Guild", "Settings"};

    for (int i = 0; i < tabCount; i++) {
        auto tabButton = std::make_shared<Button>(
            tabStartX + i * (tabWidth + tabSpacing),
            tabY,
            tabWidth,
            tabHeight,
            tabNames[i]
        );
        tabButton->SetResourceFile(ResourcePaths::INTERFACE);
        tabButton->SetUserData(i); // Use user data to store tab index
        tabButton->SetOnClick([this, i]() {
            // Use the captured index instead of getting it from the control
            OnTabButtonClick(nullptr, i);
        });
        AddChild(tabButton);
        m_tabButtons.push_back(tabButton);
    }

    // Create equipment panel - 使用UILayout中定义的常量
    m_equipmentUI = std::make_shared<EquipmentPanel>(
        m_x + UILayout::StatusUI::EQUIPMENT_PANEL_X_OFFSET,
        m_y + UILayout::StatusUI::EQUIPMENT_PANEL_Y_OFFSET,
        m_width - UILayout::StatusUI::EQUIPMENT_PANEL_WIDTH_OFFSET,
        m_height - UILayout::StatusUI::EQUIPMENT_PANEL_HEIGHT_OFFSET,
        m_player
    );
    m_equipmentUI->SetWILManager(m_wilManager);
    // Font will be set later when available

    // 设置装备面板的回调函数
    m_equipmentUI->SetOnItemUnequipped([this](Inventory::EquipmentSlot slot) {
        // 刷新状态显示
        RefreshStatus();
    });

    m_equipmentUI->SetOnItemEquipped([this](std::shared_ptr<Item> item, Inventory::EquipmentSlot slot) {
        // 刷新状态显示
        RefreshStatus();
    });

    m_equipmentUI->Hide();
    AddChild(m_equipmentUI);
}

void StatusPanel::Update(int deltaTime)
{
    // Update base class
    UIControl::Update(deltaTime);
}

void StatusPanel::Render(SDL_Renderer* renderer)
{
    // Skip if not visible
    if (!m_visible) {
        return;
    }

    // Render background and controls
    UIControl::Render(renderer);

    // Render content based on current tab
    switch (m_currentTab) {
        case 0: // Stats
            RenderStats(renderer);
            break;
        case 1: // Equipment
            // Equipment UI is rendered by its own Render method
            break;
        case 2: // Guild
            // TODO: Render guild information
            break;
        case 3: // Settings
            // TODO: Render settings
            break;
    }
}

void StatusPanel::RenderStats(SDL_Renderer* renderer)
{
    if (!m_player) {
        return;
    }

    // Render character model
    RenderCharacterModel(renderer);

    // Get player stats
    int level = m_player->GetLevel();
    std::string name = m_player->GetName();
    std::string job = m_player->GetJob();
    int hp = m_player->GetHP();
    int maxHp = m_player->GetMaxHP();
    int mp = m_player->GetMP();
    int maxMp = m_player->GetMaxMP();
    int attack = m_player->GetAttack();
    int defense = m_player->GetDefense();
    int magicAttack = m_player->GetMagicAttack();
    int magicDefense = m_player->GetMagicDefense();
    int accuracy = m_player->GetAccuracy();
    int agility = m_player->GetAgility();
    int exp = m_player->GetExp();
    int maxExp = m_player->GetMaxExp();
    std::string guildName = m_player->GetGuildName();
    std::string guildRank = m_player->GetGuildRank();

    // Render stats - 使用UILayout中定义的常量
    int statsX = m_x + UILayout::StatusUI::STATS_X_OFFSET;
    int statsY = m_y + UILayout::StatusUI::STATS_Y_OFFSET;
    int lineHeight = UILayout::StatusUI::STATS_LINE_HEIGHT;

    // Render with shadow for better readability
    auto RenderTextLine = [&](const std::string& text, int y) {
        // Skip if font is not available
        if (!m_font) {
            return;
        }

        // Render shadow
        m_font->RenderText(renderer, text, statsX + 1, y + 1, m_textShadowColor);

        // Render text
        m_font->RenderText(renderer, text, statsX, y, m_textColor);
    };

    // Render player name and level
    std::stringstream ss;
    ss << name << " Lv." << level << " " << job;
    RenderTextLine(ss.str(), statsY);
    statsY += lineHeight;

    // Render guild information
    if (!guildName.empty()) {
        ss.str("");
        ss << "Guild: " << guildName << " (" << guildRank << ")";
        RenderTextLine(ss.str(), statsY);
        statsY += lineHeight;
    }

    // Render HP and MP
    ss.str("");
    ss << "HP: " << hp << " / " << maxHp;
    RenderTextLine(ss.str(), statsY);
    statsY += lineHeight;

    ss.str("");
    ss << "MP: " << mp << " / " << maxMp;
    RenderTextLine(ss.str(), statsY);
    statsY += lineHeight;

    // Render experience
    ss.str("");
    ss << "EXP: " << exp << " / " << maxExp;
    RenderTextLine(ss.str(), statsY);
    statsY += lineHeight;

    // Add a gap
    statsY += lineHeight / 2;

    // Render attack and defense
    ss.str("");
    ss << "Attack: " << attack;
    RenderTextLine(ss.str(), statsY);
    statsY += lineHeight;

    ss.str("");
    ss << "Defense: " << defense;
    RenderTextLine(ss.str(), statsY);
    statsY += lineHeight;

    // Render magic attack and defense
    ss.str("");
    ss << "Magic Attack: " << magicAttack;
    RenderTextLine(ss.str(), statsY);
    statsY += lineHeight;

    ss.str("");
    ss << "Magic Defense: " << magicDefense;
    RenderTextLine(ss.str(), statsY);
    statsY += lineHeight;

    // Render accuracy and agility
    ss.str("");
    ss << "Accuracy: " << accuracy;
    RenderTextLine(ss.str(), statsY);
    statsY += lineHeight;

    ss.str("");
    ss << "Agility: " << agility;
    RenderTextLine(ss.str(), statsY);
}

void StatusPanel::RenderCharacterModel(SDL_Renderer* renderer)
{
    if (!m_player) {
        return;
    }

    // Character model position - 使用UILayout中定义的常量
    int modelX = m_x + UILayout::StatusUI::CHARACTER_MODEL_X_OFFSET;
    int modelY = m_y + UILayout::StatusUI::CHARACTER_MODEL_Y_OFFSET;

    // Render base character model
    int baseImageIndex = m_player->GetSex() == 0 ? UIConstants::MALE_CHARACTER_BASE : UIConstants::FEMALE_CHARACTER_BASE;

    if (m_wilManager) {
        SDL_Surface* surface = m_wilManager->GetSurface(ResourcePaths::INTERFACE, baseImageIndex);
        if (surface) {
            // Create a temporary texture from the surface
            SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
            if (texture) {
                // Render the texture
                SDL_Rect destRect = {modelX - surface->w / 2, modelY - surface->h / 2, surface->w, surface->h};
                SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                // Free the texture
                SDL_DestroyTexture(texture);
            }
        }
    }

    // Render equipped items on character model
    if (m_player->GetInventory()) {
        const auto& inventory = m_player->GetInventory();

        // Render each equipped item
        for (int i = 0; i < Inventory::MAX_EQUIPMENT_SLOTS; i++) {
            Inventory::EquipmentSlot slot = static_cast<Inventory::EquipmentSlot>(i);
            auto item = inventory->GetEquippedItem(slot);

            if (item) {
                // Get the item's appearance on the character
                int lookIndex = item->GetLook();

                // Adjust look index based on player sex for certain items
                if (slot == Inventory::EquipmentSlot::ARMOR || slot == Inventory::EquipmentSlot::HELMET) {
                    // This is a placeholder - in the real implementation, you would adjust the look index
                    // based on the player's sex and the item type
                }

                if (m_wilManager) {
                    SDL_Surface* surface = m_wilManager->GetSurface(ResourcePaths::ITEMS, lookIndex);
                    if (surface) {
                        // Create a temporary texture from the surface
                        SDL_Texture* texture = SDL_CreateTextureFromSurface(renderer, surface);
                        if (texture) {
                            // Render the texture
                            SDL_Rect destRect = {modelX - surface->w / 2, modelY - surface->h / 2, surface->w, surface->h};
                            SDL_RenderCopy(renderer, texture, nullptr, &destRect);

                            // Free the texture
                            SDL_DestroyTexture(texture);
                        }
                    }
                }
            }
        }
    }
}

bool StatusPanel::HandleMouseButton(Uint8 button, bool pressed, int x, int y)
{
    // Skip if not visible
    if (!m_visible) {
        return false;
    }

    // Check if a child control handled the event
    if (UIControl::HandleMouseButton(button, pressed, x, y)) {
        return true;
    }

    return false;
}

bool StatusPanel::HandleMouseMotion(int x, int y)
{
    // Skip if not visible
    if (!m_visible) {
        return false;
    }

    // Check if a child control handled the event
    if (UIControl::HandleMouseMotion(x, y)) {
        return true;
    }

    return false;
}

bool StatusPanel::HandleKey(SDL_Keycode key, bool pressed)
{
    // Skip if not visible
    if (!m_visible) {
        return false;
    }

    // Check if a child control handled the event
    if (UIControl::HandleKey(key, pressed)) {
        return true;
    }

    // Handle escape key to close the UI
    if (key == SDLK_ESCAPE && pressed) {
        Hide();
        return true;
    }

    return false;
}

void StatusPanel::Show()
{
    SetVisible(true);
    RefreshStatus();
}

void StatusPanel::Hide()
{
    SetVisible(false);

    // Hide equipment UI
    if (m_equipmentUI) {
        m_equipmentUI->Hide();
    }
}

void StatusPanel::RefreshStatus()
{
    // Update tab visibility
    for (int i = 0; i < m_tabButtons.size(); i++) {
        // Set the button's state based on whether it's the current tab
        if (i == m_currentTab) {
            m_tabButtons[i]->SetNormalImageIndex(UIConstants::TAB_SELECTED_INDEX);
        } else {
            m_tabButtons[i]->SetNormalImageIndex(UIConstants::TAB_NORMAL_INDEX);
        }
    }

    // Update equipment UI visibility
    if (m_equipmentUI) {
        m_equipmentUI->SetVisible(m_currentTab == 1);
    }
}

void StatusPanel::OnCloseButtonClick(UIControl* control)
{
    Hide();
}

void StatusPanel::OnTabButtonClick(UIControl* control, int tabIndex)
{
    // Switch to the selected tab
    m_currentTab = tabIndex;

    // Refresh the status display
    RefreshStatus();
}
