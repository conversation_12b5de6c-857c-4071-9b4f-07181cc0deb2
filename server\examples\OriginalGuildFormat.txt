[Guild Notice]
+欢迎加入龙城行会！
+行会活动每周日晚上8点举行
+请大家积极参与行会建设
 
[Guild War]
+霸王行会 7200000
+剑圣行会 3600000
 
[Guild Ally]
+友好行会
+联盟行会
 
[Guild Member]
#1 会长
+龙城霸主
#10 副会长
+法师传说
+剑客无名
#50 队长
+战士勇者
+法师智者
+道士仙人
#99 成员
+新手玩家1
+新手玩家2
+新手玩家3
+新手玩家4
+新手玩家5

; 原Delphi项目Guild文件格式说明：
; 
; 1. 文件结构：
;    - [Guild Notice] 或配置中的公告标识符
;    - [Guild War] 或配置中的战争标识符  
;    - [Guild Ally] 或配置中的联盟标识符
;    - [Guild Member] 或配置中的成员标识符
;
; 2. 数据格式：
;    - 以+开头的行是数据行
;    - 以#开头的行是职位定义行：#职位编号 职位名称
;    - 空格行用于分隔不同部分
;    - 以;开头的行是注释行
;
; 3. 职位编号规则：
;    - 1: 会长
;    - 10: 副会长
;    - 50: 队长
;    - 99: 普通成员
;
; 4. 战争格式：
;    - +敌对行会名 剩余时间(毫秒)
;
; 5. 联盟格式：
;    - +联盟行会名
;
; 6. 成员格式：
;    - 先定义职位：#职位编号 职位名称
;    - 然后列出该职位的成员：+成员名
;
; 这种格式与C++版本的新格式完全不同，需要专门的兼容性处理。
