#include "MapManager.h"
#include "../UI/ResourcePaths.h"
#include <fstream>
#include <iostream>
#include <algorithm>

MapManager::MapManager(std::shared_ptr<WILManager> wilManager, SDL_Renderer* renderer)
    : m_width(0)
    , m_height(0)
    , m_wil<PERSON>anager(wilManager)
    , m_cameraX(0)
    , m_cameraY(0)
    , m_viewportWidth(800)
    , m_viewportHeight(600)
    , m_tileWidth(48)
    , m_tileHeight(32)
    , m_renderBackground(true)
    , m_renderMiddle(true)
    , m_renderObjects(true)
{
}

MapManager::~MapManager()
{
    // Clear tile textures
    m_tileTextures.clear();
}

bool MapManager::LoadMap(const std::string& filename, const std::string& tilesetName)
{
    // Open the map file
    std::ifstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Failed to open map file: " << filename << std::endl;
        return false;
    }

    // Read map header
    file.read(reinterpret_cast<char*>(&m_width), sizeof(int));
    file.read(reinterpret_cast<char*>(&m_height), sizeof(int));

    if (file.fail()) {
        std::cerr << "Failed to read map header: " << filename << std::endl;
        return false;
    }

    // Validate map dimensions
    if (m_width <= 0 || m_height <= 0 || m_width > 1000 || m_height > 1000) {
        std::cerr << "Invalid map dimensions: " << m_width << "x" << m_height << std::endl;
        return false;
    }

    // Resize the cells grid
    m_cells.resize(m_height);
    for (int y = 0; y < m_height; y++) {
        m_cells[y].resize(m_width);
    }

    // Read map data
    for (int y = 0; y < m_height; y++) {
        for (int x = 0; x < m_width; x++) {
            MapCell& cell = m_cells[y][x];

            // Read cell data
            uint16_t backgroundIndex, middleIndex, objectIndex;
            uint8_t flags;
            uint8_t doorIndex, doorOffset, aniFrame;
            uint8_t aniTick, area, light;

            file.read(reinterpret_cast<char*>(&backgroundIndex), sizeof(uint16_t));
            file.read(reinterpret_cast<char*>(&middleIndex), sizeof(uint16_t));
            file.read(reinterpret_cast<char*>(&objectIndex), sizeof(uint16_t));
            file.read(reinterpret_cast<char*>(&flags), sizeof(uint8_t));

            // Read door and animation data
            file.read(reinterpret_cast<char*>(&doorIndex), sizeof(uint8_t));
            file.read(reinterpret_cast<char*>(&doorOffset), sizeof(uint8_t));
            file.read(reinterpret_cast<char*>(&aniFrame), sizeof(uint8_t));
            file.read(reinterpret_cast<char*>(&aniTick), sizeof(uint8_t));
            file.read(reinterpret_cast<char*>(&area), sizeof(uint8_t));
            file.read(reinterpret_cast<char*>(&light), sizeof(uint8_t));

            if (file.fail()) {
                std::cerr << "Failed to read map cell data at " << x << "," << y << std::endl;
                return false;
            }

            // Set cell properties
            cell.SetBackgroundImageIndex(backgroundIndex);
            cell.SetMiddleImageIndex(middleIndex);
            cell.SetObjectImageIndex(objectIndex);

            // Set door and animation properties
            cell.SetDoorIndex(doorIndex);
            cell.SetDoorOffset(doorOffset);
            cell.SetDoorOpen(doorOffset > 0);  // If doorOffset > 0, door is open
            cell.SetAniFrame(aniFrame);
            cell.SetAniTick(aniTick);

            // Set area and light properties
            cell.SetLight(light);

            // Parse flags
            bool walkable = (flags & 0x01) != 0;
            bool transparent = (flags & 0x02) != 0;
            TileType tileType = static_cast<TileType>((flags >> 2) & 0x07);

            cell.SetWalkable(walkable);
            cell.SetTransparent(transparent);
            cell.SetTileType(tileType);
        }
    }

    // 加载瓦片纹理
    if (!LoadTileTextures(tilesetName)) {
        std::cerr << "Failed to load tile textures: " << tilesetName << std::endl;
        return false;
    }

    // 从文件名中设置地图名称
    size_t lastSlash = filename.find_last_of("/\\");
    size_t lastDot = filename.find_last_of(".");

    if (lastSlash == std::string::npos) {
        lastSlash = 0;
    } else {
        lastSlash++;
    }

    if (lastDot == std::string::npos) {
        m_mapName = filename.substr(lastSlash);
    } else {
        m_mapName = filename.substr(lastSlash, lastDot - lastSlash);
    }

    // 初始化光照信息 - 根据原始Delphi项目的实现
    InitializeLightMap();

    return true;
}

bool MapManager::CreateEmptyMap(int width, int height, const std::string& tilesetName)
{
    // Validate map dimensions
    if (width <= 0 || height <= 0 || width > 1000 || height > 1000) {
        std::cerr << "Invalid map dimensions: " << width << "x" << height << std::endl;
        return false;
    }

    // Set map dimensions
    m_width = width;
    m_height = height;

    // Resize the cells grid
    m_cells.resize(m_height);
    for (int y = 0; y < m_height; y++) {
        m_cells[y].resize(m_width);
    }

    // Initialize cells with default values
    for (int y = 0; y < m_height; y++) {
        for (int x = 0; x < m_width; x++) {
            MapCell& cell = m_cells[y][x];

            // Set default properties
            cell.SetBackgroundImageIndex(0);
            cell.SetMiddleImageIndex(0);
            cell.SetObjectImageIndex(0);
            cell.SetWalkable(true);
            cell.SetTransparent(true);
            cell.SetTileType(TileType::GROUND);

            // Set default door and animation properties
            cell.SetDoorIndex(0);
            cell.SetDoorOffset(0);
            cell.SetDoorOpen(false);
            cell.SetAniFrame(0);
            cell.SetAniTick(0);
            cell.SetAniTickMax(10);
        }
    }

    // Load tile textures
    if (!LoadTileTextures(tilesetName)) {
        std::cerr << "Failed to load tile textures: " << tilesetName << std::endl;
        return false;
    }

    // 设置默认地图名称
    m_mapName = "NewMap";

    // 初始化光照信息
    InitializeLightMap();

    return true;
}

bool MapManager::SaveMap(const std::string& filename)
{
    // Open the map file
    std::ofstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Failed to open map file for writing: " << filename << std::endl;
        return false;
    }

    // Write map header
    file.write(reinterpret_cast<const char*>(&m_width), sizeof(int));
    file.write(reinterpret_cast<const char*>(&m_height), sizeof(int));

    if (file.fail()) {
        std::cerr << "Failed to write map header: " << filename << std::endl;
        return false;
    }

    // Write map data
    for (int y = 0; y < m_height; y++) {
        for (int x = 0; x < m_width; x++) {
            const MapCell& cell = m_cells[y][x];

            // Get cell data
            uint16_t backgroundIndex = cell.GetBackgroundImageIndex();
            uint16_t middleIndex = cell.GetMiddleImageIndex();
            uint16_t objectIndex = cell.GetObjectImageIndex();

            // Get door and animation data
            uint8_t doorIndex = cell.GetDoorIndex();
            uint8_t doorOffset = cell.GetDoorOffset();
            uint8_t aniFrame = cell.GetAniFrame();
            uint8_t aniTick = cell.GetAniTick();
            uint8_t area = 0; // Area is not used in this implementation
            uint8_t light = cell.GetLight();

            // Create flags
            uint8_t flags = 0;
            if (cell.IsWalkable()) flags |= 0x01;
            if (cell.IsTransparent()) flags |= 0x02;
            flags |= (static_cast<uint8_t>(cell.GetTileType()) << 2) & 0x1C;

            // Write cell data
            file.write(reinterpret_cast<const char*>(&backgroundIndex), sizeof(uint16_t));
            file.write(reinterpret_cast<const char*>(&middleIndex), sizeof(uint16_t));
            file.write(reinterpret_cast<const char*>(&objectIndex), sizeof(uint16_t));
            file.write(reinterpret_cast<const char*>(&flags), sizeof(uint8_t));

            // Write door and animation data
            file.write(reinterpret_cast<const char*>(&doorIndex), sizeof(uint8_t));
            file.write(reinterpret_cast<const char*>(&doorOffset), sizeof(uint8_t));
            file.write(reinterpret_cast<const char*>(&aniFrame), sizeof(uint8_t));
            file.write(reinterpret_cast<const char*>(&aniTick), sizeof(uint8_t));
            file.write(reinterpret_cast<const char*>(&area), sizeof(uint8_t));
            file.write(reinterpret_cast<const char*>(&light), sizeof(uint8_t));

            if (file.fail()) {
                std::cerr << "Failed to write map cell data at " << x << "," << y << std::endl;
                return false;
            }
        }
    }

    return true;
}

void MapManager::SetCamera(int x, int y)
{
    // Clamp camera position to map bounds
    m_cameraX = std::max(0, std::min(x, m_width * m_tileWidth - m_viewportWidth));
    m_cameraY = std::max(0, std::min(y, m_height * m_tileHeight - m_viewportHeight));
}

void MapManager::SetViewport(int width, int height)
{
    m_viewportWidth = width;
    m_viewportHeight = height;

    // Adjust camera position if needed
    SetCamera(m_cameraX, m_cameraY);
}

void MapManager::SetRenderLayers(bool background, bool middle, bool objects)
{
    m_renderBackground = background;
    m_renderMiddle = middle;
    m_renderObjects = objects;
}

void MapManager::Render(SDL_Renderer* renderer)
{
    // Calculate visible tile range
    int startX = m_cameraX / m_tileWidth;
    int startY = m_cameraY / m_tileHeight;
    int endX = startX + (m_viewportWidth / m_tileWidth) + 2;  // +2 for partial tiles
    int endY = startY + (m_viewportHeight / m_tileHeight) + 2;

    // Clamp to map bounds
    startX = std::max(0, startX);
    startY = std::max(0, startY);
    endX = std::min(endX, m_width);
    endY = std::min(endY, m_height);

    // Render background layer
    if (m_renderBackground) {
        for (int y = startY; y < endY; y++) {
            for (int x = startX; x < endX; x++) {
                const MapCell& cell = m_cells[y][x];
                uint16_t index = cell.GetBackgroundImageIndex();

                if (index > 0) {
                    std::shared_ptr<Texture> texture = GetTileTexture(index, 0);
                    if (texture) {
                        int screenX, screenY;
                        MapToScreen(x, y, screenX, screenY);
                        texture->Render(screenX, screenY);
                    }
                }
            }
        }
    }

    // Render middle layer
    if (m_renderMiddle) {
        for (int y = startY; y < endY; y++) {
            for (int x = startX; x < endX; x++) {
                const MapCell& cell = m_cells[y][x];
                uint16_t index = cell.GetMiddleImageIndex();

                if (index > 0) {
                    std::shared_ptr<Texture> texture = GetTileTexture(index, 1);
                    if (texture) {
                        int screenX, screenY;
                        MapToScreen(x, y, screenX, screenY);
                        texture->Render(screenX, screenY);
                    }
                }
            }
        }
    }

    // Render object layer
    if (m_renderObjects) {
        for (int y = startY; y < endY; y++) {
            for (int x = startX; x < endX; x++) {
                const MapCell& cell = m_cells[y][x];
                uint16_t index = cell.GetObjectImageIndex();

                // Check if this is a door
                if (cell.GetDoorIndex() > 0) {
                    // Adjust index based on door state
                    index = cell.GetDoorIndex() + cell.GetDoorOffset();
                }
                // Check if this is an animated tile
                else if (cell.GetAniFrame() > 0) {
                    // Adjust index based on animation frame
                    index += cell.GetAniFrame();
                }

                if (index > 0) {
                    std::shared_ptr<Texture> texture = GetTileTexture(index, 2);
                    if (texture) {
                        int screenX, screenY;
                        MapToScreen(x, y, screenX, screenY);
                        texture->Render(screenX, screenY);
                    }
                }
            }
        }
    }
}

void MapManager::UpdateAnimations(int deltaTime)
{
    // Update animations for all cells
    for (int y = 0; y < m_height; y++) {
        for (int x = 0; x < m_width; x++) {
            MapCell& cell = m_cells[y][x];

            // Update animation
            cell.UpdateAnimation(deltaTime);
        }
    }
}

bool MapManager::ToggleDoor(int x, int y)
{
    // Check if coordinates are valid
    if (!IsValidCoordinate(x, y)) {
        return false;
    }

    // Get the cell
    MapCell& cell = m_cells[y][x];

    // Check if this is a door
    if (cell.GetDoorIndex() == 0) {
        return false;
    }

    // Toggle door state
    bool isOpen = cell.ToggleDoor();

    // Update walkable status based on door state
    cell.SetWalkable(isOpen);

    return isOpen;
}

MapCell& MapManager::GetCell(int x, int y)
{
    // Ensure coordinates are valid
    x = std::max(0, std::min(x, m_width - 1));
    y = std::max(0, std::min(y, m_height - 1));

    return m_cells[y][x];
}

bool MapManager::IsValidCoordinate(int x, int y) const
{
    return x >= 0 && x < m_width && y >= 0 && y < m_height;
}

bool MapManager::IsWalkable(int x, int y) const
{
    if (!IsValidCoordinate(x, y)) {
        return false;
    }

    return m_cells[y][x].IsWalkable();
}

bool MapManager::ScreenToMap(int screenX, int screenY, int& mapX, int& mapY) const
{
    // Adjust for camera position
    screenX += m_cameraX;
    screenY += m_cameraY;

    // Convert to map coordinates
    mapX = screenX / m_tileWidth;
    mapY = screenY / m_tileHeight;

    // Check if coordinates are valid
    return IsValidCoordinate(mapX, mapY);
}

void MapManager::MapToScreen(int mapX, int mapY, int& screenX, int& screenY) const
{
    // Convert to screen coordinates
    screenX = mapX * m_tileWidth - m_cameraX;
    screenY = mapY * m_tileHeight - m_cameraY;
}

bool MapManager::LoadTileTextures(const std::string& tilesetName)
{
    // Clear existing textures
    m_tileTextures.clear();

    // 根据原始Delphi项目的格式构建文件名
    // 原始项目中，地图文件使用的是 Tiles.wil, SmTiles.wil 和 Objects.wil
    // 我们直接使用这些默认的资源文件

    // 加载调色板文件
    std::string paletteFile = ResourcePaths::DEFAULT_PALETTE;

    // 加载默认的tiles
    if (!m_wilManager->LoadWIL(ResourcePaths::TILES, paletteFile)) {
        std::cerr << "Failed to load default tiles: " << ResourcePaths::TILES << std::endl;
        return false;
    }

    if (!m_wilManager->LoadWIL(ResourcePaths::SMALL_TILES, paletteFile)) {
        std::cerr << "Failed to load default small tiles: " << ResourcePaths::SMALL_TILES << std::endl;
        return false;
    }

    if (!m_wilManager->LoadWIL(ResourcePaths::OBJECTS, paletteFile)) {
        std::cerr << "Failed to load default objects: " << ResourcePaths::OBJECTS << std::endl;
        return false;
    }

    std::cout << "Successfully loaded default tiles." << std::endl;

    return true;
}

std::shared_ptr<Texture> MapManager::GetTileTexture(uint16_t index, int layer)
{
    if (index == 0) {
        return nullptr;
    }

    // Create a unique key for the texture
    std::string key = std::to_string(layer) + "_" + std::to_string(index);

    // Check if the texture is already cached
    auto it = m_tileTextures.find(key);
    if (it != m_tileTextures.end()) {
        return it->second;
    }

    // Get the WIL file name for the layer
    std::string wilFile;
    switch (layer) {
        case 0: wilFile = ResourcePaths::TILES; break;        // Background layer
        case 1: wilFile = ResourcePaths::SMALL_TILES; break;  // Middle layer
        case 2: wilFile = ResourcePaths::OBJECTS; break;      // Object layer
        default: return nullptr;
    }

    // Get the surface from the WIL manager
    SDL_Surface* surface = m_wilManager->GetSurface(wilFile, index);
    if (!surface) {
        return nullptr;
    }

    // Create a texture from the surface
    std::shared_ptr<Texture> texture = std::make_shared<Texture>(nullptr);  // TODO: Pass renderer
    if (!texture->LoadFromSurface(surface)) {
        return nullptr;
    }

    // Cache the texture
    m_tileTextures[key] = texture;

    return texture;
}

std::vector<std::tuple<int, int, MapCell*>> MapManager::GetCellsInRange(int centerX, int centerY, int range)
{
    std::vector<std::tuple<int, int, MapCell*>> result;

    // Calculate range boundaries
    int startX = std::max(0, centerX - range);
    int endX = std::min(m_width - 1, centerX + range);
    int startY = std::max(0, centerY - range);
    int endY = std::min(m_height - 1, centerY + range);

    // Iterate through cells in range
    for (int y = startY; y <= endY; y++) {
        for (int x = startX; x <= endX; x++) {
            // Calculate distance from center
            int dx = x - centerX;
            int dy = y - centerY;
            int distanceSquared = dx * dx + dy * dy;

            // Check if cell is within range
            if (distanceSquared <= range * range) {
                result.push_back(std::make_tuple(x, y, &m_cells[y][x]));
            }
        }
    }

    return result;
}

bool MapManager::SetAreaState(int x, int y, int state)
{
    // Check if coordinates are valid
    if (!IsValidCoordinate(x, y)) {
        return false;
    }

    // Get the cell
    MapCell& cell = m_cells[y][x];

    // Set the area state
    // In a real implementation, this would update the area state
    // and potentially trigger area-specific effects
    std::cout << "Setting area state at " << x << "," << y << " to " << state << std::endl;

    // TODO: Implement area state logic
    return true;
}

bool MapManager::SetDoorState(int x, int y, int state)
{
    // Check if coordinates are valid
    if (!IsValidCoordinate(x, y)) {
        return false;
    }

    // Get the cell
    MapCell& cell = m_cells[y][x];

    // Check if this is a door
    if (cell.GetDoorIndex() == 0) {
        return false;
    }

    // Set door state (0 = closed, 1 = open)
    bool isOpen = (state != 0);

    // Update door state
    if (isOpen) {
        cell.OpenDoor();
    } else {
        cell.CloseDoor();
    }

    // Update walkable status based on door state
    cell.SetWalkable(isOpen);

    return true;
}

bool MapManager::SetDoorState(int x, int y, int doorIndex, int doorOffset, bool isOpen)
{
    // Check if coordinates are valid
    if (!IsValidCoordinate(x, y)) {
        return false;
    }

    // Get the cell
    MapCell& cell = m_cells[y][x];

    // Set door index and offset if provided
    if (doorIndex > 0) {
        cell.SetDoorIndex(doorIndex);
        cell.SetDoorOffset(doorOffset);
    }

    // Update door state
    if (isOpen) {
        cell.OpenDoor();
    } else {
        cell.CloseDoor();
    }

    // Update walkable status based on door state
    cell.SetWalkable(isOpen);

    return true;
}

void MapManager::InitializeLightMap()
{
    // 遍历所有地图单元格，初始化光照信息
    for (int y = 0; y < m_height; y++) {
        for (int x = 0; x < m_width; x++) {
            MapCell& cell = m_cells[y][x];

            // 获取单元格的光照值
            uint8_t light = cell.GetLight();

            // 如果光照值大于0，则添加光照效果
            if (light > 0) {
                // 在原始Delphi项目中，这里会调用LightManager的AddLight方法
                // 在我们的实现中，我们需要在PlayState中处理这个逻辑
                // 这里我们只是记录有光照的单元格位置
                std::cout << "Light found at (" << x << "," << y << ") with value " << static_cast<int>(light) << std::endl;

                // 在实际实现中，我们需要将这些信息传递给LightManager
                // 例如：m_lightManager->AddLight(x, y, 0, 0, light, false);
            }
        }
    }

    std::cout << "Light map initialized." << std::endl;
}
