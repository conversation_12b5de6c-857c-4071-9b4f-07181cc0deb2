#pragma once

#include <string>
#include <cstdint>

/**
 * @enum ServerStatus
 * @brief Server status
 */
enum class ServerStatus {
    OFFLINE,
    ONLINE,
    BUSY,
    FULL
};

/**
 * @class ServerInfo
 * @brief Information about a game server
 * 
 * This class represents information about a game server, including its name,
 * address, port, status, and player count.
 */
class ServerInfo {
private:
    std::string m_name;       ///< Server name
    std::string m_address;    ///< Server address
    uint16_t m_port;          ///< Server port
    ServerStatus m_status;    ///< Server status
    int m_playerCount;        ///< Current player count
    int m_maxPlayers;         ///< Maximum player count
    
public:
    /**
     * @brief Constructor
     * @param name Server name
     * @param address Server address
     * @param port Server port
     * @param status Server status
     * @param playerCount Current player count
     * @param maxPlayers Maximum player count
     */
    ServerInfo(
        const std::string& name = "",
        const std::string& address = "",
        uint16_t port = 0,
        ServerStatus status = ServerStatus::OFFLINE,
        int playerCount = 0,
        int maxPlayers = 0
    );
    
    /**
     * @brief Destructor
     */
    ~ServerInfo();
    
    /**
     * @brief Get the server name
     * @return Server name
     */
    const std::string& GetName() const { return m_name; }
    
    /**
     * @brief Set the server name
     * @param name Server name
     */
    void SetName(const std::string& name) { m_name = name; }
    
    /**
     * @brief Get the server address
     * @return Server address
     */
    const std::string& GetAddress() const { return m_address; }
    
    /**
     * @brief Set the server address
     * @param address Server address
     */
    void SetAddress(const std::string& address) { m_address = address; }
    
    /**
     * @brief Get the server port
     * @return Server port
     */
    uint16_t GetPort() const { return m_port; }
    
    /**
     * @brief Set the server port
     * @param port Server port
     */
    void SetPort(uint16_t port) { m_port = port; }
    
    /**
     * @brief Get the server status
     * @return Server status
     */
    ServerStatus GetStatus() const { return m_status; }
    
    /**
     * @brief Set the server status
     * @param status Server status
     */
    void SetStatus(ServerStatus status) { m_status = status; }
    
    /**
     * @brief Get the current player count
     * @return Current player count
     */
    int GetPlayerCount() const { return m_playerCount; }
    
    /**
     * @brief Set the current player count
     * @param playerCount Current player count
     */
    void SetPlayerCount(int playerCount) { m_playerCount = playerCount; }
    
    /**
     * @brief Get the maximum player count
     * @return Maximum player count
     */
    int GetMaxPlayers() const { return m_maxPlayers; }
    
    /**
     * @brief Set the maximum player count
     * @param maxPlayers Maximum player count
     */
    void SetMaxPlayers(int maxPlayers) { m_maxPlayers = maxPlayers; }
    
    /**
     * @brief Get the server status as a string
     * @return Server status as a string
     */
    std::string GetStatusString() const;
    
    /**
     * @brief Get the player count as a string
     * @return Player count as a string
     */
    std::string GetPlayerCountString() const;
};
