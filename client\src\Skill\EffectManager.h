#pragma once

#include <SDL2/SDL.h>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>
#include "../Graphics/Texture.h"
#include "../Graphics/WILLoader.h"
#include "Skill.h"

/**
 * @struct Effect
 * @brief 表示一个技能效果
 */
struct Effect {
    int x;                          // X坐标
    int y;                          // Y坐标
    int targetX;                    // 目标X坐标
    int targetY;                    // 目标Y坐标
    int startTime;                  // 开始时间
    int duration;                   // 持续时间
    int currentFrame;               // 当前帧
    int totalFrames;                // 总帧数
    EffectType type;                // 效果类型
    int effectValue;                // 效果值
    bool isMoving;                  // 是否移动
    bool isBlending;                // 是否混合
    int light;                      // 光照值
    std::shared_ptr<Texture> texture; // 纹理
};

/**
 * @class EffectManager
 * @brief 管理技能效果
 */
class EffectManager {
private:
    std::vector<Effect> m_effects;                  // 效果列表
    std::shared_ptr<WILManager> m_wilManager;       // WIL管理器
    SDL_Renderer* m_renderer;                       // SDL渲染器
    // 效果纹理缓存
    std::unordered_map<std::string, std::shared_ptr<Texture>> m_effectTextures;

    // 单例实例
    static EffectManager* s_instance;

    /**
     * @brief 私有构造函数（单例模式）
     */
    EffectManager();

public:
    /**
     * @brief 获取单例实例
     * @return EffectManager实例
     */
    static EffectManager* GetInstance();

    /**
     * @brief 释放单例实例
     */
    static void ReleaseInstance();

    /**
     * @brief 析构函数
     */
    ~EffectManager();

    /**
     * @brief 初始化效果管理器
     * @param wilManager WIL管理器
     * @param renderer SDL渲染器
     * @return 是否成功初始化
     */
    bool Initialize(std::shared_ptr<WILManager> wilManager, SDL_Renderer* renderer);

    /**
     * @brief 更新效果
     * @param deltaTime 时间增量
     */
    void Update(int deltaTime);

    /**
     * @brief 渲染效果
     */
    void Render();

    /**
     * @brief 添加效果
     * @param x X坐标
     * @param y Y坐标
     * @param targetX 目标X坐标
     * @param targetY 目标Y坐标
     * @param type 效果类型
     * @param effectValue 效果值
     * @param duration 持续时间
     * @return 是否成功添加
     */
    bool AddEffect(int x, int y, int targetX, int targetY, EffectType type, int effectValue, int duration);

    /**
     * @brief 清除所有效果
     */
    void ClearEffects();

    /**
     * @brief 获取效果纹理
     * @param type 效果类型
     * @param effectValue 效果值
     * @param frame 帧索引
     * @return 纹理指针
     */
    std::shared_ptr<Texture> GetEffectTexture(EffectType type, int effectValue, int frame);

    /**
     * @brief 播放技能效果
     * @param skill 技能
     * @param x 施放者X坐标
     * @param y 施放者Y坐标
     * @param targetX 目标X坐标
     * @param targetY 目标Y坐标
     * @return 是否成功播放
     */
    bool PlaySkillEffect(std::shared_ptr<Skill> skill, int x, int y, int targetX, int targetY);

    /**
     * @brief 创建火焰效果
     * @param x 施放者X坐标
     * @param y 施放者Y坐标
     * @param targetX 目标X坐标
     * @param targetY 目标Y坐标
     * @param effectValue 效果值
     * @return 是否成功创建
     */
    bool CreateFireEffect(int x, int y, int targetX, int targetY, int effectValue);

    /**
     * @brief 创建冰冻效果
     * @param x 施放者X坐标
     * @param y 施放者Y坐标
     * @param targetX 目标X坐标
     * @param targetY 目标Y坐标
     * @param effectValue 效果值
     * @return 是否成功创建
     */
    bool CreateIceEffect(int x, int y, int targetX, int targetY, int effectValue);

    /**
     * @brief 创建闪电效果
     * @param x 施放者X坐标
     * @param y 施放者Y坐标
     * @param targetX 目标X坐标
     * @param targetY 目标Y坐标
     * @param effectValue 效果值
     * @return 是否成功创建
     */
    bool CreateLightningEffect(int x, int y, int targetX, int targetY, int effectValue);

    /**
     * @brief 创建治疗效果
     * @param x 施放者X坐标
     * @param y 施放者Y坐标
     * @param targetX 目标X坐标
     * @param targetY 目标Y坐标
     * @param effectValue 效果值
     * @return 是否成功创建
     */
    bool CreateHealEffect(int x, int y, int targetX, int targetY, int effectValue);

    /**
     * @brief 创建增益效果
     * @param x 施放者X坐标
     * @param y 施放者Y坐标
     * @param targetX 目标X坐标
     * @param targetY 目标Y坐标
     * @param effectValue 效果值
     * @return 是否成功创建
     */
    bool CreateBuffEffect(int x, int y, int targetX, int targetY, int effectValue);

    /**
     * @brief 创建减益效果
     * @param x 施放者X坐标
     * @param y 施放者Y坐标
     * @param targetX 目标X坐标
     * @param targetY 目标Y坐标
     * @param effectValue 效果值
     * @return 是否成功创建
     */
    bool CreateDebuffEffect(int x, int y, int targetX, int targetY, int effectValue);
};

