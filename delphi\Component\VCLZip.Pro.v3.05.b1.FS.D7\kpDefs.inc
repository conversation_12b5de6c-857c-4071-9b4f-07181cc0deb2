{ KPDEFS.INC }

{$IFDEF VER110}
  {$ObjExportAll On}     { 4/20/98  2.11}
{$ENDIF}
{$IFDEF VER125}
  {$ObjExportAll On}     { 3/22/99  2.17+}
{$ENDIF}

{$IFNDEF NO_HUGE_FILES}
   {$DEFINE IMPLEMENT_HUGE_FILES}
{$ENDIF}
{$IFNDEF NO_USE_ZLIB}
  {$DEFINE USE_ZLIB}
{$ENDIF}

 {$IFDEF VER80}               { Delphi 1 }
  {$DEFINE ISDELPHI1}
  {$DEFINE ISDELPHI}
{$ENDIF}
{$IFDEF VER90}                { Delphi 2 }
  {$DEFINE ISDELPHI2}
  {$DEFINE ISDELPHI}
{$ENDIF}
{$IFDEF VER100}               { Delphi 3 }
  {$DEFINE DELPHI_BCB_3}
  {$DEFINE ISDELPHI3}
  {$DEFINE ISDELPHI}
{$ENDIF}
{$IFDEF VER120}               { Delphi 4 }
  {$DEFINE DELPHI_BCB_3}
  {$DEFINE ISDELPHI4}
  {$DEFINE HAS_64_BIT_INT}
  {$DEFINE ISDELPHI}
{$ENDIF}
{$IFDEF VER130}
  {$DEFINE DELPHI_BCB_3}
  {$DEFINE HAS_64_BIT_INT}
  {$IFDEF BCB}                { BCB 5    }
        {$DEFINE ISBCB5}
        {$DEFINE ISBCB}
  {$ELSE}                     { Delphi 5 }
        {$DEFINE ISDELPHI4}
        {$DEFINE ISDELPHI5}
        {$DEFINE ISDELPHI}
   {$ENDIF}
{$ENDIF}
{$IFDEF VER140}
  {$DEFINE DELPHI_BCB_3}
  {$DEFINE HAS_64_BIT_INT}
  {$DEFINE INT64STREAMS}
  {$DEFINE ISCLX}
  {$IFDEF BCB}                { BCB 6    }
        {$DEFINE ISBCB6}
        {$DEFINE ISBCB}
  {$ELSE}                     { Delphi 6 }
        {$WARN SYMBOL_PLATFORM OFF}
        {$WARN UNIT_PLATFORM OFF}
        {$DEFINE ISDELPHI4}
        {$DEFINE ISDELPHI5}
        {$DEFINE ISDELPHI6}
        {$DEFINE ISDELPHI}
   {$ENDIF}
{$ENDIF}
{$IFDEF VER150}              { Delphi 7}
  {$DEFINE DELPHI_BCB_3}
  {$DEFINE HAS_64_BIT_INT}
  {$WARN SYMBOL_PLATFORM OFF}
  {$WARN UNIT_PLATFORM OFF}
  {$DEFINE INT64STREAMS}
  {$DEFINE ISDELPHI4}
  {$DEFINE ISDELPHI5}
  {$DEFINE ISDELPHI6}
  {$DEFINE ISDELPHI7}
  {$DEFINE ISDELPHI}
  {$DEFINE ISCLX}
{$ELSE}
  {$DEFINE DELPHI_BCB_3}
  {$DEFINE HAS_64_BIT_INT}
  {$WARN SYMBOL_PLATFORM OFF}
  {$WARN UNIT_PLATFORM OFF}
  {$DEFINE INT64STREAMS}
  {$DEFINE ISDELPHI4}
  {$DEFINE ISDELPHI5}
  {$DEFINE ISDELPHI6}
  {$DEFINE ISDELPHI7}
  {$DEFINE ISDELPHI}
  {$DEFINE ISCLX}
{$ENDIF}
{$IFDEF VER93}               { BCB 1    }
  {$DEFINE DELPHI_BCB_3}
  {$DEFINE ISBCB1}
  {$DEFINE ISBCB}
{$ENDIF}
{$IFDEF VER110}               { BCB 3    }
  {$DEFINE DELPHI_BCB_3}
  {$DEFINE ISBCB3}
  {$DEFINE ISBCB}
{$ENDIF}
{$IFDEF VER125}               { BCB 4    }
  {$DEFINE DELPHI_BCB_3}
  {$DEFINE ISBCB4}
  {$DEFINE ISBCB}
  {$DEFINE HAS_64_BIT_INT}
{$ENDIF}

{$IFDEF MAKESMALL}
{$DEFINE KPSMALL}
{$IFNDEF WIN32}
{$DEFINE NOLONGNAMES}
{$DEFINE NODISKUTILS}
{$ENDIF}
{$ENDIF}
{$IFDEF WIN32}
{$IFDEF NOLONGNAMES}
{$UNDEF NOLONGNAMES}
{$UNDEF NODISKUTILS}
{$ENDIF}
{$ENDIF}

