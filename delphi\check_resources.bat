@echo off
echo Checking for required resource files...
echo.

REM Check for map file
if exist "assets\maps\town.map" (
    echo [OK] Found map file: assets\maps\town.map
) else (
    echo [MISSING] Map file not found: assets\maps\town.map
)

REM Check for tileset files
if exist "assets\data\town_background.wil" (
    echo [OK] Found tileset file: assets\data\town_background.wil
) else (
    if exist "assets\data\town_background.wzl" (
        echo [OK] Found tileset file: assets\data\town_background.wzl
    ) else (
        echo [MISSING] Tileset file not found: assets\data\town_background.wil or .wzl
    )
)

if exist "assets\data\town_middle.wil" (
    echo [OK] Found tileset file: assets\data\town_middle.wil
) else (
    if exist "assets\data\town_middle.wzl" (
        echo [OK] Found tileset file: assets\data\town_middle.wzl
    ) else (
        echo [MISSING] Tileset file not found: assets\data\town_middle.wil or .wzl
    )
)

if exist "assets\data\town_object.wil" (
    echo [OK] Found tileset file: assets\data\town_object.wil
) else (
    if exist "assets\data\town_object.wzl" (
        echo [OK] Found tileset file: assets\data\town_object.wzl
    ) else (
        echo [MISSING] Tileset file not found: assets\data\town_object.wil or .wzl
    )
)

REM Check for palette file
if exist "assets\data\town.pal" (
    echo [OK] Found palette file: assets\data\town.pal
) else (
    echo [MISSING] Palette file not found: assets\data\town.pal
)

REM Check for font file
if exist "assets\fonts\arial.ttf" (
    echo [OK] Found font file: assets\fonts\arial.ttf
) else (
    echo [MISSING] Font file not found: assets\fonts\arial.ttf
)

REM Check for main resource files
echo.
echo Checking main resource files...

if exist "Data\Prguse.wzl" (
    echo [OK] Found resource file: Data\Prguse.wzl
) else (
    if exist "Data\Prguse.wil" (
        echo [OK] Found resource file: Data\Prguse.wil
    ) else (
        echo [MISSING] Resource file not found: Data\Prguse.wzl or .wil
    )
)

if exist "Data\Prguse2.wil" (
    echo [OK] Found resource file: Data\Prguse2.wil
) else (
    if exist "Data\Prguse2.wzl" (
        echo [OK] Found resource file: Data\Prguse2.wzl
    ) else (
        echo [MISSING] Resource file not found: Data\Prguse2.wil or .wzl
    )
)

if exist "Data\Prguse3.wil" (
    echo [OK] Found resource file: Data\Prguse3.wil
) else (
    if exist "Data\Prguse3.wzl" (
        echo [OK] Found resource file: Data\Prguse3.wzl
    ) else (
        echo [MISSING] Resource file not found: Data\Prguse3.wil or .wzl
    )
)

REM Check for character resource files
echo.
echo Checking character resource files...

if exist "Data\Hum.wil" (
    echo [OK] Found resource file: Data\Hum.wil
) else (
    if exist "Data\Hum.wzl" (
        echo [OK] Found resource file: Data\Hum.wzl
    ) else (
        echo [MISSING] Resource file not found: Data\Hum.wil or .wzl
    )
)

if exist "Data\Hair.wil" (
    echo [OK] Found resource file: Data\Hair.wil
) else (
    if exist "Data\Hair.wzl" (
        echo [OK] Found resource file: Data\Hair.wzl
    ) else (
        echo [MISSING] Resource file not found: Data\Hair.wil or .wzl
    )
)

if exist "Data\Weapon.wil" (
    echo [OK] Found resource file: Data\Weapon.wil
) else (
    if exist "Data\Weapon.wzl" (
        echo [OK] Found resource file: Data\Weapon.wzl
    ) else (
        echo [MISSING] Resource file not found: Data\Weapon.wil or .wzl
    )
)

REM Check for map resource files
echo.
echo Checking map resource files...

if exist "Data\Tiles.wil" (
    echo [OK] Found resource file: Data\Tiles.wil
) else (
    if exist "Data\Tiles.wzl" (
        echo [OK] Found resource file: Data\Tiles.wzl
    ) else (
        echo [MISSING] Resource file not found: Data\Tiles.wil or .wzl
    )
)

if exist "Data\SmTiles.wil" (
    echo [OK] Found resource file: Data\SmTiles.wil
) else (
    if exist "Data\SmTiles.wzl" (
        echo [OK] Found resource file: Data\SmTiles.wzl
    ) else (
        echo [MISSING] Resource file not found: Data\SmTiles.wil or .wzl
    )
)

if exist "Data\Objects.wil" (
    echo [OK] Found resource file: Data\Objects.wil
) else (
    if exist "Data\Objects.wzl" (
        echo [OK] Found resource file: Data\Objects.wzl
    ) else (
        echo [MISSING] Resource file not found: Data\Objects.wil or .wzl
    )
)

echo.
echo Resource check complete.
pause
