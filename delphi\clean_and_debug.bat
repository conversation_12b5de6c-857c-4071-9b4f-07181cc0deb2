@echo off
echo Cleaning logs and running MirClient in Debug Mode...

REM 设置DEBUG环境变量以启用调试输出
set _DEBUG=1
set DEBUG=1

REM 切换到可执行文件所在目录
cd %~dp0\build\bin

REM 创建logs目录（如果不存在）
if not exist logs mkdir logs

REM 清空日志文件
echo. > logs\main.log
echo. > logs\application.log
echo. > logs\fallback.log

REM 运行程序
echo Starting MirClient.exe...
MirClient.exe

REM A运行结束后暂停
echo.
echo Program exited. Press any key to continue...
pause 