# Manager接口与事件通信实现指南

## 1. 接口通信实现

### 1.1 UserEngine实现IPlayerProvider接口

```cpp
// UserEngine.h
class UserEngine : public IManager, public IPlayerProvider, public IEventPublisher {
private:
    std::unordered_map<std::string, std::shared_ptr<PlayObject>> m_players;
    std::unique_ptr<EventBus> m_eventBus;
    mutable std::shared_mutex m_playersMutex;

public:
    // IManager接口实现
    bool Initialize() override;
    void Finalize() override;
    void Update() override;
    const std::string& GetManagerName() const override;

    // IPlayerProvider接口实现  
    PlayObject* FindPlayer(const std::string& playerName) override;
    std::vector<PlayObject*> GetPlayersInRange(const Point& center, int range) override;
    int GetOnlinePlayerCount() const override;

    // IEventPublisher接口实现
    void PublishEvent(const std::string& eventType, const EventData& data) override;

    // UserEngine特有功能
    bool AddPlayer(std::shared_ptr<PlayObject> player);
    bool RemovePlayer(const std::string& playerName);
    void ProcessPlayers();
};

// UserEngine.cpp
bool UserEngine::Initialize() {
    m_eventBus = std::make_unique<EventBus>();
    Logger::Info("UserEngine initialized");
    return true;
}

PlayObject* UserEngine::FindPlayer(const std::string& playerName) {
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);
    auto it = m_players.find(playerName);
    return (it != m_players.end()) ? it->second.get() : nullptr;
}

std::vector<PlayObject*> UserEngine::GetPlayersInRange(const Point& center, int range) {
    std::vector<PlayObject*> result;
    std::shared_lock<std::shared_mutex> lock(m_playersMutex);
    
    for (const auto& [name, player] : m_players) {
        if (GetDistance(player->GetPosition(), center) <= range) {
            result.push_back(player.get());
        }
    }
    return result;
}

bool UserEngine::AddPlayer(std::shared_ptr<PlayObject> player) {
    {
        std::unique_lock<std::shared_mutex> lock(m_playersMutex);
        m_players[player->GetCharName()] = player;
    }
    
    // 发布玩家登录事件
    auto eventData = std::make_unique<PlayerLoginEventData>();
    eventData->playerName = player->GetCharName();
    eventData->player = player.get();
    eventData->loginTime = GetCurrentTime();
    
    PublishEvent("PlayerLogin", *eventData);
    
    Logger::Info("Player added: " + player->GetCharName());
    return true;
}
```

### 1.2 ItemManager实现IItemProvider接口

```cpp
// ItemManager.h
class ItemManager : public IManager, public IItemProvider, public IEventSubscriber {
private:
    std::vector<std::unique_ptr<StdItem>> m_stdItems;
    std::unordered_map<int, ItemDropConfig> m_dropConfigs;
    IPlayerProvider* m_playerProvider;
    EventBus* m_eventBus;
    mutable std::shared_mutex m_itemsMutex;

public:
    // 依赖注入
    void SetPlayerProvider(IPlayerProvider* provider);
    void SetEventBus(EventBus* eventBus);

    // IItemProvider接口实现
    const StdItem* GetStdItem(int itemIndex) const override;
    UserItem CreateItem(int itemIndex) const override;
    bool ValidateItem(const UserItem& item) const override;

    // IEventSubscriber接口实现
    void OnEvent(const std::string& eventType, const EventData& data) override;

    // ItemManager特有功能
    bool LoadItemDatabase();
    std::vector<UserItem> GenerateMonsterDrops(const std::string& monsterName, int level);
};

// ItemManager.cpp
void ItemManager::SetPlayerProvider(IPlayerProvider* provider) {
    m_playerProvider = provider;
}

void ItemManager::SetEventBus(EventBus* eventBus) {
    m_eventBus = eventBus;
    
    // 订阅相关事件
    if (m_eventBus) {
        m_eventBus->Subscribe("PlayerLogin", [this](const EventData& data) {
            OnEvent("PlayerLogin", data);
        });
        
        m_eventBus->Subscribe("MonsterKilled", [this](const EventData& data) {
            OnEvent("MonsterKilled", data);
        });
    }
}

const StdItem* ItemManager::GetStdItem(int itemIndex) const {
    std::shared_lock<std::shared_mutex> lock(m_itemsMutex);
    if (itemIndex >= 0 && itemIndex < static_cast<int>(m_stdItems.size())) {
        return m_stdItems[itemIndex].get();
    }
    return nullptr;
}

UserItem ItemManager::CreateItem(int itemIndex) const {
    const StdItem* stdItem = GetStdItem(itemIndex);
    if (!stdItem) {
        Logger::Warning("Invalid item index: " + std::to_string(itemIndex));
        return UserItem{};
    }

    UserItem item;
    item.wIndex = itemIndex;
    item.MakeIndex = GenerateUniqueId();
    item.Dura = stdItem->DuraMax;
    // 初始化其他属性...
    
    return item;
}

void ItemManager::OnEvent(const std::string& eventType, const EventData& data) {
    if (eventType == "PlayerLogin") {
        const auto& loginData = static_cast<const PlayerLoginEventData&>(data);
        ValidatePlayerItems(loginData.player);
    }
    else if (eventType == "MonsterKilled") {
        const auto& killData = static_cast<const MonsterKilledEventData&>(data);
        ProcessMonsterDrops(killData);
    }
}

void ItemManager::ProcessMonsterDrops(const MonsterKilledEventData& killData) {
    // 生成掉落物品
    auto drops = GenerateMonsterDrops(killData.monsterName, killData.monsterLevel);
    
    for (const auto& item : drops) {
        // 发布物品掉落事件
        if (m_eventBus) {
            auto dropEvent = std::make_unique<ItemDropEventData>();
            dropEvent->playerName = killData.killerName;
            dropEvent->item = item;
            dropEvent->position = killData.position;
            dropEvent->mapName = killData.mapName;
            
            m_eventBus->PublishAsync("ItemDropped", std::move(dropEvent));
        }
    }
}
```

## 2. 事件系统实现

### 2.1 事件数据定义

```cpp
// EventData.h
#pragma once
#include "../Common/Types.h"

// 事件数据基类
struct EventData {
    DWORD timestamp;
    
    EventData() : timestamp(GetCurrentTime()) {}
    virtual ~EventData() = default;
};

// 玩家登录事件
struct PlayerLoginEventData : public EventData {
    std::string playerName;
    std::string account;
    PlayObject* player;
    std::string ipAddress;
    DWORD loginTime;
};

// 玩家登出事件
struct PlayerLogoutEventData : public EventData {
    std::string playerName;
    std::string account;
    DWORD onlineTime;
    std::string reason;
};

// 物品掉落事件
struct ItemDropEventData : public EventData {
    std::string playerName;
    UserItem item;
    Point position;
    std::string mapName;
    std::string reason; // "monster_drop", "player_drop", "death_drop"
};

// 怪物击杀事件
struct MonsterKilledEventData : public EventData {
    std::string killerName;
    std::string monsterName;
    int monsterLevel;
    Point position;
    std::string mapName;
    int experience;
};

// 任务完成事件
struct QuestCompletedEventData : public EventData {
    std::string playerName;
    int questId;
    std::string questName;
    int experience;
    std::vector<UserItem> rewards;
};

// 地图切换事件
struct MapChangeEventData : public EventData {
    std::string playerName;
    std::string fromMap;
    std::string toMap;
    Point fromPosition;
    Point toPosition;
};

// PK事件
struct PKEventData : public EventData {
    std::string killerName;
    std::string victimName;
    Point position;
    std::string mapName;
    int pkPoints;
    bool isRedName;
};
```

### 2.2 事件总线实现

```cpp
// EventBus.h
#pragma once
#include "EventData.h"
#include <functional>
#include <unordered_map>
#include <vector>
#include <mutex>
#include <queue>
#include <thread>
#include <atomic>

using EventHandler = std::function<void(const EventData&)>;

class EventBus {
private:
    // 同步事件处理器
    std::unordered_map<std::string, std::vector<EventHandler>> m_syncHandlers;
    std::mutex m_syncMutex;
    
    // 异步事件队列
    std::queue<std::pair<std::string, std::unique_ptr<EventData>>> m_asyncQueue;
    std::mutex m_asyncMutex;
    std::condition_variable m_asyncCondition;
    
    // 异步处理线程
    std::thread m_asyncThread;
    std::atomic<bool> m_running;
    
    // 统计信息
    std::atomic<uint64_t> m_totalEvents;
    std::atomic<uint64_t> m_processedEvents;

public:
    EventBus();
    ~EventBus();
    
    // 生命周期管理
    bool Start();
    void Stop();
    bool IsRunning() const { return m_running; }
    
    // 事件订阅
    void Subscribe(const std::string& eventType, EventHandler handler);
    void Unsubscribe(const std::string& eventType);
    
    // 同步事件发布
    void Publish(const std::string& eventType, const EventData& data);
    
    // 异步事件发布
    void PublishAsync(const std::string& eventType, std::unique_ptr<EventData> data);
    
    // 批量处理异步事件
    void ProcessAsyncEvents();
    
    // 统计信息
    uint64_t GetTotalEvents() const { return m_totalEvents; }
    uint64_t GetProcessedEvents() const { return m_processedEvents; }
    size_t GetQueueSize() const;

private:
    void AsyncEventLoop();
    void PublishToHandlers(const std::string& eventType, const EventData& data);
};

// EventBus.cpp
EventBus::EventBus() : m_running(false), m_totalEvents(0), m_processedEvents(0) {
}

EventBus::~EventBus() {
    Stop();
}

bool EventBus::Start() {
    if (m_running) return true;
    
    m_running = true;
    m_asyncThread = std::thread(&EventBus::AsyncEventLoop, this);
    
    Logger::Info("EventBus started");
    return true;
}

void EventBus::Stop() {
    if (!m_running) return;
    
    m_running = false;
    m_asyncCondition.notify_all();
    
    if (m_asyncThread.joinable()) {
        m_asyncThread.join();
    }
    
    Logger::Info("EventBus stopped");
}

void EventBus::Subscribe(const std::string& eventType, EventHandler handler) {
    std::lock_guard<std::mutex> lock(m_syncMutex);
    m_syncHandlers[eventType].push_back(std::move(handler));
    
    Logger::Debug("Event subscription added: " + eventType);
}

void EventBus::Publish(const std::string& eventType, const EventData& data) {
    ++m_totalEvents;
    PublishToHandlers(eventType, data);
    ++m_processedEvents;
}

void EventBus::PublishAsync(const std::string& eventType, std::unique_ptr<EventData> data) {
    ++m_totalEvents;
    
    {
        std::lock_guard<std::mutex> lock(m_asyncMutex);
        m_asyncQueue.emplace(eventType, std::move(data));
    }
    
    m_asyncCondition.notify_one();
}

void EventBus::AsyncEventLoop() {
    while (m_running) {
        std::unique_lock<std::mutex> lock(m_asyncMutex);
        
        m_asyncCondition.wait(lock, [this] {
            return !m_asyncQueue.empty() || !m_running;
        });
        
        while (!m_asyncQueue.empty()) {
            auto [eventType, eventData] = std::move(m_asyncQueue.front());
            m_asyncQueue.pop();
            lock.unlock();
            
            try {
                PublishToHandlers(eventType, *eventData);
                ++m_processedEvents;
            } catch (const std::exception& e) {
                Logger::Error("Error processing async event: " + std::string(e.what()));
            }
            
            lock.lock();
        }
    }
}

void EventBus::PublishToHandlers(const std::string& eventType, const EventData& data) {
    std::lock_guard<std::mutex> lock(m_syncMutex);
    
    auto it = m_syncHandlers.find(eventType);
    if (it != m_syncHandlers.end()) {
        for (const auto& handler : it->second) {
            try {
                handler(data);
            } catch (const std::exception& e) {
                Logger::Error("Event handler error for " + eventType + ": " + e.what());
            }
        }
    }
}
```

## 3. 服务容器实现

### 3.1 ServiceContainer完整实现

```cpp
// ServiceContainer.h
#pragma once
#include "IManager.h"
#include "EventBus.h"
#include <memory>
#include <unordered_map>
#include <typeindex>

class ServiceContainer {
private:
    std::unordered_map<std::string, std::shared_ptr<IManager>> m_managersByName;
    std::unordered_map<std::type_index, std::shared_ptr<IManager>> m_managersByType;
    std::unique_ptr<EventBus> m_eventBus;
    bool m_initialized;

public:
    ServiceContainer();
    ~ServiceContainer();
    
    // 生命周期管理
    bool Initialize();
    void Finalize();
    bool IsInitialized() const { return m_initialized; }
    
    // Manager注册
    template<typename T>
    void RegisterManager(std::shared_ptr<T> manager) {
        static_assert(std::is_base_of_v<IManager, T>, "T must inherit from IManager");
        
        // 按名称注册
        m_managersByName[manager->GetManagerName()] = manager;
        
        // 按类型注册
        m_managersByType[std::type_index(typeid(T))] = manager;
        
        Logger::Info("Manager registered: " + manager->GetManagerName());
    }
    
    // 按类型获取Manager
    template<typename T>
    T* GetManager() {
        auto it = m_managersByType.find(std::type_index(typeid(T)));
        if (it != m_managersByType.end()) {
            return std::static_pointer_cast<T>(it->second).get();
        }
        return nullptr;
    }
    
    // 按名称获取Manager
    IManager* GetManager(const std::string& name);
    
    // 获取服务接口
    template<typename Interface>
    Interface* GetService() {
        for (const auto& [name, manager] : m_managersByName) {
            if (auto service = std::dynamic_pointer_cast<Interface>(manager)) {
                return service.get();
            }
        }
        return nullptr;
    }
    
    // 获取事件总线
    EventBus* GetEventBus() { return m_eventBus.get(); }
    
    // 获取所有Manager
    std::vector<std::string> GetManagerNames() const;
    
private:
    void SetupManagerDependencies();
    void SetupEventSubscriptions();
    bool InitializeAllManagers();
    void FinalizeAllManagers();
};

// ServiceContainer.cpp
ServiceContainer::ServiceContainer() : m_initialized(false) {
    m_eventBus = std::make_unique<EventBus>();
}

ServiceContainer::~ServiceContainer() {
    Finalize();
}

bool ServiceContainer::Initialize() {
    if (m_initialized) return true;
    
    Logger::Info("Initializing ServiceContainer...");
    
    // 启动事件总线
    if (!m_eventBus->Start()) {
        Logger::Error("Failed to start EventBus");
        return false;
    }
    
    // 设置Manager间依赖关系
    SetupManagerDependencies();
    
    // 设置事件订阅
    SetupEventSubscriptions();
    
    // 初始化所有Manager
    if (!InitializeAllManagers()) {
        Logger::Error("Failed to initialize managers");
        return false;
    }
    
    m_initialized = true;
    Logger::Info("ServiceContainer initialized successfully");
    return true;
}

void ServiceContainer::SetupManagerDependencies() {
    // UserEngine依赖设置
    auto userEngine = GetManager<UserEngine>();
    if (userEngine) {
        userEngine->SetEventBus(m_eventBus.get());
    }
    
    // ItemManager依赖设置
    auto itemManager = GetManager<ItemManager>();
    if (itemManager) {
        itemManager->SetPlayerProvider(GetService<IPlayerProvider>());
        itemManager->SetEventBus(m_eventBus.get());
    }
    
    // QuestManager依赖设置
    auto questManager = GetManager<QuestManager>();
    if (questManager) {
        questManager->SetPlayerProvider(GetService<IPlayerProvider>());
        questManager->SetItemProvider(GetService<IItemProvider>());
        questManager->SetMapProvider(GetService<IMapProvider>());
        questManager->SetEventBus(m_eventBus.get());
    }
    
    // MonsterManager依赖设置
    auto monsterManager = GetManager<MonsterManager>();
    if (monsterManager) {
        monsterManager->SetPlayerProvider(GetService<IPlayerProvider>());
        monsterManager->SetEventBus(m_eventBus.get());
    }
}

void ServiceContainer::SetupEventSubscriptions() {
    // 自动设置各Manager的事件订阅
    for (const auto& [name, manager] : m_managersByName) {
        if (auto subscriber = std::dynamic_pointer_cast<IEventSubscriber>(manager)) {
            SetupManagerEventSubscription(subscriber.get(), name);
        }
    }
}

bool ServiceContainer::InitializeAllManagers() {
    // 按依赖顺序初始化Manager
    std::vector<std::string> initOrder = {
        "GameConfigManager",
        "LogManager", 
        "DatabaseManager",
        "UserEngine",
        "MapManager",
        "ItemManager",
        "MonsterManager",
        "NPCManager",
        "MagicManager",
        "QuestManager",
        "TradeManager"
        // ... 其他Manager
    };
    
    for (const std::string& managerName : initOrder) {
        auto manager = GetManager(managerName);
        if (manager && !manager->Initialize()) {
            Logger::Error("Failed to initialize manager: " + managerName);
            return false;
        }
    }
    
    return true;
}
```

## 4. 实际使用示例

### 4.1 GameEngine集成使用

```cpp
// GameEngine.cpp
bool GameEngine::InitializeManagers() {
    m_serviceContainer = std::make_unique<ServiceContainer>();
    
    // 创建并注册Manager
    auto userEngine = std::make_shared<UserEngine>();
    auto itemManager = std::make_shared<ItemManager>();
    auto mapManager = std::make_shared<MapManager>();
    auto questManager = std::make_shared<QuestManager>();
    auto monsterManager = std::make_shared<MonsterManager>();
    
    m_serviceContainer->RegisterManager(userEngine);
    m_serviceContainer->RegisterManager(itemManager);
    m_serviceContainer->RegisterManager(mapManager);
    m_serviceContainer->RegisterManager(questManager);
    m_serviceContainer->RegisterManager(monsterManager);
    
    // 初始化服务容器
    return m_serviceContainer->Initialize();
}

// 使用示例：处理玩家登录
bool GameEngine::HandlePlayerLogin(const std::string& account, const std::string& charName) {
    // 通过UserEngine添加玩家
    auto userEngine = m_serviceContainer->GetManager<UserEngine>();
    if (!userEngine) return false;
    
    auto player = std::make_shared<PlayObject>();
    player->SetCharName(charName);
    player->SetAccount(account);
    
    // UserEngine会自动发布PlayerLogin事件
    // ItemManager、QuestManager等会自动响应该事件
    return userEngine->AddPlayer(player);
}
```

### 4.2 QuestManager使用多接口示例

```cpp
// QuestManager.cpp - 完成任务的完整流程
bool QuestManager::CompleteQuest(const std::string& playerName, int questId) {
    // 1. 通过IPlayerProvider获取玩家
    PlayObject* player = m_playerProvider->FindPlayer(playerName);
    if (!player) {
        Logger::Warning("Player not found: " + playerName);
        return false;
    }
    
    // 2. 获取任务信息
    Quest* quest = GetQuest(questId);
    if (!quest || !IsQuestActive(player, questId)) {
        Logger::Warning("Invalid or inactive quest: " + std::to_string(questId));
        return false;
    }
    
    // 3. 检查完成条件
    if (!CheckQuestConditions(player, quest)) {
        return false;
    }
    
    // 4. 给予奖励
    for (const auto& reward : quest->rewards) {
        switch (reward.type) {
            case RewardType::ITEM: {
                // 通过IItemProvider创建物品
                UserItem item = m_itemProvider->CreateItem(reward.itemIndex);
                if (item.wIndex > 0) {
                    player->AddToBag(item);
                    
                    // 发布物品获得事件
                    auto itemEvent = std::make_unique<ItemObtainEventData>();
                    itemEvent->playerName = playerName;
                    itemEvent->item = item;
                    itemEvent->source = "quest_reward";
                    m_eventBus->PublishAsync("ItemObtained", std::move(itemEvent));
                }
                break;
            }
            
            case RewardType::EXPERIENCE: {
                player->IncExp(reward.value);
                break;
            }
            
            case RewardType::TELEPORT: {
                // 通过IMapProvider验证地图
                Environment* targetMap = m_mapProvider->GetEnvironment(reward.mapName);
                if (targetMap && targetMap->CanWalk(reward.x, reward.y)) {
                    player->SpaceMove(reward.mapName, reward.x, reward.y);
                }
                break;
            }
        }
    }
    
    // 5. 标记任务完成
    MarkQuestCompleted(player, questId);
    
    // 6. 发布任务完成事件
    auto questEvent = std::make_unique<QuestCompletedEventData>();
    questEvent->playerName = playerName;
    questEvent->questId = questId;
    questEvent->questName = quest->name;
    questEvent->experience = quest->expReward;
    questEvent->rewards = quest->itemRewards;
    
    m_eventBus->PublishAsync("QuestCompleted", std::move(questEvent));
    
    Logger::Info("Quest completed: " + playerName + " - " + quest->name);
    return true;
}

// 响应其他Manager的事件
void QuestManager::OnEvent(const std::string& eventType, const EventData& data) {
    if (eventType == "PlayerLogin") {
        const auto& loginData = static_cast<const PlayerLoginEventData&>(data);
        LoadPlayerQuests(loginData.player);
        CheckDailyQuestReset(loginData.player);
    }
    else if (eventType == "MonsterKilled") {
        const auto& killData = static_cast<const MonsterKilledEventData&>(data);
        UpdateKillQuests(killData.killerName, killData.monsterName, killData.monsterLevel);
    }
    else if (eventType == "ItemObtained") {
        const auto& itemData = static_cast<const ItemObtainEventData&>(data);
        UpdateCollectionQuests(itemData.playerName, itemData.item.wIndex);
    }
    else if (eventType == "MapChange") {
        const auto& mapData = static_cast<const MapChangeEventData&>(data);
        UpdateExplorationQuests(mapData.playerName, mapData.toMap);
    }
}
```

这个实现指南提供了完整的Manager间通信机制，包括接口设计、事件系统、依赖注入和具体使用示例。通过这种架构，各Manager之间实现了低耦合、高内聚的设计，便于维护和扩展。 