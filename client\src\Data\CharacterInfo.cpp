#include "CharacterInfo.h"

CharacterInfo::CharacterInfo(
    const std::string& name,
    PlayerClass playerClass,
    int level,
    int experience,
    int health,
    int mana,
    int strength,
    int dexterity,
    int vitality,
    int energy,
    int lastMapIndex,
    int lastPositionX,
    int lastPositionY
)
    : m_name(name)
    , m_class(playerClass)
    , m_level(level)
    , m_experience(experience)
    , m_health(health)
    , m_mana(mana)
    , m_strength(strength)
    , m_dexterity(dexterity)
    , m_vitality(vitality)
    , m_energy(energy)
    , m_lastMapIndex(lastMapIndex)
    , m_lastPositionX(lastPositionX)
    , m_lastPositionY(lastPositionY)
{
}

CharacterInfo::~CharacterInfo()
{
}

std::string CharacterInfo::GetClassString() const
{
    switch (m_class) {
        case PlayerClass::WARRIOR:
            return "Warrior";
        case PlayerClass::WIZARD:
            return "Wizard";
        case PlayerClass::TAOIST:
            return "Taoist";
        case PlayerClass::ASSASSIN:
            return "Assassin";
        default:
            return "Unknown";
    }
}
